<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.scrm.mapper.FormsSpecialResultsMapper">

    <select id="selectUnConvertOne" resultType="com.panda.pollen.scrm.domain.FormsSpecialResults">
        select t.*
        from ads_forms_special_results t
        join ads_forms_results afr on afr.advertiser_id = t.a_id and afr.plan_id = t.plan_id
        where t.a_id = #{aId}
          and t.plan_id = #{planId}
          and t.click_id != #{clickId}
          and afr.sub_convert_flag is null
          and afr.pay_convert_flag is null
          and afr.add_convert_flag is null
          <if test="within24Hours == true">
              and t.create_time &gt;= now() - interval 24 hour
              order by t.id
          </if>
          <if test="within24Hours == false">
              and t.create_time &lt; now() - interval 24 hour
              order by t.id desc
          </if>
        limit 1
    </select>

    <delete id="deletePayedResult">
        delete from ads_forms_special_results
        where a_id = #{aId}
          and plan_id = #{planId}
          and click_id = #{clickId}
    </delete>

</mapper>