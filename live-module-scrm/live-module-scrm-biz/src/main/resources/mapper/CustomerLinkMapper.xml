<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.scrm.mapper.CustomerLinkMapper">

    <resultMap type="com.panda.pollen.scrm.domain.CustomerLink" id="CustomerLinkResult">
        <result property="id" column="id"/>
        <result property="linkName" column="link_name"/>
        <result property="linkId" column="link_id"/>
        <result property="linkUrl" column="link_url"/>
        <result property="state" column="state"/>
        <result property="linkShortUrl" column="link_short_url"/>
        <result property="skipVerify" column="skip_verify"/>
        <result property="userList" column="user_list"/>
        <result property="departmentList" column="department_list"/>
        <result property="tagIds" column="tag_ids"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
        <result property="isOpenFeedback" column="is_open_feedback"/>
        <result property="corpId" column="corp_id"/>
    </resultMap>

    <sql id="selectCustomerLinkVo">
        select id,
               link_name,
               link_id,
               link_url,
               state,
               link_short_url,
               skip_verify,
               user_list,
               department_list,
               tag_ids,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               deleted,
               is_open_feedback,
               corp_id
        from wx_customer_link
    </sql>

    <select id="selectCustomerLinkList" parameterType="com.panda.pollen.scrm.query.CustomerLinkQuery"
            resultType="com.panda.pollen.scrm.domain.CustomerLink">
        select wcl.*,
        u.nick_name,
        d.dept_name,
        d.dept_id,
        dd.dept_name AS first_dept_name,
        dd.dept_id AS first_dept_id,
        dd.create_by AS business,
        uu.nick_name AS person_in_charge
        from wx_customer_link wcl
        LEFT JOIN sys_user u ON wcl.create_by = u.user_name and u.del_flag = '0'
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id AND d.del_flag = '0'
        LEFT JOIN sys_dept dd ON d.first_dept_id = dd.dept_id AND d.del_flag = '0'
        LEFT JOIN sys_user uu ON dd.create_by = uu.user_name and u.del_flag = '0'
        left join wx_customer_link_group_item wclgi on wclgi.link_id = wcl.link_id
        left join wx_customer_link_user wclu on wclu.link_id=wcl.link_id
        where wcl.deleted = 0
          and wclgi.id is null
        <if test="linkName != null and linkName != ''">and wcl.link_name like concat('%', #{linkName}, '%')</if>
        <if test="linkType != null">and wcl.link_type = #{linkType}</if>
        <if test="linkId != null  and linkId != ''">and wcl.link_id = #{linkId}</if>
        <if test="params.beginTime != null and params.beginTime != ''"> and  date_format(wcl.create_time, '%Y-%m-%d') &gt;= #{params.beginTime} </if>
        <if test="params.endTime != null and params.endTime != ''">and date_format(wcl.create_time, '%Y-%m-%d') &lt;= #{params.endTime} </if>
        <if test="deptIds != null">
            AND u.dept_id IN
            <foreach collection="deptIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="createBy != null and createBy != ''">and wcl.create_by = #{createBy}</if>
        <if test="business != null and business != ''">and dd.create_by = #{business}</if>
        <if test="linkUserName != null and linkUserName != ''">and (wclu.user_name like concat('%', #{linkUserName}, '%') or wcl.user_list_name like concat('%', #{linkUserName}, '%'))</if>
        ${params.dataScope}
        group by wcl.id
        order by create_time DESC
    </select>

    <select id="selectCustomerLinkById" parameterType="Long" resultMap="CustomerLinkResult">
        <include refid="selectCustomerLinkVo"/>
        where id = #{id}
    </select>

</mapper>