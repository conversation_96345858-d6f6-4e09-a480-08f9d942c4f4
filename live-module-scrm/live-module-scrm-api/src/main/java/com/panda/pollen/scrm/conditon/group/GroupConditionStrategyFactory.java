package com.panda.pollen.scrm.conditon.group;

import com.panda.pollen.scrm.dto.GroupSearchConditionDTO;
import com.panda.pollen.scrm.enums.GroupSearchType;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date : 2025年06月28日 15:22
 */
public class GroupConditionStrategyFactory {

    private static final Map<GroupSearchType, GroupConditionStrategy> strategyMap = new HashMap<>();

    static {
        strategyMap.put(GroupSearchType.GROUP_NAME, new GroupNameSearchStrategy());
        // 可以继续添加其他类型的策略
    }

    public static GroupConditionStrategy getStrategy(GroupSearchType searchType) {
        return strategyMap.getOrDefault(searchType, new DefaultSearchStrategy());
    }

    /**
     * 默认策略,如果没有找到对应的策略，默认返回一个空串，防止报错
     */
    private static class DefaultSearchStrategy implements GroupConditionStrategy {
        @Override
        public String generateConditionSql(GroupSearchConditionDTO conditionDTO) {
            return "";
        }
    }

}
