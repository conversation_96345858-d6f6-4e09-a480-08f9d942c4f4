package com.panda.pollen.scrm.enums;

import cn.hutool.core.util.ObjectUtil;

import java.util.Arrays;

/**
 * 表单提交结果状态
 * <AUTHOR>
 * @Date : 2024年02月22日 17:22
 */
public enum FormsResultsStatus {

    PENDING_PROCESSING(1, "待处理"),
    PROCESSED(2, "已处理"),
    REVIEW_FAILED(3, "审核失败")
    ;

    private int code;
    private String desc;

    FormsResultsStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据枚举Code值返回枚举类型
     * @param code
     * @return
     */
    public static FormsResultsStatus getByValue(int code) {
        return Arrays.stream(values())
                .filter(status -> ObjectUtil.equals(status.getCode(), code))
                .findFirst()
                .orElse(PENDING_PROCESSING);
    }

}
