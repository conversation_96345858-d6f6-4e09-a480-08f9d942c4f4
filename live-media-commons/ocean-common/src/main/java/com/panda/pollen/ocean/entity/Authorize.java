/**
 * All rights Reserved, Designed By <br>
 * Title：Authorize.java <br>
 * Package：com.panda.ocean.entity <br>
 * Description：(用一句话描述该文件做什么) <br>
 * Copyright © 2023 luojl All rights reserved. <br>
 *
 * <AUTHOR> <br>
 * date 2023年6月6日 上午11:07:56 <br>
 * @version v1.0 <br>
 */
package com.panda.pollen.ocean.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * ClassName：com.panda.ocean.entity.Authorize <br>
 * Description：授权对象 <br>
 * Copyright © 2023 luojl All rights reserved. <br>
 *
 * <AUTHOR> <br>
 * date 2023年6月6日 上午11:07:56 <br>
 * @version v1.0 <br>
 */
@Data
public class Authorize {

    @JSONField(name = "access_token")
    private String accessToken;

    @JSONField(name = "expires_in")
    private Integer expiresIn;

    @JSONField(name = "refresh_token")
    private String refreshToken;

    @JSONField(name = "refresh_token_expires_in")
    private Integer refreshTokenExpiresIn;

    @JSONField(name = "advertiser_ids")
    private List<Long> advertiserIds;

}
