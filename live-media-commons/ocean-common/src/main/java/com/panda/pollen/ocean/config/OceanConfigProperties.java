package com.panda.pollen.ocean.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 巨量引擎配置类参数
 *
 * <AUTHOR>
 * @date 2023/03/02
 */
@Data
@Component
@ConfigurationProperties(OceanConfigProperties.PREFIX)
public class OceanConfigProperties {

    public static final String PREFIX = "panda.ocean";

    private Long appId;

    private String secret;

    private Long conversionAppId;

    private String conversionSecret;

    private String conversionTecAgent;
}
