package com.panda.pollen.wecom.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.framework.ratelimiter.core.annotation.RateRedisLimiter;
import com.panda.pollen.scrm.dto.AddGroupTagDTO;
import com.panda.pollen.scrm.dto.AddTagGroupDTO;
import com.panda.pollen.scrm.dto.DeleteGroupTagDTO;
import com.panda.pollen.scrm.dto.DeleteTagGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.panda.pollen.wecom.constants.WeComConstants.*;

/**
 * 企微客户标签相关
 * <AUTHOR>
 * @Date : 2025年06月24日 14:52
 */
@Component
@Slf4j
public class WeComTagService {

    /**
     * 获取企业标签库
     * https://developer.work.weixin.qq.com/document/path/96320
     * @param accessToken
     * @param groupIdList   分组id集合，如果传了该参数，那么只返回该标签组以及其下的所有标签信息
     * @return
     */
    @RateRedisLimiter(count = 10000, timeUnit = TimeUnit.MINUTES, keyArg = GET_CORP_TAG)
    public JSONObject getCorpTags(String accessToken, List<String> groupIdList) {
        String url = GET_CORP_TAG + "?access_token=" + accessToken;
        Map<String, Object> params = new HashMap<>();
        if (CollUtil.isNotEmpty(groupIdList)) {
            params.put("group_id", groupIdList);
        }
        HttpResponse response = HttpUtil.createPost(url).body(JSON.toJSONString(params)).execute();
        if (response.isOk()) {
            String body = response.body();
            JSONObject resultObject = JSON.parseObject(body);
            int errCode = resultObject.getIntValue("errcode", -1);
            if (errCode == 0) {
                return resultObject;
            } else {
                log.error("获取企业标签库失败：{}", resultObject.getString("errmsg"));
                throw new ServiceException("获取企业标签库失败：{}", resultObject.getString("errmsg"));
            }
        } else {
            log.error("获取企业标签库失败");
            throw new ServiceException("获取企业标签库失败");
        }
    }


    /**
     * 添加标签分组
     * https://developer.work.weixin.qq.com/document/path/96320
     * @param accessToken
     * @param addTagGroupDTO
     * @param groupOrderNo
     * @return
     */
    @RateRedisLimiter(count = 10000, timeUnit = TimeUnit.MINUTES, keyArg = ADD_CORP_TAG)
    public JSONObject addTagGroup(String accessToken, AddTagGroupDTO addTagGroupDTO, int groupOrderNo) {
        String url = ADD_CORP_TAG + "?access_token=" + accessToken;
        Map<String, Object> params = new HashMap<>();
        params.put("group_name", addTagGroupDTO.getGroupName());
        params.put("order", groupOrderNo);
        List<JSONObject> tags = new ArrayList<>();
        for (String tag : addTagGroupDTO.getTags()) {
            JSONObject tagObject = new JSONObject();
            tagObject.put("name", tag);
            tags.add(tagObject);
        }
        params.put("tag", tags);
        HttpResponse response = HttpUtil.createPost(url).body(JSON.toJSONString(params)).execute();
        if (response.isOk()) {
            String body = response.body();
            JSONObject resultObject = JSON.parseObject(body);
            int errCode = resultObject.getIntValue("errcode", -1);
            if (errCode == 0) {
                return resultObject;
            } else {
                log.error("添加标签分组失败：{}", resultObject.getString("errmsg"));
                throw new ServiceException("添加标签分组失败：{}", resultObject.getString("errmsg"));
            }
        } else {
            log.error("添加标签分组失败");
            throw new ServiceException("添加标签分组失败");
        }
    }

    /**
     * 添加分组标签
     * https://developer.work.weixin.qq.com/document/path/96320
     * @param accessToken
     * @param addGroupTagDTO
     * @return
     */
    @RateRedisLimiter(count = 10000, timeUnit = TimeUnit.MINUTES, keyArg = ADD_CORP_TAG)
    public JSONObject addGroupTag(String accessToken, AddGroupTagDTO addGroupTagDTO) {
        String url = ADD_CORP_TAG + "?access_token=" + accessToken;
        Map<String, Object> params = new HashMap<>();
        params.put("group_id", addGroupTagDTO.getGroupId());
        List<JSONObject> tags = new ArrayList<>();
        for (String tag : addGroupTagDTO.getTags()) {
            JSONObject tagObject = new JSONObject();
            tagObject.put("name", tag);
            tags.add(tagObject);
        }
        params.put("tag", tags);
        HttpResponse response = HttpUtil.createPost(url).body(JSON.toJSONString(params)).execute();
        if (response.isOk()) {
            String body = response.body();
            JSONObject resultObject = JSON.parseObject(body);
            int errCode = resultObject.getIntValue("errcode", -1);
            if (errCode == 0) {
                return resultObject;
            } else {
                log.error("添加分组标签失败：{}", resultObject.getString("errmsg"));
                throw new ServiceException("添加分组标签失败：{}", resultObject.getString("errmsg"));
            }
        } else {
            log.error("添加分组标签失败");
            throw new ServiceException("添加分组标签失败");
        }
    }


    /**
     * 删除客户标签分组/标签
     * @param accessToken
     * @param deleteTagGroupDTO
     */
    @RateRedisLimiter(count = 10000, timeUnit = TimeUnit.MINUTES, keyArg = DELETE_CORP_TAG)
    public JSONObject deleteTagOrGroup(String accessToken, DeleteTagGroupDTO deleteTagGroupDTO, DeleteGroupTagDTO deleteGroupTagDTO) {
        String url = DELETE_CORP_TAG + "?access_token=" + accessToken;
        Map<String, Object> params = new HashMap<>();
        if (deleteTagGroupDTO != null && CollUtil.isNotEmpty(deleteTagGroupDTO.getGroupIdList())) {
            params.put("group_id", deleteTagGroupDTO.getGroupIdList());
        }
        if (deleteGroupTagDTO != null && CollUtil.isNotEmpty(deleteGroupTagDTO.getTagIdList())) {
            params.put("tag_id", deleteGroupTagDTO.getTagIdList());
        }
        HttpResponse response = HttpUtil.createPost(url).body(JSON.toJSONString(params)).execute();
        if (response.isOk()) {
            String body = response.body();
            JSONObject resultObject = JSON.parseObject(body);
            int errCode = resultObject.getIntValue("errcode", -1);
            if (errCode == 0) {
                return resultObject;
            } else {
                log.error("删除企微客户标签失败：{}", resultObject.getString("errmsg"));
                throw new ServiceException("删除企微客户标签失败：{}", resultObject.getString("errmsg"));
            }
        } else {
            log.error("删除企微客户标签失败");
            throw new ServiceException("删除企微客户标签失败");
        }
    }


    /**
     * 编辑企业客户标签
     * 企业可通过此接口编辑客户标签/标签组的名称或次序值。
     * https://developer.work.weixin.qq.com/document/path/96320
     * @param accessToken
     * @param targetId
     * @param targetName
     * @param targetOrder
     * @return
     */
    @RateRedisLimiter(count = 10000, timeUnit = TimeUnit.MINUTES, keyArg = EDIT_CORP_TAG)
    public JSONObject editTagOrGroup(String accessToken, String targetId, String targetName, Integer targetOrder) {
        String url = EDIT_CORP_TAG + "?access_token=" + accessToken;
        Map<String, Object> params = new HashMap<>();
        params.put("id", targetId);
        if (StrUtil.isNotBlank(targetName)) {
            params.put("name", targetName);
        }
        if (targetOrder != null) {
            params.put("order", targetOrder);
        }
        HttpResponse response = HttpUtil.createPost(url).body(JSON.toJSONString(params)).execute();
        if (response.isOk()) {
            String body = response.body();
            JSONObject resultObject = JSON.parseObject(body);
            int errCode = resultObject.getIntValue("errcode", -1);
            if (errCode == 0) {
                return resultObject;
            } else {
                log.error("编辑企业客户标签失败：{}", resultObject.getString("errmsg"));
                throw new ServiceException("编辑企业客户标签失败：{}", resultObject.getString("errmsg"));
            }
        } else {
            log.error("编辑企业客户标签失败");
            throw new ServiceException("编辑企业客户标签失败");
        }
    }



}
