
/**  
 * All rights Reserved, Designed By http://www.eternal.com/ <br>
 * Title：BodyArguments.java <br>
 * Package：com.panda.robot.domin <br> 
 * Copyright © 2025 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2025年6月7日 下午10:54:59 <br>
 * @version v1.0 <br>
 */ 
package com.panda.robot.domain;

import com.panda.robot.enums.CommandType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * ClassName：com.panda.robot.domin.BodyArguments <br>
 * Description：链接消息请求Body参数对象 <br>
 * Copyright © 2025 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2025年6月7日 下午10:54:59 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LinkArguments implements CommandArguments{

	/** 指令类型 */
	private CommandType type;
	/** 昵称,群名/备注 */
	private String searchText;
	/** 菜单名称 */
	private String menuName;
	
	private String link;
	
	private String title;
	
	private String desc;
	
	private String imgUrl;
	
	@Override
	public Integer getType() {
		if(type != null) {
			return type.getType();
		}
		return null;
	}
	
}
