package com.panda.pollen.controller;

import cn.hutool.json.JSONUtil;
import com.panda.pollen.aui.client.utils.LiveGroupSignUtils;
import com.panda.pollen.aui.model.dto.LiveCallbackNoticeDto;
import com.panda.pollen.aui.model.dto.LiveCallbackPushBreakExpNoticeDto;
import com.panda.pollen.aui.model.dto.LiveCallbackPushBreakNoticeDto;
import com.panda.pollen.common.annotation.Anonymous;
import com.panda.pollen.common.core.domain.AjaxResultV2;
import com.panda.pollen.service.ILiveCallbackBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(value = "直播回调业务")
@RestController
@RequestMapping("/liveCallbackBiz")
public class LiveCallbackBizController {
    @Resource
    private ILiveCallbackBizService iLiveCallbackBizService;


    @Anonymous
    @ApiOperation("回调事件-云转推")
    @PostMapping("/notice")
    public AjaxResultV2<Boolean> notice(@RequestBody String body) {
        log.info("回调事件-云转推:{}", body);
        LiveCallbackNoticeDto dto = JSONUtil.toBean(body, LiveCallbackNoticeDto.class);
        log.info("回调事件-云转推：{}", dto);
        Boolean result = this.iLiveCallbackBizService.notice(dto);
        return AjaxResultV2.success(result);
    }


    @Anonymous
    @ApiOperation("回调事件-推流-推断流回调地址")
    @GetMapping("/noticePushBreak")
    public AjaxResultV2<Boolean> noticePushBreak(@RequestHeader("Ali-Live-Timestamp") String aliLiveTimestamp,
                                                 @RequestHeader(value = "Ali-Live-Signature") String aliLiveSignature,
                                                 LiveCallbackPushBreakNoticeDto dto) {
        log.info("回调事件-推流-推断流回调地址，aliLiveTimestamp is：{}", aliLiveTimestamp);
        log.info("回调事件-推流-推断流回调地址，aliLiveSignature is：{}", aliLiveSignature);
        log.info("回调事件-推流-推断流回调地址，LiveCallbackPushBreakNoticeDto is：{}", dto);
        this.iLiveCallbackBizService.verifySign(dto.getApp(), aliLiveTimestamp, aliLiveSignature);
        log.info("回调事件-推流-推断流回调地址：{}", dto);
        Boolean result = this.iLiveCallbackBizService.noticePushBreak(dto);
        return AjaxResultV2.success(result);
    }

    @Anonymous
    @ApiOperation("回调事件-推流-异常事件回调地址")
    @PostMapping("/noticePushBreakExp")
    public AjaxResultV2<Boolean> noticePushBreakExp(@RequestHeader("Ali-Live-Timestamp") String aliLiveTimestamp,
                                                    @RequestHeader(value = "Ali-Live-Signature") String aliLiveSignature,
                                                    @RequestBody LiveCallbackPushBreakExpNoticeDto dto) {
        log.info("回调事件-推流-异常事件回调地址，aliLiveTimestamp is：{}", aliLiveTimestamp);
        log.info("回调事件-推流-异常事件回调地址，aliLiveSignature is：{}", aliLiveSignature);
        log.info("回调事件-推流-异常事件回调地址，LiveCallbackPushBreakExpNoticeDto is：{}", dto);
        this.iLiveCallbackBizService.verifySign(dto.getDomain(), aliLiveTimestamp, aliLiveSignature);
        log.info("回调事件-推流-异常事件回调地址：{}", dto);
        Boolean result = this.iLiveCallbackBizService.noticePushBreakExp(dto);
        return AjaxResultV2.success(result);
    }
}
