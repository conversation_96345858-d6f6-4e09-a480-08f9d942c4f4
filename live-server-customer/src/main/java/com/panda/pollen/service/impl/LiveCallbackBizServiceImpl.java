package com.panda.pollen.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.SecureUtil;
import com.panda.pollen.aui.api.ILiveScriptTaskBizApi;
import com.panda.pollen.aui.api.LiveAuiMessageApi;
import com.panda.pollen.aui.mapstruct.MsLiveCallbackBiz;
import com.panda.pollen.aui.model.dto.*;
import com.panda.pollen.aui.system.domain.LiveAuiCloudTranscodeTask;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfo;
import com.panda.pollen.aui.system.service.ILiveAuiCloudTranscodeTaskService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoService;
import com.panda.pollen.common.core.service.ISysConfigService;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.exception.base.BaseException;
import com.panda.pollen.common.utils.sentinel.ThreadPoolUtils;
import com.panda.pollen.service.ILiveCallbackBizService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LiveCallbackBizServiceImpl implements ILiveCallbackBizService {
    @Resource
    private ILiveAuiRoomInfoService iLiveAuiRoomInfoService;
    @Resource
    private MsLiveCallbackBiz msLiveCallbackBiz;
    @Resource
    private ILiveAuiCloudTranscodeTaskService iLiveAuiCloudTranscodeTaskService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private LiveAuiMessageApi liveAuiMessageApi;
    @Resource
    private ILiveScriptTaskBizApi iLiveScriptTaskBizApi;

    private final ExecutorService downloadMessage4Ali = ThreadPoolUtils.createThreadPoolExecutor("downloadMessage4Ali", 4, 8);

    private final ExecutorService liveScriptInterpretationBiz = ThreadPoolUtils.createThreadPoolExecutor("liveScriptInterpretationBiz", 4, 8);

    @Override
    public Boolean notice(LiveCallbackNoticeDto dto) {
        String event = dto.getEvent();
        log.info("回调事件：{}", event);
        boolean result;
        switch (event) {
            // 拉流转推任务运行状态变化回调
            case "LivePullToPushRunning" -> {
                log.info("拉流转推任务运行状态变化回调");
                LiveCallbackNoticeStatusDto statusDto = this.msLiveCallbackBiz.dto2StatusDto(dto);
                result = this.notice(statusDto);
            }
            // 拉流转推任务已退出回调
            case "LivePullToPushExit" -> {
                log.info("拉流转推任务已退出回调");
                LiveCallbackNoticeExitDto exitDto = this.msLiveCallbackBiz.dto2ExitDto(dto);
                result = this.notice(exitDto);
            }
            default -> throw new IllegalStateException("Unexpected value: " + event);
        }
        return result;
    }

    @Override
    public Boolean noticePushBreak(LiveCallbackPushBreakNoticeDto dto) {
        String action = dto.getAction();
        log.info("事件:{}", action);
        LiveAuiRoomInfo liveAuiRoomInfo;
        switch (action) {
            case "publish" -> {
                log.info("推流回调");
                liveAuiRoomInfo = this.noticePushBreak(dto, 1);
                liveScriptInterpretationBiz.submit(()-> iLiveScriptTaskBizApi.play(liveAuiRoomInfo.getId()));
            }
            case "publish_done" -> {
                log.info("断流回调");
                liveAuiRoomInfo = this.noticePushBreak(dto, 2);
                // 使用线程下载消息
                downloadMessage4Ali.submit(() -> {
                    // 下载消息
                    boolean downloaded = liveAuiMessageApi.downloadMessage4Ali(String.valueOf(liveAuiRoomInfo.getId()));
                    if (!downloaded) {
                        throw new ServiceException("下载消息失败");
                    }
                });
            }
            default -> throw new IllegalStateException("Unexpected value: " + action);
        }
        return true;
    }

    /**
     * 推流-推断流回调地址
     *
     * @param dto    回调参数
     * @param status 状态
     * @return 回调结果
     */
    private LiveAuiRoomInfo noticePushBreak(LiveCallbackPushBreakNoticeDto dto, int status) {
        String id = dto.getId();
        // 获取直播间信息
        LiveAuiRoomInfo liveAuiRoomInfo = this.getLiveAuiRoomInfo(id);
        // 判断状态
        if (liveAuiRoomInfo.getStatus() != status) {
            // 直播间id
            boolean updated = this.iLiveAuiRoomInfoService.lambdaUpdate()
                    .set(LiveAuiRoomInfo::getStatus, status)
                    .eq(LiveAuiRoomInfo::getChatId, id)
                    .update();
            if (!updated) {
                throw new BaseException("更新直播间状态失败！");
            }
        }
        return liveAuiRoomInfo;
    }

    /**
     * 获取直播间信息
     * @param id 直播间id
     * @return 直播间信息
     */
    private LiveAuiRoomInfo getLiveAuiRoomInfo(String id) {
        List<LiveAuiRoomInfo> liveAuiRoomInfos = this.iLiveAuiRoomInfoService.lambdaQuery()
                .eq(LiveAuiRoomInfo::getChatId, id)
                .list();
        if (ObjectUtils.isEmpty(liveAuiRoomInfos)) {
            throw new BaseException("直播间不存在");
        }
        if (liveAuiRoomInfos.size() > 1) {
            throw new BaseException("存在脏数据，请清理");
        }
        return liveAuiRoomInfos.get(0);
    }

    @Override
    public Boolean noticePushBreakExp(LiveCallbackPushBreakExpNoticeDto dto) {
        log.info("发生异常，目前不知道做什么事情:{}", dto);
        return true;
    }

    @Override
    public void verifySign(String pushDomain, String aliLiveTimestamp, String aliLiveSignature) {
        /*
            ALI-LIVE-SIGNATURE的值由如下计算得出：
            ALI-LIVE-SIGNATURE=MD5SUM（MD5CONTENT)
            MD5CONTENT=推流域名|ALI-LIVE-TIMESTAMP取值|鉴权KEY
         */
        String liveCallbackAuthKey = this.sysConfigService.selectConfigByKey("live_callback_authKey");
        String md5content = CharSequenceUtil.format("{}|{}|{}", pushDomain, aliLiveTimestamp, liveCallbackAuthKey);
        String md5sum = SecureUtil.md5(md5content);
        log.info("md5content={}, md5sum={}", md5content, md5sum);
        if (!CharSequenceUtil.equals(md5sum, aliLiveSignature)) {
            log.error("处理阿里云视频直播间回调消息的时候，签名校验失败, aliLiveTimestamp={}, aliLiveSignature={}, authKey={},pushDomain={}",
                    aliLiveTimestamp, aliLiveSignature, liveCallbackAuthKey, pushDomain);
            throw new ServiceException("处理阿里云视频直播间回调消息的时候，签名校验失败");
        }
    }

    /**
     * 拉流转推任务已退出回调
     *
     * @param exitDto 退出回调
     * @return 执行结果
     */
    private boolean notice(LiveCallbackNoticeExitDto exitDto) {
        if (ObjectUtils.isEmpty(exitDto)) {
            throw new BaseException("退出回调参数错误");
        }
        // 任务状态
        String taskStatus = exitDto.getTaskStatus();
        boolean result = false;
        if ("-1".equalsIgnoreCase(taskStatus)) {
            String taskId = exitDto.getTaskId();
            result = this.stopLive(taskId);
        }
        return result;
    }

    /**
     * 停止直播
     * @param taskId 任务id
     * @return 停止结果
     */
    private boolean stopLive(String taskId) {
        LiveAuiCloudTranscodeTask liveAuiCloudTranscodeTask = this.getLiveAuiCloudTranscodeTask(taskId);
        // 直播间id
        Long roomInfoId = liveAuiCloudTranscodeTask.getRoomInfoId();
        boolean updated = this.iLiveAuiRoomInfoService.lambdaUpdate().set(LiveAuiRoomInfo::getStatus, 2).eq(LiveAuiRoomInfo::getId, roomInfoId).update();
        if (!updated) {
            throw new BaseException("更新直播间状态失败！");
        }
        return true;
    }

    /**
     * 获取本地云转推任务数据
     * @param taskId 任务id
     * @return 云转推任务数据
     */
    private LiveAuiCloudTranscodeTask getLiveAuiCloudTranscodeTask(String taskId) {
        LiveAuiCloudTranscodeTask liveAuiCloudTranscodeTask = this.iLiveAuiCloudTranscodeTaskService.lambdaQuery().eq(LiveAuiCloudTranscodeTask::getTaskId, taskId).one();
        if (ObjectUtils.isEmpty(liveAuiCloudTranscodeTask)) {
            throw new BaseException("本地云转推业务任务不存在");
        }
        return liveAuiCloudTranscodeTask;
    }

    /**
     * 拉流转推任务运行状态变化回调
     *
     * @param statusDto 运行状态回调
     * @return 执行结果
     */
    private boolean notice(LiveCallbackNoticeStatusDto statusDto) {
        if (ObjectUtils.isEmpty(statusDto)) {
            throw new BaseException("运行状态回调参数错误");
        }
        // 任务状态
        String taskStatus = statusDto.getTaskStatus();
        boolean result = false;
        switch (taskStatus) {
            case "1":
                log.info("Running......");
                String taskId = statusDto.getTaskId();
                log.info("任务ID:{}", taskId);
                result = this.runLive(taskId);
                break;
            case "2":
                log.info("Recovering......");
                break;
            case "3":
                log.info("Offline......");
                break;
            default:
                log.error("任务状态错误");
                throw new BaseException("任务状态错误");
        }
        return result;
    }

    /**
     * 开始直播
     * @param taskId 任务id
     * @return 直播结果
     */
    private boolean runLive(String taskId) {
        log.info("任务id:{}", taskId);
        LiveAuiCloudTranscodeTask liveAuiCloudTranscodeTask = this.getLiveAuiCloudTranscodeTask(taskId);
        // 直播间id
        Long roomInfoId = liveAuiCloudTranscodeTask.getRoomInfoId();
        boolean updated = this.iLiveAuiRoomInfoService.lambdaUpdate()
                .set(LiveAuiRoomInfo::getStatus, 1)
                .eq(LiveAuiRoomInfo::getId, roomInfoId)
                .update();
        if (!updated) {
            throw new BaseException("更新直播间状态失败！");
        }
        return true;
    }
}
