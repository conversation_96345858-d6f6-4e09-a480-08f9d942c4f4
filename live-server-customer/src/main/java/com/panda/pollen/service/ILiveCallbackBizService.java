package com.panda.pollen.service;

import com.panda.pollen.aui.model.dto.LiveCallbackNoticeDto;
import com.panda.pollen.aui.model.dto.LiveCallbackPushBreakExpNoticeDto;
import com.panda.pollen.aui.model.dto.LiveCallbackPushBreakNoticeDto;

/**
 * <AUTHOR>
 */
public interface ILiveCallbackBizService {
    /**
     * 直播回调
     *
     * @param dto 回调参数
     * @return 回调结果
     */
    Boolean notice(LiveCallbackNoticeDto dto);
    /**
     * 推流-推断流回调地址
     *
     * @param dto 回调参数
     * @return 回调结果
     */
    Boolean noticePushBreak(LiveCallbackPushBreakNoticeDto dto);
    /**
     * 推流-异常事件回调地址
     *
     * @param dto 回调参数
     * @return 回调结果
     */
    Boolean noticePushBreakExp(LiveCallbackPushBreakExpNoticeDto dto);

    /**
     * 验证签名
     *
     * @param pushDomain 回调参数
     * @param aliLiveTimestamp 时间戳
     * @param aliLiveSignature 签名
     */
    void verifySign(String pushDomain, String aliLiveTimestamp, String aliLiveSignature);
}
