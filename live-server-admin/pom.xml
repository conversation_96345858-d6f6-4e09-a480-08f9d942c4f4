<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.panda.pollen</groupId>
        <artifactId>pollen</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>live-server-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>
		<!-- mapstruct -->
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
		</dependency>
	
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-processor</artifactId>
		</dependency>
	
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok-mapstruct-binding</artifactId>
		</dependency> 
		
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 系统模块 -->
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-module-system</artifactId>
        </dependency>

        <!-- 代码生成 -->
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-module-generator</artifactId>
        </dependency>

        <!-- 通用工具 -->
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-commons</artifactId>
        </dependency>

        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-module-pay</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <!-- SpringDoc接口文档 -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.6.12</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-javadoc</artifactId>
            <version>1.6.12</version>
        </dependency>

        <!-- 单元测试 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
        </dependency>
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-module-scrm-biz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-module-aui-admin-mvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.panda.robot</groupId>
            <artifactId>live-module-robot-mvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-module-scrm-mvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-module-cid-mvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-module-aui-client-biz</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.18</version>
                <configuration>
                    <executable>true</executable>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>

        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>
</project>
