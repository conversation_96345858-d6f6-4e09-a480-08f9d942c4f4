//package com.panda.pollen.modules.ads.test;
//
//import com.panda.pollen.LiveAdminApplication;
//import com.panda.pollen.aui.client.api.LiveRoomCustomerActionDataApiImpl;
//import com.panda.pollen.aui.client.vo.LiveAuiRoomActiveMemberVO;
//import com.panda.pollen.aui.client.vo.LiveRoomCustomerPrivateMsgVO;
//import com.panda.pollen.aui.client.vo.LiveRoomCustomerTrackVO;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//import java.util.List;
//
///**
// * 模拟直播间客户轨迹测试类
// *
// * @Author: liuy
// * @Date: 2025年07月22日 16:41
// **/
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = {LiveAdminApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@ActiveProfiles({"dev","aui"})
//@Slf4j
//public class TrackTest {
//
//    @Autowired
//    private LiveRoomCustomerActionDataApiImpl liveRoomCustomerActionDataApi;
//    /**
//      * 模拟执行任务
//     *
//      * @param
//      * @return
//      **/
//    @Test
//    public void simulateBatchTaskJobExecute() {
//        log.info("开始执行模拟任务");
////        liveRoomCustomerActionDataApi.updateLiveMemberSpeakCount(1957642283973902336L,"1960681090651873280", true);
////        liveRoomCustomerActionDataApi.addLiveMemberBandCount(1957642283973902336L,"1960681090651873280");
////        liveRoomCustomerActionDataApi.addLiveMemberKickOutCount(1957642283973902336L,"1960681090651873280");
//        LiveAuiRoomActiveMemberVO liveRoomCustomerData = liveRoomCustomerActionDataApi.getLiveRoomCustomerData(1957642283973902336L, 1960681091591397376L);
//        log.info("获取直播间成员数据：{}", liveRoomCustomerData);
//        List<LiveRoomCustomerTrackVO> liveRoomCustomerTrackData = liveRoomCustomerActionDataApi.getLiveRoomCustomerTrackData(1957642283973902336L, 1960681091591397376L);
//        log.info("获取直播间成员行为数据：{}", liveRoomCustomerTrackData);
//        List<LiveRoomCustomerPrivateMsgVO> liveRoomCustomerPrivateMsgData = liveRoomCustomerActionDataApi.getLiveRoomCustomerPrivateMsgData(1957642283973902336L, 1960681091591397376L);
//        log.info("获取直播间成员私信数据：{}", liveRoomCustomerPrivateMsgData);
//        log.info("结束执行模拟任务");
//    }
//}
