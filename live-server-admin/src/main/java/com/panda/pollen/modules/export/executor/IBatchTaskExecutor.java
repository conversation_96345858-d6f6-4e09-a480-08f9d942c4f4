package com.panda.pollen.modules.export.executor;

import cn.hutool.core.util.StrUtil;
import com.panda.pollen.common.enums.advanced.export.BatchTaskType;
import com.panda.pollen.common.utils.ExceptionUtil;
import com.panda.pollen.modules.export.BatchTask;

/**
 * <AUTHOR>
 * @Date : 2024年08月26日 9:36
 */
public interface IBatchTaskExecutor {

    BatchTaskType getTaskType();

    void doExecute(BatchTask batchTask);

    /**
     * 记录任务失败的原因（兜底方案）
     *
     * @param batchTask
     * @param e
     */
    default void setFailMsg(BatchTask batchTask, Exception e) {
        if (batchTask != null && e != null && StrUtil.isBlank(batchTask.getFailMsg())) {
            batchTask.setFailMsg(ExceptionUtil.getExceptionMessage(e));
            if (StrUtil.length(batchTask.getFailMsg()) > 65535) {
                batchTask.setFailMsg(batchTask.getFailMsg().substring(0, 65535 - 1));
            }
        }
    }

}
