package com.panda.pollen.modules.ads.controller;

import cn.hutool.core.util.ArrayUtil;
import com.panda.pollen.common.annotation.Log;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResult;
import com.panda.pollen.common.core.domain.BaseEntity;
import com.panda.pollen.common.core.page.TableDataInfo;
import com.panda.pollen.common.core.redis.RedisCache;
import com.panda.pollen.common.enums.BusinessType;
import com.panda.pollen.common.enums.ads.MediaTypeEnum;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.utils.SecurityUtils;
import com.panda.pollen.common.utils.StringUtils;
import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.common.utils.poi.ExcelUtil;
import com.panda.pollen.modules.ads.domain.MediaAccountInfo;
import com.panda.pollen.modules.ads.dto.*;
import com.panda.pollen.modules.ads.manager.MediaAccountInfoManager;
import com.panda.pollen.modules.ads.manger.RedisInfoManager;
import com.panda.pollen.modules.ads.manger.impl.TransferManagerImpl;
import com.panda.pollen.modules.ads.service.IConversionConfigService;
import com.panda.pollen.modules.ads.service.IMediaAccountInfoService;
import com.panda.pollen.modules.ads.vo.MediaAccountComboVO;
import com.panda.pollen.modules.domain.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.panda.pollen.modules.ads.constant.Constants.DISABLE_THE_ADVERTISERS_PUBLIC_KEY;

/**
 * 媒体账户Controller
 *
 * <AUTHOR>
 * @date 2023-03-10
 */
@Api("媒体账户信息")
@RestController
@Slf4j
@RequestMapping("/promotion/media")
public class MediaAccountInfoController extends BaseController {

    @Autowired
    private IMediaAccountInfoService mediaAccountInfoService;

    @Autowired
    private RedisInfoManager redisInfoManager;

    @Autowired
    private MediaAccountInfoManager mediaAccountInfoManager;

    @Autowired
    private IConversionConfigService conversionConfigService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private TransferManagerImpl manager;


    /**
     * 查询媒体账户列表
     */
    @ApiOperation("获取媒体账户列表")
    @PreAuthorize("@ss.hasPermi('promotion:media:list')")
    @GetMapping("/list")
    public TableDataInfo list(MediaAccountInfo mac) {
        //请求分页
        startPage();
        //mac.getParams().get("parentAccountId")--》查询所有子账户
        if (!StringUtils.isAllEmpty(mac.getAdvertiserName(), mac.getAdvertiserId())
                || StringUtils.isNotNull(mac.getIsEnableConversion())
                || ObjectUtils.isNotEmpty(mac.getParams().get("parentAccountId"))) {
            mac.setSearchValue("1");
        }
        //判断属于账户ID属于单查询还是多查询
        if (ListUtils.isNotEmpty(mac.getAdvertiserIds())) {
            if (mac.getAdvertiserIds().size() == 1) {
                mac.setAdvertiserId(mac.getAdvertiserIds().get(0));
                mac.setAdvertiserIds(null);
            } else {
                Map<String, Object> params = mac.getParams();
                params.put("advertiserIds", mac.getAdvertiserIds());
            }
        }
        //查询媒体数据
        List<MediaAccountInfo> list = mediaAccountInfoService.selectMediaAccountInfoList(mac);
        //把商务负责人转为中文昵称
        getBusinessNickname(list);
        conversionConfigService.listSetConversionConfig(list);
        return getDataTable(list);
    }

    private void getBusinessNickname(List<MediaAccountInfo> list) {
        for (MediaAccountInfo mediaAccountInfo : list) {
            //判断商务负责人不为空的情况
            if (StringUtils.isNotNull(mediaAccountInfo.getBusiness())) {
                //从缓存当中取数据
                UserVO user = redisInfoManager.getUser(mediaAccountInfo.getBusiness());
                if (StringUtils.isNotNull(user)) {
                    //获取中文昵称
                    mediaAccountInfo.setPersonInCharge(user.getNickName());
                }
            }
        }
    }

    /**
     * 导出媒体账户列表
     */
    @ApiOperation("导出媒体账户列表")
    @PreAuthorize("@ss.hasPermi('promotion:media:export')")
    @Log(title = "导出媒体账户列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MediaAccountInfo mediaAccountInfo) {
        List<MediaAccountInfo> list =
                mediaAccountInfoService.selectMediaAccountInfoList(mediaAccountInfo);
        ExcelUtil<MediaAccountInfo> util = new ExcelUtil<MediaAccountInfo>(MediaAccountInfo.class);
        util.exportExcel(response, list, "媒体账户数据");
    }

    /**
     * 获取媒体账户详细信息
     */
    @ApiOperation("获取媒体账户详细信息")
    @PreAuthorize("@ss.hasPermi('promotion:media:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(mediaAccountInfoService.selectMediaAccountInfoById(id));
    }

    /**
     * 新增媒体账户
     */
    @ApiOperation("新增媒体账户")
    @PreAuthorize("@ss.hasPermi('promotion:media:add')")
    @Log(title = "新增媒体账户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MediaAccountInfo mediaAccountInfo) {
        return toAjax(mediaAccountInfoService.insertMediaAccountInfo(mediaAccountInfo));
    }

    /**
     * 修改媒体账户
     */
    @ApiOperation("修改媒体账户")
    @PreAuthorize("@ss.hasPermi('promotion:media:edit')")
    @Log(title = "修改媒体账户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MediaAccountInfo mediaAccountInfo) {
        mediaAccountInfo.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(mediaAccountInfoService.updateMediaAccountInfo(mediaAccountInfo));
    }


    @ApiOperation("同步媒体账户")
    @PreAuthorize("@ss.hasPermi('promotion:media:sync')")
    @Log(title = "同步媒体账户", businessType = BusinessType.UPDATE)
    @GetMapping("/sync/{id}")
    public AjaxResult syncAccountInfo(@PathVariable("id") Long id) {
        mediaAccountInfoManager.syncAccountPlansAndVideoListings(id);
        return success("同步账户名称成功");
    }


    @ApiOperation("根据授权拉取最新账户列表")
    @PreAuthorize("@ss.hasPermi('promotion:media:authorize')")
    @Log(title = "根据授权拉取最新账户列表", businessType = BusinessType.UPDATE)
    @GetMapping("/listByAuth/{id}")
    public AjaxResult listByAuth(@PathVariable("id") Long id) {
        return success(mediaAccountInfoManager.getMediaAccountListById(id));
    }

    /**
     * 删除媒体账户
     */
    @ApiOperation("删除媒体账户")
    @ApiImplicitParam(name = "ids", value = "媒体账户ID", required = true, dataType = "long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('promotion:media:remove')")
    @Log(title = "删除媒体账户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(mediaAccountInfoService.deleteMediaAccountInfoByIds(ids));
    }

    /**
     * 批量删除媒体账户
     */
    @ApiOperation("批量删除媒体账户")
    @ApiImplicitParam(name = "ids", value = "媒体账户ID", required = true, dataType = "long",
            paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('promotion:media:remove')")
    @Log(title = "批量删除媒体账户", businessType = BusinessType.DELETE)
    @PostMapping("/delMedia")
    public AjaxResult batchRemove(@RequestBody BatchDeleteDTO batchDeleteDTO) {
        return toAjax(mediaAccountInfoService.deleteMediaAccountInfoByIds(batchDeleteDTO.getIds()));
    }

    @ApiOperation("批量授权媒体账户")
    @PreAuthorize("@ss.hasPermi('promotion:media:authorize')")
    @Log(title = "批量授权媒体账户", businessType = BusinessType.GRANT)
    @PostMapping("/authorize")
    public AjaxResult authorize(@RequestBody MediaAccountAuthorizeParam mediaAccountAuthorize) {
        mediaAccountInfoService.authorizeMediaAccount(mediaAccountAuthorize);
        //保存巨量的当天授权用户
        boolean hasSaved = false;
        //报表迁移媒体对象集合
        List<MediaAccountInfo> accountInfos = new ArrayList<>();
        for (MediaAccountAuthorizeDTO dto : mediaAccountAuthorize.getMediaAccounts()) {
            for (MediaAccountInfo accountInfo : dto.getChildAccountList()) {
                //排除空数据
                if (StringUtils.isBlank(accountInfo.getAdvertiserId()) || "null".equals(accountInfo.getAdvertiserId())) {
                    continue;
                }
                accountInfos.add(accountInfo);
                //组装参数
                if (MediaTypeEnum.OCEAN.getCode() == accountInfo.getMediaType()) {
                    //存入缓存
                    redisCache.setCacheMapValue(DISABLE_THE_ADVERTISERS_PUBLIC_KEY, accountInfo.getAdvertiserId(), mediaAccountAuthorize.getCreateAsset());
                    hasSaved = mediaAccountInfoService.reportAdd(accountInfo, hasSaved);
                }
            }
        }
//        //报表迁移以及报表同步
//        if (ListUtils.isNotEmpty(accountInfos)) {
//            if (StringUtils.equals(mediaAccountAuthorize.getChangeCreateBy(), "1")) {
//                //报表迁移
//                manager.mediaTransfer(SecurityUtils.getUserId(), null, accountInfos);
//            }
//            //非巨量的通过批量任务拉，巨量的通过这个任务拉行业数据：disablePublisherPublicKeyTask.disablePublicKey
//            if (MediaTypeEnum.OCEAN.getCode() != accountInfos.get(0).getMediaType()) {
//                //同步行业名称跟余额
//                mediaAccountInfoService.taskCreation(accountInfos);
//            }
//        }

        return success();
    }

    /**
     * 临时设置账户通过接口拉报表--通过定时任务（）
     */
    @ApiOperation("临时设置账户通过接口拉报表")
    @PreAuthorize("@ss.hasPermi('promotion:media:reportAdd')")
    @PostMapping("/reportAdd")
    public AjaxResult reportAdd(@RequestBody BatchDeleteDTO batchDeleteDTO) {
        if (ArrayUtil.isEmpty(batchDeleteDTO.getIds())) {
            return success();
        }
        List<MediaAccountInfo> mediaAccountInfos = mediaAccountInfoService.listByIds(ListUtils.newArrayList(batchDeleteDTO.getIds()));
        for (MediaAccountInfo accountInfo : mediaAccountInfos) {
            mediaAccountInfoService.reportAdd(accountInfo, false);
        }
        return success();
    }

    /**
     * 媒体账户下拉框
     */
    @ApiOperation("媒体账户下拉框")
    @PreAuthorize("@ss.hasPermi('promotion:media:list')")
    @GetMapping("/combo")
    public AjaxResult combo(MediaAccountComboVO mac) {
        return success(mediaAccountInfoService.selectMediaAccountForCombo(mac));
    }

    /**
     * 媒体账户评控
     */
    @ApiOperation("媒体账户评控")
    @PreAuthorize("@ss.hasPermi('promotion:media:chgcontrol')")
    @Log(title = "媒体账户评控", businessType = BusinessType.UPDATE)
    @PostMapping("/chg-control")
    public AjaxResult chgControlStatus(@RequestBody MediaAccountDTO mac) {
        return success(mediaAccountInfoService.changeControlStatus(mac));
    }

    /**
     * 批量更新媒体账户落地页链接
     */
    @ApiOperation("批量更新媒体账户落地页链接")
    @PreAuthorize("@ss.hasPermi('promotion:media:upmonitor')")
    @Log(title = "批量更新媒体账户落地页链接", businessType = BusinessType.UPDATE)
    @PostMapping("/upmonitor")
    public AjaxResult chgMonitor(@RequestBody MediaAccountRenewalDTO dto) {
        if (ListUtils.isEmpty(dto.getAdvertiserId())) {
            throw new ServiceException("媒体账户为空,请选择后重新提交！");
        }
        mediaAccountInfoService.batchChangeMonitorUrl(dto);
        return AjaxResult.success("批量更新媒体账户落地页链接任务创建成功，您可前往右上角【任务中心】查看任务进度~");
    }

    /**
     * 备注修改
     */
    @ApiOperation("备注修改")
    @PreAuthorize("@ss.hasPermi('promotion:media:edit')")
    @Log(title = "备注修改", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/updateRemark")
    public AjaxResult updateRemark(@RequestBody List<MediaAccountInfo> mediaAccountInfoList) {
        if (ListUtils.isEmpty(mediaAccountInfoList)) {
            throw new ServiceException("参数异常!");
        }
        mediaAccountInfoService.updateRemark(mediaAccountInfoList);
        return success();
    }

    /**
     * 添加父账户
     */
    @ApiOperation("添加父账户")
    @PreAuthorize("@ss.hasPermi('promotion:media:custom')")
    @Log(title = "添加父账户", businessType = BusinessType.INSERT)
    @PostMapping(value = "/addParent")
    public AjaxResult addParent(@Validated @RequestBody MediaAccountAuthorizeAddDTO dto) {
        mediaAccountInfoService.addParent(dto);
        return success();
    }

    /**
     * 修改父账户
     */
    @ApiOperation("修改父账户")
    @PreAuthorize("@ss.hasPermi('promotion:media:custom')")
    @Log(title = "修改父账户", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateParent")
    public AjaxResult updateParent(@Validated @RequestBody MediaAccountAuthorizeAddDTO dto) {
        mediaAccountInfoService.updateParent(dto);
        return success();
    }

    /**
     * 获取账户子节点
     */
    @ApiOperation("获取账户子节点")
    @PreAuthorize("@ss.hasPermi('promotion:media:list')")
    @GetMapping(value = "/children")
    public AjaxResult children(@RequestParam String advertiserId, boolean showParent) {
        List<MediaAccountInfo> list = mediaAccountInfoService.children(advertiserId, showParent);
        return success(list);
    }

    /**
     * 修改媒体账户
     */
    @ApiOperation("媒体账户删除鉴权")
    @PreAuthorize("@ss.hasPermi('promotion:media:edit')")
    @Log(title = "媒体账户删除鉴权", businessType = BusinessType.DELETE)
    @PostMapping(value = "/delPublicKey")
    public AjaxResult delPublicKey(@RequestBody MediaAccountInfo mediaAccountInfo) {
        mediaAccountInfo = mediaAccountInfoService.selectMediaAccountInfoById(mediaAccountInfo.getId());
        mediaAccountInfoService.delPublicKey(mediaAccountInfo);
        return success();
    }


    /**
     * 配置千展阀值
     */
    @ApiOperation("配置千展阀值")
    @PreAuthorize("@ss.hasPermi('promotion:media:edit')")
    @Log(title = "配置千展阀值", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/configureTheKilobarThreshold")
    public AjaxResult configureTheKilobarThreshold(@RequestBody MediaAccountInfoUpdateDTO dto) {
        mediaAccountInfoService.configureTheKilobarThreshold(dto);
        return success();
    }


    /**
     * 配置账户余额阀值
     */
    @ApiOperation("配置账户余额阀值")
    @PreAuthorize("@ss.hasPermi('promotion:media:edit')")
    @Log(title = "配置账户余额阀值", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/updateFundThresholdValue")
    public AjaxResult updateFundThresholdValue(@RequestBody @Validated MediaAccountInfoUpdateFundThresholdValueDTO dto) {
        mediaAccountInfoService.updateFundThresholdValue(dto);
        return success();
    }


    /**
     * 查询父账户列表(列表下拉框)
     */
    @GetMapping(value = "/parentList")
    @PreAuthorize("@ss.hasPermi('promotion:media:list')")
    @ApiOperation("查询父账户列表")
    public AjaxResult parentAccountList(@RequestParam(required = false) Integer mediaType) {
        List<MediaAccountInfo> mediaAccountInfoList = mediaAccountInfoService.parentAccountList(new BaseEntity(), mediaType);
        return success(mediaAccountInfoList);
    }


    /**
     * 查询账户行业列表
     */
    @ApiOperation("查询账户行业列表")
    @PreAuthorize("@ss.hasAnyPermi('promotion:media:authorize,data:crowd:list')")
    @GetMapping("/getIndustryList")
    public AjaxResult getIndustryList() {
        return success(mediaAccountInfoService.getMediaIndustryList());
    }


    /**
     * 手动同步帐户余额
     */
    @ApiOperation("手动同步帐户余额")
    @PreAuthorize("@ss.hasPermi('promotion:media:list')")
    @Log(title = "手动同步帐户余额", businessType = BusinessType.UPDATE)
    @PutMapping("/updateFund")
    public AjaxResult updateFund(@RequestBody MediaOceanAssetsDTO dto) {
        //字符串分割
        String[] advertiserId = dto.getAdvertiserId().split(",");
        List<MediaAccountInfo> mediaAccountInfos = mediaAccountInfoService.selectMediaAccountInfoByAdvertiserIdOrParentId(advertiserId);
        if (ListUtils.isEmpty(mediaAccountInfos)) {
            throw new ServiceException("账户信息为空!");
        }
        mediaAccountInfoService.synchronizationAccountFund(mediaAccountInfos);
        return AjaxResult.success("手动同步帐户余额任务创建成功，您可前往右上角【任务中心】查看任务进度~");
    }

    /**
     * 更新启用停用状态
     */
    @ApiOperation("更新启用停用状态")
    @PreAuthorize("@ss.hasPermi('promotion:media:list')")
    @Log(title = "更新启用停用状态", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStatus")
    public AjaxResult updateStatus(@RequestBody MediaStatusDTO dto) {
        mediaAccountInfoService.updateStatus(dto);
        return AjaxResult.success();
    }

    /**
     * 同步账户报表数据
     */
    @ApiOperation("同步账户报表数据")
    @PreAuthorize("@ss.hasPermi('promotion:media:advsync')")
    @Log(title = "同步账户报表数据", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/synchronizeReportData")
    public AjaxResult synchronizeReportData(@RequestBody MediaAccountInfoSynchronizationDTO dto) {
        mediaAccountInfoService.synchronizationMediaAccountInfo(dto);
        return AjaxResult.success("同步媒体账户报表数据任务创建成功，您可前往右上角【任务中心】查看任务进度~");
    }

}
