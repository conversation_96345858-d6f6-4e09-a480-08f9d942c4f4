package com.panda.pollen.web.controller.system;

import com.panda.pollen.common.annotation.Log;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResult;
import com.panda.pollen.common.enums.BusinessType;
import com.panda.pollen.common.utils.SecurityUtils;
import com.panda.pollen.system.domain.SysUserConfig;
import com.panda.pollen.system.service.ISysUserConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户配置Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Api(tags = "用户配置管理")
@RestController
@RequestMapping("/system/userConfig")
public class SysUserConfigController extends BaseController {

    @Autowired
    private ISysUserConfigService userConfigService;
    /**
     * 获取用户指定配置
     */
    @ApiOperation("获取用户指定配置")
    @GetMapping("/{configKey}")
    public AjaxResult getConfig(@PathVariable String configKey) {
        SysUserConfig config = userConfigService.selectUserConfig(SecurityUtils.getUserId(), configKey);
        return success(config);
    }

    /**
     * 新增用户配置
     */
    @ApiOperation("新增用户配置")
    @Log(title = "用户配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUserConfig userConfig) {
        userConfig.setUserId(SecurityUtils.getUserId());
        return toAjax(userConfigService.insertUserConfig(userConfig));
    }
} 