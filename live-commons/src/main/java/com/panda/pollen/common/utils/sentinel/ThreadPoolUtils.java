package com.panda.pollen.common.utils.sentinel;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.panda.pollen.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 限流工具类
 *
 * <AUTHOR>
 * @date 2023/05/08
 */
@Slf4j
public class ThreadPoolUtils {

    /**
     * 核心线程数
     */
    private static final int CORE_POOL_SIZE = 10;

    /**
     * 最大线程数
     */
    private static final int MAXIMUM_POOL_SIZE = 30;

    /**
     * 空闲线程存活时间
     */
    private static final int KEEP_ALIVE_SECONDS = 30;


    /**
     * 创建限流线程池
     *
     * @param name 名字
     * @return {@link ExecutorService}
     */
    public static ExecutorService createQpsFlowRuleThreadPool(String name) {
        return createQpsFlowRuleThreadPool(name, CORE_POOL_SIZE, MAXIMUM_POOL_SIZE);
    }

    /**
     * 创建限流线程池
     *
     * @param name 名字
     * @return {@link ExecutorService}
     */
    public static ExecutorService createQpsFlowRuleThreadPool(String name, int corePoolSize, int maximumPoolSize) {
        ThreadPoolExecutor executor = createThreadPoolExecutor(name, corePoolSize, maximumPoolSize);
        return TtlExecutors.getTtlExecutorService(executor);
    }

    public static ThreadPoolExecutor createThreadPoolExecutor(String name, int corePoolSize, int maximumPoolSize) {
        return createThreadPoolExecutor(name, corePoolSize, maximumPoolSize, 30);
    }

    public static ThreadPoolExecutor createThreadPoolExecutor(String name, int corePoolSize, int maximumPoolSize, int workQueueSize) {
        return ExecutorBuilder.create()
                //核心线程数
                .setCorePoolSize(corePoolSize)
                //最大线程数
                .setMaxPoolSize(maximumPoolSize)
                //空闲线程执行时间
                .setKeepAliveTime(KEEP_ALIVE_SECONDS, TimeUnit.SECONDS)
                //有界等待队列，最大等待数是60
                .setWorkQueue(new LinkedBlockingQueue<>(workQueueSize))
                //设置线程前缀
                .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix(name + "-" + DateUtils.getTimeOnly() + "-Pool-").build())
                //当任务队列过长时处于阻塞状态，直到添加到队列中，固定并发数去访问，并且不希望丢弃任务时使用此策略
//                .setHandler(RejectPolicy.BLOCK.getValue())
                //由主线程来直接执行
                .setHandler(new ThreadPoolExecutor.CallerRunsPolicy()).build();
    }

    /**
     * 等待线程池完成
     *
     * @param executor
     */
    public static void waitThreadPoolFinish(ExecutorService executor) {
        // 关闭线程池，不再接受新的任务
        executor.shutdown();
        try {
            // 等待线程池中的任务全部完成，或者等待30秒钟
            if (!executor.awaitTermination(30, TimeUnit.MINUTES)) {
                // 如果超过指定时间线程池中的任务还未完成，可以选择取消任务
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            // 处理中断异常
            log.error(e.getMessage(), e);
            //强关
            executor.shutdownNow();
        }
    }


    /**
     * 等待线程池完成
     *
     * @param executor
     */
    public static void waitThreadPoolFinish(ExecutorService executor, long timeout, TimeUnit timeUnit) {
        // 关闭线程池，不再接受新的任务
        executor.shutdown();
        try {
            // 等待线程池中的任务全部完成，或者等待30秒钟
            if (!executor.awaitTermination(timeout, timeUnit)) {
                // 如果超过指定时间线程池中的任务还未完成，可以选择取消任务
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            // 处理中断异常
            log.error(e.getMessage(), e);
            //强关
            executor.shutdownNow();
        }
    }


}
