package com.panda.pollen.common.convertor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;

/**
 * 将String类型的字符串转为字符串(主要是规避科学计数法)
 * Excel表格内容转化器
 */
public class StringToStringConverter implements Converter<String> {
    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<String> context) throws Exception {
        String value = context.getValue();
        if (StrUtil.isNotEmpty(value)) {
            return new WriteCellData<>("=\"" + value + "\"");
        }
        return new WriteCellData<>();
    }

    @Override
    public Class<String> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }
}
