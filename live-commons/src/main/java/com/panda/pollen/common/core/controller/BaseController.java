package com.panda.pollen.common.core.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.panda.pollen.common.constant.AdsConstant;
import com.panda.pollen.common.constant.HttpStatus;
import com.panda.pollen.common.core.domain.AjaxResult;
import com.panda.pollen.common.core.domain.BaseEntity;
import com.panda.pollen.common.core.domain.model.LoginUser;
import com.panda.pollen.common.core.page.Paged;
import com.panda.pollen.common.core.page.TableDataInfo;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.beans.PropertyEditorSupport;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 */
public class BaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage() {
        PageUtils.startPage();
    }

    /**
     * 设置请求排序数据
     */
    protected void startOrderBy() {
        PageUtils.startOrderBy();
    }

    /**
     * 清理分页的线程变量
     */
    protected void clearPage() {
        PageUtils.clearPage();
    }

    /**
     * 响应请求分页数据
     */
    protected <T> TableDataInfo<T> getDataTable(List<T> list) {
        TableDataInfo<T> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo<>(list).getTotal());
        return rspData;
    }

    /**
     * Description：基于MybatisPlus分页插件的分页对象 <br>
     * author：罗江林 <br>
     * date：2023年4月27日 下午9:37:34 <br>
     *
     * @param <K>  :
     * @return <br>
     */
    protected <K> Page<K> getPage() {
        return Paged.getPage();
    }

    /**
     * 根据MybatisPlus分页插件对象响应请求分页数据
     */
    protected <K> TableDataInfo<K> getDataTable(IPage<K> data) {
        TableDataInfo<K> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(data.getRecords());
        rspData.setTotal(data.getTotal());
        return rspData;
    }

    /**
     * 搜索范围纠正
     */
    protected void searchRangeRectify(BaseEntity entity, int max) {
        ToolUtil.searchRangeRectify(entity, max);
    }

    /**
     * 查询时间范围校验
     * @param beginTime :
     * @param endTime  :
     * @param maxDays  :
     */
    protected void searchDateRangeVerify(Date beginTime, Date endTime, Integer maxDays) {
        if (beginTime == null) {
            throw new ServiceException("查询开始时间不能为空");
        }
        if (endTime == null) {
            throw new ServiceException("查询结束时间不能为空");
        }

        if (beginTime.after(endTime)) {
            throw new ServiceException("查询结束时间必须大于开始时间");
        }

        if (DateUtil.betweenDay(beginTime, endTime, true) > maxDays) {
            throw new ServiceException("查询时间范围不得超过{}天", maxDays);
        }
    }

    /**
     * 搜索日期范围纠正
     *
     * @param entity :
     * @param max :
     */
    protected void searchDateTimeRectify(BaseEntity entity, int max) {
        ToolUtil.searchDateTimeRectify(entity, max, true);
    }

    protected void rangeSearch(LocalDate startSaleDate, LocalDate endSaleDate) {
        //时间必传
        if (ObjectUtil.isEmpty(startSaleDate) || ObjectUtil.isEmpty(endSaleDate)) {
            throw new ServiceException("请选择日期范围!");
        }
        //参数组
        BaseEntity baseEntity = new BaseEntity();
        Map<String, Object> params = baseEntity.getParams();
        params.put(AdsConstant.BEGIN_TIME, startSaleDate);
        params.put(AdsConstant.END_TIME, endSaleDate);
        //范围搜索纠正
        searchRangeRectify(baseEntity, 365);
    }

    /**
     * 返回成功
     */
    public AjaxResult success() {
        return AjaxResult.success();
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error() {
        return AjaxResult.error();
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(String message) {
        return AjaxResult.success(message);
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(Object data) {
        return AjaxResult.success(data);
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error(String message) {
        return AjaxResult.error(message);
    }

    /**
     * 返回警告消息
     */
    public AjaxResult warn(String message) {
        return AjaxResult.warn(message);
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected AjaxResult toAjax(boolean result) {
        return result ? success() : error();
    }

    /**
     * 页面跳转
     */
    public String redirect(String url) {
        return StringUtils.format("redirect:{}", url);
    }

    /**
     * 获取用户缓存信息
     */
    public LoginUser getLoginUser() {
        return SecurityUtils.getLoginUser();
    }

    /**
     * 获取登录用户id
     */
    public Long getUserId() {
        return getLoginUser().getUserId();
    }

    /**
     * 获取登录部门id
     */
    public Long getDeptId() {
        return getLoginUser().getDeptId();
    }

    /**
     * 获取登录用户名
     */
    public String getUsername() {
        return getLoginUser().getUsername();
    }
}
