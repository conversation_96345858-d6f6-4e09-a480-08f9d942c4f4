/**
 * All rights Reserved, Designed By <br>
 * Title：SpiltDateUtil.java <br>
 * Package：com.panda.pollen.common.utils.utils <br>
 * Description：(用一句话描述该文件做什么) <br>
 * Copyright © 2023 luojl All rights reserved. <br>
 * 
 * <AUTHOR> <br>
 * @date 2023年5月16日 上午9:27:35 <br>
 * @version v1.0 <br>
 */
package com.panda.pollen.common.utils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;

/**
 * ClassName：com.panda.pollen.common.utils.SpiltDateUtil <br>
 * Description：日期分割工具 <br>
 * Copyright © 2023 luojl All rights reserved. <br>
 * 
 * <AUTHOR> <br>
 * @date 2023年5月16日 上午9:27:35 <br>
 * @version v1.0 <br>
 */
public class SpiltDateUtil {

    private static SimpleDateFormat monthDateFormat = new SimpleDateFormat("MM");

    /**
     * Description：将时间段按星期分割 <br>
     * author：luojl <br>
     * date：2023年5月16日 上午9:56:28 <br>
     * @param start
     * @param end
     * @return <br>
     */
    public static final List<Range> splitToWeeks(Date start, Date end) {
        List<Range> result = new ArrayList<>();
        result.add(Range.create(start));
        Date from = new Date(start.getTime() + 7L * 24 * 3600 * 1000);
        Date weekEnd = DateUtil.endOfWeek(end);
        while (from.compareTo(weekEnd) <= 0) {
            Date dt = DateUtil.beginOfWeek(from);
            CollectionUtil.getLast(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
            CollectionUtil.getLast(result).setMonth(monthDateFormat.format(new Date(dt.getTime() - 24L * 3600 * 1000)));
            result.add(Range.create(dt));
            from.setTime(from.getTime() + 7L * 24 * 3600 * 1000);
        }
        CollectionUtil.getLast(result).end(end);
        CollectionUtil.getLast(result).setMonth(monthDateFormat.format(end));
        return result;
    }

    public static final List<Range> splitToMonths(Date start, Date end) {
        List<Range> result = new ArrayList<>();
        result.add(Range.create(start));
        Calendar cal = Calendar.getInstance();
        cal.setTime(start);
        cal.add(Calendar.MONTH, 1);
        Date monthEnd = DateUtil.endOfMonth(end);
        while (cal.getTimeInMillis() <= monthEnd.getTime()) {
            Date dt = DateUtil.beginOfMonth(cal.getTime());
            CollectionUtil.getLast(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
            CollectionUtil.getLast(result).setMonth(monthDateFormat.format(new Date(dt.getTime() - 24L * 3600 * 1000)));
            result.add(Range.create(dt));
            cal.add(Calendar.MONTH, 1);
        }
        CollectionUtil.getLast(result).end(end);
        CollectionUtil.getLast(result).setMonth(monthDateFormat.format(end));
        return result;
    }

    /**
     * Description：将时间段按照季度分割 <br>
     * author：luojl <br>
     * date：2023年5月16日 上午9:56:09 <br>
     * @param start
     * @param end
     * @return <br>
     */
    public static final List<Range> splitToQuarts(Date start, Date end) {
        List<Range> result = new ArrayList<>();
        result.add(Range.create(start));
        Calendar cal = Calendar.getInstance();
        cal.setTime(start);
        cal.add(Calendar.MONTH, 3);
        Date quartEnd = DateUtil.endOfQuarter(end);
        while (cal.getTimeInMillis() <= quartEnd.getTime()) {
            Date dt = DateUtil.beginOfQuarter(cal.getTime());
            CollectionUtil.getLast(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
            System.out.println(cal.get(Calendar.MONTH));
            if (cal.get(Calendar.MONTH) >= 1 && cal.get(Calendar.MONTH) <= 3) {
                CollectionUtil.getLast(result).setMonth("4");
            } else if (cal.get(Calendar.MONTH) >= 4 && cal.get(Calendar.MONTH) <= 6) {
                CollectionUtil.getLast(result).setMonth("1");
            } else if (cal.get(Calendar.MONTH) >= 7 && cal.get(Calendar.MONTH) <= 9) {
                CollectionUtil.getLast(result).setMonth("2");
            } else if (cal.get(Calendar.MONTH) >= 10 && cal.get(Calendar.MONTH) <= 12) {
                CollectionUtil.getLast(result).setMonth("3");
            }
            result.add(Range.create(dt));
            cal.add(Calendar.MONTH, 3);
        }
        CollectionUtil.getLast(result).end(end);
        if (cal.get(Calendar.MONTH) >= 1 && cal.get(Calendar.MONTH) <= 3) {
            CollectionUtil.getLast(result).setMonth("4");
        } else if (cal.get(Calendar.MONTH) >= 4 && cal.get(Calendar.MONTH) <= 6) {
            CollectionUtil.getLast(result).setMonth("1");
        } else if (cal.get(Calendar.MONTH) >= 7 && cal.get(Calendar.MONTH) <= 9) {
            CollectionUtil.getLast(result).setMonth("2");
        } else if (cal.get(Calendar.MONTH) >= 10 && cal.get(Calendar.MONTH) <= 12) {
            CollectionUtil.getLast(result).setMonth("3");
        }
        return result;
    }

    /**
     * Description：将时间段按照半年分割 <br>
     * author：luojl <br>
     * date：2023年5月16日 上午9:55:50 <br>
     * @param start
     * @param end
     * @return <br>
     */
    public static final List<Range> splitToHalfOfYears(Date start, Date end) {
        List<Range> result = new ArrayList<>();
        result.add(Range.create(start));
        Calendar cal = Calendar.getInstance();
        cal.setTime(start);
        cal.add(Calendar.MONTH, 6);
        Calendar calOne = Calendar.getInstance();
        calOne.setTime(end);
        int mon = cal.get(Calendar.MONTH);
        // Calendar tmp = Calendar.getInstance();
        // if (mon < 6) {
        // tmp.setTimeInMillis(cal.getTimeInMillis());
        // tmp.set(Calendar.MONTH, 5);
        // } else {
        // tmp.setTimeInMillis(cal.getTimeInMillis());
        // tmp.set(Calendar.MONTH, 11);
        // }
        Calendar halfYearEnd = DateUtil.endOfMonth(calOne);
        while (cal.getTimeInMillis() <= halfYearEnd.getTimeInMillis()) {
            mon = cal.get(Calendar.MONTH);
            Calendar cal1 = Calendar.getInstance();
            if (mon < 6) {
                cal1.setTimeInMillis(cal.getTimeInMillis());
                cal1.set(Calendar.MONTH, 0);
            } else {
                cal1.setTimeInMillis(cal.getTimeInMillis());
                cal1.set(Calendar.MONTH, 6);
            }
            Date dt = DateUtil.beginOfMonth(cal1.getTime());
            CollectionUtil.getLast(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
            result.add(Range.create(dt));
            cal.add(Calendar.MONTH, 6);
        }
        CollectionUtil.getLast(result).end(end);
        return result;
    }

    /**
     * Description：将时间段按照年分割 <br>
     * author：luojl <br>
     * date：2023年5月16日 上午9:55:28 <br>
     * @param start
     * @param end
     * @return <br>
     */
    public static final List<Range> splitToYears(Date start, Date end) {
        List<Range> result = new ArrayList<>();
        result.add(Range.create(start));
        Calendar cal = Calendar.getInstance();
        cal.setTime(start);
        cal.add(Calendar.YEAR, 1);
        Date yearEnd = DateUtil.endOfYear(end);
        while (cal.getTimeInMillis() <= yearEnd.getTime()) {
            Date dt = DateUtil.beginOfYear(cal.getTime());
            CollectionUtil.getLast(result).end(new Date(dt.getTime() - 24L * 3600 * 1000));
            result.add(Range.create(dt));
            cal.add(Calendar.YEAR, 1);
        }
        CollectionUtil.getLast(result).end(end);
        return result;
    }

    static class Range {
        Date start;
        Date end;
        String Month;

        public Range() {

        }

        private Range(Date start) {
            this.start = start;
        }

        public static Range create(Date start) {
            return new Range(start);
        }

        public Range end(Date end) {
            this.end = end;
            return this;
        }

        public void setStart(Date start) {
            this.start = start;
        }

        public void setEnd(Date end) {
            this.end = end;
        }

        public Date getStart() {
            return start;
        }

        public Date getEnd() {
            return end;
        }

        public String getMonth() {
            return Month;
        }

        public void setMonth(String month) {
            Month = month;
        }

        @Override
        public String toString() {
            return "[" + DateUtil.format(start, "yyyy-MM-dd") + ","
                + DateUtil.format(end, "yyyy-MM-dd") + "]";
        }
    }
}
