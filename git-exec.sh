# 获取当前所在的分支名
branch_name=$(git symbolic-ref --short HEAD)
# 获取当前用户的用户名
user_name=$(git config user.name)
# 查看当前git仓库的状态，显示有哪些文件被修改、新增或删除等信息
git status
# 添加所有文件到暂存区，准备提交
git add -- *
# 单独将.gitignore文件添加到暂存区，确保其变更也会被提交
git add .gitignore
# 使用动态获取的分支名作为提交信息的一部分
git commit -m "当前分支名:[$branch_name]-当前提交用户:[$user_name]-$1"
# 从远程仓库拉取最新的代码，合并到本地当前分支
git pull
# 将本地仓库的代码推送到远程仓库
git push
# 再次查看git仓库的状态，确认推送后的状态信息
git status