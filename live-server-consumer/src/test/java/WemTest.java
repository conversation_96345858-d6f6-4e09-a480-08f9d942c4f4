//import com.panda.pollen.LiveConsumerApplication;
//import com.panda.pollen.common.core.domain.entity.CustomerUser;
//import com.panda.pollen.scrm.service.ICustomerUserService;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = {LiveConsumerApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@ActiveProfiles({"dev","aui"})
//@Slf4j
//public class WemTest {
//
//    @Autowired
//    private ICustomerUserService customerUserService;
//
//    @Test
//    public void test() throws Exception {
//        CustomerUser customerUser = new CustomerUser();
//        customerUser.setCompanyId(1L);
//        customerUser.setMobile("9527272727");
//        customerUserService.save(customerUser);
//    }
//
//
//
//    public static void main(String[] args) {
//
//    }
//
//
//
//}