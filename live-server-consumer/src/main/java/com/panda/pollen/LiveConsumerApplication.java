//package com.panda.pollen;
//
//import org.springframework.boot.SpringApplication;
//import org.springframework.boot.autoconfigure.SpringBootApplication;
//import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
//import org.springframework.scheduling.annotation.EnableAsync;
//
//@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class}, scanBasePackages = {"com.panda.pollen", "com.panda"})
//@EnableAsync
//public class LiveConsumerApplication {
//
//    public static void main(String[] args) {
//        SpringApplication.run(LiveConsumerApplication.class, args);
//        System.out.println("(♥◠‿◠)ﾉﾞ 订单服务消费者系统启动成功   ლ(´ڡ`ლ)ﾞ  \n");
//    }
//}
