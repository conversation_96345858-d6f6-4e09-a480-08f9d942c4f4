# 项目相关配置
pollen:
  # 名称
  name: live-consumer-server
  # 版本
  version: 1.0.1
  # 版权年份
  copyrightYear: 2025
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /opt/ads/uploadPath）
  profile: /opt/ads/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
  # redis key 前缀
  key-prefix: 'pollen:'
  # 引入quartz后是否加载定时任务
  quartz-enabled: false
  # 机器人消息
  robot:
    work-bot-api: https://api.flowbot.xlrpa.com
# 数据源配置
spring:
  data:
    mongodb:
      uri: ***************************************************
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: ***************************************************************************************************************************************************************************
        username: root
        password: pangda@123
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 5000
      # 配置连接超时时间
      connectTimeout: 1000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      keepAlive: true
      keepAliveBetweenTimeMillis: 180000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: ruoyi
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # redis 配置
  redis:
    # 地址
    host: ************
    # 端口，默认为6379
    port: 36006
    # 数据库索引
    database: 10
    # 密码
    password: pangda@123
    # 连接超时时间
    timeout: 0
    jedis:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 5
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池的最大数据库连接数
        max-active: 50
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: 5000
  kafka:
    bootstrap-servers: ************:36015
    consumer:
      # 消费者组 ID（必须有一个默认的）
      group-id: live
      # 该属性指定了消费者在读取一个没有偏移量的分区或者偏移量无效的情况下该作何处理：
      # latest（默认值）在偏移量无效的情况下，消费者将从最新的记录开始读取数据（在消费者启动之后生成的记录）
      # earliest ：在偏移量无效的情况下，消费者将从起始位置读取分区的记录
      auto-offset-reset: latest
      # 是否自动提交偏移量，默认值是true,为了避免出现重复数据和数据丢失，可以把它设置为false,然后手动提交偏移量
      enable-auto-commit: false
      # 键的反序列化方式
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 值的反序列化方式
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 会话超时：消费者被认为“失联”的时间
      session-timeout: 10000
      # 心跳间隔：必须小于 session-timeout，建议为 1/3
      heartbeat-interval: 3000
      # 单次 poll() 允许的最大处理时间（根据业务耗时调整）
      max-poll-interval: 300000  # 5分钟（默认就是300秒）
      # 批量消费并发数
      batch-concurrency: 3
      # 批量消费轮询最大record--max.poll.records
      batch-max-poll-records: 10
panda:
  #  巨量配置
  ocean:
    conversion_app_id:
    conversion_secret:
    conversion_tec_agent: 上海脉穗网络科技有限公司-C8108
  # 企业微信配置
  wecom:
    corpId: ww7dded8527037f378
    token: IwhIntta5VC5JnyXxMZiGL3K4V5V
    aesKey: DWN9qVPfwFlezVhtWZKR6nn618sVJ31JcO8jqT9rJQw
    providerSecret: UBa3aPWn8-yySMUfDZsOAP3b56kHWtSdxVrmQQjv6zRHIB3QHSsQRl5YA8oLbPC2
    suiteId: dk1386e9cd6a8e48e2
    suiteSecret: AdloxZm6Bl0bPM1SxHcxehobKOI_XDkIbvXjwacSvls
  # 微信服务号配置
  mp:
    appId: wxef0ed36332a50cf1
    appSecret: d52af481a56cbe90c17d90c6e8883230

monitor:
  # 监控链接写入mongo开关
  mongo:
    enabled: true

## 接口文档 ##
springdoc:
  api-docs:
    enabled: false
    # 指定 API 文档的路径
    path: /v3/api-docs
  swagger-ui:
    enabled: false
    # 指定 Swagger UI 的访问路径
    path: /swagger-ui

# 数据加密
mybatis-encryptor:
  # 是否开启加密
  enable: true
  # 默认加密算法
  algorithm: AES
  # 编码方式 BASE64/HEX。默认BASE64
  encode: BASE64
  # 安全秘钥 对称算法的秘钥 如：AES，SM4
  password: AxPSmD1IthnMDYFt