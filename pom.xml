<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.panda.pollen</groupId>
    <artifactId>pollen</artifactId>
    <name>live</name>
    <version>1.0.0-SNAPSHOT</version>

    <packaging>pom</packaging>

    <modules>
        <module>live-commons</module>
        <module>live-framework</module>
        <module>live-module-system</module>
        <module>live-module-generator</module>
        <module>live-server-admin</module>
        <module>live-server-monitor</module>
        <module>live-media-commons</module>
<!--        <module>live-server-consumer</module>-->
        <module>live-module-pay</module>
        <module>live-module-leaf</module>
        <module>live-module-sender</module>
        <module>live-module-scrm</module>
        <module>live-module-wecom</module>
        <module>live-module-aui</module>
        <module>live-module-workrobot</module>
        <module>live-module-robot</module>
        <module>live-server-customer</module>
        <module>live-module-customer</module>
        <module>live-module-cid</module>
    </modules>

    <properties>
        <pollen.version>${project.version}</pollen.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <druid.version>1.2.16</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>1.4.6</pagehelper.boot.version>
        <fastjson.version>2.0.23</fastjson.version>
        <oshi.version>6.4.0</oshi.version>
        <minio.version>8.2.1</minio.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <commons.fileupload.version>1.5</commons.fileupload.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <commons-text.version>1.8</commons-text.version>
        <poi.version>4.1.2</poi.version>
        <easyexcel.version>3.2.1</easyexcel.version>
        <velocity.version>2.3</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <kafka.version>2.7.1</kafka.version>
        <hutool.version>5.8.15</hutool.version>
        <oss.version>3.17.4</oss.version>
        <sentinel.version>1.8.6</sentinel.version>
        <qiniu.version>7.10.4</qiniu.version>
        <sleuth.version>2.2.7.RELEASE</sleuth.version>
        <redisson.version>3.16.3</redisson.version>
        <xxl-job.version>2.4.1</xxl-job.version>
        <bouncycastle.version>1.68</bouncycastle.version>
        <tencent.ads.version>1.1.86</tencent.ads.version>
        <alipay.sdk.version>4.38.197.ALL</alipay.sdk.version>
        <weixin.version>4.7.7.B</weixin.version>
        <weixin.pay.version>4.7.7.B</weixin.pay.version>
        <lombok.version>1.18.34</lombok.version>
        <spring.boot.version>2.5.14</spring.boot.version>
        <ve.tos.version>2.6.3</ve.tos.version>
        <swagger.version>2.9.2</swagger.version>
        <oceanengine-mapi.version>32</oceanengine-mapi.version>
        <okhttp-version>4.10.0</okhttp-version>
        <ik-analyzer.version>5.0.1</ik-analyzer.version>
        <yuntai.sdk.version>2025-05-15</yuntai.sdk.version>
        <ip2region.version>2.7.0</ip2region.version>
        <unirest.version>4.4.7</unirest.version>
        <jaffree.version>2024.08.29</jaffree.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <lombok-mapstruct.version>0.2.0</lombok-mapstruct.version>
        <aliyun-java-sdk-core.version>4.7.1</aliyun-java-sdk-core.version>
        <aliyun-java-sdk-live.version>3.9.67</aliyun-java-sdk-live.version>
        <aliyun-java-sdk-vod.version>2.16.14</aliyun-java-sdk-vod.version>
        <pdd.version>1.19.50-all</pdd.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>
            <!-- SpringBoot的依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.5.14</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-core</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-annotation-aspectj</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <!-- kafka依赖 -->
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${kafka.version}</version>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>

            <!--apache commons-text占位符替换-->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>

            <!-- fileupload常用工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <!-- Minio 文件存储 -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- easyexcel -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- collections工具类 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>

            <!-- fast-metrics 方法时间统计 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fast-metrics</artifactId>
                <version>1.0.0</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- Token生成与解析 -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok-mapstruct.version}</version>
            </dependency>


            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${oss.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-cp</artifactId>
                <version>${weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-open</artifactId>
                <version>${weixin.version}</version>
            </dependency>

            <!-- swagger2-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <!-- 代码生成 -->
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-generator</artifactId>
                <version>${pollen.version}</version>
            </dependency>

            <!-- 核心模块 -->
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-framework</artifactId>
                <version>${pollen.version}</version>
            </dependency>

            <!-- 系统模块 -->
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-system</artifactId>
                <version>${pollen.version}</version>
            </dependency>

            <!-- 通用工具 -->
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-commons</artifactId>
                <version>${pollen.version}</version>
            </dependency>

            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-pay</artifactId>
                <version>${pollen.version}</version>
            </dependency>

            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>ocean-common</artifactId>
                <version>${pollen.version}</version>
            </dependency>

            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>tencent-common</artifactId>
                <version>${pollen.version}</version>
            </dependency>

            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>0.4.19</version>
            </dependency>

            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>${qiniu.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tencent.ads</groupId>
                <artifactId>marketing-api-java-sdk</artifactId>
                <version>${tencent.ads.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okio</groupId>
                        <artifactId>okio</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>${alipay.sdk.version}</version>
            </dependency>

            <!-- 微信支付 https://mvnrepository.com/artifact/com.github.binarywang/weixin-java-pay -->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${weixin.pay.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>com.volcengine</groupId>
                <artifactId>ve-tos-java-sdk</artifactId>
                <version>${ve.tos.version}</version>
            </dependency>
            <dependency>
                <groupId>org.openapitools</groupId>
                <artifactId>oceanengine-mapi-java-client</artifactId>
                <version>${oceanengine-mapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp-version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp-version}</version>
            </dependency>
            <dependency>
                <groupId>org.truenewx</groupId>
                <artifactId>ik-analyzer-core</artifactId>
                <version>${ik-analyzer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao</groupId>
                <artifactId>yuntai-sdk</artifactId>
                <version>${yuntai.sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.konghq</groupId>
                <artifactId>unirest-java-core</artifactId>
                <version>${unirest.version}</version>
            </dependency>

            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-sender</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-leaf</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-scrm-api</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-scrm-biz</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-aui</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-wecom</artifactId>
                <version>${pollen.version}</version>
            </dependency>

            <dependency>
                <groupId>com.panda.robot</groupId>
                <artifactId>live-module-workrobot</artifactId>
                <version>${pollen.version}</version>
            </dependency>

            <dependency>
                <groupId>com.panda.robot</groupId>
                <artifactId>live-module-robot-api</artifactId>
                <version>${pollen.version}</version>
            </dependency>

            <dependency>
                <groupId>com.panda.robot</groupId>
                <artifactId>live-module-robot-biz</artifactId>
                <version>${pollen.version}</version>
            </dependency>

            <dependency>
                <groupId>com.panda.robot</groupId>
                <artifactId>live-module-robot-mvc</artifactId>
                <version>${pollen.version}</version>
            </dependency>

            <!-- 视频解码、音频解码、视频转码、音视频同步、流媒体处理、视频剪辑与合并、元数据提取-->
            <dependency>
                <groupId>com.github.kokorin.jaffree</groupId>
                <artifactId>jaffree</artifactId>
                <version>${jaffree.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun-java-sdk-core.version}</version>
            </dependency>
            <!-- 视频直播SDK -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-live</artifactId>
                <version>${aliyun-java-sdk-live.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-vod</artifactId>
                <version>${aliyun-java-sdk-vod.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-aui-admin-api</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-aui-admin-biz</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-aui-admin-mvc</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-scrm-mvc</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-aui-control-biz</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-aui-control-mvc</artifactId>
                <version>${pollen.version}</version>
            </dependency>

            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-aui-client-biz</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-aui-client-mvc</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-aui-common</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-customer-api</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-customer-biz</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-customer-mvc</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-aui-client-api</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-cid-api</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-cid-biz</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.panda.pollen</groupId>
                <artifactId>live-module-cid-mvc</artifactId>
                <version>${pollen.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pdd</groupId>
                <artifactId>pop-sdk</artifactId>
                <version>${pdd.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>OceanengineOpenApi</id>
            <name>ad_open_sdk_java</name>
            <layout>default</layout>
            <url>https://artifact.bytedance.com/repository/releases/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>
