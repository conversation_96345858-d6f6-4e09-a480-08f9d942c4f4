package com.panda.pollen.framework.lock;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date : 2025年01月03日 18:14
 */
@Component
@Slf4j
public class RedisLock {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 分布式锁key的前缀
     */
    private static final String LOCK_KEY_PREFIX = "RLock:";

    /**
     * redis分布式锁
     * @param lockKey
     * @param success
     * @param fail
     */
    public void tryLock(String lockKey, Runnable success, Runnable fail) {
        lockKey = LOCK_KEY_PREFIX + lockKey;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean tryLock = lock.tryLock();
            if (tryLock) {
                // 获取锁成功后要干什么
                success.run();
            } else {
                // 获取锁失败后要干什么
                log.error("【{}】获取锁失败", lockKey);
                fail.run();
            }
        } finally {
            log.info("对【{}】释放锁", lockKey);
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * redis分布式锁
     * 我最多愿意等 waitTime 时间来获取锁，一旦拿到锁，我会持有 leaseTime 时间，然后自动释放
     * @param lockKey   分布式锁key
     * @param waitTime  最大等待时间
     * @param leaseTime 获取锁以后，自动释放锁的时间
     * @param timeUnit  时间单位
     * @param success   获取锁成功后想干什么
     * @param fail      获取锁失败后想干什么
     */
    public void tryLock(String lockKey, long waitTime, long leaseTime, TimeUnit timeUnit, Runnable success, Runnable fail) {
        lockKey = LOCK_KEY_PREFIX + lockKey;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean tryLock = lock.tryLock(waitTime, leaseTime, timeUnit);
            if (tryLock) {
                // 获取锁成功后要干什么
                success.run();
            } else {
                // 获取锁失败后要干什么
                log.error("【{}】获取锁失败", lockKey);
                fail.run();
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            log.info("对【{}】释放锁", lockKey);
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

}
