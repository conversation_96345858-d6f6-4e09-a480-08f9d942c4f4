package com.panda.pollen.robot.domain;

import java.util.List;

import javax.validation.constraints.NotNull;

import com.panda.pollen.common.base.BaseDomain;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.robot.enums.CrowdType;
import com.panda.pollen.robot.repository.RobotSopTemplateRepository;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * SOP消息模板Domain对象
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RobotSopTemplate extends BaseDomain{

    private static final long serialVersionUID = -6618449979028065983L;

    /** 主键ID */
    private Long id;
    /** 公司ID */
    private Long companyId;
    /** 任务名称 */
    private String taskName;
    /** 模板分组 */
    private Long groupId;
    /** 群发类型，1-群聊、2-私聊 */
    private Integer crowdType;
    /** 创建人ID */
    private Long userId;
    
    /** 任务节点集 {@link RobotSopTemplateNode}*/
    @NotNull
    private List<RobotSopTemplateNode> nodes;
    
    private RobotSopTemplateRepository repository;
    
    public RobotSopTemplate(RobotSopTemplateRepository repository) {
        this.repository = repository;
    }
    
    public static RobotSopTemplate build(RobotSopTemplateRepository repository) {
        return new RobotSopTemplate(repository);
    }
    
    public RobotSopTemplate valifyNodes() {
        if(ListUtils.isEmpty(nodes)) {
        }
        this.nodes.forEach(node -> {
            if(node.getExecuteTime() == null) {
            	throw new ServiceException("模板任务节点【{}】执行时间不能为空", node.getTaskDay());
            }
        	node.valifyContents();
        });
        if(this.crowdType == null) {
            this.crowdType = CrowdType.GROUP.getCode();
        }
        return this;
    }
    
    public RobotSopTemplate valifyId() {
        if(this.id == null) {
            throw new ServiceException("ID不能为空");
        }
        return this;
    }
    
}
