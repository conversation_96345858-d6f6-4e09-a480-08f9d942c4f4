package com.panda.pollen.robot.vo;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SOP消息模板VO
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "SOP消息模板VO", value = "RobotSopTemplateVO")
public class RobotSopTemplateVO {

    /** 主键ID */
    private Long id;
    /** 公司ID */
    private Long companyId;
    /** 任务名称 */
    private String taskName;
    /** 模板分组 */
    private Long groupId;
    /** 群发类型，1-群聊、2-私聊 */
    private Integer crowdType;
    /** 创建人ID */
    private Long userId; 
    /** 任务数量 */
    private Integer taskCount;
    /** 内容类型，多个逗号分隔 */
    private String contents;
    /** 创建时间 */
    private LocalDateTime createTime;
    
    private String createBy;
    
    private Date updateTime;
    
    private String updateBy;
    
    /** 任务节点集 {@link RobotSopTemplateNodeVO}*/
    @NotNull
    private List<RobotSopTemplateNodeVO> nodes;
    
    
}
