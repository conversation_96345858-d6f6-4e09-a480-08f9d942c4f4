package com.panda.pollen.robot.assembler;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.robot.domain.RobotSopTemplate;
import com.panda.pollen.robot.domain.factory.RobotSopTemplateFactory;
import com.panda.pollen.robot.dto.RobotSopTemplateDTO;
import com.panda.pollen.robot.vo.RobotSopTemplateVO;

/**   
 * ClassName：com.panda.robot.domain.assembler.SopTemplateAssembler <br>
 * Description：SOP消息模板领域对象转换器 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * @date 2025年6月16日 17:06:15 <br>
 * @version v1.0 <br>  
 */
@Mapper
public interface SopTemplateAssembler {

    public static SopTemplateAssembler INSTANCE = Mappers.getMapper(SopTemplateAssembler.class);
    
    default RobotSopTemplate fromDTO(RobotSopTemplateDTO dto) {
        RobotSopTemplate task = RobotSopTemplateFactory.create();
        INSTANCE.update(dto, task);
        return task;
    } 
    
    default RobotSopTemplate fromVO(RobotSopTemplateVO vo) {
        RobotSopTemplate template = RobotSopTemplateFactory.create();
        INSTANCE.update(vo, template);
        return template;
    }
    
    default List<RobotSopTemplate> fromVOs(List<RobotSopTemplateVO> vos) {
        if(ListUtils.isEmpty(vos)) {
            return ListUtils.newArrayList();
        }
        return vos.stream().map(SopTemplateAssembler.INSTANCE::fromVO).collect(Collectors.toList());
    }
    
    @Mapping(target = "repository", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateBy", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "nodes", expression ="java(com.panda.pollen.robot.assembler.SopTemplateNodeAssembler.INSTANCE.fromDTOs(source.getNodes()))")
    void update(RobotSopTemplateDTO source, @MappingTarget RobotSopTemplate template);
    
    @Mapping(target = "repository", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateBy", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "nodes", ignore = true)
    void update(RobotSopTemplateVO source, @MappingTarget RobotSopTemplate task);
    
}
