package com.panda.pollen.robot.vo;

import java.time.LocalDate;
import java.util.List;

import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.robot.enums.RobotMsgBusinessType;
import com.panda.pollen.robot.enums.TaskStatus;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SOP消息任务详情VO对象
 * 包含Node和Receive内容
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "SOP消息任务详情VO", value = "RobotMessageSopTaskDetailVO")
public class RobotMessageSopTaskDetailVO {

    /** 主键ID */
    private Long id;

    /** 企微主体ID */
    private String corpId;

    /** 公司ID */
    private Long companyId;

    /** 部门ID */
    private Long deptId;

    /** 任务名称 */
    private String taskName;

    /** 描述 */
    private String describe;

    /** 任务状态，0-执行中、1-成功、-1-失败 {@link TaskStatus} */
    private TaskStatus taskStatus;

    /** 业务类型 */
    private RobotMsgBusinessType businessType;

    /** 任务开始日期 */
    private LocalDate startDate;

    /** 任务数量 */
    private Integer taskCount;

    /** 已执行任务数量 */
    private Integer executeCount;
    
    /** 目标总数 */
    private Integer targetCount;
    
    /** 触达成功数 */
    private Integer successCount;

    /** 统一配置 */
    @Builder.Default
    private Integer unified = 0;

    /** 任务节点集 */
    private List<RobotMessageSopTaskNodeVO> nodes;

    /** 统一配置，接收者集合 {@link RobotMessageReceiveVO} */
    private List<RobotMessageReceiveVO> receives;

    public boolean isSop() {
        return RobotMsgBusinessType.isSop(this.businessType);
    }

    public Integer getUnified() {
        if(ListUtils.isNotEmpty(receives)) {
            this.unified = 1;
        }
        return this.unified;
    }

}
