package com.panda.pollen.robot.domain;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

import com.panda.pollen.common.base.BaseDomain;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.common.utils.collect.SetUtils;
import com.panda.pollen.robot.dto.WecomUserDTO;
import com.panda.pollen.robot.enums.RobotMsgBusinessType;
import com.panda.pollen.robot.enums.TaskStatus;
import com.panda.pollen.robot.repository.RobotMessageSopTaskRepository;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * ClassName：com.panda.robot.domain.RobotMessageSopTask <br>
 * Description：SOP消息任务领域对象 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * @date 2025年6月17日 10:04:26 <br>
 * @version v1.0 <br>
 */
@Data
@Slf4j
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RobotMessageSopTask extends BaseDomain {
    
    private static final long serialVersionUID = 8049172391693049782L;

    /** 企微主体ID */
    private String corpId;
    /** 公司ID */
    private Long companyId;
    /** 部门ID */
    private Long deptId;
    /** 任务名称 */
    private String taskName;
    /** 描述 */
    private String describe;
    /** 任务开始日期 */
    private LocalDate startDate;
    /** 任务结束日期 */
    private LocalDate endDate;
    /** 任务状态（0：待执行，1：执行中，2：已完成，-1：已取消） */
    private TaskStatus taskStatus;
    /** 业务类型 */
    private RobotMsgBusinessType businessType;
    /** SOP消息退订，客户回复消息终止后续消息推送 */
    private Integer unsubscribe;
    /** 任务数量 */
    private Integer taskCount;
    /** 已执行次数 */
    private Integer executeCount;
    /** 创建人ID */
    private Long userId;
    /** 消息模式：100-常规模式、101-转发模式 */
    private Integer mode;
    
    private RobotMessageSopTaskRepository repository;
    @Builder.Default
    private List<RobotMessageContent> contents = ListUtils.newArrayList();
    @Builder.Default
    private List<RobotMessageReceive> receives = ListUtils.newArrayList();
    
    private List<RobotMessageSopTaskNode> nodes;
    
    private List<RobotMessageDepository> messages;
    
    private List<WecomUserDTO> wecomUsers;
    
    private RobotMessageTaskNode node;
    
    public RobotMessageSopTask(RobotMessageSopTaskRepository repository) {
        this.repository = repository;
    }
    
    public static RobotMessageSopTask build(RobotMessageSopTaskRepository repository) {
        return new RobotMessageSopTask(repository);
    }
    
    /**
     * <p> 获取消息任务中所有企微员工ID<p/>
     * <AUTHOR> 
     * @date 2025年7月19日 下午3:11:46 
     * @return List<String> 
     * @throws
     */
    public List<String> gainCorpUserIds(){
        Set<String> corpUserIs = SetUtils.newHashSet();
        if(ListUtils.isEmpty(this.receives)) {
            return ListUtils.newArrayList(corpUserIs);
        }
        this.receives.forEach(re -> {
            corpUserIs.addAll(re.gainWecomUserIds());
        });
        return ListUtils.newArrayList(corpUserIs);
    }
    
    public RobotMessageSopTask handleTaskNodes(){
        if(ListUtils.isNotEmpty(this.nodes)) {
            this.taskCount = nodes.size();
            if(this.contents == null) {
                this.contents = ListUtils.newArrayList();
            }
            this.nodes.forEach(node -> {
                node.setId(IdUtil.getSnowflakeNextId());
                List<RobotMessageReceive> mrs = node.getReceives();
                if(ListUtils.isNotEmpty(mrs)) {
                    mrs.forEach(rec -> {
                        rec.setNodeId(node.getId());
                        this.receives.add(rec);
                    });
                }
                List<RobotMessageContent> ctns = node.getContents();
                if(ListUtils.isNotEmpty(ctns)) {
                    ctns.forEach(c -> {
                       c.setNodeId(node.getId());
                       this.contents.add(c);
                    });
                }
            });
        }
        return this;
    }
    
    /**
     * Description：对象进行仓储 <br>
     * author：luojl <br>
     * date：2025年6月24日 11:32:45 <br>
     * @return <br>
     */
    public boolean save() {
        return this.repository.save(this);
    }
    
    public boolean update() {
        return this.repository.update(this);
    }
    
    /**
     * <p> 更新任务状态和执行次数<p/>
     * @return boolean 
     * @throws
     */
    public boolean updateStatus() {
    	return this.repository.updateStatus(this);
    }

    /**
     * Description：初始化对象属性值 <br>
     * author：luojl <br>
     * date：2025年6月19日 09:53:07 <br>
     * @return <br>
     */
    public RobotMessageSopTask init() {
        this.taskStatus = TaskStatus.UNEXCUTE;
        if(this.businessType == null) {
            this.businessType = RobotMsgBusinessType.SOP;
        }
        return this;
    }
    
    /**
     * <p> 验证数据<p/>
     * <AUTHOR> 
     * @date 2025年7月24日 下午11:26:39 
     * @return RobotMessageTask 
     * @throws
     */
    public RobotMessageSopTask verifyInfo() {
        if(StrUtil.isEmpty(this.taskName)) {
            throw new ServiceException("消息任务名称不能为空");
        }
        if(ListUtils.isEmpty(this.nodes) || this.nodes.size() == 0) {
            throw new ServiceException("任务不能为空");
        }
        if(ListUtils.isEmpty(this.receives) || this.receives.size() == 0) {
            throw new ServiceException("接收者不能为空");
        }
        if(ListUtils.isEmpty(this.contents) || this.contents.size() == 0) {
            throw new ServiceException("消息内容不能为空");
        }
        return this;
    }
    
    public RobotMessageSopTask changeStatus() {
    	this.executeCount++;
    	this.taskStatus = (this.executeCount >= this.taskCount) ? TaskStatus.READY : TaskStatus.EXCUTE;
    	return this;
    }
    
    public void addMessages(List<RobotMessageDepository> msgs) {
        if(ListUtils.isEmpty(messages)) {
            this.messages = ListUtils.newArrayList();
        }
        this.messages.addAll(msgs);
    }
    
    public Integer getMode() {
    	if(this.mode == null) {
    		return 101;
    	}
    	return this.mode;
    }

}
