package com.panda.pollen.robot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * 接收客户 <br>
 * ClassName：com.panda.pollen.robot.dto.ReceiveCustomerDTO <br>
 * Copyright © 2025 eternal.net Inc. All rights reserved. <br> 
 * <AUTHOR> <br>
 * date 2025年6月26日 下午10:13:12 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReceiveCustomerDTO {

    /** 客户ID */
    private String id;
    /** 企微员工ID(wecomUserId) */
    private String wuId;
    /** 客户名称 */
    private String name;
    /** 企微员工绑定RobotId */
    private String robotId;
    
}
