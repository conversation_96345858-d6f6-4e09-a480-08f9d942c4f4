package com.panda.pollen.robot.domain;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

import com.panda.pollen.common.base.BaseDomain;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.utils.Dater;
import com.panda.pollen.common.utils.LdtUtils;
import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.robot.assembler.MessageContentAssembler;
import com.panda.pollen.robot.domain.factory.RobotMessageTaskNodeFactory;
import com.panda.pollen.robot.dto.WecomUserDTO;
import com.panda.pollen.robot.enums.NodeStatus;
import com.panda.pollen.robot.enums.RobotMsgBusinessType;
import com.panda.pollen.robot.enums.TaskRuleType;
import com.panda.pollen.robot.enums.TaskStatus;
import com.panda.pollen.robot.repository.RobotMessageTaskRepository;
import com.panda.pollen.robot.util.CronParser;
import com.panda.pollen.robot.vo.RobotMessageContentVO;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * ClassName：com.panda.robot.domain.RobotMessageTask <br>
 * Description：消息任务领域对象 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * @date 2025年6月17日 10:04:26 <br>
 * @version v1.0 <br>
 */
@Data
@Slf4j
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RobotMessageTask extends BaseDomain {
    
    private static final long serialVersionUID = 8049172391693049782L;

    /** 企微主体ID */
    private String corpId;
    /** 公司ID */
    private Long companyId;
    /** 部门ID */
    private Long deptId;
    /** 任务名称 */
    private String taskName;
    /** 描述 */
    private String describe;
    /** 规则类型，0-立即、1-单次、2-每日、3-每周 */
    private TaskRuleType ruleType;
    /** 发送规则(cron表达式) */
    private String sendRule;
    /** 任务开始日期 */
    private LocalDate startDate;
    /** 任务结束日期 */
    private LocalDate endDate;
    /** 星期几 */
    private List<DayOfWeek> ruleWeek;
    /** 执行时间 */
    private LocalTime ruleTime;
    /** 任务状态（0：待执行，1：执行中，2：已完成，-1：已取消） */
    private TaskStatus taskStatus;
    /** 业务类型 */
    private RobotMsgBusinessType businessType;
    /** SOP消息退订，客户回复消息终止后续消息推送 */
    private Integer unsubscribe;
    /** 任务数量 */
    private Integer taskCount;
    /** 已执行次数 */
    private Integer executeCount;
    /** 创建人ID */
    private Long userId;
    /** 消息模式：100-常规模式、101-转发模式 */
    private Integer mode;
    
    private RobotMessageTaskRepository repository;
    
    private List<RobotMessageContent> contents;
    
    private List<RobotMessageReceive> receives;
    
    private List<RobotMessageTaskNode> nodes;
    
    private List<RobotMessageDepository> messages;
    
    private List<LocalDateTime> taskTimes;
    
    private List<WecomUserDTO> wecomUsers;
    
    private RobotMessageTaskNode node;
    /** 是否立即 */
    @Builder.Default
    private Boolean instantly = Boolean.FALSE;
    /** 是否解析Cron */
    @Builder.Default
    private Boolean analysisCron = Boolean.FALSE;
    
    public RobotMessageTask(RobotMessageTaskRepository repository) {
        this.repository = repository;
    }
    
    public static RobotMessageTask build(RobotMessageTaskRepository repository) {
        return new RobotMessageTask(repository);
    }
    
    /**
     * <p> 获取消息任务中所有企微员工ID<p/>
     * <AUTHOR> 
     * @date 2025年7月19日 下午3:11:46 
     * @return List<String> 
     * @throws
     */
    public List<String> gainCorpUserIds(){
        List<String> corpUserIs = ListUtils.newArrayList();
        if(ListUtils.isEmpty(this.receives)) {
            return corpUserIs;
        }
        this.receives.forEach(re -> {
            corpUserIs.addAll(re.gainWecomUserIds());
        });
        return corpUserIs;
    }
    
    /**
     * Description：对象进行仓储 <br>
     * author：luojl <br>
     * date：2025年6月24日 11:32:45 <br>
     * @return <br>
     */
    public boolean save() {
        return this.repository.save(this);
    }
    
    public boolean update() {
        return this.repository.update(this);
    }
    
    /**
     * <p> 更新任务状态和执行次数<p/>
     * @return boolean 
     * @throws
     */
    public boolean updateStatus() {
    	return this.repository.updateStatus(this);
    }

    /**
     * Description：初始化对象属性值 <br>
     * author：luojl <br>
     * date：2025年6月19日 09:53:07 <br>
     * @return <br>
     */
    public RobotMessageTask init() {
        this.taskStatus = TaskStatus.UNEXCUTE;
        if(this.businessType == null) {
            this.businessType = RobotMsgBusinessType.TASK;
        }
        return this;
    }
    
    /**
     * <p> 验证数据<p/>
     * <AUTHOR> 
     * @date 2025年7月24日 下午11:26:39 
     * @return RobotMessageTask 
     * @throws
     */
    public RobotMessageTask verifyInfo() {
        if(StrUtil.isEmpty(this.taskName)) {
            throw new ServiceException("消息任务名称不能为空");
        }
        if(ListUtils.isEmpty(this.contents) || this.contents.size() == 0) {
            throw new ServiceException("消息内容不能为空");
        }
        if(ListUtils.isEmpty(this.receives) || this.receives.size() == 0) {
            throw new ServiceException("接收者不能为空");
        }
        return this;
    }
    
    public RobotMessageTask changeStatus() {
    	this.executeCount ++;
    	this.taskStatus = (this.executeCount >= this.taskCount) ? TaskStatus.READY : TaskStatus.EXCUTE;
    	return this;
    }
    
    /**
     * Description：检查规则类型是否为cron解析类型，如果是需要生成Cron，<br>
     *          则调用toCron获取cron表达式，同时根据cron计算出执行时间 <br>
     * author：luojl <br>
     * date：2025年6月24日 10:40:20 <br>
     * @return <br>
     */
    public RobotMessageTask analysisCron() {
        this.analysisCron = TaskRuleType.isAnalysis(ruleType.getCode());
        if(this.analysisCron) {
            this.sendRule = this.ruleType.toCron(this.startDate, this.endDate, this.ruleTime, this.ruleWeek);
            this.taskTimes = CronParser.getTaskTimes(sendRule, this.ruleTime.atDate(startDate), this.ruleTime.atDate(endDate));
        } else if(excuteOnce()){
        	LocalDateTime et = this.ruleTime.atDate(startDate);
        	if(et.isBefore(LocalDateTime.now())) {
        		throw new ServiceException("执行时间不能小于当前时间");
        	}
            this.taskTimes = ListUtils.newArrayList(et);
        } else if(this.instantly){
            this.taskTimes = ListUtils.newArrayList(LdtUtils.now());
        } else {
            this.taskTimes = ListUtils.newArrayList();
        }
        return this;
    }
    
    /**
     * 解析任务执行节点 
     * <AUTHOR> 
     * @date 2025年6月29日 下午10:24:24 
     */ 
    public RobotMessageTask analysisTaskNode(){
        List<LocalDateTime> executeTimes = getTaskTimes();
        if(ListUtils.isEmpty(executeTimes)){
            return this;
        }
        List<RobotMessageTaskNode> nodes = ListUtils.newArrayList();
        for (LocalDateTime execTime : executeTimes) {
            RobotMessageTaskNode node = RobotMessageTaskNodeFactory.create();
            node.setId(IdUtil.getSnowflakeNextId());
            node.setExecuteTime(execTime);
            node.setTaskId(getId());
            node.setNodeStatus(this.instantly ? NodeStatus.EXECUTED : NodeStatus.UNEXECUTE);
            nodes.add(node);
        }
        this.taskCount = nodes.size();
        this.nodes = nodes;
        return this;
    }
    
    /**
     * 执行一次 <br>
     */
    public boolean excuteOnce() {
        return TaskRuleType.isOnce(ruleType.getCode());
    }
    
    /**
     * 检查规则类型是否为立即执行，如果是则分解出Robot消息 <br>
     * <AUTHOR> luojl
     * @date 2025/6/30
     * @return: RobotMessageTask
     */
    public RobotMessageTask instantly() {
        this.instantly = TaskRuleType.isInstantly(ruleType.getCode());
        if(this.instantly) {
            this.executeCount ++;
            this.taskStatus = TaskStatus.READY;
            this.startDate = LdtUtils.nowLd();
            this.ruleTime = LocalTime.now();
            if(this.getId() == null) {
            	this.setId(IdUtil.getSnowflakeNextId());
            }
        }
        return this;
    }

    /**
     * 根据推送Cron表达式解析出下次执行时间，如果下次执行时间大于EndDate将变更任务状态为已完成，此时不再更新executeTime <br>
     * <AUTHOR> luojl
     * @date 2025/6/30
     * @return: RobotMessageTask
     */
    public RobotMessageTask nextExcuteTime() {
    	if(StrUtil.isEmpty(sendRule)) {
    		log.error("【nextTime】 Cron 表达式为空 ------------>>> ");
    		return this;
    	}
    	LocalDateTime nextTime = CronParser.getNextTime(sendRule, LocalDateTime.now());
    	LocalDate nt = nextTime.toLocalDate();
    	if(DateUtil.compare(Dater.ldtToDate(nt), Dater.ldtToDate(endDate)) > 0) {
    		this.taskStatus = TaskStatus.READY;
    		return this;
    	}
    	return this;
    } 
    
    public void addMessages(List<RobotMessageDepository> msgs) {
        if(ListUtils.isEmpty(messages)) {
            this.messages = ListUtils.newArrayList();
        }
        this.messages.addAll(msgs);
    }
    
    public List<RobotMessageContentVO> getContentVOs(){
        return MessageContentAssembler.INSTANCE.toVOs(contents);
    }
    
    public Integer getMode() {
    	if(this.mode == null) {
    		return 101;
    	}
    	return this.mode;
    }
    
    public boolean isSop() {
        return RobotMsgBusinessType.isSop(this.businessType);
    }
    
    /**
     * SOP任务是否开启退订业务
     * <AUTHOR> 
     * @date 2025年9月10日 下午8:53:26 
     * @return boolean true-开启，false-未开启
     * @throws
     */
    public boolean sopUnsubscribe() {
        return isSop() && Integer.valueOf(1).equals(this.unsubscribe);
    }

}
