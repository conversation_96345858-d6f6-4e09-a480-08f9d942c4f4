package com.panda.pollen.robot.vo;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.panda.pollen.robot.enums.NodeStatus;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * SOP消息任务节点VO对象
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RobotMessageSopTaskNodeVO {

    /** 主键ID */
    private Long id;
    /** 任务ID */
    private Long taskId;
    /** 任务天 */
    private Integer taskDay;
    /** 任务时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime executeTime;
    /** 任务节点状态（-1:已取消，0-待执行，1-已执行） */
    private NodeStatus nodeStatus;
    /** 已选群数量 */
    private Integer crowdCount;
    /** 已选客户数量 */
    private Integer customerCount;
    /** 失败数量 */
    private Integer failCount;
    /** 执行开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    /** 执行结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    /** 备注 */
    private String remark;
    
    /** 单独配置-接收者集 */
    private List<RobotMessageReceiveVO> receives;
    
    /** 任务消息内容集 */
    private List<RobotMessageContentVO> contents;
    
    public String formatConent() {
        if(this.executeTime != null) {
            String date = LocalDateTimeUtil.format(this.executeTime, DatePattern.NORM_DATETIME_PATTERN) + " (%s)";
            return String.format(date, this.nodeStatus.getName());
        }
        return StrUtil.EMPTY;
    }
    
}
