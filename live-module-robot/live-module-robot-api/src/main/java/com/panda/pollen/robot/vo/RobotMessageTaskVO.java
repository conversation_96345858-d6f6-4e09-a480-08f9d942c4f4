package com.panda.pollen.robot.vo;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.robot.enums.RobotMsgBusinessType;
import com.panda.pollen.robot.enums.TaskRuleType;
import com.panda.pollen.robot.enums.TaskStatus;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 消息任务VO对象
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "消息任务VO", value = "RobotMessageTaskVO")
public class RobotMessageTaskVO {

    /** 主键ID */
    private Long id;
    /** 公司ID */
    private Long companyId;
    /** 部门ID */
    private Long deptId;
    /** 创建人ID */
    private Long userId;
    /** 企微主体ID */
    private String corpId;
    /** 任务名称 */
    private String taskName;
    /** 描述 */
    private String describe;
    /** 规则类型，0-立即、1-单次、2-每日、3-每周 */
    private TaskRuleType ruleType;
    /** 发送规则(cron表达式) 后端处理 */
    private String sendRule;
    /** 任务开始日期 */
    private LocalDate startDate;
    /** 任务结束日期 */
    private LocalDate endDate;
    /** 星期几 */
    private List<DayOfWeek> ruleWeek;
    /** 星期几-值*/
    private List<Integer> ruleWeeks;
    /** 任务时间 */
    private LocalTime ruleTime;
    /** 任务状态（0：待执行，1：执行中，2：已完成，-1：已取消） */
    private TaskStatus taskStatus;
    /** 业务类型 */
    private RobotMsgBusinessType businessType;
    /** SOP消息退订，客户回复消息终止后续消息推送 */
    private Integer unsubscribe;
    /** 任务数量 */
    private Integer taskCount;
    /** 已执行次数 */
    private Integer executeCount;
    /** 目标总数 */
    private Integer targetCount;
    /** 创建者 */
    private String createBy;
    /** 创建时间 */
    private Date createTime;
    /** 修改者 */
    private String updateBy;
    /** 修改时间 */
    private Date updateTime;
    /** 创建者昵称 */
    private String nickName;

    public List<Integer> getRuleWeeks(){
    	if(this.ruleWeek == null) {
    		return ListUtils.newArrayList();
    	}
    	this.ruleWeeks = ListUtils.newArrayList();
        for ( DayOfWeek dayOfWeek : this.ruleWeek) {
        	this.ruleWeeks.add(dayOfWeek.ordinal());
        }
        return this.ruleWeeks;
    }
    
}
