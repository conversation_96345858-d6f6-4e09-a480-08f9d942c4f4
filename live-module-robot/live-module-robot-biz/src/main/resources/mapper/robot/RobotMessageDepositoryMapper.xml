<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.robot.mapper.RobotMessageDepositoryMapper">
    
    <resultMap type="com.panda.pollen.robot.vo.RobotMessageDepositoryVO" id="RobotMessageDepositoryResult">
        <result property="id"                   column="id"    />
        <result property="companyId"            column="company_id"    />
        <result property="robotId"              column="robot_id"    />
        <result property="receiveCode"          column="receive_code"   />
        <result property="deptId"               column="dept_id"    />
        <result property="taskId"               column="task_id"    />
        <result property="nodeId"               column="node_id"    />
        <result property="receiveType"          column="receive_type"    />
        <result property="customAccount"        column="custom_account"    />
        <result property="customerId"           column="customer_id"    />
        <result property="receiveCustomer"      column="receive_customer"    />
        <result property="sendStatus"           column="send_status"    />
        <result property="executeTime"          column="execute_time"    />
        <result property="sendTime"             column="send_time"    />
        <result property="createBy"             column="create_by"    />
        <result property="createTime"           column="create_time"    />
        <result property="updateBy"             column="update_by"    />
        <result property="updateTime"           column="update_time"    />
    </resultMap>

    <sql id="depositoryCommonSql">
        SELECT t.id, t.task_id, t.node_id, t.receive_code, t.company_id, t.dept_id, t.robot_id, t.receive_type, t.wecom_user_id, 
                t.customer_id, t.receive_customer, t.send_status, 
                t.execute_time, t.send_time, t.create_by, t.create_time, t.update_by, t.update_time 
        FROM live_robot_message_depository t
    </sql>
    
    <select id="selectPageList" parameterType="com.panda.pollen.robot.param.MessageDepositoryParam" 
    		resultType="com.panda.pollen.robot.vo.MessageDepositoryPageVO">
        SELECT  t.id, t.task_id, t.node_id, t.dept_id, t.wecom_user_id, 
                t.customer_id as receive_id, t.receive_customer as receive_name, t.send_status, 
                e.user_name as wecom_user_name, e.avatar, 
                c.avatar as head_img_url, c.remark_mobiles as phone_number,
                c.add_way, c.customer_type
        FROM live_robot_message_depository t 
        	LEFT JOIN sys_user u ON u.user_id = t.user_id AND u.del_flag = 0
        	LEFT JOIN sys_dept d ON u.dept_id = d.dept_id AND d.del_flag = 0
        	LEFT JOIN wx_auth_corp_user e ON e.user_id = t.wecom_user_id AND e.deleted = 0
        	LEFT JOIN wx_auth_corp_user_customer c ON c.external_user_id = t.customer_id AND e.user_id = c.user_id AND c.deleted = 0
        <where>  
        	t.receive_type = 1 AND c.friend_status = 0 
            <if test="companyId != null "> and t.company_id = #{companyId}</if>
            <if test="deptId != null "> and t.dept_id = #{deptId}</if>
            <if test="taskId != null "> and t.task_id = #{taskId}</if>
            <if test="nodeId != null "> and t.node_id = #{nodeId}</if>
            <if test="wecomUserId != null  and wecomUserId != ''"> and t.wecom_user_id = #{wecomUserId}</if>
            <if test="customerId != null and customerId !='' "> and t.customer_id = #{customerId}</if>
            <if test="receiveCustomer != null  and receiveCustomer != ''"> and t.receive_customer = #{receiveCustomer}</if>
            <if test="sendStatus != null "> and t.send_status = #{sendStatus}</if>
            <if test="params.beginTime != null and params.beginTime != ''">
                and t.execute_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime !=''">
                and t.execute_time &lt;= #{params.endTime}
            </if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
    </select>
    <select id="selectGroupPageList" parameterType="com.panda.pollen.robot.param.MessageDepositoryParam" 
    		resultType="com.panda.pollen.robot.vo.MessageDepositoryGroupPageVO">
        SELECT  t.id, t.task_id, t.node_id, t.dept_id, t.wecom_user_id, 
                t.customer_id as group_id, t.receive_customer as group_name, t.send_status, 
                e.user_name as wecom_user_name, e.avatar, g.group_create_time
        FROM live_robot_message_depository t 
        	LEFT JOIN sys_user u ON u.user_id = t.user_id AND u.del_flag = 0
        	LEFT JOIN sys_dept d ON u.dept_id = d.dept_id AND d.del_flag = 0
        	LEFT JOIN wx_auth_corp_user e ON e.user_id = t.wecom_user_id AND e.deleted = 0
        	LEFT JOIN wx_auth_corp_user_group g ON g.group_id = t.customer_id AND g.deleted = 0
        <where>  
        	t.receive_type = 0 
            <if test="companyId != null "> and t.company_id = #{companyId}</if>
            <if test="deptId != null "> and t.dept_id = #{deptId}</if>
            <if test="taskId != null "> and t.task_id = #{taskId}</if>
            <if test="nodeId != null "> and t.node_id = #{nodeId}</if>
            <if test="wecomUserId != null  and wecomUserId != ''"> and t.wecom_user_id = #{wecomUserId}</if>
            <if test="customerId != null and customerId != ''"> and t.customer_id = #{customerId}</if>
            <if test="receiveCustomer != null and receiveCustomer != ''"> and t.receive_customer = #{receiveCustomer}</if>
            <if test="sendStatus != null "> and t.send_status = #{sendStatus}</if>
            <if test="params.beginTime != null and params.beginTime != ''">
                and t.execute_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime !=''">
                and t.execute_time &lt;= #{params.endTime}
            </if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
    </select>

    <select id="selectByCondition" parameterType="com.panda.pollen.robot.dto.RobotMessageDepositoryDTO" resultMap="RobotMessageDepositoryResult">
        <include refid="depositoryCommonSql"/>
        <where>  
            <if test="companyId != null "> and t.company_id = #{companyId}</if>
            <if test="receiveCode != null  and receiveCode != ''"> and t.receive_code = #{receiveCode}</if>
            <if test="robotId != null  and robotId != ''"> and t.robot_id = #{robotId}</if>
            <if test="deptId != null "> and t.dept_id = #{deptId}</if>
            <if test="taskId != null "> and t.task_id = #{taskId}</if>
            <if test="nodeId != null "> and t.node_id = #{nodeId}</if>
            <if test="wecomUserId != null  and wecomUserId != ''"> and t.wecom_user_id = #{wecomUserId}</if>
            <if test="customerId != null "> and t.customer_id = #{customerId}</if>
            <if test="receiveCustomer != null  and receiveCustomer != ''"> and t.receive_customer = #{receiveCustomer}</if>
            <if test="sendStatus != null "> and t.send_status = #{sendStatus}</if>
            <if test="sendTime != null "> and t.send_time = #{sendTime}</if>
        </where>
    </select>
    
    <select id="selectReceivcesExcludeUnsubscribe" parameterType="java.lang.Long" resultType="com.panda.pollen.robot.vo.RobotMessageDepositoryVO">
    	SELECT t.* FROM live_robot_message_depository t
    		WHERE t.task_id = #{taskId} 
    			AND t.node_id = (SELECT MIN(t1.node_id) AS node_id  
    					FROM live_robot_message_depository t1 
						WHERE t1.task_id = #{taskId} GROUP BY t1.task_id)
				AND NOT EXISTS (SELECT s.customer_id FROM live_robot_sop_message_unsubscribe s WHERE s.customer_id = t.customer_id)
    </select>
    
    <select id="selectSendMessages" parameterType="com.panda.pollen.robot.dto.RobotMessageDepositoryDTO" 
            resultType="com.panda.pollen.robot.vo.RobotMessageDepositoryVO">
        SELECT t.id, k.corp_id, t.task_id, t.node_id, t.receive_code, t.company_id, t.dept_id, t.robot_id, t.receive_type, t.wecom_user_id, 
                t.customer_id, t.receive_customer, t.send_status, 
                t.execute_time, t.send_time, t.create_by, t.create_time, t.update_by, t.update_time 
        	FROM live_robot_message_depository t
        	LEFT JOIN live_robot_message_task k ON k.id = t.task_id
        <where>  
            t.send_status = 0 
            <if test="companyId != null "> and t.company_id = #{companyId}</if>
            <if test="receiveCode != null  and receiveCode != ''"> and t.receive_code = #{receiveCode}</if>
            <if test="robotId != null  and robotId != ''"> and t.robot_id = #{robotId}</if>
            <if test="deptId != null "> and t.dept_id = #{deptId}</if>
            <if test="taskId != null "> and t.task_id = #{taskId}</if>
            <if test="nodeId != null "> and t.node_id = #{nodeId}</if>
            <if test="wecomUserId != null  and wecomUserId != ''"> and t.wecom_user_id = #{wecomUserId}</if>
            <if test="customerId != null "> and t.customer_id = #{customerId}</if>
            <if test="receiveCustomer != null  and receiveCustomer != ''"> and t.receive_customer = #{receiveCustomer}</if>
            <if test="sendStatus != null "> and t.send_status = #{sendStatus}</if>
            <if test="sendTime != null "> and t.send_time = #{sendTime}</if>
            <if test="params.beginTime != null and params.beginTime !=''">
                and t.execute_time &lt;= #{params.beginTime}
            </if>
        </where>
    </select>
    
    <select id="selectRobotMessageDepositoryById" parameterType="Long" resultMap="RobotMessageDepositoryResult">
        <include refid="depositoryCommonSql"/>
        where id = #{id}
    </select>

</mapper>