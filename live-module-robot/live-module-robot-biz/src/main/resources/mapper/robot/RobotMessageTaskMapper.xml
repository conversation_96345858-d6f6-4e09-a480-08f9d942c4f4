<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.robot.mapper.RobotMessageTaskMapper">
    
    <resultMap type="com.panda.pollen.robot.vo.RobotMessageTaskVO" id="RobotMessageTaskResult">
        <result property="id"               column="id"    />
        <result property="corpId"        	column="corp_id"    />
        <result property="companyId"        column="company_id"    />
        <result property="deptId"           column="dept_id"    />
        <result property="taskName"         column="task_name"    />
        <result property="describe"         column="describe"    />
        <result property="ruleType"         column="rule_type"    />
        <result property="sendRule"         column="send_rule"    />
        <result property="startDate"        column="start_date"    />
        <result property="endDate"          column="end_date"    />
        <result property="ruleWeek"         column="rule_week" typeHandler="com.panda.pollen.robot.handler.WeekTypeHandler"/>
        <result property="ruleTime"         column="rule_time"    />
        <result property="taskCount"        column="task_count"    />
        <result property="executeCount"     column="execute_count"    />
        <result property="taskStatus"       column="task_status"    />
        <result property="businessType"  	column="business_type" typeHandler="com.panda.pollen.robot.handler.RobotMsgBusinessTypeHandler"/>
        <result property="userId"          	column="user_id"    />
        <result property="createBy"         column="create_by"    />
        <result property="createTime"       column="create_time"    />
        <result property="updateBy"         column="update_by"    />
        <result property="updateTime"       column="update_time"    />
        <result property="nickName"       column="nick_name"    />
    </resultMap>

    <sql id="taskCommonSql">
        SELECT t.id, t.corp_id, t.company_id, t.dept_id, t.task_name, t.describe, 
                t.rule_type, t.send_rule, t.start_date, t.end_date, t.rule_week, t.rule_time,
                t.task_count, t.execute_count, t.task_status, t.business_type, t.user_id, t.create_by,
                t.create_time, t.update_by, t.update_time, s.nick_name
        FROM live_robot_message_task t
        LEFT JOIN sys_user s ON s.user_id = t.user_id
    </sql>

    <select id="selectPageList" parameterType="com.panda.pollen.robot.param.MessageTaskParam" resultMap="RobotMessageTaskResult">
        <include refid="taskCommonSql"/>
        <where>  
        	t.deleted = 0
            <if test="corpId != null  and corpId != ''"> and t.corp_id = #{describe}</if>
            <if test="companyId != null "> and t.company_id = #{companyId}</if>
            <if test="deptId != null "> and t.dept_id = #{deptId}</if>
            <if test="taskName != null  and taskName != ''"> and t.task_name like concat('%', #{taskName}, '%')</if>
            <if test="describe != null  and describe != ''"> and t.describe = #{describe}</if>
            <if test="ruleType != null "> and t.rule_type = #{ruleType}</if>
            <if test="taskStatus != null "> and t.task_status = #{taskStatus}</if>
            <if test="businessType != null "> and t.business_type = #{businessType}</if>
            <if test="params.beginTime != null and params.beginTime != ''">
            	and t.create_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
            	and t.create_time &lt;= #{params.endTime}
            </if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        ORDER BY t.create_time desc
    </select>
    
    <select id="selectRobotMessageTaskById" parameterType="Long" resultMap="RobotMessageTaskResult">
        SELECT t.id, t.corp_id, t.company_id, t.dept_id, t.task_name, t.describe, 
                t.rule_type, t.send_rule, t.start_date, t.end_date, t.rule_week, t.rule_time,
                t.task_count, 
                t.execute_count, 
                (SELECT COUNT(1) as reveice_count FROM live_robot_message_depository d WHERE d.task_id = t.id) AS target_count,
                t.task_status, t.business_type, t.user_id, t.create_by,
                t.create_time, t.update_by, t.update_time, s.nick_name
        FROM live_robot_message_task t
        	LEFT JOIN sys_user s ON s.user_id = t.user_id
       	 	WHERE id = #{id} and deleted = 0
    </select>
    
    <select id="selectByCondition" parameterType="com.panda.pollen.robot.dto.RobotMessageTaskDTO" 
            resultType="com.panda.pollen.robot.entity.RobotMessageTaskDO">
        <include refid="taskCommonSql"/>
        <where>  
        	t.deleted = 0
        	<if test="corpId != null  and corpId != ''"> and t.corp_id = #{describe}</if>
            <if test="companyId != null "> and t.company_id = #{companyId}</if>
            <if test="deptId != null "> and t.dept_id = #{deptId}</if>
            <if test="taskName != null  and taskName != ''"> and t.task_name like concat('%', #{taskName}, '%')</if>
            <if test="describe != null  and describe != ''"> and t.describe = #{describe}</if>
            <if test="ruleType != null "> and t.rule_type = #{ruleType}</if>
            <if test="sendRule != null  and sendRule != ''"> and t.send_rule = #{sendRule}</if>
            <if test="taskStatus != null "> and t.task_status = #{taskStatus}</if>
            <if test="businessType != null "> and t.business_type = #{businessType}</if>
            <if test="params.taskIds != null and params.taskIds.size() > 0">
                AND t.id IN
                <foreach collection="params.taskIds" item="taskId" open="(" separator="," close=")">
                    #{taskId}
                </foreach>
            </if>
        </where>
    </select>
    
</mapper>