<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.robot.mapper.RobotSopTemplateMapper">
    
    <resultMap type="com.panda.pollen.robot.vo.RobotSopTemplateVO" id="RobotSopTemplateResult">
        <result property="id"               column="id"    />
        <result property="companyId"        column="company_id"    />
        <result property="taskName"         column="task_name"    />
        <result property="userId"          	column="user_id"    />
        <result property="groupId"          column="group_id"    />
        <result property="crowdType"        column="crowd_type"    />
        <result property="createBy"         column="create_by"    />
        <result property="createTime"       column="create_time"    />
        <result property="updateBy"         column="update_by"    />
        <result property="updateTime"       column="update_time"    />
    </resultMap>

    <sql id="taskCommonSql">
        SELECT t.id, t.company_id, t.task_name, t.group_id, t.crowd_type, t.user_id, t.create_by,
                t.create_time, t.update_by, t.update_time 
        FROM live_robot_sop_template t
        LEFT JOIN sys_user s ON s.user_id = t.user_id
    </sql>

    <select id="selectSopTemplatePages" parameterType="com.panda.pollen.robot.dto.RobotSopTemplateDTO" 
    		resultType="com.panda.pollen.robot.vo.RobotSopTemplateVO">
        SELECT t.id, t.company_id, t.task_name, t.group_id, t.crowd_type, t.user_id, t.create_by,
        		(SELECT COUNT(1) FROM live_robot_sop_template_content c WHERE c.template_id = t.id) AS task_count,
        		(SELECT GROUP_CONCAT(DISTINCT c.content_type) AS types FROM live_robot_sop_template_content c WHERE c.template_id = t.id) as contents,
                t.create_time, t.update_by, t.update_time 
        FROM live_robot_sop_template t
        LEFT JOIN sys_user s ON s.user_id = t.user_id
        <where>  
        	t.deleted = 0
            <if test="companyId != null "> and t.company_id = #{companyId}</if>
            <if test="groupId != null "> and t.group_id = #{groupId}</if>
            <if test="taskName != null  and taskName != ''"> and t.task_name like concat('%', #{taskName}, '%')</if>
            <if test="crowdType != null "> and t.crowd_type = #{crowdType}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        ORDER BY t.create_time desc
    </select>
    
    <select id="selectRobotSopTemplateById" parameterType="Long" resultMap="RobotSopTemplateResult">
        <include refid="taskCommonSql"/>
        where id = #{id} and deleted = 0
    </select>
    
    <select id="selectByCondition" parameterType="com.panda.pollen.robot.dto.RobotSopTemplateDTO" 
            resultType="com.panda.pollen.robot.entity.RobotSopTemplateDO">
        <include refid="taskCommonSql"/>
        <where>  
        	t.deleted = 0
        	<if test="corpId != null  and corpId != ''"> and t.corp_id = #{describe}</if>
            <if test="companyId != null "> and t.company_id = #{companyId}</if>
            <if test="deptId != null "> and t.dept_id = #{deptId}</if>
            <if test="taskName != null  and taskName != ''"> and t.task_name like concat('%', #{taskName}, '%')</if>
            <if test="describe != null  and describe != ''"> and t.describe = #{describe}</if>
            <if test="ruleType != null "> and t.rule_type = #{ruleType}</if>
            <if test="sendRule != null  and sendRule != ''"> and t.send_rule = #{sendRule}</if>
            <if test="taskStatus != null "> and t.task_status = #{taskStatus}</if>
            <if test="businessType != null "> and t.business_type = #{businessType}</if>
            <if test="params.taskIds != null and params.taskIds.size() > 0">
                AND t.id IN
                <foreach collection="params.taskIds" item="taskId" open="(" separator="," close=")">
                    #{taskId}
                </foreach>
            </if>
        </where>
    </select>
    
    <delete id="batchDeleteTemplate">
    	UPDATE live_robot_sop_template SET deleted = 1 where
    	<if test="templateIds != null and templateIds.size() > 0">
            id IN
            <foreach collection="templateIds" item="tId" open="(" separator="," close=")">
                #{tId}
            </foreach>
        </if>
    </delete>
    
</mapper>