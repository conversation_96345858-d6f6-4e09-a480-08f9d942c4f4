package com.panda.pollen.robot.wrapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.excel.util.MapUtils;
import com.panda.pollen.common.utils.LdtUtils;
import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.robot.assembler.MessageContentAssembler;
import com.panda.pollen.robot.assembler.MessageDepositoryAssembler;
import com.panda.pollen.robot.assembler.WecomCorpAssembler;
import com.panda.pollen.robot.consts.RobotConst;
import com.panda.pollen.robot.context.RelayMsgContext;
import com.panda.pollen.robot.domain.RobotMessageDepository;
import com.panda.pollen.robot.domain.RobotMessageTask;
import com.panda.pollen.robot.domain.WecomCorp;
import com.panda.pollen.robot.dto.RelayMsg;
import com.panda.pollen.robot.dto.RobotMessageDepositoryDTO;
import com.panda.pollen.robot.dto.RobotMessageTaskDTO;
import com.panda.pollen.robot.dto.RobotMessageTaskNodeDTO;
import com.panda.pollen.robot.enums.SendStatus;
import com.panda.pollen.robot.factory.RobotMessageFactory;
import com.panda.pollen.robot.factory.RobotRelayMessageFactory;
import com.panda.pollen.robot.repository.RobotMessageContentRepository;
import com.panda.pollen.robot.repository.RobotMessageDepositoryRepository;
import com.panda.pollen.robot.repository.RobotMessageDetailsRepository;
import com.panda.pollen.robot.repository.RobotMessageTaskNodeRepository;
import com.panda.pollen.robot.repository.RobotMessageTaskRepository;
import com.panda.pollen.robot.util.RobotCacheUtil;
import com.panda.pollen.robot.vo.RobotMessageContentVO;
import com.panda.pollen.robot.vo.RobotMessageDepositoryVO;
import com.panda.pollen.robot.vo.RobotMessageTaskNodeVO;
import com.panda.pollen.scrm.api.AuthCorpApi;
import com.panda.pollen.scrm.domain.AuthCorp;
import com.panda.robot.domain.Response;
import com.panda.robot.domain.dto.Message;
import com.panda.robot.enums.RobotMsgMode;
import com.panda.robot.util.WeComRobotUtil;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**   
 * ClassName：com.panda.pollen.robot.wrapper.RobotMessagePushThread <br>
 * Description：Robot消息推送业务线程类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * @date 2025年6月24日 11:38:23 <br>
 * @version v1.0 <br>  
 */
@Data
@Slf4j
@Builder
public class RobotMessagePushThread implements Runnable {

    private String robotId;
    private LocalDateTime executeTime;
    private RobotMessageTaskRepository taskRepository;
    private RobotMessageTaskNodeRepository nodeRepository;
    private RobotMessageDepositoryRepository depositoryRepository;
    private RobotMessageDetailsRepository detailsRepository;
    private RobotMessageContentRepository contentRepository;
    private AuthCorpApi corpApi;
    private RobotMsgMode mode;
    
    @Override
    public void run() {
        log.info("【push】 execute --------->> {}", robotId);
        RobotMessageDepositoryDTO params = RobotMessageDepositoryDTO.builder().robotId(this.robotId)
            .sendStatus(SendStatus.UNSEND).executeTime(this.executeTime).build();
        List<RobotMessageDepositoryVO> vos = depositoryRepository.querySendMessages(params);
        if(ListUtils.isEmpty(vos)) {
            log.info("【push】{} 本次任务没有未推送消息------------->>> {}", robotId, executeTime);
            return;
        }
        if(mode == RobotMsgMode.RELAY) {
        	handleRelayMessage(vos);
        } else {
        	handleSendMessage(vos);
        }
    }
    
    private void handleRelayMessage(List<RobotMessageDepositoryVO> vos) {
    	WecomCorp corp = fetchCorp(vos);
        List<RobotMessageDepository> msgs = MessageDepositoryAssembler.INSTANCE.fromVOs(vos);
        Map<Long, List<RobotMessageDepository>> datas = msgs.stream().collect(Collectors.groupingBy(RobotMessageDepository::getTaskId));
        List<Long> taskIds = ListUtils.newArrayList(datas.keySet());
        Map<Long, List<RobotMessageDepository>> nodeReceives = msgs.stream().collect(Collectors.groupingBy(RobotMessageDepository::getNodeId));
        
        List<RobotMessageContentVO> contents = contentRepository.queryByTaskIds(taskIds);
        Map<Long, List<RobotMessageContentVO>> msgContents = contents.stream().collect(Collectors.groupingBy(RobotMessageContentVO::getTaskId));
        Map<Long, List<RobotMessageContentVO>> nodeContents = contents.stream().filter(c -> c.getNodeId() != null).collect(Collectors.groupingBy(RobotMessageContentVO::getNodeId));
        
        List<RobotMessageTask> taskTemps = getRobotMessageTasks(taskIds);
        Map<Long, RobotMessageTask> tasks = taskTemps.stream().collect(Collectors.toMap(RobotMessageTask::getId, t -> t));
        List<RobotMessageTaskNodeVO> nodes = nodeRepository.getByCondition(RobotMessageTaskNodeDTO.builder().build().addParams(RobotConst.NODEIDS_KEY, ListUtils.newArrayList(nodeReceives.keySet())));
        nodes.forEach(n -> {
            RobotMessageTask task = tasks.get(n.getTaskId());
            List<RobotMessageContentVO> ctns = nodeContents.get(n.getId());
            if(ListUtils.isEmpty(ctns)) {
                ctns = msgContents.get(n.getTaskId());
            } 
            task.setContents(MessageContentAssembler.INSTANCE.fromVOs(ctns));
            List<RobotMessageDepository> reveices = nodeReceives.get(n.getId());
            reveices.forEach(d -> {d.setCorp(corp);});
            task.setMessages(reveices);
            RelayMsgContext ctx = RobotRelayMessageFactory.generateRobotMsg(task);
            sendRelay(ctx);
        }); 
    }
    
    /**
     * <p> 消息转发模式推送<p/>
     * <AUTHOR> 
     * @date 2025年8月5日 13:52:28 
     * @param ctx
     * @return void 
     * @throws
     */
    private void sendRelay(RelayMsgContext ctx) {
    	List<Message> masters = ctx.getMasters();
    	List<RelayMsg> messages = ctx.getMessage();
    	masters.forEach(m ->{
    		Response res = RobotCacheUtil.offline(ctx.getRobotId()) ? null : WeComRobotUtil.sendMix(m);
    		log.info("【master】定时任务 --->>> 转发主消息推送结果：{}", res);
    		if(res != null && res.success()) {
    			messages.forEach(relay -> {
    			    Message relayMsg = relay.getRelayMsg();
                    relayMsg.setLimit(m.getLimit());
    				Response rps = WeComRobotUtil.sendMix(relayMsg);
    				RobotRelayMessageFactory.saveRelayMsgDetails(m, relay, rps, res);
    			});
    		}
    	});
    }
    
    /**
     * <p> 加载任务对象<p/>
     * <AUTHOR> 
     * @date 2025年8月5日 13:52:58 
     * @param taskIds
     * @return List<RobotMessageTask> 
     * @throws
     */
    private List<RobotMessageTask> getRobotMessageTasks(List<Long> taskIds){
    	RobotMessageTaskDTO params = RobotMessageTaskDTO.builder().build();
        Map<String, Object> p = MapUtils.newHashMap();
        p.put("taskIds", taskIds);
        params.setParams(p);
        List<RobotMessageTask> tasks = taskRepository.getByCondition(params);
        return tasks;
    }
    
    /**
     * <p> 常规模式推送消息<p/>
     * <AUTHOR> 
     * @date 2025年8月5日 13:36:19 
     * @param vos void 
     * @throws
     */
    private void handleSendMessage(List<RobotMessageDepositoryVO> vos) {
    	WecomCorp corp = fetchCorp(vos);
        List<RobotMessageDepository> msgs = MessageDepositoryAssembler.INSTANCE.fromVOs(vos);
        Map<Long, List<RobotMessageDepository>> datas = msgs.stream().collect(Collectors.groupingBy(RobotMessageDepository::getTaskId));
        List<RobotMessageContentVO> contents = contentRepository.queryByTaskIds(ListUtils.newArrayList(ListUtils.newArrayList(datas.keySet())));
        Map<Long, List<RobotMessageContentVO>> msgContents = contents.stream().collect(Collectors.groupingBy(RobotMessageContentVO::getTaskId));
        Map<Long, List<RobotMessageContentVO>> nodeContents = contents.stream().filter(c -> c.getNodeId() != null).collect(Collectors.groupingBy(RobotMessageContentVO::getNodeId));
        for (RobotMessageDepository message : msgs) {
            try {
                message.setCorp(corp);
                List<RobotMessageContentVO> ctns = message.getNodeId() != null ? nodeContents.get(message.getNodeId()) : null;
                if(ListUtils.isEmpty(ctns)) {
                    ctns = msgContents.get(message.getTaskId());
                }
                List<Message> msgList = RobotMessageFactory.generateMsg(message, ctns);
                log.info("【pushContents】---->> {} ", msgList);
                // 验证机器人是否在线
                List<Response> res = RobotCacheUtil.offline(message.getRobotId()) ? null : WeComRobotUtil.send(msgList);
                RobotMessageFactory.buildMessageDetails(message, res);
                message.setSendTime(LdtUtils.now());
                message.setSendStatus((res != null) ? SendStatus.PUSHED : SendStatus.FAIL);
                message.renew();
            } catch (Exception e) {
                log.error("【push】执行Robot消息推送时发生异常 ------->>> {}", e);
            }
        } 
    }
    
    /**
     * <p> 获取企微主体对象<p/>
     * <AUTHOR> 
     * @date 2025年7月22日 下午9:36:53 
     * @param deps 接收者集
     * @return WecomCorp 企微主体对象
     */
    private WecomCorp fetchCorp(List<RobotMessageDepositoryVO> deps) {
        RobotMessageDepositoryVO dep = deps.get(0);
        AuthCorp corp = corpApi.selectByCorpId(dep.getCorpId());
        return WecomCorpAssembler.INSTANCE.toCorp(corp);
    }

}
