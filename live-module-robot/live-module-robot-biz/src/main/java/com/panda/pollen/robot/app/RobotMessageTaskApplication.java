package com.panda.pollen.robot.app;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.util.MapUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.panda.pollen.common.core.date.SearchDateScopeHelper;
import com.panda.pollen.common.core.date.SearchDateType;
import com.panda.pollen.common.core.domain.model.LoginUser;
import com.panda.pollen.common.utils.DateUtils;
import com.panda.pollen.common.utils.LdtUtils;
import com.panda.pollen.common.utils.SecurityUtils;
import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.common.utils.counter.ThreadLocalCounter;
import com.panda.pollen.robot.assembler.MessageReceiveAssembler;
import com.panda.pollen.robot.consts.RobotConst;
import com.panda.pollen.robot.converter.MessageTaskNodeDOConvert;
import com.panda.pollen.robot.domain.RobotMessageTask;
import com.panda.pollen.robot.domain.RobotMessageTaskNode;
import com.panda.pollen.robot.dto.RobotMessageReceiveDTO;
import com.panda.pollen.robot.dto.RobotMessageTaskDTO;
import com.panda.pollen.robot.dto.RobotMsgDTO;
import com.panda.pollen.robot.entity.RobotMessageTaskNodeDO;
import com.panda.pollen.robot.enums.RobotMsgBusinessType;
import com.panda.pollen.robot.factory.RobotMessageFactory;
import com.panda.pollen.robot.factory.RobotRelayMessageFactory;
import com.panda.pollen.robot.kafka.RobotInstantlyPushProducer;
import com.panda.pollen.robot.param.MessageTaskNodeParam;
import com.panda.pollen.robot.repository.RobotMessageReceiveRepository;
import com.panda.pollen.robot.repository.RobotMessageTaskNodeRepository;
import com.panda.pollen.robot.repository.RobotMessageTaskRepository;
import com.panda.pollen.robot.vo.RobotMessageReceiveVO;
import com.panda.pollen.robot.wrapper.RobotMessageTaskWrapper;
import com.panda.robot.enums.RobotMsgMode;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;

/**   
 * ClassName：com.panda.live.robot.message.app.RobotMessageTaskApplication <br>
 * Description：消息任务应用 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * @date 2025年6月17日 11:08:37 <br>
 * @version v1.0 <br>  
 */
@Slf4j
@Component
public class RobotMessageTaskApplication {

    @Autowired
    private RobotMessageTaskRepository repository;
    @Autowired
    private RobotMessageTaskNodeRepository taskNodeRepository;
    @Autowired
    private RobotMessageReceiveRepository receiveRepository;
    @Autowired
    private RobotMessageTaskWrapper wrapper;
    @Autowired
    private RobotInstantlyPushProducer pushProducer;
    
    /** 节点的接收者 */
    private Map<Long, List<RobotMessageReceiveVO>> nodeReceives = MapUtils.newHashMap();
    
    /**
     * Description：消息任务经过处理Cron及执行时间等数据后进行保存 <br>
     * author：luojl <br>
     * date：2025年6月19日 09:58:38 <br>
     * @param task
     * @return <br>
     */
    public boolean saveMessageTask(RobotMessageTask task, LoginUser user, boolean oper) {
        boolean flag = false;
        if (oper) {
            task.setUserId(user.getUserId());
            task.setCompanyId(SecurityUtils.getCompanyId());
            task.setDeptId(user.getDeptId());
            task.init();
        }
        task.verifyInfo().instantly().analysisCron().analysisTaskNode(); 
        
        wrapper.valifyCorpUserRobot(task);
        List<RobotMsgDTO> msgs = null;
        if (task.getInstantly()) {
            // 分解接收者创建MessageDepository
            wrapper.decomposeRobotMessage(task);
            if (RobotMsgMode.relayMode(task.getMode())) {
            	msgs = RobotRelayMessageFactory.buildRobotMsg(task); 
            } else {
            	msgs = RobotMessageFactory.generateRobotMsg(task.getMessages());
            }
        }
        flag = oper ? task.save() : task.update();
        // 立即执行时
        if (flag && ListUtils.isNotEmpty(msgs)) {
            // 将分解出来的MessageDepository按Content转换成RobotMsgDTO，推送到MQ
            msgs.forEach(m -> {
                log.info(JSON.toJSONString(m, JSONWriter.Feature.WriteClassName));
                pushProducer.send(m.getRobotId(), JSON.toJSONString(m, JSONWriter.Feature.WriteClassName));
            });
        }
        return flag;
    } 

    /**
     * <p> 消息任务的定时任务执行，按消息任务节点时间加载在执行时间范围的任务进行业务处理 </p>
     * <AUTHOR> 
     * @date 2025年7月7日 10:44:20 
     * @param start 任务开始时间，为空时默认值：-10 (分钟)
     * @param end 任务结束时间，为空时默认值：20 (分钟)
     * @return void 
     * @throws
     */
    public void executeScheduledTask(Long start, Long end, RobotMsgBusinessType bt) {
    	Long st = (start == null) ? -10 : start;
    	Long et = (end == null) ? 20 : end;
        MessageTaskNodeParam params = buildQueryCondition(st, et, bt);
        // 加载定时消息任务
        List<RobotMessageTask> tasks = queryScheduledTasks(params);
        if(ListUtils.isEmpty(tasks)){
            log.info("【task】{} 没有任务节点可执行------------->>", params.getParams());
            return;
        }
        // 对消息任务进行分解
        wrapper.decomposeMessage(tasks);
        tasks.forEach(t -> {
        	t.changeStatus();
        	ThreadLocalCounter.add();
            repository.batchSaveOrUpTask(ListUtils.newArrayList(t));
        });

    }
    
    /**
     * <p> 构建查询条件 <p/>
     * <AUTHOR> 
     * @date 2025年7月3日 16:39:21 
     * @return MessageTaskNodeParam 
     * @throws
     */
    private MessageTaskNodeParam buildQueryCondition(Long s, Long e, RobotMsgBusinessType bt) {
    	// 构建业务查询条件
        MessageTaskNodeParam condition = MessageTaskNodeParam.builder().build();
        Map<String, Object> search = MapUtils.newHashMap();
        LocalDateTime executeDt = LdtUtils.now();
        LocalDateTime start = executeDt.plusMinutes(s);
        LocalDateTime end = executeDt.plusMinutes(e);
        search.put(SearchDateScopeHelper.DATE_TYPE, SearchDateType.OTHER.getCode());
        search.put(SearchDateScopeHelper.BEGIN_TIME, LocalDateTimeUtil.format(start, DateUtils.YYYY_MM_DD_HH_MM_SS));
        search.put(SearchDateScopeHelper.END_TIME, LocalDateTimeUtil.format(end, DateUtils.YYYY_MM_DD_HH_MM_SS));
        condition.setParams(search);
        condition.setBusinessType(bt.getCode());
        return condition;
    }
    
    /**
     * <p> 查找在当前时间需要执行的任务节点 <p/>
     * <AUTHOR> 
     * @date 2025年7月3日 16:41:43 
     * @return List<RobotMessageTask> 
     * @throws
     */
    private List<RobotMessageTask> queryScheduledTasks(MessageTaskNodeParam params){
        List<RobotMessageTaskNodeDO> nodes = taskNodeRepository.queryScheduledTasks(params);
        if(ListUtils.isEmpty(nodes)) {
            log.info("【task】 Robot消息任务执行，本次未查到有效任务------------->>>>>>> ");
            return null;
        }
        Map<Long, List<RobotMessageTaskNodeDO>> nodeMap = nodes.stream().collect(Collectors.groupingBy(RobotMessageTaskNodeDO::getTaskId));
        // 根据任务节点加载所对应的任务对象
        List<Long> taskIds = ListUtils.newArrayList(nodeMap.keySet());
        List<RobotMessageTask> tasks = loadTasks(taskIds);
        Map<Long, List<RobotMessageReceiveVO>> receiveMaps = loadTaskReceives(taskIds);
        tasks.forEach(t -> {
            List<RobotMessageTaskNode> nodeDomains = MessageTaskNodeDOConvert.INSTANCE.toDomains(nodeMap.get(t.getId()));
            nodeDomains.forEach(node -> {
                if(t.isSop()) {
                    node.setReceives(MessageReceiveAssembler.INSTANCE.fromVOs(nodeReceives.get(node.getId())));
                } else {
                    node.setReceives(MessageReceiveAssembler.INSTANCE.fromVOs(receiveMaps.get(t.getId())));
                }
            });
            t.setNodes(nodeDomains);
        });
        return tasks;
    }
    
    private List<RobotMessageTask> loadTasks(List<Long> taskIds){
    	// 根据任务节点加载所对应的任务对象
        RobotMessageTaskDTO param = RobotMessageTaskDTO.builder().build().addParams(RobotConst.TASKIDS_KEY, taskIds);
        List<RobotMessageTask> tasks = repository.getByCondition(param);
        return tasks;
    }
    
    private Map<Long, List<RobotMessageReceiveVO>> loadTaskReceives(List<Long> taskIds){
    	RobotMessageReceiveDTO rp = RobotMessageReceiveDTO.builder().build().addParams(RobotConst.TASKIDS_KEY, taskIds);
        List<RobotMessageReceiveVO> receives = receiveRepository.getByCondition(rp);
        // 找出节点数据
        receiveGroupByNode(receives);
        return receives.stream().collect(Collectors.groupingBy(RobotMessageReceiveVO::getTaskId));
    }
    
    private void receiveGroupByNode(List<RobotMessageReceiveVO> receives) {
        List<RobotMessageReceiveVO> datas = ListUtils.newArrayList();
        receives.forEach(d -> {
            if(d.getNodeId() != null) {
                datas.add(d);
            }
        });
        nodeReceives = datas.stream().collect(Collectors.groupingBy(RobotMessageReceiveVO::getNodeId));
    }
    
}
