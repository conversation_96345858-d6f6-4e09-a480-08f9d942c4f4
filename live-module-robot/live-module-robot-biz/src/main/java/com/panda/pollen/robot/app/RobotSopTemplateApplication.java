package com.panda.pollen.robot.app;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.panda.pollen.common.core.domain.model.LoginUser;
import com.panda.pollen.common.utils.SecurityUtils;
import com.panda.pollen.robot.domain.RobotSopTemplate;
import com.panda.pollen.robot.repository.RobotSopTemplateRepository;

/**
 * SOP消息模板
 * ClassName com.panda.pollen.robot.app.RobotSopTemplateApplication 
 * <AUTHOR> 
 * @date 2025年9月6日 上午10:51:51 
 * @version v1.0   
 */
@Component
public class RobotSopTemplateApplication {

    @Autowired
    private RobotSopTemplateRepository repository;
    
    /** 
     * 新增SOP消息模板
     * <AUTHOR> 
     * @date 2025年9月6日 上午10:52:40 
     * @param template SOP模板对象
     * @param user 当前用户
     * @throws   
     */ 
    public boolean saveTemplate(RobotSopTemplate template, LoginUser user) {
        template.setUserId(user.getUserId());
        template.setCompanyId(SecurityUtils.getCompanyId());
        return repository.save(template);
    }
    
    /**
     * 更新SOP消息模板
     * <AUTHOR> 
     * @date 2025年9月6日 下午12:06:54 
     * @param template SOP模板对象
     * @param user 当前用户
     * @return boolean 
     * @throws
     */
    public boolean update(RobotSopTemplate template, LoginUser user) {
        return repository.update(template);
    }
    
}
