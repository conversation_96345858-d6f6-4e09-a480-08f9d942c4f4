package com.panda.pollen.robot.converter;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.robot.domain.RobotMessageSopTask;
import com.panda.pollen.robot.domain.RobotMessageTask;
import com.panda.pollen.robot.domain.factory.RobotMessageTaskFactory;
import com.panda.pollen.robot.entity.RobotMessageTaskDO;
import com.panda.pollen.robot.vo.RobotMessageContentVO;
import com.panda.pollen.robot.vo.RobotMessageReceiveVO;
import com.panda.pollen.robot.vo.RobotMessageTaskDetailVO;
import com.panda.pollen.robot.vo.RobotMessageTaskVO;

/**   
 * ClassName：com.panda.live.robot.message.message.converter.MessageTaskDOConvert <br>
 * Description：消息任务DO转换器 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * @date 2025年6月16日 16:49:42 <br>
 * @version v1.0 <br>  
 */
@Mapper
public interface MessageTaskDOConvert {

    MessageTaskDOConvert INSTANCE = Mappers.getMapper(MessageTaskDOConvert.class);
    
    @Mapping(target = "deleted", ignore = true) 
    RobotMessageTaskDO toDO(RobotMessageTask task);
    
    @Mapping(target = "deleted", ignore = true) 
    @Mapping(target = "ruleTime", ignore = true) 
    @Mapping(target = "ruleType", ignore = true) 
    @Mapping(target = "ruleWeek", ignore = true) 
    @Mapping(target = "sendRule", ignore = true) 
    RobotMessageTaskDO toDO(RobotMessageSopTask task);
    
    RobotMessageTaskVO toVO(RobotMessageTaskDO task);
    
    @Mapping(target = "startTime", ignore = true) 
    @Mapping(target = "endTime", ignore = true) 
    @Mapping(target = "receives", source = "recVO")
    @Mapping(source = "ctnVO" ,target = "contents") 
    RobotMessageTaskDetailVO toDetailVO(RobotMessageTaskVO task, List<RobotMessageContentVO> ctnVO, List<RobotMessageReceiveVO> recVO);
    
    @Mapping(target = "contents", ignore = true) 
    @Mapping(target = "receives", ignore = true) 
    default RobotMessageTask toDomain(RobotMessageTaskDO t) {
        RobotMessageTask domain = RobotMessageTaskFactory.create();
        INSTANCE.update(t, domain);
        return domain;
    }
    
    default List<RobotMessageTask> toDomains(List<RobotMessageTaskDO> datas){
        if(ListUtils.isEmpty(datas)) {
            return ListUtils.newArrayList();
        }
        return datas.stream().map(MessageTaskDOConvert.INSTANCE::toDomain).collect(Collectors.toList());
    }
    
    default List<RobotMessageTaskDO> toDOs(List<RobotMessageTask> datas){
        if(ListUtils.isEmpty(datas)) {
            return ListUtils.newArrayList();
        }
        return datas.stream().map(MessageTaskDOConvert.INSTANCE::toDO).collect(Collectors.toList());
    }
    
    default List<RobotMessageTaskDO> fromSOPs(List<RobotMessageSopTask> datas){
        if(ListUtils.isEmpty(datas)) {
            return ListUtils.newArrayList();
        }
        return datas.stream().map(MessageTaskDOConvert.INSTANCE::toDO).collect(Collectors.toList());
    }
    
    default List<RobotMessageTaskVO> toVOs(List<RobotMessageTaskDO> datas){
        if(ListUtils.isEmpty(datas)) {
            return ListUtils.newArrayList();
        }
        return datas.stream().map(MessageTaskDOConvert.INSTANCE::toVO).collect(Collectors.toList());
    }
    
    @Mapping(target = "contents", ignore = true) 
    @Mapping(target = "receives", ignore = true)
    @Mapping(target = "repository", ignore = true)
    @Mapping(target = "analysisCron", ignore = true)
    @Mapping(target = "instantly", ignore = true)
    @Mapping(target = "messages", ignore = true) 
    @Mapping(target = "nodes", ignore = true) 
    @Mapping(target = "taskTimes", ignore = true) 
    void update(RobotMessageTaskDO source, @MappingTarget RobotMessageTask target);
    
}
