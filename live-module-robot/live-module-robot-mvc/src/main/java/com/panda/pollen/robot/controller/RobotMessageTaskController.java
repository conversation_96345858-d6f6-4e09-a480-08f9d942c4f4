package com.panda.pollen.robot.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.panda.pollen.common.annotation.Log;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResult;
import com.panda.pollen.common.core.domain.AjaxResultV2;
import com.panda.pollen.common.core.page.TableDataInfo;
import com.panda.pollen.common.enums.BusinessType;
import com.panda.pollen.common.utils.poi.ExcelUtil;
import com.panda.pollen.robot.app.RobotMessageTaskApplication;
import com.panda.pollen.robot.assembler.MessageTaskAssembler;
import com.panda.pollen.robot.context.RobotSyncContext;
import com.panda.pollen.robot.domain.RobotMessageTask;
import com.panda.pollen.robot.dto.RobotMessageTaskDTO;
import com.panda.pollen.robot.enums.RobotMsgBusinessType;
import com.panda.pollen.robot.param.MessageTaskParam;
import com.panda.pollen.robot.service.RobotMessageTaskService;
import com.panda.pollen.robot.service.RobotService;
import com.panda.pollen.robot.service.impl.RobotSyncStateWrapper;
import com.panda.pollen.robot.vo.RobotMessageTaskDetailVO;
import com.panda.pollen.robot.vo.RobotMessageTaskVO;

import cn.hutool.core.collection.CollectionUtil;

/**
 * 消息任务接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/robot/task")
public class RobotMessageTaskController extends BaseController {

    @Autowired
    private RobotMessageTaskService robotMessageTaskService;
    @Autowired
    private RobotMessageTaskApplication messageTaskApplication;
    @Autowired
    private RobotService robotService;
    @Autowired
    private RobotSyncStateWrapper robotWrapper;

    /**
     * 查询消息任务列表
     */
    @PostMapping("/list")
    @PreAuthorize("@ss.hasPermi('robot:message:list')")
    public TableDataInfo<RobotMessageTaskVO> list(@RequestBody MessageTaskParam p) {
        startPage();
        p.setBusinessType(RobotMsgBusinessType.TASK.getCode());
        List<RobotMessageTaskVO> list = robotMessageTaskService.queryTaskList(p);
        return getDataTable(list);
    } 
    
    /**
     * <p> 任务消息任务节点定时任务执行接口 <p/>
     * <AUTHOR> 
     * @date 2025年7月3日 15:00:11 
     * @return AjaxResult 
     * @throws
     */
    @GetMapping("/run_scheduled_task")
    public AjaxResult executeRobotSend(Long start, Long end) {
    	messageTaskApplication.executeScheduledTask(start, end, RobotMsgBusinessType.TASK);
    	return success();
    }
    
    /**
     * <p> 检验更新Robot状态<p/>
     * <AUTHOR> 
     * @date 2025年7月18日 下午9:49:33 
     * @return AjaxResult 
     * @throws
     */
    @GetMapping("/check_robot")
    public AjaxResult checkRobot() {
        RobotSyncContext ctx = robotService.synchronizerRobotState(null);
        robotWrapper.handleRobotSyncState(ctx);
        return success();
    }

    /**
     * 导出消息任务列表（依赖权限：robot:message:export）
     */
    @PreAuthorize("@ss.hasPermi('robot:message:export')")
    @Log(title = "消息任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MessageTaskParam task) {
        List<RobotMessageTaskVO> list = robotMessageTaskService.queryTaskList(task);
        ExcelUtil<RobotMessageTaskVO> util = new ExcelUtil<RobotMessageTaskVO>(RobotMessageTaskVO.class);
        util.exportExcel(response, list, "消息任务数据");
    }

    /**
     * 获取消息任务详细信息（依赖权限：robot:message:query）
     */
    @PreAuthorize("@ss.hasPermi('robot:message:query')")
    @GetMapping(value = "/{id}")
    public AjaxResultV2<RobotMessageTaskDetailVO> getInfo(@PathVariable Long id) {
        RobotMessageTaskDetailVO task = robotMessageTaskService.getDetailById(id);
        return AjaxResultV2.success(task);
    }

    /**
     * 新增消息任务（依赖权限：robot:message:add）
     */
    @PostMapping
    @PreAuthorize("@ss.hasPermi('robot:message:add')")
    @Log(title = "消息任务", businessType = BusinessType.INSERT)
    public AjaxResult add(@RequestBody RobotMessageTaskDTO dto) {
        dto.valifyData();
        dto.setBusinessType(RobotMsgBusinessType.TASK);
        RobotMessageTask task = MessageTaskAssembler.INSTANCE.fromDTO(dto);
        return toAjax(messageTaskApplication.saveMessageTask(task, getLoginUser(), Boolean.TRUE));
    }

    /**
     * 修改消息任务（依赖权限：robot:message:edit）
     */
    @PutMapping
    @PreAuthorize("@ss.hasPermi('robot:message:edit')")
    @Log(title = "消息任务", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@RequestBody RobotMessageTaskDTO dto) {
        dto.valifyData();
        RobotMessageTask task = MessageTaskAssembler.INSTANCE.fromDTO(dto);
        return toAjax(messageTaskApplication.saveMessageTask(task, getLoginUser(), Boolean.FALSE));
    }

    /**
     * 删除消息任务（依赖权限：robot:message:remove）
     */
    @PreAuthorize("@ss.hasPermi('robot:message:remove')")
    @Log(title = "消息任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(robotMessageTaskService.batchDeleteTask(CollectionUtil.toList(ids)));
    }
    
}
