package com.panda.pollen.robot.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.panda.pollen.common.utils.Dater;
import com.panda.pollen.robot.app.RobotMessageDepositoryApplication;
import com.panda.pollen.robot.app.RobotMessageTaskApplication;
import com.panda.pollen.robot.context.RobotSyncContext;
import com.panda.pollen.robot.enums.RobotMsgBusinessType;
import com.panda.pollen.robot.service.RobotService;
import com.panda.pollen.robot.service.impl.RobotSyncStateWrapper;
import com.panda.pollen.robot.task.dto.TaskParamDTO;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

/**   
 * ClassName：com.panda.pollen.robot.task.RobotMessageTask <br>
 * Description：消息任务定时任务入口 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * @date 2025年6月23日 16:55:26 <br>
 * @version v1.0 <br>  
 */
@Slf4j
@Component
public class RobotMessageTask {

    @Autowired
    private RobotMessageTaskApplication taskApp;
    @Autowired
    private RobotMessageDepositoryApplication depositoryApp;
    @Autowired
    private RobotService robotService;
    @Autowired
    private RobotSyncStateWrapper robotWrapper;
    
    /**
     * <p> 消息接收者拆分业务定时任务，开始时间最好为负数，表示当前时间往前偏移量；结束时间按当前时间往后偏移分钟数。 <p/> 
     * <AUTHOR> 
     * @date 2025年7月7日 10:38:09 
     * @param param.start 任务开始时间，为空时默认值：-10 (分钟)
     * @param param.end 任务结束时间，为空时默认值：20 (分钟)
     * @return void 
     * @throws
     */
    @XxlJob("robotMessageTask.executeMessageTask")
    public void executeMessageTask() {
    	 String jobParams = XxlJobHelper.getJobParam();
    	 TaskParamDTO param = JSON.parseObject(jobParams, TaskParamDTO.class);
        long start = System.currentTimeMillis();
        log.info("【Robot Split】{}---------->> Start Task ", Dater.ymdhms());
        taskApp.executeScheduledTask(param.getStart(), param.getEnd(), RobotMsgBusinessType.TASK);
        log.info("【Robot Split】---------->>耗时：{} End",  System.currentTimeMillis() - start);
    }
    
    /**
     * SOP消息节点任务 
     * <AUTHOR> 
     * @date 2025年9月20日 下午1:59:35  void 
     * @throws
     */
    @XxlJob("robotMessageTask.executeSopMessageTask")
    public void executeSopMessageTask() {
         String jobParams = XxlJobHelper.getJobParam();
         TaskParamDTO param = JSON.parseObject(jobParams, TaskParamDTO.class);
        long start = System.currentTimeMillis();
        log.info("【Robot Split】{}---------->> Start Task ", Dater.ymdhms());
        taskApp.executeScheduledTask(param.getStart(), param.getEnd(), RobotMsgBusinessType.SOP);
        log.info("【Robot Split】---------->>耗时：{} End",  System.currentTimeMillis() - start);
    }
    
    /**
     * <p> Robot消息推送任务，根据MessageDepository中executeTime时间来获取执行任务数据。<p/>
     * <AUTHOR> 
     * @date 2025年7月7日 10:51:04 
     * @param param.start 时间偏移量值，默认值：5分钟, param.mode 推送模式，100 常规模式、101 转发模式，默认：100。
     * @return void 
     * @throws
     */
    @XxlJob("robotMessageTask.pushRobotMessage")
    public void pushRobotMessage() {
        long start = System.currentTimeMillis();
        String jobParams = XxlJobHelper.getJobParam();
   	 	TaskParamDTO param = JSON.parseObject(jobParams, TaskParamDTO.class);
        log.info("【Robot Push】{}---------->> Start", Dater.ymdhms());
    	depositoryApp.executeRobotSend(param.getStart(), param.getMode());
    	log.info("【Robot Push】---------->>耗时：{} End",  System.currentTimeMillis() - start);
    }
    
    /**
     * <p> 同步更新Robot信息状态<p/>
     * <AUTHOR> 
     * @date 2025年7月17日 下午11:23:06 
     * @return void 
     * @throws
     */
    @XxlJob("robotMessageTask.checkRobotState")
    public void checkRobotState() {
        // todo 多线程一个robot 一个线程
        RobotSyncContext ctx = robotService.synchronizerRobotState(null);
        robotWrapper.handleRobotSyncState(ctx);
    }
    
}
