package com.panda.pollen.sender.notice.sender.impl;

import com.panda.pollen.sender.core.model.SendResult;
import com.panda.pollen.sender.notice.enums.NoticeTemplate;
import com.panda.pollen.sender.notice.sender.IMessageSender;
import com.panda.pollen.sender.notice.utils.ReplaceUtils;
import com.panda.pollen.sender.core.feishu.FeiShuSender;
import com.panda.pollen.sender.notice.model.BaseMessage;
import com.panda.pollen.sender.notice.model.FeiShuMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date : 2024年12月25日 13:51
 */
@Component
public class FeiShuSenderImpl implements IMessageSender {

    @Autowired
    private FeiShuSender feiShuSender;

    @Override
    public Class<? extends BaseMessage> getMessageType() {
        return FeiShuMessage.class;
    }

    @Override
    public SendResult send(BaseMessage message, boolean atAll) {
        NoticeTemplate noticeTemplate = message.getNoticeTemplate();
        FeiShuMessage feiShuMessage = (FeiShuMessage) message;
        String content = ReplaceUtils.replace(noticeTemplate.getTextContent(), feiShuMessage.getTemplateParams());
        message.setLogContent(content);
        return feiShuSender.sendTextMsg(content, feiShuMessage.getAccessToken(), feiShuMessage.getSecret(), atAll);
    }
}
