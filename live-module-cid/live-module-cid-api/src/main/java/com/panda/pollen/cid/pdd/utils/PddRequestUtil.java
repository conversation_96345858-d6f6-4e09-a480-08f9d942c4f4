package com.panda.pollen.cid.pdd.utils;

import com.alibaba.fastjson2.JSONObject;
import com.panda.pollen.cid.dto.PddClientInfoDTO;
import com.panda.pollen.common.core.service.ISysConfigService;
import com.pdd.pop.sdk.http.PopClient;
import com.pdd.pop.sdk.http.PopHttpClient;
import com.pdd.pop.sdk.http.api.pop.request.PddDdkGoodsPidGenerateRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddDdkOrderDetailGetRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddDdkResourceUrlGenRequest;
import com.pdd.pop.sdk.http.api.pop.response.PddDdkGoodsPidGenerateResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddDdkOrderDetailGetResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddDdkResourceUrlGenResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <br>
 * @version v1.0 <br>
 * @ClassName：com.panda.pdd.utils.PddRequestUtil <br>
 * @Description：拼多多请求实现 <br>
 * @Date 2023/3/10 10:07 <br>
 */
@Component
@Slf4j
public class PddRequestUtil {

    @Autowired
    private ISysConfigService sysConfigService;

    Map<String, PddClientInfoDTO> pddClientInfoMap = new HashMap<>();

    @PostConstruct
    private void initDdClientInfoMap() {
        PddClientInfoDTO pddClientInfo = new PddClientInfoDTO();
        pddClientInfo.setMediaId(sysConfigService.selectConfigByKey("ddjb_media_id_43324986"));
        pddClientInfo.setDuoId("43324986");
        //初始化默认多多client
        pddClientInfo.setPopClient(new PopHttpClient(sysConfigService.selectConfigByKey("ddjb_client_id_43324986"),
                sysConfigService.selectConfigByKey("ddjb_client_secret_43324986")));
        pddClientInfoMap.put("43324986", pddClientInfo);
    }

    /**
     * 获取拼多多订单详情
     * https://open.pinduoduo.com/application/document/api?id=pdd.ddk.order.detail.get
     *
     * @param orderSn 订单号
     * @param duoId   duoId
     * @return PddDdkOrderDetailGetResponse 订单详情响应对象
     */
    public PddDdkOrderDetailGetResponse getOrderDetail(String orderSn, String duoId) {
        PddDdkOrderDetailGetResponse response = null;
        try {
            PopClient client = getPddClientInfo(duoId).getPopClient();
            PddDdkOrderDetailGetRequest request = new PddDdkOrderDetailGetRequest();
            request.setOrderSn(orderSn);
            response = client.syncInvoke(request);
        } catch (Exception e) {
            log.error("【拼多多Common】通过订单号获取订单错误,订单号:{},错误:{}", orderSn, e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return response;
    }

    /**
     * https://open.pinduoduo.com/application/document/api?id=pdd.ddk.resource.url.gen
     * pdd转链接口
     *
     * @param goodsId
     * @param pid
     * @return
     */
    public PddDdkResourceUrlGenResponse pddDdkResourceUrlGen(String goodsId, String pid, String duoId, String goodsUrl) {
        PddDdkResourceUrlGenResponse response = null;
        try {
            PopClient client = getPddClientInfo(duoId).getPopClient();
            PddDdkResourceUrlGenRequest request = new PddDdkResourceUrlGenRequest();
            request.setGenerateWeApp(true);
            request.setPid(pid);
            request.setResourceType(4);
            request.setUrl(goodsUrl);
            response = client.syncInvoke(request);
            log.info("【拼多多Common】获取推广链接商品SIGN:{};searchId:{};", goodsId, JSONObject.toJSONString(response));
        } catch (Exception e) {
            log.error("【拼多多Common】获取推广链接商品错误,商品Sign:{},错误:{}", goodsId, e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return ObjectUtils.isNotEmpty(response) ? response : null;
    }

    /**
     * 生成pid
     *
     * @throws Exception
     */
    public PddDdkGoodsPidGenerateResponse producePid(String duoId) {
        PddDdkGoodsPidGenerateResponse response = null;
        try {
            PddDdkGoodsPidGenerateRequest request = new PddDdkGoodsPidGenerateRequest();
            request.setNumber(1L);
            PddClientInfoDTO pddClientInfo = getPddClientInfo(duoId);
            PopClient pddClient = pddClientInfo.getPopClient();
            request.setMediaId(Long.valueOf(pddClientInfo.getMediaId()));
            response = pddClient.syncInvoke(request);
        } catch (Exception e) {
            log.error("【拼多多Common】生成PID错误,错误:{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }

        return response;
    }

    /**
     * @param duoId
     * @Description 获取拼多多client
     * <AUTHOR>
     * @param:
     * @Date: 2025/8/28 14:08
     * @return: com.pdd.pop.sdk.http.PopHttpClient
     */
    private PddClientInfoDTO getPddClientInfo(String duoId) {
        //map里获取
        PddClientInfoDTO pddClientInfo =  pddClientInfoMap.get(duoId);
        //获取不到则重新构建
        if (ObjectUtils.isEmpty(pddClientInfo)) {
            String clientId = sysConfigService.selectConfigByKey("ddjb_client_id_" + duoId);
            String clientSecret = sysConfigService.selectConfigByKey("ddjb_client_secret_" + duoId);
            if (StringUtils.isNotBlank(clientId)) {
                pddClientInfo = new PddClientInfoDTO();
                PopClient popClient = new PopHttpClient(clientId, clientSecret);
                pddClientInfo.setMediaId(sysConfigService.selectConfigByKey("ddjb_media_id_" + duoId));
                pddClientInfo.setDuoId(duoId);
                pddClientInfo.setPopClient(popClient);
                pddClientInfoMap.put(duoId, pddClientInfo);
            }
        }
        if (ObjectUtils.isNotEmpty(pddClientInfo)) {
            return pddClientInfo;
        } else {
            //如果还是空的就用默认多多兜底
            return pddClientInfoMap.get("43324986");
        }
    }

}
