import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isRelogin } from '@/utils/request'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/social-login', '/auth-redirect', '/bind', '/register']

router.beforeEach((to, from, next) => {
  NProgress.start()
  if (getToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) {
        isRelogin.show = true
        // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(() => {
          isRelogin.show = false
          store.dispatch('GenerateRoutes').then(accessRoutes => {
            // 根据roles权限生成可访问的路由表
            // router.addRoutes(accessRoutes) // 动态添加可访问路由表
            accessRoutes.forEach(route => router.addRoute(route))

            // 初始化当前父级路由
            initializeCurrentParentRoute(to.path)

            if (to.path === '/') {
              next({ ...to, replace: true, path: store.getters.indexUrl })
            } else {
              next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
            }
          })
        }).catch(err => {
          store.dispatch('LogOut').then(() => {
            Message.error(err)
            next({ path: '/' })
          })
        })
      } else {
        // 确保当前父级路由已初始化（用于页面刷新的情况）
        if (!store.state.permission.currentParentRoute) {
          initializeCurrentParentRoute(to.path)
        }

        if (to.path === '/') {
          next(store.getters.indexUrl)
        } else {
          next()
        }
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})
router.afterEach((to) => {
  NProgress.done()
  // 在路由完成后确保父级路由状态正确
  if (store.getters.sidebarRouters && store.getters.sidebarRouters.length > 0) {
    initializeCurrentParentRoute(to.path)
  }
})

// 初始化当前父级路由
function initializeCurrentParentRoute(currentPath) {
  const routes = store.getters.sidebarRouters || []

  // 查找包含当前路径的父级路由
  const parentRoute = findParentRouteByPath(routes, currentPath)

  if (parentRoute) {
    store.commit('SET_CURRENT_PARENT_ROUTE', parentRoute)
  }
}

// 递归查找包含当前路径的父级路由
function findParentRouteByPath(routes, currentPath) {
  for (const route of routes) {
    if (route.hidden || !route.meta) {
      continue
    }

    // 如果当前路由有子菜单，检查子菜单中是否包含当前路径
    if (route.children && route.children.length > 0) {
      // 递归检查子菜单
      if (hasMatchingChild(route.children, currentPath, route.path)) {
        return route
      }
    }
  }

  return null
}

// 递归检查子菜单中是否包含匹配的路径
function hasMatchingChild(children, currentPath, parentPath) {
  for (const child of children) {
    if (child.hidden || !child.meta) {
      continue
    }

    // 构建子菜单的完整路径
    const childPath = child.path.startsWith('/')
      ? child.path
      : `${parentPath}/${child.path}`

    // 检查当前路径是否匹配
    if (currentPath === childPath) {
      return true
    }

    // 如果子菜单还有子菜单，递归检查
    if (child.children && child.children.length > 0) {
      if (hasMatchingChild(child.children, currentPath, childPath)) {
        return true
      }
    }
  }

  return false
}
