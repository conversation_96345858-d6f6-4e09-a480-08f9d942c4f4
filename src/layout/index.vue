<template>
  <!-- New iOS-style Layout -->
  <div class="app-wrapper-new">
    <!-- Top Navigation Bar -->
    <top-navbar />

    <!-- Left Sidebar -->
    <left-sidebar />

    <!-- Main Content Area -->
    <div class="main-content-new" :class="{ 'sidebar-hidden': isSidebarHidden, 'sidebar-collapsed': isSidebarCollapsed, 'mobile-device': isMobileDevice }">
      <div v-if="needTagsView" class="tags-view-container">
        <tags-view />
      </div>
      <app-main />
      <right-panel>
        <settings />
      </right-panel>
    </div>
  </div>
</template>

<script>

import RightPanel from '@/components/RightPanel/index.vue'
import { AppMain, Settings, TagsView, TopNavbar, LeftSidebar } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from 'vuex'

export default {
  name: 'AppLayout',
  components: {
    AppMain,
    RightPanel,
    Settings,
    TagsView,
    TopNavbar,
    LeftSidebar
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: state => state.settings.theme,
      sideTheme: state => state.settings.sideTheme,
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader
    }),

    // Check if sidebar should be hidden (single child route)
    isSidebarHidden() {
      // 如果是首页，隐藏边栏
      if (this.$route.path === '/index') {
        return true
      }

      const currentParentRoute = this.$store.state.permission.currentParentRoute
      if (!currentParentRoute || !currentParentRoute.children) {
        return true
      }

      // 获取可显示的子路由（非 hidden 的）
      const childRoutes = currentParentRoute.children.filter(child => !child.hidden && child.meta)

      // 只有当子路由数量大于1时才显示边栏
      return childRoutes.length <= 1
    },

    // Check if sidebar is collapsed
    isSidebarCollapsed() {
      // Check if manually collapsed (not on mobile since mobile uses drawer)
      const manuallyCollapsed = this.$store.state.permission.sidebarCollapsed
      return manuallyCollapsed && this.device !== 'mobile' && !this.isSidebarHidden
    },

    // Check if on mobile device
    isMobileDevice() {
      return this.device === 'mobile'
    },

    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  watch: {
    // 监听路由变化，实时更新当前父级路由
    '$route.path': {
      handler() {
        this.initializeCurrentParentRoute()
      },
      immediate: false
    }
  },
  mounted() {
    this.initializeCurrentParentRoute()
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },

    // Initialize the current parent route based on the current path
    initializeCurrentParentRoute() {
      const currentPath = this.$route.path
      const routes = this.$store.getters.sidebarRouters || []

      // 优先使用 activeMenu 查找父级路由
      const { meta } = this.$route
      const targetPath = meta.activeMenu || currentPath

      // Find the parent route that contains the target path
      const parentRoute = this.findParentRouteByPath(routes, targetPath)

      if (parentRoute) {
        this.$store.commit('SET_CURRENT_PARENT_ROUTE', parentRoute)
      }
    },

    // 递归查找包含当前路径的父级路由
    findParentRouteByPath(routes, targetPath) {
      for (const route of routes) {
        if (route.hidden || !route.meta) {
          continue
        }

        // 直接检查路由路径是否匹配
        if (route.path === targetPath) {
          return route
        }

        // 如果当前路由有子菜单，检查子菜单中是否包含目标路径
        if (route.children && route.children.length > 0) {
          // 递归检查子菜单
          if (this.hasMatchingChild(route.children, targetPath, route.path)) {
            return route
          }
        }
      }

      return null
    },

    // 递归检查子菜单中是否包含匹配的路径
    hasMatchingChild(children, currentPath, parentPath) {
      for (const child of children) {
        // 只跳过没有 meta 的路由，保留 hidden 路由用于父级匹配
        if (!child.meta) {
          continue
        }

        // 构建子菜单的完整路径
        const childPath = child.path.startsWith('/')
          ? child.path
          : `${parentPath}/${child.path}`

        // 检查当前路径是否匹配
        if (currentPath === childPath) {
          return true
        }

        // 如果子菜单还有子菜单，递归检查
        if (child.children && child.children.length > 0) {
          if (this.hasMatchingChild(child.children, currentPath, childPath)) {
            return true
          }
        }
      }

      return false
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "@/assets/styles/mixin.scss";
  @import "@/assets/styles/variables.scss";

  // New iOS-style Layout Styles
  .app-wrapper-new {
    position: relative;
    height: 100vh;
    width: 100%;
    background-color: var(--ios-light-gray-05);
    overflow: hidden;
  }

  .main-content-new {
    margin-left: 160px; // Width of left sidebar
    margin-top: 60px; // Height of top navbar
    height: calc(100vh - 60px);
    overflow: auto;
    transition: margin-left 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;

    .tags-view-container {
      position: sticky;
      top: 0;
      z-index: 10;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
      // border-bottom: 1px solid var(--ios-light-gray-2);
    }

    // When sidebar is hidden (single child route)
    &.sidebar-hidden {
      margin-left: 0;
    }

    // When sidebar is collapsed
    &.sidebar-collapsed {
      margin-left: 64px;
    }

    // When on mobile device (no sidebar, use drawer instead)
    &.mobile-device {
      margin-left: 0;
    }
  }

  // Responsive adjustments for new layout
  @media (max-width: 768px) {
    .main-content-new {
      margin-left: 0; // No sidebar on mobile, use drawer instead
    }
  }

  @media (max-width: 1024px) and (min-width: 769px) {
    .main-content-new {
      margin-left: 200px; // Tablet sidebar width

      &.sidebar-hidden {
        margin-left: 0; // Full width when sidebar is hidden
      }

      &.sidebar-collapsed {
        margin-left: 64px; // Collapsed width on tablet
      }
    }
  }

  // Handle very small screens
  @media (max-width: 480px) {
    .main-content-new {
      margin-left: 64px;
      margin-top: 60px;

      &.sidebar-hidden {
        margin-left: 0; // Full width when sidebar is hidden
      }
    }
  }

  // Original Layout Styles
  .app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;
    background-color: var(--ios-background);

    &.mobile.openSidebar {
      position: fixed;
      top: 0;
    }
  }

  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 99;
    width: calc(100% - #{$base-sidebar-width});
    transition: width 0.28s;
    // backdrop-filter: blur(20px);
    // background: rgba(255, 255, 255, 0.7);
    // box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 64px); // 调整折叠宽度
  }

  .sidebarHide .fixed-header {
    width: 100%;
  }

  .mobile .fixed-header {
    width: 100%;
  }

  // 主容器样式
  .main-container {
    min-height: 100%;
    transition: margin-left 0.28s;
    margin-left: $base-sidebar-width;
    position: relative;
  }

  .sidebarHide {
    .main-container {
      margin-left: 0;
    }
  }

  .hideSidebar {
    .main-container {
      margin-left: 64px;
    }
  }

  .mobile {
    .main-container {
      margin-left: 0;
    }
  }
</style>
