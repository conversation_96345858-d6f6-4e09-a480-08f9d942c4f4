/**
 * Template1 - 经典电商模板配置
 * 统一定义HTML结构、CSS样式和功能特性
 */

export const template1Config = {
  id: 'template1',
  name: '经典电商模板',
  description: '金色标签+倒计时+价格融合设计，专为电商转化优化',
  status: 'active',
  preview: '/template1-preview.png',

  // 统一的HTML结构定义
  structure: {
    tag: 'div',
    class: 'template-page',
    children: [
      {
        tag: 'div',
        class: 'template-hero',
        children: [
          {
            tag: 'img',
            class: 'template-goods-image',
            props: {
              src: '{{templateImage}}',
              alt: '商品图片'
            },
            condition: {
              field: 'templateImage',
              operator: 'exists'
            }
          }
        ]
      },
      {
        tag: 'div',
        class: 'template-content',
        children: [
          {
            tag: 'h1',
            class: 'template-goods-name',
            content: '{{templateGoodsName}}'
          },
          {
            tag: 'div',
            class: 'template-price-section',
            children: [
              {
                tag: 'div',
                class: 'template-countdown',
                condition: {
                  field: 'templatePrice',
                  operator: 'exists'
                },
                children: [
                  {
                    tag: 'div',
                    class: 'countdown-left',
                    children: [
                      {
                        tag: 'div',
                        class: 'promo-text-box',
                        content: '限时<br>特价'
                      },
                      {
                        tag: 'div',
                        class: 'countdown-timer',
                        children: [
                          {
                            tag: 'div',
                            class: 'countdown-item',
                            children: [
                              {
                                tag: 'div',
                                class: 'countdown-number',
                                props: { id: 'hours' },
                                content: '02'
                              },
                              {
                                tag: 'div',
                                class: 'countdown-unit',
                                content: '时'
                              }
                            ]
                          },
                          {
                            tag: 'div',
                            class: 'countdown-item',
                            children: [
                              {
                                tag: 'div',
                                class: 'countdown-number',
                                props: { id: 'minutes' },
                                content: '30'
                              },
                              {
                                tag: 'div',
                                class: 'countdown-unit',
                                content: '分'
                              }
                            ]
                          },
                          {
                            tag: 'div',
                            class: 'countdown-item',
                            children: [
                              {
                                tag: 'div',
                                class: 'countdown-number',
                                props: { id: 'seconds' },
                                content: '00'
                              },
                              {
                                tag: 'div',
                                class: 'countdown-unit',
                                content: '秒'
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  },
                  {
                    tag: 'div',
                    class: 'template-price',
                    children: [
                      {
                        tag: 'span',
                        class: 'currency',
                        content: '￥'
                      },
                      {
                        tag: 'span',
                        class: 'main',
                        content: '{{templatePrice}}'
                      }
                    ]
                  }
                ]
              }
            ]
          }

        ]
      },
      {
        tag: 'div',
        class: 'company-name',
        content: '{{companyName}}'
      },
      {
        tag: 'div',
        class: 'template-footer',
        children: [
          {
            tag: 'button',
            class: 'template-buy-btn',
            props: {
              onclick: 'openApp()'
            },
            content: '{{templateButtonText}}'
          }
        ]
      }
    ]
  },

  // 统一的样式定义
  styles: {
    '.template-page': {
      display: 'flex',
      flexDirection: 'column',
      minHeight: '100vh',
      background: '#fff',
      maxWidth: '600px',
      margin: '0 auto',
      boxShadow: '0 2px 12px rgba(0,0,0,0.08)'
    },

    '.template-hero': {
      background: '#fff',
      textAlign: 'center'
    },

    '.template-goods-image': {
      width: '100%',
      height: 'auto',
      objectFit: 'cover'
    },

    '.template-content': {
      flex: 1,
      padding: '20px 16px 16px',
      background: '#fff'
    },

    '.template-goods-name': {
      fontSize: '18px',
      color: '#222',
      margin: '0 0 16px 0',
      lineHeight: '1.4',
      fontWeight: '600',
      textAlign: 'left',
      display: '-webkit-box',
      webkitLineClamp: '3',
      webkitBoxOrient: 'vertical',
      overflow: 'hidden'
    },

    '.template-countdown': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: '12px',
      padding: '12px 16px',
      background: 'linear-gradient(135deg, #ff4757, #ff3838)',
      borderRadius: '8px',
      color: '#fff'
    },

    '.countdown-left': {
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
      flex: 1
    },

    '.promo-text-box': {
      position: 'relative',
      background: 'linear-gradient(135deg, #ffd700, #ffb300)',
      color: '#333',
      padding: '6px 10px 6px 8px',
      fontSize: '11px',
      fontWeight: '700',
      textAlign: 'center',
      lineHeight: '1.2',
      marginRight: '10px',
      flexShrink: '0',
      transform: 'rotate(-3deg)',
      clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 50%, calc(100% - 6px) 100%, 0 100%)',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(255, 215, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3)',
      animation: 'subtle-float 3s ease-in-out infinite'
    },

    '.countdown-timer': {
      display: 'flex',
      gap: '6px'
    },

    '.countdown-item': {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      background: 'rgba(255, 255, 255, 0.2)',
      borderRadius: '4px',
      padding: '4px 6px',
      minWidth: '32px',
      backdropFilter: 'blur(10px)'
    },

    '.countdown-number': {
      fontSize: '16px',
      fontWeight: '700',
      lineHeight: '1',
      color: '#fff',
      fontFamily: 'Courier New, monospace'
    },

    '.countdown-unit': {
      fontSize: '10px',
      color: '#fff',
      opacity: '0.8'
    },

    '.template-price': {
      fontSize: '24px',
      color: '#fff',
      fontWeight: '700',
      display: 'flex',
      alignItems: 'baseline',
      gap: '4px',
      whiteSpace: 'nowrap',
      textShadow: '0 1px 3px rgba(0,0,0,0.3)'
    },

    '.template-price .currency': {
      fontSize: '16px'
    },

    '.template-price .decimal': {
      fontSize: '16px'
    },

    '.template-footer': {
      position: 'sticky',
      bottom: '0',
      padding: '16px',
      background: '#fff',
      borderTop: '1px solid #f0f0f0',
      boxShadow: '0 -2px 12px rgba(0,0,0,0.06)'
    },

    '.template-buy-btn': {
      width: '100%',
      height: '50px',
      background: 'linear-gradient(135deg, #ff4757 0%, #ff3838 100%)',
      color: '#fff',
      border: 'none',
      borderRadius: '25px',
      fontSize: '16px',
      fontWeight: '600',
      cursor: 'pointer',
      boxShadow: '0 4px 12px rgba(255, 71, 87, 0.3)',
      transition: 'all 0.2s ease',
      animation: 'breathe 2s ease-in-out infinite'
    },
    '.company-name': {
      position: 'sticky',
      bottom: '90px',
      zIndex: '9999',
      textAlign: 'center',
      fontSize: '14px',
      color: '#949494',
      lineHeight: '18px'
    },

    // 响应式设计
    '@media (max-width: 375px)': {
      '.template-countdown': {
        flexDirection: 'column',
        alignItems: 'flex-start',
        gap: '8px',
        padding: '10px 12px'
      },
      '.promo-text-box': {
        fontSize: '10px',
        padding: '4px 8px 4px 6px',
        transform: 'rotate(-2deg)'
      },
      '.template-price': {
        fontSize: '22px',
        alignSelf: 'flex-end'
      }
    },

    // 动画效果
    '@keyframes subtle-float': {
      '0%, 100%': {
        transform: 'rotate(-3deg) translateY(0px)'
      },
      '50%': {
        transform: 'rotate(-3deg) translateY(-1px)'
      }
    },

    '@keyframes breathe': {
      '0%, 100%': {
        boxShadow: '0 4px 12px rgba(255, 71, 87, 0.3)',
        background: 'linear-gradient(135deg, #ff4757 0%, #ff3838 100%)',
        transform: 'scale(1)'
      },
      '50%': {
        boxShadow: '0 6px 20px rgba(255, 71, 87, 0.5)',
        background: 'linear-gradient(135deg, #ff5a6a 0%, #ff4a4a 100%)',
        transform: 'scale(1.05)'
      }
    }
  },

  // 功能特性配置
  features: {
    countdown: {
      duration: 2.5 * 60 * 60 * 1000, // 2.5小时
      elements: ['hours', 'minutes', 'seconds']
    }
  }

}
