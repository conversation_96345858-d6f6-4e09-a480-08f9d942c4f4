/**
 * 模板渲染引擎
 * 支持Vue组件渲染（预览）和HTML字符串渲染（生成）
 */

export class TemplateRenderer {
  /**
   * 渲染Vue组件（用于预览）
   * @param {Object} config - 模板配置
   * @param {Object} data - 数据
   * @returns {Object} Vue组件选项
   */
  static renderVueComponent(config, data) {
    return {
      template: this.generateVueTemplate(config.structure, data),
      style: this.generateVueStyles(config.styles, config.id),
      setup() {
        return this.generateVueSetup(config.features, data)
      }
    }
  }

  /**
   * 渲染HTML字符串（用于生成）
   * @param {Object} config - 模板配置
   * @param {Object} data - 数据
   * @returns {Object} HTML、CSS、JS字符串
   */
  static renderHtmlString(config, data) {
    return {
      html: this.generateHtmlString(config.structure, data),
      css: this.generateCssString(config.styles),
      js: this.generateJsString(config.features, data)
    }
  }

  /**
   * 生成Vue模板字符串
   */
  static generateVueTemplate(structure, data) {
    return this.traverseStructure(structure, (node) => {
      return this.buildVueNode(node, data)
    })
  }

  /**
   * 生成HTML字符串
   */
  static generateHtmlString(structure, data) {
    return this.traverseStructure(structure, (node) => {
      return this.buildHtmlNode(node, data)
    })
  }

  /**
   * 遍历结构树
   */
  static traverseStructure(structure, nodeHandler) {
    if (!structure) return ''

    if (Array.isArray(structure)) {
      return structure.map(item => this.traverseStructure(item, nodeHandler)).join('')
    }

    return nodeHandler(structure)
  }

  /**
   * 构建Vue节点
   */
  static buildVueNode(node, data) {
    const { tag, class: className, props = {}, children, content, condition } = node

    // 条件渲染
    if (condition && !this.evaluateCondition(condition, data)) {
      return ''
    }

    // 处理属性
    const vueProps = this.buildVueProps(props, data)
    const classAttr = className ? ` class="${className}"` : ''
    const propsAttr = vueProps ? ` ${vueProps}` : ''

    // 处理内容
    let innerContent = ''
    if (content) {
      innerContent = this.processTemplate(content, data)
    } else if (children) {
      innerContent = this.traverseStructure(children, (child) => this.buildVueNode(child, data))
    }

    return `<${tag}${classAttr}${propsAttr}>${innerContent}</${tag}>`
  }

  /**
   * 构建HTML节点
   */
  static buildHtmlNode(node, data) {
    const { tag, class: className, props = {}, children, content, condition } = node

    // 条件渲染
    if (condition && !this.evaluateCondition(condition, data)) {
      return ''
    }

    // 处理属性
    const htmlProps = this.buildHtmlProps(props, data)
    const classAttr = className ? ` class="${className}"` : ''
    const propsAttr = htmlProps ? ` ${htmlProps}` : ''

    // 处理内容
    let innerContent = ''
    if (content) {
      innerContent = this.processTemplate(content, data)
    } else if (children) {
      innerContent = this.traverseStructure(children, (child) => this.buildHtmlNode(child, data))
    }

    return `<${tag}${classAttr}${propsAttr}>${innerContent}</${tag}>`
  }

  /**
   * 构建Vue属性
   */
  static buildVueProps(props, data) {
    const attributes = []

    for (const [key, value] of Object.entries(props)) {
      if (key === 'src' && value.includes('{{')) {
        // 动态绑定
        const processedValue = this.processTemplate(value, data)
        attributes.push(`:${key}="'${processedValue}'"`)
      } else if (typeof value === 'string' && value.includes('{{')) {
        // 模板字符串
        const processedValue = this.processTemplate(value, data)
        attributes.push(`${key}="${processedValue}"`)
      } else {
        // 静态属性
        attributes.push(`${key}="${value}"`)
      }
    }

    return attributes.join(' ')
  }

  /**
   * 构建HTML属性
   */
  static buildHtmlProps(props, data) {
    const attributes = []

    for (const [key, value] of Object.entries(props)) {
      if (typeof value === 'string' && value.includes('{{')) {
        // 处理模板字符串
        const processedValue = this.processTemplate(value, data)
        attributes.push(`${key}="${processedValue}"`)
      } else {
        // 静态属性
        attributes.push(`${key}="${value}"`)
      }
    }

    return attributes.join(' ')
  }

  /**
   * 处理模板字符串
   */
  static processTemplate(template, data) {
    if (!template || typeof template !== 'string') return template || ''

    return template.replace(/\{\{(\w+(?:\.\w+)*)\}\}/g, (match, key) => {
      return this.getDataValue(data, key) || ''
    })
  }

  /**
   * 获取数据值
   */
  static getDataValue(data, field) {
    if (!field) return null

    // 支持嵌套字段，如 'user.name' 或 'countdownTime.hours'
    const keys = field.split('.')
    let value = data

    for (const key of keys) {
      value = value && value[key]
      if (value === undefined) break
    }

    return value
  }

  /**
   * 评估条件
   */
  static evaluateCondition(condition, data) {
    // 简单的条件评估，可以扩展
    if (typeof condition === 'string') {
      return !!data[condition]
    }
    if (typeof condition === 'object') {
      const { field, operator, value } = condition
      const fieldValue = data[field]

      switch (operator) {
        case '===':
          return fieldValue === value
        case '!==':
          return fieldValue !== value
        case 'exists':
          return fieldValue !== undefined && fieldValue !== null && fieldValue !== ''
        default:
          return !!fieldValue
      }
    }
    return true
  }

  /**
   * 生成Vue样式
   */
  static generateVueStyles(styles, templateId) {
    if (!styles || typeof styles !== 'object') return ''

    let css = ''
    for (const [selector, rules] of Object.entries(styles)) {
      // 处理嵌套选择器（如媒体查询、伪类等）
      if (selector.startsWith('@') || typeof rules === 'object' && Object.values(rules).some(v => typeof v === 'object')) {
        // 处理@media、@keyframes等特殊规则
        if (selector.startsWith('@')) {
          css += `${selector} {\n`
          for (const [nestedSelector, nestedRules] of Object.entries(rules)) {
            if (typeof nestedRules === 'object') {
              css += `  ${nestedSelector} {\n`
              for (const [prop, value] of Object.entries(nestedRules)) {
                const cssProp = this.camelToKebab(prop)
                css += `    ${cssProp}: ${value};\n`
              }
              css += '  }\n'
            }
          }
          css += '}\n\n'
        } else {
          // 处理普通嵌套选择器
          css += `${selector} {\n`
          for (const [prop, value] of Object.entries(rules)) {
            if (typeof value === 'object') {
              // 嵌套子选择器
              for (const [subProp, subValue] of Object.entries(value)) {
                const cssProp = this.camelToKebab(subProp)
                css += `  ${cssProp}: ${subValue};\n`
              }
            } else {
              const cssProp = this.camelToKebab(prop)
              css += `  ${cssProp}: ${value};\n`
            }
          }
          css += '}\n\n'
        }
      } else {
        // 处理普通CSS规则
        css += `${selector} {\n`
        for (const [prop, value] of Object.entries(rules)) {
          const cssProp = this.camelToKebab(prop)
          css += `  ${cssProp}: ${value};\n`
        }
        css += '}\n\n'
      }
    }

    return css
  }

  /**
   * 生成CSS字符串
   */
  static generateCssString(styles) {
    return this.generateVueStyles(styles)
  }

  /**
   * 生成Vue setup函数返回值
   */
  static generateVueSetup(features, data) {
    const setupData = {}

    if (features && features.countdown) {
      // 倒计时功能 - 返回setup函数的字符串表示
      setupData._setupFunction = `
        const { ref, onMounted } = require('vue')

        const countdownTime = ref({
          hours: '02',
          minutes: '30',
          seconds: '00'
        })

        const startCountdown = () => {
          const endTime = new Date().getTime() + ${features.countdown.duration}

          const updateCountdown = () => {
            const now = new Date().getTime()
            const timeLeft = endTime - now

            if (timeLeft <= 0) {
              countdownTime.value = { hours: '00', minutes: '00', seconds: '00' }
              return
            }

            const hours = Math.floor(timeLeft / (1000 * 60 * 60))
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60))
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000)

            countdownTime.value = {
              hours: hours.toString().padStart(2, '0'),
              minutes: minutes.toString().padStart(2, '0'),
              seconds: seconds.toString().padStart(2, '0')
            }
          }

          updateCountdown()
          setInterval(updateCountdown, 1000)
        }

        onMounted(() => {
          startCountdown()
        })

        return { countdownTime }
      `
    }

    return setupData
  }

  /**
   * 生成JavaScript字符串
   */
  static generateJsString(features, data) {
    if (!features) return ''

    let js = ''

    if (features.countdown) {
      js += `
        function initCountdown() {
          const endTime = new Date().getTime() + ${features.countdown.duration};

          function updateCountdown() {
            const now = new Date().getTime();
            const timeLeft = endTime - now;

            if (timeLeft <= 0) {
              document.getElementById('hours').textContent = '00';
              document.getElementById('minutes').textContent = '00';
              document.getElementById('seconds').textContent = '00';
              return;
            }

            const hours = Math.floor(timeLeft / (1000 * 60 * 60));
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

            document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
          }

          updateCountdown();
          setInterval(updateCountdown, 1000);
        }

        document.addEventListener('DOMContentLoaded', function() {
          if (document.getElementById('hours')) {
            initCountdown();
          }
        });
      `
    }

    return js
  }

  /**
   * 驼峰转短横线
   */
  static camelToKebab(str) {
    return str.replace(/[A-Z]/g, letter => `-${letter.toLowerCase()}`)
  }
}
