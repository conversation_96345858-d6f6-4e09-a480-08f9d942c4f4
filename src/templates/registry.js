/**
 * 模板注册中心
 * 统一管理所有模板的配置和元数据
 */

class TemplateRegistry {
  constructor() {
    this.templates = new Map()
  }

  /**
   * 注册模板
   * @param {string} id - 模板ID
   * @param {Object} config - 模板配置
   */
  register(id, config) {
    this.templates.set(id, config)
  }

  /**
   * 获取模板配置
   * @param {string} id - 模板ID
   * @returns {Object|null} 模板配置
   */
  get(id) {
    return this.templates.get(id) || null
  }

  /**
   * 获取所有模板列表
   * @returns {Array} 模板列表
   */
  list() {
    return Array.from(this.templates.entries()).map(([id, config]) => ({
      id,
      name: config.name,
      description: config.description,
      status: config.status,
      preview: config.preview
    }))
  }

  /**
   * 检查模板是否存在
   * @param {string} id - 模板ID
   * @returns {boolean}
   */
  has(id) {
    return this.templates.has(id)
  }
}

// 创建全局单例
export const templateRegistry = new TemplateRegistry()

// 导出类用于测试
export { TemplateRegistry }
