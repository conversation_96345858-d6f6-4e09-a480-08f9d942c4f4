import { ref } from 'vue'
import { Message } from 'element-ui'

export default function({ type, onSave }) {
  // 扣后补回传
  const autoConversionForm = ref([])
  const autoConversionDialogVisible = ref(false)

  const showAutoConversion = (row) => {
    autoConversionForm.value = [row]
    autoConversionDialogVisible.value = true
  }

  const batchUpdateAutoConversion = (selections) => {
    if (!selections || selections.length === 0) {
      Message.warning('请选择要设置项')
      return
    }
    autoConversionForm.value = selections
    autoConversionDialogVisible.value = true
  }

  const handleAutoConversionSave = () => {
    autoConversionDialogVisible.value = false
    onSave && onSave()
  }

  const handleAutoConversionCancel = () => {
    autoConversionDialogVisible.value = false
  }

  return {
    autoConversionType: type,
    autoConversionForm,
    autoConversionDialogVisible,
    showAutoConversion,
    batchUpdateAutoConversion,
    handleAutoConversionSave,
    handleAutoConversionCancel
  }
}
