<script setup>
import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue'
import { getAutoConversionConfig, saveAutoConversionConfig } from '@/api/system/autoconversion'
import { queryImproveConversionList } from '@/api/data/monitor'
import { Message } from 'element-ui'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import SvgIcon from '@/components/SvgIcon/index.vue'
import { typeMap } from '@/components/ConversionSetups/config'
import { useStore } from '@/store'
import useDicts from '@/hooks/useDicts'
import { parseTime } from '@/utils/ruoyi'
import dayjs from 'dayjs'

const self = getCurrentInstance().proxy
const store = useStore()
const userName = computed(() => store.getters.userInfo.userName)

// 获取字典数据
const { dicts, dictMap } = useDicts(['platform_type', 'media_type'])

// 计算属性：平台类型选项
const platformTypes = computed(() => {
  return dicts.value.platform_type?.filter(item => item.value !== '12') || []
})

// 计算属性：媒体类型选项
const mediaTypes = computed(() => {
  return dicts.value.media_type || []
})

const props = defineProps({
  form: {
    type: Array,
    default: () => ([])
  },
  type: {
    type: String,
    default: 'media' // plan, goods, user
  }
})

const emit = defineEmits(['save', 'cancel'])

// 显示信息
const info = computed(() => {
  if (props.form?.length !== 1) return false
  const form = props.form[0]
  switch (props.type) {
    case 'media':
    case 'plan':
      return {
        id: form.advertiserId,
        name: form.advertiserName
      }
    case 'goods':
      return {
        id: form.goodsId,
        name: form.goodsName
      }
    default:
      return false
  }
})

// 回传类型
const conversionType = computed(() => {
  return typeMap[props.type].type
})

// 回传id
const businessIdKey = computed(() => {
  return typeMap[props.type].idKey
})

const autoConversionRef = ref(null)
const autoConversionForm = reactive({
  id: null,
  enableConversion: false, // 是否开启回传限制(0:不开启;1:开启)
  type: 1, // 类型(1计划,2媒体账户,3商品,4用户)
  businessId: '', // 业务id根据type对应不同id
  goodsPlatformType: null, // 商品的电商平台
  mediaPlatformType: null, // 媒体的媒体平台
  conversionScope: 0, // 回传生效范围(默认0:账户,1计划)
  startExecuteHourMinute: '00:00', // 执行回传开始小时和分钟数
  endExecuteHourMinute: '6:00', // 执行回传结束小时和分钟数
  orderHour: 24, // 订单获取小时数据
  intervalMinute: 30, // 回传间隔分钟
  conversionOrderCount: 10, // 每次回传订单数
  conversionOrderState: 0, // 回传订单状态(0:全部,1付款,2退款)
  cycle: 0, // 重复周期(0:不重复,1每天)
  executeDate: null // 重复周期为不重复时的固定执行时间
})

// 日志弹窗相关
const logDialogVisible = ref(false)
const logLoading = ref(false)
const logList = ref([])
const logTotal = ref(0)
const logQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  advertiserId: '',
  planId: '',
  goodsId: '',
  goodsName: ''
})
const logQueryTime = ref([
  dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
  dayjs().format('YYYY-MM-DD HH:mm:ss')
])

// 打开日志弹窗
const openLogDialog = () => {
  logDialogVisible.value = true
  getLogList()
}

// 获取日志列表
const getLogList = () => {
  logLoading.value = true
  const query = Object.assign({}, logQueryParams)
  query.type = 2 // 扣后补回传日志
  switch (props.type) {
    case 'media':
      query.advertiserId = info.value.id
      break
    case 'plan':
      query.planId = info.value.id
      break
    case 'goods':
      query.goodsId = info.value.id
  }
  // 添加时间查询条件
  if (logQueryTime.value && logQueryTime.value.length > 0) {
    query.params = {
      beginTime: Math.floor(new Date(logQueryTime.value[0]).getTime() / 1000),
      endTime: Math.floor(new Date(logQueryTime.value[1]).getTime() / 1000)
    }
  }
  query.deductOrderAutoConversionConfigId = autoConversionForm.id
  query.mediaType = autoConversionForm.mediaPlatformType

  queryImproveConversionList(query).then(response => {
    logList.value = response.rows || []
    logTotal.value = response.total
  }).finally(() => {
    logLoading.value = false
  })
}

// 分页改变
const handleLogPageChange = () => {
  getLogList()
}

// 搜索日志
const handleLogQuery = () => {
  logQueryParams.pageNum = 1
  getLogList()
}

// 重置日志搜索
const resetLogQuery = () => {
  Object.assign(logQueryParams, {
    pageNum: 1,
    pageSize: 10,
    advertiserId: '',
    planId: '',
    goodsId: '',
    goodsName: ''
  })
  logQueryTime.value = []
  getLogList()
}

// 获取回传状态文本
const getConversionStateText = (state) => {
  const stateMap = {
    0: '未回传',
    1: '已回传'
  }
  return stateMap[state] || '未知'
}

// 获取执行状态文本
const getExecuteStatusText = (status) => {
  const statusMap = {
    0: '未执行',
    1: '已执行',
    2: '过期不执行'
  }
  return statusMap[status] || '未知'
}

// 格式化回传结果
const formatConversionResult = (conversionMessage) => {
  try {
    if (!conversionMessage) return ''
    const obj = JSON.parse(conversionMessage)
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return conversionMessage
  }
}

const setDefaultAutoConversion = () => {
  autoConversionForm.type = conversionType.value
  if (props.type === 'user') {
    autoConversionForm.businessId = userName.value
    getAutoConversionConfig({
      type: conversionType.value,
      businessId: userName.value
    }).then(data => {
      if (data.data) {
        setAutoConversionForm(data.data)
      }
    })
    return
  }
  if (props.form.length === 1) {
    const form = props.form[0]
    const businessId = form[businessIdKey.value]
    getAutoConversionConfig({
      businessId,
      type: conversionType.value,
      goodsPlatformType: form.platform,
      mediaPlatformType: form.mediaPlatformType || form.mediaType
    }).then(data => {
      if (data.data) {
        setAutoConversionForm(data.data)
      }
    })
  }
}

function setAutoConversionForm(data) {
  Object.assign(autoConversionForm, data)
  // 如果有执行时间，保持原格式
  if (data.executeDate) {
    autoConversionForm.executeDate = data.executeDate
  }
}

const timeOptions = computed(() => {
  const options = []
  for (let h = 0; h < 24; h++) {
    for (let m = 0; m < 60; m += 30) {
      const hour = h.toString().padStart(2, '0')
      const minute = m.toString().padStart(2, '0')
      options.push({
        value: `${hour}:${minute}`,
        label: `${hour}:${minute}`
      })
    }
  }
  options.push({
    value: '23:59',
    label: '23:59'
  })
  return options
})

// 表单校验规则
const rules = {
  goodsPlatformType: [
    { required: true, message: '请选择商品平台类型', trigger: 'change' }
  ],
  mediaPlatformType: [
    { required: true, message: '请选择媒体平台类型', trigger: 'change' }
  ],
  executeDate: [
    { required: true, message: '请选择执行时间', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (value && new Date(value) <= new Date()) {
          callback(new Error('执行时间不能小于等于当前时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  startExecuteHourMinute: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endExecuteHourMinute: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  intervalMinute: [
    { required: true, message: '请输入回传间隔', trigger: 'blur' },
    { type: 'number', min: 1, max: 1440, message: '回传间隔必须在1-1440分钟之间', trigger: 'blur' }
  ],
  orderHour: [
    { required: true, message: '请输入订单获取时长', trigger: 'blur' },
    { type: 'number', min: 1, max: 168, message: '订单获取时长必须在1-168小时之间', trigger: 'blur' }
  ],
  conversionOrderCount: [
    { required: true, message: '请输入每次回传订单数', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: '每次回传订单数必须在1-1000之间', trigger: 'blur' }
  ]
}

// 自定义时间范围校验
const validateTimeRange = () => {
  if (autoConversionForm.cycle === 1 && autoConversionForm.startExecuteHourMinute && autoConversionForm.endExecuteHourMinute) {
    const startTime = autoConversionForm.startExecuteHourMinute.split(':')
    const endTime = autoConversionForm.endExecuteHourMinute.split(':')
    const startMinutes = parseInt(startTime[0]) * 60 + parseInt(startTime[1])
    const endMinutes = parseInt(endTime[0]) * 60 + parseInt(endTime[1])

    if (startMinutes > endMinutes) {
      Message.error('结束时间必须晚于开始时间')
      return false
    }
  }
  return true
}

const loading = ref(false)
const submit = () => {
  // 表单验证
  autoConversionRef.value.validate((valid) => {
    if (!valid) {
      return false
    }

    // 自定义校验
    if (!validateTimeRange()) {
      return false
    }

    // 当开启自动回传时，检查必要字段
    if (autoConversionForm.enableConversion) {
      // 用户类型需要选择平台类型
      if (props.type === 'user') {
        if (!autoConversionForm.goodsPlatformType) {
          Message.error('请选择商品平台类型')
          return false
        }
        if (!autoConversionForm.mediaPlatformType) {
          Message.error('请选择媒体平台类型')
          return false
        }
      }

      if (autoConversionForm.cycle === 0 && !autoConversionForm.executeDate) {
        Message.error('请选择执行时间')
        return false
      }

      if (autoConversionForm.cycle === 1) {
        if (!autoConversionForm.startExecuteHourMinute || !autoConversionForm.endExecuteHourMinute) {
          Message.error('请选择执行时间范围')
          return false
        }
        if (!autoConversionForm.intervalMinute) {
          Message.error('请输入回传间隔')
          return false
        }
      }
    }

    submitForm()
  })
}

const submitForm = () => {
  let postData = []
  if (props.type === 'user') {
    postData = [{ ...autoConversionForm }]
  } else {
    postData = props.form.map(item => {
      const form = { ...autoConversionForm }
      form.businessId = item[businessIdKey.value]

      if (['plan', 'media'].includes(props.type)) {
        form.mediaPlatformType = item.mediaType
      } else if (props.type === 'goods') {
        form.goodsPlatformType = item.platform
        form.mediaPlatformType = item.mediaPlatformType
      }

      return form
    })
  }

  loading.value = true
  saveAutoConversionConfig(postData).then(data => {
    Message.success('操作成功')
    emit('save')
  }).finally(() => {
    loading.value = false
  })
}

const cancel = () => {
  emit('cancel')
}

const close = () => {
  self.$tab.closePage()
}

// 清除表单验证
const clearValidate = () => {
  autoConversionRef.value && autoConversionRef.value.clearValidate()
}

// 获取订单状态文本
const getOrderStateText = (state) => {
  const stateMap = {
    0: '全部',
    1: '付款',
    2: '退款'
  }
  return stateMap[state] || '全部'
}

onMounted(() => {
  setDefaultAutoConversion()
})
</script>

<template>
  <div>
    <div v-if="info" class="flex">
      <BaseInfoCell :id="info.id" :name="info.name" label="名称:" type="info" class="flex1" style="margin-bottom: 20px" no-copy />
      <el-button v-if="form.length === 1 && autoConversionForm.id" type="primary" size="mini" style="height: 32px;" @click="openLogDialog">回传明细</el-button>
    </div>
    <el-form ref="autoConversionRef" :model="autoConversionForm" :rules="rules" label-width="140px">
      <el-form-item label="开启自动回传">
        <el-switch
          v-model="autoConversionForm.enableConversion"
          active-color="#13ce66"
          @change="clearValidate"
        />
      </el-form-item>

      <template v-if="autoConversionForm.enableConversion">
        <el-form-item v-if="props.type !== 'goods'" label="商品平台类型" prop="goodsPlatformType">
          <el-select v-model="autoConversionForm.goodsPlatformType" placeholder="请选择商品平台类型" clearable>
            <el-option
              v-for="dict in platformTypes"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            >
              <svg-icon :icon-class="dict.label" />
              <span> {{ dict.label }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="props.type === 'user'" label="媒体平台类型" prop="mediaPlatformType">
          <el-select v-model="autoConversionForm.mediaPlatformType" placeholder="请选择媒体平台类型" clearable>
            <el-option
              v-for="dict in mediaTypes"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            >
              <svg-icon :icon-class="dict.label" />
              <span> {{ dict.label }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="type !== 'plan'" label="回传生效范围">
          <el-radio-group v-model="autoConversionForm.conversionScope">
            <el-radio-button :label="0">账户</el-radio-button>
            <el-radio-button :label="1">计划</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="重复周期">
          <el-radio-group v-model="autoConversionForm.cycle" @change="clearValidate">
            <el-radio-button :label="0">不重复</el-radio-button>
            <el-radio-button :label="1">每天</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="autoConversionForm.cycle === 0" label="执行时间" prop="executeDate">
          <el-date-picker
            v-model="autoConversionForm.executeDate"
            type="datetime"
            placeholder="选择执行时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="{
              disabledDate(time) {
                return time.getTime() <= Date.now() - 24 * 60 * 60 * 1000
              },
            }"
          />
        </el-form-item>

        <template v-if="autoConversionForm.cycle === 1">
          <el-form-item label="执行时间范围">
            <div class="time-range">
              <el-form-item prop="startExecuteHourMinute" style="margin-bottom: 0; margin-right: 10px;">
                <el-select v-model="autoConversionForm.startExecuteHourMinute" placeholder="开始时间">
                  <el-option
                    v-for="option in timeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
              <span class="mx10">至</span>
              <el-form-item prop="endExecuteHourMinute" style="margin-bottom: 0;">
                <el-select v-model="autoConversionForm.endExecuteHourMinute" placeholder="结束时间" @change="validateTimeRange">
                  <el-option
                    v-for="option in timeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </el-form-item>

          <el-form-item label="回传间隔" prop="intervalMinute">
            <el-input-number
              v-model="autoConversionForm.intervalMinute"
              :min="5"
              :max="60"
              :controls="false"
            />
            <span class="ml10">分钟</span>
          </el-form-item>
        </template>

        <el-form-item label="订单获取时长" prop="orderHour">
          <el-input-number
            v-model="autoConversionForm.orderHour"
            :min="1"
            :max="48"
            :controls="false"
          />
          <span class="ml10">小时</span>
          <div class="form-tip">
            <template v-if="autoConversionForm.cycle === 1 && autoConversionForm.startExecuteHourMinute && autoConversionForm.orderHour">
              获取 <span class="highlight">{{ autoConversionForm.startExecuteHourMinute }}</span> 以前 <span class="highlight">{{ autoConversionForm.orderHour }}</span> 小时内的订单数据进行回传
            </template>
            <template v-else-if="autoConversionForm.cycle === 0 && autoConversionForm.orderHour">
              获取 <span class="highlight">{{ autoConversionForm.executeDate || '执行时间' }}</span> 以前 <span class="highlight">{{ autoConversionForm.orderHour }}</span> 小时内的订单数据进行回传
            </template>
            <template v-else>
              获取开始时间以前多少小时内的订单数据进行回传
            </template>
          </div>
        </el-form-item>

        <el-form-item label="每次回传订单数" prop="conversionOrderCount">
          <el-input-number
            v-model="autoConversionForm.conversionOrderCount"
            :min="1"
            :max="1000"
            :controls="false"
          />
          <span class="ml10">单</span>
        </el-form-item>

        <el-form-item label="回传订单状态" prop="conversionOrderState">
          <el-radio-group v-model="autoConversionForm.conversionOrderState">
            <el-radio-button :label="0">全部</el-radio-button>
            <el-radio-button :label="1">付款</el-radio-button>
            <el-radio-button :label="2">退款</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </template>

      <!-- 整体配置提示 -->
      <el-form-item v-if="autoConversionForm.enableConversion">

        <div class="summary-description">
          <template v-if="autoConversionForm.cycle === 0 && autoConversionForm.executeDate">
            系统将在 <span class="highlight">{{ autoConversionForm.executeDate }}</span>
            对每个<span class="highlight">{{ autoConversionForm.conversionScope ? '计划' : '账户' }}</span>执行一次回传，
            获取<span class="highlight">{{ autoConversionForm.executeDate || '执行时间' }}</span>
            以前 <span class="highlight">{{ autoConversionForm.orderHour || 0 }}</span> 小时内的
            <span class="highlight">{{ getOrderStateText(autoConversionForm.conversionOrderState) }}</span>订单，
            一次性回传 <span class="highlight">{{ autoConversionForm.conversionOrderCount || 0 }}</span> 单。
          </template>
          <template v-else-if="autoConversionForm.cycle === 1 && autoConversionForm.startExecuteHourMinute && autoConversionForm.endExecuteHourMinute">
            系统将每天在 <span class="highlight">{{ autoConversionForm.startExecuteHourMinute }}</span>
            至 <span class="highlight">{{ autoConversionForm.endExecuteHourMinute }}</span> 时间段内，
            每个 <span class="highlight">{{ autoConversionForm.conversionScope ? '计划' : '账户' }}</span>
            每隔 <span class="highlight">{{ autoConversionForm.intervalMinute || 0 }}</span> 分钟执行一次回传，
            每次获取 <span class="highlight">{{ autoConversionForm.startExecuteHourMinute }}</span>
            以前 <span class="highlight">{{ autoConversionForm.orderHour || 0 }}</span> 小时内的
            <span class="highlight">{{ getOrderStateText(autoConversionForm.conversionOrderState) }}</span>订单，
            每次回传 <span class="highlight">{{ autoConversionForm.conversionOrderCount || 0 }}</span> 单。
          </template>
          <template v-else>
            请完善上述配置信息以查看完整的执行计划。
          </template>

        </div></el-form-item>

      <el-form-item v-if="props.type === 'user'">
        <el-button type="primary" size="mini" :loading="loading" @click="submit">保存</el-button>
        <el-button type="danger" size="mini" @click="close">关闭</el-button>
      </el-form-item>
    </el-form>

    <div v-if="props.type !== 'user'" slot="footer" class="dialog-footer">
      <el-button :loading="loading" type="primary" @click="submit">保存</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>

    <!-- 回传明细弹窗 -->
    <el-dialog
      title="扣后补回传回传明细"
      :visible.sync="logDialogVisible"
      width="80%"
      :append-to-body="true"
    >
      <!-- 搜索表单 -->
      <el-form :model="logQueryParams" class="search-form" size="small" :inline="true" label-width="80px" style="margin-bottom: 20px;">
        <el-form-item prop="conversionState">
          <el-select v-model="logQueryParams.conversionState" placeholder="回传状态" clearable @change="handleLogQuery">
            <el-option label="未回传" :value="0" />
            <el-option label="已回传" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item prop="advertiserId">
          <el-input
            v-model.trim="logQueryParams.advertiserId"
            placeholder="广告主ID"
            clearable
            @keyup.enter.native="handleLogQuery"
          />
        </el-form-item>
        <el-form-item prop="planId">
          <el-input
            v-model.trim="logQueryParams.planId"
            placeholder="计划ID"
            clearable
            @keyup.enter.native="handleLogQuery"
          />
        </el-form-item>
        <el-form-item prop="goodsId">
          <el-input
            v-model.trim="logQueryParams.goodsId"
            placeholder="商品ID"
            clearable
            @keyup.enter.native="handleLogQuery"
          />
        </el-form-item>
        <el-form-item prop="goodsName">
          <el-input
            v-model.trim="logQueryParams.goodsName"
            placeholder="商品名称"
            clearable
            @keyup.enter.native="handleLogQuery"
          />
        </el-form-item>
        <el-form-item label="时间范围" prop="queryTime">
          <el-date-picker
            v-model="logQueryTime"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
            style="width: 350px;"
            @change="handleLogQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleLogQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetLogQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="logLoading"
        :data="logList"
        stripe
        border
        height="400"
      >
        <el-table-column label="平台类型" align="center" width="100" prop="platform">
          <template #default="scope">
            <svg-icon v-if="dictMap.platform_type[scope.row.platform]" :icon-class="dictMap.platform_type[scope.row.platform]" />
            <span> {{ dictMap.platform_type[scope.row.platform] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="媒体平台" align="center" width="100" prop="mediaPlatformType">
          <template #default="scope">
            <svg-icon v-if="dictMap.media_type[scope.row.mediaPlatformType]" :icon-class="dictMap.media_type[scope.row.mediaPlatformType]" />
            <span>{{ dictMap.media_type[scope.row.mediaPlatformType] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="账户信息" align="center" min-width="165" prop="advertiserName">
          <template #default="scope">
            <BaseInfoCell :id="scope.row.advertiserId" :name="scope.row.advertiserName" />
          </template>
        </el-table-column>
        <el-table-column label="计划信息" align="center" min-width="160" prop="planName">
          <template #default="scope">
            <BaseInfoCell :id="scope.row.planId" :name="scope.row.planName" />
          </template>
        </el-table-column>
        <el-table-column label="商品信息" align="center" prop="goods" min-width="160">
          <template #default="scope">
            <BaseInfoCell :id="scope.row.goodsId" :name="scope.row.goodsName" />
          </template>
        </el-table-column>
        <el-table-column label="订单编号" align="center" prop="orderSn" min-width="120" />
        <el-table-column label="执行ID" align="center" width="100" prop="deductOrderAutoConversionExecuteId" />
        <el-table-column label="回传状态" align="center" width="100" prop="conversionState">
          <template #default="scope">
            <el-tag :type="scope.row.conversionState === 1 ? 'success' : 'warning'">
              {{ getConversionStateText(scope.row.conversionState) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="执行状态" align="center" width="100" prop="executeStatus">
          <template #default="scope">
            <el-tag :type="scope.row.executeStatus === 1 ? 'success' : scope.row.executeStatus === 2 ? 'danger' : 'info'">
              {{ getExecuteStatusText(scope.row.executeStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="执行时间" align="center" width="100" prop="executeDate">
          <template #default="scope">
            {{ scope.row.executeDate ? parseTime(scope.row.executeDate) : '' }}
          </template>
        </el-table-column>
      </el-table>

      <div style="margin-top: 20px; text-align: right;">
        <el-pagination
          v-show="logTotal > 0"
          :total="logTotal"
          :page-size="logQueryParams.pageSize"
          :current-page="logQueryParams.pageNum"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="val => {logQueryParams.pageSize = val; handleLogPageChange()}"
          @current-change="val => {logQueryParams.pageNum = val; handleLogPageChange()}"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="logDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<style scoped lang="scss">
.time-range {
  display: flex;
  align-items: center;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: right;
}

.mx10 {
  margin: 0 10px;
}

.ml10 {
  margin-left: 10px;
}

.time-range .el-form-item {
  display: inline-block;
  vertical-align: top;
}

.time-range .el-form-item__error {
  position: static;
  margin-top: 2px;
}

.form-tip .highlight {
  color: #409EFF;
  font-weight: bold;
}

.config-summary {
  margin-top: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.summary-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.summary-content {
  font-size: 13px;
  line-height: 1.6;
}

.summary-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.summary-item .label {
  flex-shrink: 0;
  width: 80px;
  color: #666;
  font-weight: 500;
}

.summary-item .value {
  flex: 1;
  color: #333;
}

.summary-item .highlight,
.summary-description .highlight {
  color: #409EFF;
  font-weight: bold;
  background-color: #ecf5ff;
  padding: 1px 4px;
  border-radius: 3px;
}

.summary-description {
  padding: 12px;
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-left: 4px solid #409EFF;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.8;
  color: #333;
}

.el-dialog .el-form--inline .el-form-item {
  margin-right: 15px;
  margin-bottom: 10px;
}

.el-dialog .el-form--inline .el-form-item__label {
  width: 80px !important;
}
</style>
