<template>
  <el-drawer
    :title="drawerTitle"
    :visible="visible"
    :close-on-press-escape="false"
    direction="rtl"
    size="100%"
    :before-close="handleClose"
    class="create-task-drawer"
    @close="handleClose"
  >
    <div class="drawer-content">
      <el-form ref="taskForm" :model="formData" :rules="formRules" label-width="120px" size="small">
        <!-- 基础设置 -->
        <BasicSettings
          ref="basicSettings"
          :form-data="formData"
          :task-mode="taskMode"
          @corp-change="handleCorpChange"
          @users-change="handleUsersChange"
          @update-form="handleFormUpdate"
        />

        <!-- 群发设置 -->
        <TargetSettings
          ref="targetSettings"
          :form-data="formData"
          @target-change="handleTargetChange"
          @customer-change="handleCustomerChange"
          @group-change="handleGroupChange"
          @update-form="handleFormUpdate"
        />

        <!-- 内容设置 -->
        <ContentSettings
          v-if="visible"
          ref="contentSettings"
          :form-data="formData"
          @content-error="handleContentError"
          @update-form="handleFormUpdate"
        />

        <!-- 发送设置 -->
        <ScheduleSettings
          ref="scheduleSettings"
          :form-data="formData"
          @send-type-change="handleSendTypeChange"
          @time-change="handleTimeChange"
          @update-form="handleFormUpdate"
        />
      </el-form>
    </div>

    <!-- 抽屉底部操作 -->
    <div class="drawer-footer">
      <el-button class="cancel-button" type="text" @click="handleClose">取消</el-button>
      <el-button
        class="submit-button"
        :type="taskMode === 'copy' ? 'success' : 'primary'"
        :loading="submitting"
        @click="handleSubmit"
      >
        {{ submitButtonText }}
      </el-button>
    </div>
  </el-drawer>
</template>

<script>
import { cloneDeep, debounce } from 'lodash'
import BasicSettings from './components/BasicSettings.vue'
import TargetSettings from './components/TargetSettings.vue'
import ContentSettings from './components/ContentSettings.vue'
import ScheduleSettings from './components/ScheduleSettings.vue'
import { addTask, updateTask, getTask } from '@/api/robot/task'
import { convertTaskDataToFormData, convertReceiveData, buildTaskData } from './utils/dataTransform'
import {
  baseRules,
  createTargetTypesValidator,
  createStartDateValidator,
  createEndDateValidator,
  createRuleTimeValidator,
  createRuleWeekValidator,
  validateContents
} from './utils/validation'
import { DEFAULT_FORM_DATA, TASK_MODES, SEND_TYPE_MAP } from './constants.js'

export default {
  name: 'CreateTaskDrawer',
  components: {
    BasicSettings,
    TargetSettings,
    ContentSettings,
    ScheduleSettings
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    corpId: {
      type: String,
      default: ''
    },
    taskMode: {
      type: String,
      default: TASK_MODES.CREATE,
      validator: value => Object.values(TASK_MODES).includes(value)
    },
    taskId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:visible', 'close', 'task-created', 'task-updated'],
  data() {
    return {
      submitting: false,
      formData: cloneDeep(DEFAULT_FORM_DATA)
    }
  },
  computed: {
    // 抽屉标题
    drawerTitle() {
      const titleMap = {
        [TASK_MODES.CREATE]: '新建任务',
        [TASK_MODES.EDIT]: '修改任务',
        [TASK_MODES.COPY]: '复制任务'
      }
      return titleMap[this.taskMode] || '新建任务'
    },

    // 提交按钮文本
    submitButtonText() {
      if (this.taskMode === TASK_MODES.EDIT) {
        return '更新任务'
      }

      const textMap = {
        immediate: '立即发送',
        single: '创建单次任务',
        daily: '创建每日任务',
        weekly: '创建每周任务'
      }
      return textMap[this.formData.sendType] || '确定'
    },

    // 动态表单验证规则
    formRules() {
      return {
        ...baseRules,
        targetTypes: [
          { required: true, message: '请选择群发对象', trigger: 'change' },
          { type: 'array', min: 1, message: '至少选择一种群发对象', trigger: 'change' },
          createTargetTypesValidator(this.formData)
        ],
        startDate: [createStartDateValidator(this.formData)],
        endDate: [createEndDateValidator(this.formData)],
        ruleTime: [createRuleTimeValidator(this.formData)],
        ruleWeek: [createRuleWeekValidator(this.formData)]
      }
    }
  },
  watch: {
    // 监听visible变化，在打开时加载数据
    visible(newVal) {
      if (newVal) {
        if ((this.taskMode === TASK_MODES.EDIT || this.taskMode === TASK_MODES.COPY) && this.taskId) {
          this.loadTaskData()
        } else {
          this.resetFormData()
        }
      }
    },

    // 监听开始日期变化，触发执行时间的重新验证
    'formData.startDate'() {
      if (this.formData.ruleTime) {
        this.$nextTick(() => {
          this.$refs.taskForm?.validateField('ruleTime')
        })
      }
    }
  },
  created() {
    // 创建防抖的验证函数
    this.debouncedValidate = debounce(this.validateField, 300)
  },
  beforeDestroy() {
    // 清理防抖函数
    if (this.debouncedValidate) {
      this.debouncedValidate.cancel()
    }
  },
  methods: {
    // 处理表单更新
    handleFormUpdate(field, value) {
      console.log(`表单更新: ${field} =`, value)
      this.formData[field] = value
    },

    // 处理企业变化
    handleCorpChange(corpId) {
      this.formData.selectedCustomers = []
      this.formData.selectedGroups = []
    },

    // 处理用户变化
    handleUsersChange(users) {
      this.formData.selectedUsers = users
    },

    // 处理目标类型变化
    handleTargetChange(types) {
      this.debouncedValidate('targetTypes')
    },

    // 处理客户变化
    handleCustomerChange(type, data) {
      this.debouncedValidate('targetTypes')
    },

    // 处理群聊变化
    handleGroupChange(type, data) {
      this.debouncedValidate('targetTypes')
    },

    // 处理内容错误
    handleContentError({ type, message }) {
      this.$message.error(message)
    },

    // 处理发送方式变化
    handleSendTypeChange(sendType) {
      // 发送方式变化时，验证相关字段
      this.$nextTick(() => {
        this.validateField('startDate')
        this.validateField('endDate')
        this.validateField('ruleTime')
        this.validateField('ruleWeek')
      })
    },

    // 处理时间变化
    handleTimeChange(type, value) {
      if (type === 'startDate') {
        // 开始日期变化时，清空结束日期
        this.formData.endDate = ''
      }
      // 触发相关字段的验证
      this.$nextTick(() => {
        this.$refs.taskForm?.validateField('endDate')
      })
    },

    // 验证单个字段
    validateField(field) {
      this.$nextTick(() => {
        this.$refs.taskForm?.validateField(field)
      })
    },

    // 加载任务数据（编辑模式）
    async loadTaskData() {
      if (!this.taskId) return

      try {
        const response = await getTask(this.taskId)
        const taskData = response.data

        // 转换任务数据为表单数据
        await this.convertAndSetFormData(taskData)
      } catch (error) {
        console.error('加载任务数据失败：', error)
        this.$message.error('加载任务数据失败，请稍后重试')
        this.handleClose()
      }
    },

    // 转换并设置表单数据
    async convertAndSetFormData(taskData) {
      // 基础数据转换
      const baseFormData = convertTaskDataToFormData(taskData)
      Object.assign(this.formData, baseFormData)

      // 等待下一个tick，确保子组件已渲染
      await this.$nextTick()

      // 设置企业和用户
      if (taskData.corpId && this.$refs.basicSettings) {
        // 解析用户ID
        const userIds = this.parseUserIds(taskData.receives)
        console.log('解析到的用户ID:', userIds)

        await this.$refs.basicSettings.setCorpAndUsers(taskData.corpId, userIds)

        // 等待更长时间确保用户数据加载完成
        await new Promise(resolve => setTimeout(resolve, 300))

        // 处理接收者数据
        if (taskData.receives && taskData.receives.length > 0) {
          const receiveFormData = convertReceiveData(
            taskData.receives[0],
            this.$refs.basicSettings.corpUsers || []
          )
          console.log('接收者数据转换结果:', receiveFormData)
          console.log('企业用户列表:', this.$refs.basicSettings.corpUsers)
          Object.assign(this.formData, receiveFormData)

          // 确保所有数据都已同步
          await this.$nextTick()
          console.log('最终选择的用户:', this.formData.selectedUsers)
        }
      }
    },

    // 解析用户ID
    parseUserIds(receives) {
      if (!receives || receives.length === 0) return []
      const wecomUserId = receives[0].wecomUserId
      return wecomUserId ? wecomUserId.split(',').filter(id => id.trim()) : []
    },

    // 重置表单数据
    resetFormData() {
      this.formData = cloneDeep(DEFAULT_FORM_DATA)
    },

    // 关闭抽屉
    handleClose() {
      this.$refs.taskForm?.resetFields()
      this.resetFormData()
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    // 提交表单
    async handleSubmit() {
      try {
        // 表单验证
        await this.$refs.taskForm.validate()

        // 内容验证
        validateContents(this.formData.contents)

        this.submitting = true

        // 构建任务数据
        const taskData = this.buildTaskData()

        // 如果是编辑模式，添加任务ID
        if (this.taskMode === TASK_MODES.EDIT && this.taskId) {
          taskData.id = this.taskId
        }

        // 如果是复制模式，清空内容ID
        if (this.taskMode === TASK_MODES.COPY) {
          taskData.contents.forEach(c => delete c.id)
        }

        // 调用API
        const response = this.taskMode === TASK_MODES.EDIT
          ? await updateTask(taskData)
          : await addTask(taskData)

        if (response.code === 200) {
          const successMessage = this.taskMode === TASK_MODES.EDIT ? '任务更新成功！' : '任务创建成功！'
          this.$message.success(successMessage)

          this.handleClose()

          const eventName = this.taskMode === TASK_MODES.EDIT ? 'task-updated' : 'task-created'
          this.$emit(eventName, {
            ...taskData,
            id: response.data?.id || this.taskId
          })
        } else {
          const errorMessage = response.msg || (this.taskMode === TASK_MODES.EDIT ? '任务更新失败' : '任务创建失败')
          this.$message.error(errorMessage)
        }
      } catch (error) {
        if (typeof error === 'string' || error.message) {
          // 验证错误或业务错误
          this.$message.error(error.message || error)
        } else {
          // 其他错误
          console.error('操作失败：', error)
          const errorMessage = this.taskMode === TASK_MODES.EDIT ? '任务更新失败，请稍后重试' : '任务创建失败，请稍后重试'
          this.$message.error(errorMessage)
        }
      } finally {
        this.submitting = false
      }
    },

    // 构建任务数据
    buildTaskData() {
      const filterRefs = this.$refs.targetSettings?.getFilterRefs() || {}
      return buildTaskData(
        this.formData,
        filterRefs.customerFilterConditions,
        filterRefs.groupFilterConditions
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.create-task-drawer {
  .drawer-content {
    padding: 0 0 40px 0;
    min-height: calc(100vh - 60px);
  }

  .drawer-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 12px;
    background: linear-gradient(to top, #ffffff 0%, #ffffff 90%, rgba(255, 255, 255, 0.7) 100%);
    border-top: none;
    display: flex;
    justify-content: space-between;
    gap: 16px;
    z-index: 10;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);

    .cancel-button {
      flex: 0 0 auto;
      min-width: 80px;
      color: var(--ios-red);
    }

    .submit-button {
      flex: 1;
      min-height: 48px;
      font-size: 16px;
      font-weight: 600;

      &:hover {
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0) scale(0.98);
      }

      &.is-loading {
        opacity: 0.8;
      }
    }
  }
}
</style>
