// 任务创建相关常量

// 发送方式映射
export const SEND_TYPE_MAP = {
  immediate: { value: 'immediate', label: '立即发送', ruleType: 0 },
  single: { value: 'single', label: '单次发送', ruleType: 1 },
  daily: { value: 'daily', label: '每日发送', ruleType: 2 },
  weekly: { value: 'weekly', label: '每周发送', ruleType: 3 }
}

// 规则类型映射
export const RULE_TYPE_MAP = {
  0: 'immediate',
  1: 'single',
  2: 'daily',
  3: 'weekly'
}

// 选择类型映射
export const SELECT_TYPE_MAP = {
  all: 0,
  condition: 1,
  precise: 2
}

// 反向选择类型映射
export const SELECT_TYPE_REVERSE_MAP = {
  0: 'all',
  1: 'condition',
  2: 'precise'
}

// 发送方式提示文案
export const SEND_TYPE_TIPS = {
  immediate: '企业任务将同步至小组控制台中，处理数据需要一些时间。可能会有短暂的延时',
  single: '任务将在指定的日期和时间执行一次',
  daily: '任务将在指定的时间段内每天执行',
  weekly: '任务将在指定的时间段内按所选星期执行'
}

// 星期映射
export const WEEK_MAP = [
  { label: '周一', value: 0 },
  { label: '周二', value: 1 },
  { label: '周三', value: 2 },
  { label: '周四', value: 3 },
  { label: '周五', value: 4 },
  { label: '周六', value: 5 },
  { label: '周日', value: 6 }
]

// 默认表单数据
export const DEFAULT_FORM_DATA = {
  taskName: '',
  selectedCorp: '',
  selectedUsers: [],
  targetTypes: ['customer'],
  customerSelectType: 'all',
  groupSelectType: 'all',
  customerConditions: [{
    searchType: null,
    compareType: null,
    params: ''
  }],
  groupConditions: [{
    searchType: null,
    compareType: null,
    params: ''
  }],
  selectedCustomers: [],
  selectedGroups: [],
  contents: [],
  mentionAll: false,
  groupAnnouncement: false,
  sendType: 'immediate',
  startDate: '',
  endDate: '',
  ruleWeek: [],
  ruleTime: ''
}

// 任务模式
export const TASK_MODES = {
  CREATE: 'create',
  EDIT: 'edit',
  COPY: 'copy'
}
