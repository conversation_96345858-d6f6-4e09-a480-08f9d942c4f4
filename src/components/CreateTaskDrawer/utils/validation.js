/**
 * 表单验证规则和工具函数
 */

// 基础验证规则
export const baseRules = {
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { max: 100, message: '任务名称不能超过100个字符', trigger: 'blur' }
  ],
  selectedCorp: [
    { required: true, message: '请选择企业', trigger: 'change' }
  ],
  selectedUsers: [
    { required: true, message: '请选择员工', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个员工', trigger: 'change' }
  ],
  sendType: [
    { required: true, message: '请选择发送方式', trigger: 'change' }
  ]
}

/**
 * 目标类型验证器
 */
export function createTargetTypesValidator(formData) {
  return {
    validator: (rule, value, callback) => {
      // 当选择了客户时，必须配置客户选择
      if (value.includes('customer')) {
        if (formData.customerSelectType === 'condition' &&
          (!formData.customerConditions || formData.customerConditions.length === 0 ||
            !formData.customerConditions.some(c => c.searchType))) {
          callback(new Error('请配置客户筛选条件'))
          return
        }
        if (formData.customerSelectType === 'precise' &&
          (!formData.selectedCustomers || formData.selectedCustomers.length === 0)) {
          callback(new Error('请选择具体客户'))
          return
        }
      }

      // 当选择了群聊时，必须配置群聊选择
      if (value.includes('group')) {
        if (formData.groupSelectType === 'condition' &&
          (!formData.groupConditions || formData.groupConditions.length === 0 ||
            !formData.groupConditions.some(c => c.searchType))) {
          callback(new Error('请配置群聊筛选条件'))
          return
        }
        if (formData.groupSelectType === 'precise' &&
          (!formData.selectedGroups || formData.selectedGroups.length === 0)) {
          callback(new Error('请选择具体群聊'))
          return
        }
      }

      callback()
    },
    trigger: 'change'
  }
}

/**
 * 开始日期验证器
 */
export function createStartDateValidator(formData) {
  return {
    required: true,
    validator: (rule, value, callback) => {
      if (['single', 'daily', 'weekly'].includes(formData.sendType)) {
        if (!value) {
          callback(new Error('请选择开始日期'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    trigger: 'change'
  }
}

/**
 * 结束日期验证器
 */
export function createEndDateValidator(formData) {
  return {
    required: true,
    validator: (rule, value, callback) => {
      if (['daily', 'weekly'].includes(formData.sendType)) {
        if (!value) {
          callback(new Error('请选择结束日期'))
        } else if (formData.startDate && new Date(value) <= new Date(formData.startDate)) {
          callback(new Error('结束日期必须大于开始日期'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    trigger: 'change'
  }
}

/**
 * 执行时间验证器
 */
export function createRuleTimeValidator(formData) {
  return {
    required: true,
    validator: (rule, value, callback) => {
      if (['single', 'daily', 'weekly'].includes(formData.sendType)) {
        if (!value) {
          callback(new Error('请选择执行时间'))
          return
        }

        // 当开始日期是今天时，执行时间不能早于当前时间
        if (formData.startDate) {
          const today = new Date()
          today.setHours(0, 0, 0, 0)
          const startDate = new Date(formData.startDate)
          startDate.setHours(0, 0, 0, 0)

          if (startDate.getTime() === today.getTime()) {
            const now = new Date()
            const currentHour = now.getHours()
            const currentMinute = now.getMinutes()

            const [selectedHour, selectedMinute] = value.split(':').map(Number)

            if (selectedHour < currentHour ||
                (selectedHour === currentHour && selectedMinute < currentMinute)) {
              callback(new Error('执行时间不能早于当前时间'))
              return
            }
          }
        }

        callback()
      } else {
        callback()
      }
    },
    trigger: 'change'
  }
}

/**
 * 星期验证器
 */
export function createRuleWeekValidator(formData) {
  return {
    validator: (rule, value, callback) => {
      if (formData.sendType === 'weekly') {
        if (!value || value.length === 0) {
          callback(new Error('请选择执行的星期'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    trigger: 'change'
  }
}

/**
 * 验证消息内容
 */
export function validateContents(contents) {
  if (!contents || contents.length === 0) {
    throw new Error('请至少添加一条消息内容')
  }
  return true
}