import { SELECT_TYPE_MAP, SELECT_TYPE_REVERSE_MAP, SEND_TYPE_MAP, RULE_TYPE_MAP } from '../constants.js'

/**
 * 将任务数据转换为表单数据
 */
export function convertTaskDataToFormData(taskData) {
  const formData = {
    taskName: taskData.taskName || '',
    contents: taskData.contents || [],
    mentionAll: taskData.mentionAll || false,
    groupAnnouncement: taskData.groupAnnouncement || false,
    selectedCorp: taskData.corpId,
    startDate: taskData.startDate || '',
    endDate: taskData.endDate || '',
    ruleTime: taskData.ruleTime || '',
    ruleWeek: taskData.ruleWeek ? (Array.isArray(taskData.ruleWeek) ? taskData.ruleWeek : [taskData.ruleWeek]) : [],
    sendType: RULE_TYPE_MAP[taskData.ruleType] || 'immediate'
  }

  console.log('转换任务数据为表单数据:', formData)
  console.log('原始内容数据:', taskData.contents)

  return formData
}

/**
 * 转换接收者数据
 */
export function convertReceiveData(receiveData, corpUsers = []) {
  const result = {
    selectedUsers: [],
    targetTypes: [],
    customerSelectType: 'all',
    groupSelectType: 'all',
    customerConditions: [{ searchType: null, compareType: null, params: '' }],
    groupConditions: [{ searchType: null, compareType: null, params: '' }],
    selectedCustomers: [],
    selectedGroups: []
  }

  // 解析企微用户ID
  if (receiveData.wecomUserId) {
    const userIds = receiveData.wecomUserId.split(',').filter(id => id.trim())
    result.selectedUsers = corpUsers.filter(user => userIds.includes(user.userId))
  }

  // 处理客户类型
  if (receiveData.customerType !== null && receiveData.customerType !== undefined) {
    result.targetTypes.push('customer')
    result.customerSelectType = SELECT_TYPE_REVERSE_MAP[receiveData.customerType] || 'all'

    if (receiveData.customerCondition) {
      try {
        if (receiveData.customerType === 1) {
          // 按条件选择
          const conditions = JSON.parse(receiveData.customerCondition)
          result.customerConditions = conditions.length > 0 ? conditions : result.customerConditions
        } else if (receiveData.customerType === 2) {
          // 精确选择
          const customers = JSON.parse(receiveData.customerCondition)
          result.selectedCustomers = customers.map(customer => ({
            externalUserId: customer.id,
            customerName: customer.remark || customer.name,
            userId: customer.wuId
          }))
        }
      } catch (error) {
        console.error('解析客户条件失败：', error)
      }
    }
  }

  // 处理群聊类型
  if (receiveData.groupType !== null && receiveData.groupType !== undefined) {
    result.targetTypes.push('group')
    result.groupSelectType = SELECT_TYPE_REVERSE_MAP[receiveData.groupType] || 'all'

    if (receiveData.groupCondition) {
      try {
        const conditions = JSON.parse(receiveData.groupCondition)
        result.groupConditions = conditions.length > 0 ? conditions : result.groupConditions
      } catch (error) {
        console.error('解析群聊条件失败：', error)
      }
    }

    if (receiveData.groups && Array.isArray(receiveData.groups)) {
      result.selectedGroups = receiveData.groups.map(group => ({
        groupId: group.id,
        groupName: group.name,
        userId: group.wuId
      }))
    }
  }

  return result
}

/**
 * 构建任务数据
 */
export function buildTaskData(formData, customerFilterRef, groupFilterRef) {
  const data = {
    taskName: formData.taskName,
    corpId: formData.selectedCorp,
    ruleType: SEND_TYPE_MAP[formData.sendType]?.ruleType || 0,
    receives: [buildReceiveData(formData, customerFilterRef, groupFilterRef)],
    contents: formData.contents,
    mentionAll: formData.mentionAll,
    groupAnnouncement: formData.groupAnnouncement
  }

  // 根据发送方式添加时间字段
  if (formData.sendType === 'single') {
    data.startDate = formData.startDate
    data.ruleTime = formData.ruleTime
  } else if (formData.sendType === 'daily') {
    data.startDate = formData.startDate
    data.endDate = formData.endDate
    data.ruleTime = formData.ruleTime
  } else if (formData.sendType === 'weekly') {
    data.startDate = formData.startDate
    data.endDate = formData.endDate
    data.ruleWeek = formData.ruleWeek
    data.ruleTime = formData.ruleTime
  }

  return data
}

/**
 * 构建接收者数据
 */
export function buildReceiveData(formData, customerFilterRef, groupFilterRef) {
  const receiveData = {
    wecomUserId: formData.selectedUsers.map(user => user.userId).join(','),
    groupType: null,
    customerType: null,
    customers: null,
    groups: null,
    customerCondition: null,
    groupCondition: null
  }

  // 处理客户群发
  if (formData.targetTypes.includes('customer')) {
    receiveData.customerType = SELECT_TYPE_MAP[formData.customerSelectType] || 0

    if (formData.customerSelectType === 'condition') {
      const conditions = customerFilterRef?.getApiConditions?.() || []
      receiveData.customerCondition = JSON.stringify(conditions)
    } else if (formData.customerSelectType === 'precise') {
      receiveData.customerCondition = JSON.stringify(formData.selectedCustomers.map(customer => ({
        id: customer.externalUserId,
        name: customer.customerName,
        remark: customer.customerName,
        wuId: customer.userId
      })))
    }
  }

  // 处理群聊群发
  if (formData.targetTypes.includes('group')) {
    receiveData.groupType = SELECT_TYPE_MAP[formData.groupSelectType] || 0

    if (formData.groupSelectType === 'condition') {
      const conditions = groupFilterRef?.getApiConditions?.() || []
      receiveData.groupCondition = JSON.stringify(conditions)
    } else if (formData.groupSelectType === 'precise') {
      receiveData.groups = formData.selectedGroups.map(group => ({
        id: group.groupId,
        name: group.groupName,
        wuId: group.userId
      }))
    }
  }

  return receiveData
}