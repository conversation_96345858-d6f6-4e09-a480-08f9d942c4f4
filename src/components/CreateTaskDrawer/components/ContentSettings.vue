<template>
  <div class="section-card">
    <div class="section-header">
      <i class="el-icon-edit" />
      <span class="section-title">内容设置</span>
    </div>

    <div class="section-content">
      <!-- 消息内容编辑器 -->
      <el-form-item prop="contents" label-width="0">
        <WeChatMessageEditor
          :model-value="formData.contents"
          :max-count="10"
          @update:modelValue="updateContents"
          @error="handleMessageEditorError"
        />
      </el-form-item>
    </div>
  </div>
</template>

<script>
import WeChatMessageEditor from '@/components/WeChatMessageEditor/index.vue'

export default {
  name: 'ContentSettings',
  components: {
    WeChatMessageEditor
  },
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  emits: ['content-change', 'content-error', 'update-form'],
  methods: {
    // 更新内容
    updateContents(value) {
      console.log('ContentSettings 收到内容更新:', value)
      this.$emit('update-form', 'contents', value)
    },

    // 处理消息编辑器错误
    handleMessageEditorError(type, message) {
      this.$emit('content-error', { type, message })
    },

    // 验证内容
    validateContents() {
      if (!this.formData.contents || this.formData.contents.length === 0) {
        throw new Error('请至少添加一条消息内容')
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.section-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  border: 0.5px solid rgba(0, 0, 0, 0.05);

  .section-header {
    background: #ffffff;
    padding: 12px 16px;
    border-bottom: none;
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      font-size: 16px;
      color: var(--ios-blue);
      background: rgba(0, 122, 255, 0.1);
      width: 28px;
      height: 28px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--ios-dark-gray);
      letter-spacing: -0.01em;
    }
  }

  .section-content {
    padding: 0 16px 16px 16px;
    background: #ffffff;
  }
}
</style>
