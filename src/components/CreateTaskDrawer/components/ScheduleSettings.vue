<template>
  <div class="section-card">
    <div class="section-header">
      <i class="el-icon-timer" />
      <span class="section-title">发送设置</span>
    </div>

    <div class="section-content">
      <!-- 发送方式 -->
      <el-form-item label="发送方式" prop="sendType">
        <el-radio-group :value="formData.sendType" class="send-type-buttons" @input="updateSendType">
          <el-radio-button label="immediate">立即发送</el-radio-button>
          <el-radio-button label="single">单次发送</el-radio-button>
          <el-radio-button label="daily">每日发送</el-radio-button>
          <el-radio-button label="weekly">每周发送</el-radio-button>
        </el-radio-group>

        <!-- 发送方式说明 -->
        <div class="send-type-tips">
          <div class="send-option-tip active">
            {{ sendTypeTips[formData.sendType] }}
          </div>
        </div>
      </el-form-item>

      <!-- 开始日期 -->
      <el-form-item
        v-if="['single', 'daily', 'weekly'].includes(formData.sendType)"
        label="开始日期"
        prop="startDate"
      >
        <el-date-picker
          :value="formData.startDate"
          type="date"
          placeholder="选择开始日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          style="width: 100%"
          :picker-options="startDatePickerOptions"
          @input="updateStartDate"
        />
      </el-form-item>

      <!-- 结束日期 -->
      <el-form-item v-if="['daily', 'weekly'].includes(formData.sendType)" label="结束日期" prop="endDate">
        <el-date-picker
          :value="formData.endDate"
          type="date"
          placeholder="选择结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          style="width: 100%"
          :picker-options="endDatePickerOptions"
          @input="updateEndDate"
        />
      </el-form-item>

      <!-- 执行时间 -->
      <el-form-item v-if="['single', 'daily', 'weekly'].includes(formData.sendType)" label="执行时间" prop="ruleTime">
        <el-time-picker
          :value="formData.ruleTime"
          placeholder="选择执行时间"
          format="HH:mm"
          value-format="HH:mm"
          style="width: 100%"
          @input="updateRuleTime"
        />
      </el-form-item>

      <!-- 每周发送设置 -->
      <el-form-item v-if="formData.sendType === 'weekly'" label="执行星期" prop="ruleWeek">
        <el-checkbox-group :value="formData.ruleWeek" class="week-checkboxes" @input="updateRuleWeek">
          <el-checkbox v-for="week in weekOptions" :key="week.value" :label="week.value">
            {{ week.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </div>
  </div>
</template>

<script>
import { SEND_TYPE_TIPS, WEEK_MAP } from '../constants.js'

export default {
  name: 'ScheduleSettings',
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  emits: ['send-type-change', 'time-change', 'update-form'],
  data() {
    return {
      sendTypeTips: SEND_TYPE_TIPS,
      weekOptions: WEEK_MAP
    }
  },
  computed: {
    // 开始日期选择器配置
    startDatePickerOptions() {
      return {
        disabledDate: (time) => {
          const today = new Date()
          today.setHours(0, 0, 0, 0)
          return time.getTime() < today.getTime()
        }
      }
    },

    // 结束日期选择器配置
    endDatePickerOptions() {
      return {
        disabledDate: (time) => {
          const today = new Date()
          today.setHours(0, 0, 0, 0)

          // 不能早于今天
          if (time.getTime() < today.getTime()) {
            return true
          }

          // 如果有开始日期
          if (this.formData.startDate) {
            const startDate = new Date(this.formData.startDate)
            startDate.setHours(0, 0, 0, 0)

            // 不能早于开始日期
            if (time.getTime() < startDate.getTime()) {
              return true
            }

            // 不能超过开始日期后一个月
            const maxDate = new Date(startDate)
            maxDate.setMonth(maxDate.getMonth() + 1)
            if (time.getTime() > maxDate.getTime()) {
              return true
            }
          }

          return false
        }
      }
    }
  },
  methods: {
    // 更新发送方式
    updateSendType(value) {
      this.$emit('update-form', 'sendType', value)
      this.handleSendTypeChange(value)
    },

    // 更新开始日期
    updateStartDate(value) {
      this.$emit('update-form', 'startDate', value)
      this.handleStartDateChange(value)
    },

    // 更新结束日期
    updateEndDate(value) {
      this.$emit('update-form', 'endDate', value)
    },

    // 更新执行时间
    updateRuleTime(value) {
      this.$emit('update-form', 'ruleTime', value)
    },

    // 更新执行星期
    updateRuleWeek(value) {
      this.$emit('update-form', 'ruleWeek', value)
    },

    // 处理发送方式变化
    handleSendTypeChange(value) {
      // 清空所有时间相关字段
      this.$emit('update-form', 'startDate', '')
      this.$emit('update-form', 'endDate', '')
      this.$emit('update-form', 'ruleWeek', [])
      this.$emit('update-form', 'ruleTime', '')

      this.$emit('send-type-change', value)
    },

    // 处理开始日期变化
    handleStartDateChange(value) {
      this.$emit('time-change', 'startDate', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.section-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  border: 0.5px solid rgba(0, 0, 0, 0.05);

  .section-header {
    background: #ffffff;
    padding: 12px 16px;
    border-bottom: none;
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      font-size: 16px;
      color: var(--ios-blue);
      background: rgba(0, 122, 255, 0.1);
      width: 28px;
      height: 28px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--ios-dark-gray);
      letter-spacing: -0.01em;
    }
  }

  .section-content {
    padding: 0 16px 16px 16px;
    background: #ffffff;
  }
}

.send-type-buttons {
  margin-bottom: 16px;
}

.send-type-tips {
  margin-top: 12px;

  .send-option-tip {
    font-size: 13px;
    color: var(--ios-gray);
    line-height: 1.5;
    font-weight: 400;
    padding: 12px 16px;
    background: var(--ios-light-gray);
    border-radius: 8px;
    border-left: 3px solid var(--ios-blue);

    &.active {
      animation: fadeIn 0.3s ease-in-out;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 星期选择样式
.week-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  :deep(.el-checkbox) {
    margin-right: 0;

    .el-checkbox__label {
      font-size: 14px;
      color: var(--ios-dark-gray);
      font-weight: 500;
    }

    &.is-checked {
      .el-checkbox__label {
        color: var(--ios-blue);
      }
    }
  }
}
</style>
