<template>
  <div v-if="formData.selectedUsers.length > 0" class="section-card">
    <div class="section-header">
      <i class="el-icon-message" />
      <span class="section-title">群发设置</span>
      <span class="section-subtitle">配置群发对象和目标客户</span>
    </div>

    <div class="section-content">
      <!-- 群发对象 -->
      <el-form-item label="群发对象" prop="targetTypes">
        <el-checkbox-group :value="formData.targetTypes" @input="handleTargetTypesChange">
          <el-checkbox label="customer">客户</el-checkbox>
          <el-checkbox label="group">群聊</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 客户选择方式 -->
      <el-form-item v-if="formData.targetTypes.includes('customer')" label="客户选择" prop="customerSelectType">
        <div class="select-type-container">
          <el-radio-group :value="formData.customerSelectType" @input="handleCustomerSelectTypeChange">
            <el-radio-button label="all">全部客户</el-radio-button>
            <el-radio-button label="condition">按条件选择</el-radio-button>
            <el-radio-button label="precise">精确选择</el-radio-button>
          </el-radio-group>

          <!-- 按条件选择 -->
          <div v-if="formData.customerSelectType === 'condition'" class="condition-selector">
            <AdvancedFilterConditions
              ref="customerFilterConditions"
              v-model="formData.customerConditions"
              :search-types="customerSearchTypes"
              :corp-id="formData.selectedCorp"
              @change="handleCustomerConditionsChange"
            />
            <div class="count-query-row">
              <span>预计触达客户数：
                <span v-if="customerCount !== null" class="count-result">{{ customerCount }}</span>
              </span>
              <el-button
                size="mini"
                type="text"
                :loading="countLoading.customer"
                @click="handleQueryCustomerCount"
              >查询</el-button>
            </div>
          </div>

          <!-- 精确选择 -->
          <div v-if="formData.customerSelectType === 'precise'" class="precise-selector">
            <el-button size="mini" type="primary" @click="openCustomerSelector">
              <i class="el-icon-user" />
              选择客户 {{ formData.selectedCustomers.length > 0 ? `(${formData.selectedCustomers.length})` : '' }}
            </el-button>
            <div v-if="formData.selectedCustomers.length > 0" class="selected-info">
              已选择 {{ formData.selectedCustomers.length }} 个客户
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 群聊选择方式 -->
      <el-form-item v-if="formData.targetTypes.includes('group')" label="群聊选择" prop="groupSelectType">
        <div class="select-type-container">
          <el-radio-group :value="formData.groupSelectType" @input="handleGroupSelectTypeChange">
            <el-radio-button label="all">全部群聊</el-radio-button>
            <el-radio-button label="condition">按条件选择</el-radio-button>
            <el-radio-button label="precise">精确选择</el-radio-button>
          </el-radio-group>

          <!-- 按条件选择 -->
          <div v-if="formData.groupSelectType === 'condition'" class="condition-selector">
            <AdvancedFilterConditions
              ref="groupFilterConditions"
              v-model="formData.groupConditions"
              :search-types="groupSearchTypes"
              :corp-id="formData.selectedCorp"
              @change="handleGroupConditionsChange"
            />
            <div class="count-query-row">
              <span>预计触达群聊数：<span v-if="groupCount !== null" class="count-result">{{ groupCount }}</span></span>
              <el-button
                size="mini"
                type="text"
                :loading="countLoading.group"
                @click="handleQueryGroupCount"
              >查询</el-button>
            </div>
          </div>

          <!-- 精确选择 -->
          <div v-if="formData.groupSelectType === 'precise'" class="precise-selector">
            <el-button size="mini" type="primary" @click="openGroupSelector">
              <i class="el-icon-chat-line-square" />
              选择群聊 {{ formData.selectedGroups.length > 0 ? `(${formData.selectedGroups.length})` : '' }}
            </el-button>
            <div v-if="formData.selectedGroups.length > 0" class="selected-info">
              已选择 {{ formData.selectedGroups.length }} 个群聊
            </div>
          </div>
        </div>
      </el-form-item>
    </div>

    <!-- 客户选择器弹窗 -->
    <CustomerSelector
      :visible="customerSelectorVisible"
      :corp-id="formData.selectedCorp"
      :user-ids="getUserIds()"
      :customer-search-types="customerSearchTypes"
      :selected-customers="formData.selectedCustomers"
      @close="closeCustomerSelector"
      @confirm="handleCustomerSelectorConfirm"
    />

    <!-- 群聊选择器弹窗 -->
    <GroupSelector
      :visible="groupSelectorVisible"
      :corp-id="formData.selectedCorp"
      :user-ids="getUserIds()"
      :group-search-types="groupSearchTypes"
      :selected-groups="formData.selectedGroups"
      @close="closeGroupSelector"
      @confirm="handleGroupSelectorConfirm"
    />
  </div>
</template>

<script>
import AdvancedFilterConditions from '@/views/wx/task/components/AdvancedFilterConditions.vue'
import GroupSelector from '@/views/wx/task/components/GroupSelector.vue'
import CustomerSelector from '@/views/wx/task/components/CustomerSelector.vue'
import { findGroupSearchTypes, findGroupCountByCondition } from '@/api/wx/groups'
import { findCustomerSearchTypes, findCustomerCountByCondition } from '@/api/wx/customers'

export default {
  name: 'TargetSettings',
  components: {
    AdvancedFilterConditions,
    GroupSelector,
    CustomerSelector
  },
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  emits: ['target-change', 'customer-change', 'group-change', 'update-form'],
  data() {
    return {
      customerSelectorVisible: false,
      groupSelectorVisible: false,
      groupSearchTypes: [],
      customerSearchTypes: [],
      customerCount: null,
      groupCount: null,
      countLoading: {
        customer: false,
        group: false
      }
    }
  },
  mounted() {
    this.fetchSearchTypes()
  },
  methods: {
    // 获取搜索类型
    async fetchSearchTypes() {
      try {
        const [groupResponse, customerResponse] = await Promise.all([
          findGroupSearchTypes(),
          findCustomerSearchTypes()
        ])

        this.groupSearchTypes = groupResponse.data || []
        this.customerSearchTypes = customerResponse.data || []
      } catch (error) {
        console.error('获取搜索类型失败：', error)
      }
    },

    // 处理群发对象类型变化
    handleTargetTypesChange(value) {
      this.$emit('update-form', 'targetTypes', value)
      
      // 清空未选择类型的配置
      if (!value.includes('customer')) {
        this.$emit('update-form', 'customerSelectType', 'all')
        this.$emit('update-form', 'customerConditions', [{
          searchType: null,
          compareType: null,
          params: ''
        }])
        this.$emit('update-form', 'selectedCustomers', [])
      }

      if (!value.includes('group')) {
        this.$emit('update-form', 'groupSelectType', 'all')
        this.$emit('update-form', 'groupConditions', [{
          searchType: null,
          compareType: null,
          params: ''
        }])
        this.$emit('update-form', 'selectedGroups', [])
      }

      this.$emit('target-change', value)
    },

    // 处理客户选择方式变化
    handleCustomerSelectTypeChange(value) {
      this.$emit('update-form', 'customerSelectType', value)
      this.$emit('update-form', 'customerConditions', [{
        searchType: null,
        compareType: null,
        params: ''
      }])
      this.$emit('update-form', 'selectedCustomers', [])
      this.customerCount = null

      this.$emit('customer-change', 'select-type', value)
    },

    // 处理群聊选择方式变化
    handleGroupSelectTypeChange(value) {
      this.$emit('update-form', 'groupSelectType', value)
      this.$emit('update-form', 'groupConditions', [{
        searchType: null,
        compareType: null,
        params: ''
      }])
      this.$emit('update-form', 'selectedGroups', [])
      this.groupCount = null

      this.$emit('group-change', 'select-type', value)
    },

    // 处理客户条件变化
    handleCustomerConditionsChange(conditions) {
      this.customerCount = null
      this.$emit('customer-change', 'conditions', conditions)
    },

    // 处理群聊条件变化
    handleGroupConditionsChange(conditions) {
      this.groupCount = null
      this.$emit('group-change', 'conditions', conditions)
    },

    // 查询客户数量
    async handleQueryCustomerCount() {
      this.countLoading.customer = true
      this.customerCount = null

      try {
        const filterRef = this.$refs.customerFilterConditions
        const conditions = filterRef ? filterRef.getApiConditions() : []
        const userIdList = this.getUserIds()

        const data = {
          createBy: '',
          userIdList,
          conditionDTOList: conditions
        }

        const res = await findCustomerCountByCondition(data)
        if (res.code === 200) {
          this.customerCount = res.data || 0
        } else {
          this.$message.error(res.msg || '查询客户数失败')
        }
      } catch (e) {
        this.$message.error('查询客户数失败')
      } finally {
        this.countLoading.customer = false
      }
    },

    // 查询群聊数量
    async handleQueryGroupCount() {
      this.countLoading.group = true
      this.groupCount = null

      try {
        const filterRef = this.$refs.groupFilterConditions
        const conditions = filterRef ? filterRef.getApiConditions() : []
        const userIdList = this.getUserIds()

        const data = {
          createBy: '',
          userIdList,
          conditionDTOList: conditions
        }

        const res = await findGroupCountByCondition(data)
        if (res.code === 200) {
          this.groupCount = res.data || 0
        } else {
          this.$message.error(res.msg || '查询群聊数失败')
        }
      } catch (e) {
        this.$message.error('查询群聊数失败')
      } finally {
        this.countLoading.group = false
      }
    },

    // 打开客户选择器
    openCustomerSelector() {
      this.customerSelectorVisible = true
    },

    // 关闭客户选择器
    closeCustomerSelector() {
      this.customerSelectorVisible = false
    },

    // 客户选择确认
    handleCustomerSelectorConfirm(selectedCustomers) {
      this.$emit('update-form', 'selectedCustomers', selectedCustomers)
      this.customerSelectorVisible = false
      this.$emit('customer-change', 'selected', selectedCustomers)
    },

    // 打开群聊选择器
    openGroupSelector() {
      this.groupSelectorVisible = true
    },

    // 关闭群聊选择器
    closeGroupSelector() {
      this.groupSelectorVisible = false
    },

    // 群聊选择确认
    handleGroupSelectorConfirm(selectedGroups) {
      this.$emit('update-form', 'selectedGroups', selectedGroups)
      this.groupSelectorVisible = false
      this.$emit('group-change', 'selected', selectedGroups)
    },

    // 获取所有选择的用户ID
    getUserIds() {
      return this.formData.selectedUsers.map(user => user.userId)
    },

    // 获取筛选条件组件引用
    getFilterRefs() {
      return {
        customerFilterConditions: this.$refs.customerFilterConditions,
        groupFilterConditions: this.$refs.groupFilterConditions
      }
    },

    // 重置计数
    resetCounts() {
      this.customerCount = null
      this.groupCount = null
    }
  }
}
</script>

<style lang="scss" scoped>
.section-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  border: 0.5px solid rgba(0, 0, 0, 0.05);

  .section-header {
    background: #ffffff;
    padding: 12px 16px;
    border-bottom: none;
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      font-size: 16px;
      color: var(--ios-blue);
      background: rgba(0, 122, 255, 0.1);
      width: 28px;
      height: 28px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--ios-dark-gray);
      letter-spacing: -0.01em;
    }

    .section-subtitle {
      font-size: 13px;
      color: var(--ios-gray);
      margin-left: auto;
      font-weight: 400;
    }
  }

  .section-content {
    padding: 0 16px 16px 16px;
    background: #ffffff;
  }
}

.select-type-container {
  .condition-selector {
    margin-top: 16px;
    padding: 16px;
    border: 1px solid var(--ios-border-color);
    border-radius: var(--ios-border-radius);

    :deep(.advanced-filter-conditions) {
      background: transparent;
      box-shadow: none;
      border: none;
    }

    :deep(.empty-conditions) {
      padding: 20px 16px;
      margin: 0;
      background: transparent;
      border: 1px dashed #c7c7cc;
      border-radius: 12px;
    }
  }

  .precise-selector {
    margin-top: 16px;
    padding: 16px;
    border-radius: var(--ios-border-radius);
    border: 1px solid var(--ios-border-color);
  }

  .selected-info {
    margin-top: 12px;
    font-size: 13px;
    color: var(--ios-gray);
    font-weight: 500;
  }
}

.count-query-row {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: var(--ios-gray);

  .count-result {
    font-weight: bold;
    color: var(--ios-blue);
  }
}
</style>
