<template>
  <div class="section-card">
    <div class="section-header">
      <i class="el-icon-setting" />
      <span class="section-title">基础设置</span>
    </div>

    <div class="section-content">
      <!-- 任务名称 -->
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          :value="formData.taskName"
          placeholder="请输入任务名称（100字以内）"
          maxlength="100"
          show-word-limit
          clearable
          @input="updateTaskName"
        />
      </el-form-item>

      <!-- 选择企业 -->
      <el-form-item label="选择企业" prop="selectedCorp">
        <el-select
          :value="formData.selectedCorp"
          placeholder="请选择企业"
          :disabled="taskMode === 'edit'"
          style="width: 100%"
          :loading="corpLoading"
          @input="updateSelectedCorp"
        >
          <el-option
            v-for="corp in corpList"
            :key="corp.corpId"
            :label="corp.corpName"
            :value="corp.corpId"
          />
        </el-select>
      </el-form-item>

      <!-- 选择员工 -->
      <el-form-item label="选择员工" prop="selectedUsers">
        <el-select
          :value="formData.selectedUsers"
          placeholder="请选择企微员工"
          clearable
          multiple
          filterable
          collapse-tags
          style="width: 100%"
          :disabled="!formData.selectedCorp"
          :loading="userLoading"
          value-key="userId"
          @input="updateSelectedUsers"
        >
          <el-option
            v-for="user in corpUsers"
            :key="user.id"
            :label="user.userName"
            :value="user"
            :disabled="!user.robotId || user.robotStatus !== 1"
          >
            <div class="user-option-content">
              <span class="user-name">{{ user.userName }}</span>
              <div class="status-indicators">
                <!-- 机器人状态指示器 -->
                <el-tooltip v-if="!user.robotStatus" content="机器人离线，无法选择" placement="top">
                  <span class="status-offline">
                    <i class="el-icon-error" />离线
                  </span>
                </el-tooltip>
                <!-- 未绑定机器人警告 -->
                <el-tooltip v-if="!user.robotId" content="该员工未绑定机器人ID，无法选择" placement="top">
                  <i class="el-icon-warning warning-icon" />
                </el-tooltip>
              </div>
            </div>
          </el-option>
        </el-select>
        <div v-if="formData.selectedUsers.length > 0" class="selected-users-info">
          已选择 {{ formData.selectedUsers.length }} 个员工
        </div>
      </el-form-item>
    </div>
  </div>
</template>

<script>
import { getAuthCorpList } from '@/api/wx/authCorp'
import { getAllCorpUsers } from '@/api/wx/corpUsers'

export default {
  name: 'BasicSettings',
  props: {
    formData: {
      type: Object,
      required: true
    },
    taskMode: {
      type: String,
      default: 'create'
    }
  },
  emits: ['corp-change', 'users-change', 'update-form'],
  data() {
    return {
      corpList: [],
      corpUsers: [],
      corpLoading: false,
      userLoading: false
    }
  },
  async mounted() {
    await this.fetchCorpList()
  },
  methods: {
    // 更新任务名称
    updateTaskName(value) {
      this.$emit('update-form', 'taskName', value.trim())
    },

    // 获取企业列表
    async fetchCorpList() {
      this.corpLoading = true
      try {
        const response = await getAuthCorpList()
        this.corpList = response.rows || []

        // 如果是创建模式且没有选择企业，默认选择第一个
        if (this.taskMode === 'create' && !this.formData.selectedCorp && this.corpList.length > 0) {
          this.$emit('update-form', 'selectedCorp', this.corpList[0].corpId)
          await this.getCorpUsers(this.corpList[0].corpId)
        }
      } catch (error) {
        console.error('获取企业列表失败：', error)
        this.$message.error('获取企业列表失败，请稍后重试')
      } finally {
        this.corpLoading = false
      }
    },

    // 更新选择的企业
    updateSelectedCorp(corpId) {
      this.$emit('update-form', 'selectedCorp', corpId)
      this.handleCorpChange(corpId)
    },

    // 更新选择的用户
    updateSelectedUsers(users) {
      this.$emit('update-form', 'selectedUsers', users)
      this.handleSelectedUsersChange(users)
    },

    // 处理企业变化
    async handleCorpChange(corpId) {
      // 清空用户选择
      this.$emit('update-form', 'selectedUsers', [])
      this.corpUsers = []

      if (corpId) {
        await this.getCorpUsers(corpId)
      }

      this.$emit('corp-change', corpId)
    },

    // 获取企业员工列表
    async getCorpUsers(corpId) {
      if (!corpId) return

      this.userLoading = true
      try {
        const response = await getAllCorpUsers({
          corpId: corpId,
          pageNum: 1,
          pageSize: 1000
        })

        this.corpUsers = response.data || []
      } catch (error) {
        console.error('获取企业员工失败：', error)
        this.$message.error('获取企业员工失败，请稍后重试')
      } finally {
        this.userLoading = false
      }
    },

    // 处理员工选择变化
    handleSelectedUsersChange(selectedUsers) {
      this.$emit('users-change', selectedUsers)
    },

    // 根据用户ID加载用户信息（用于编辑模式）
    async loadUsersByIds(userIds) {
      if (!userIds || userIds.length === 0) return
      
      console.log('loadUsersByIds 调用，用户ID:', userIds)
      console.log('当前企业用户列表长度:', this.corpUsers.length)

      const matchedUsers = this.corpUsers.filter(user => userIds.includes(user.userId))
      console.log('匹配到的用户:', matchedUsers.map(u => ({ userId: u.userId, userName: u.userName })))
      
      if (matchedUsers.length > 0) {
        console.log('发送用户更新事件')
        this.$emit('update-form', 'selectedUsers', matchedUsers)
        
        // 确保父组件收到事件
        this.$nextTick(() => {
          console.log('formData.selectedUsers 已更新为:', this.formData.selectedUsers)
        })
      } else {
        console.warn('没有找到匹配的用户，可能的原因：')
        console.warn('- 用户ID不匹配')
        console.warn('- 企业用户列表为空')
        console.warn('可用用户:', this.corpUsers.map(u => ({ userId: u.userId, userName: u.userName })))
      }
    },

    // 设置企业和用户（外部调用）
    async setCorpAndUsers(corpId, userIds = []) {
      console.log('setCorpAndUsers 调用，参数:', { corpId, userIds })
      
      if (corpId) {
        // 确保企业ID已设置
        if (corpId !== this.formData.selectedCorp) {
          this.$emit('update-form', 'selectedCorp', corpId)
        }
        
        // 加载企业员工
        await this.getCorpUsers(corpId)
        
        // 等待员工数据加载完成后再匹配用户
        if (userIds.length > 0) {
          // 增加重试逻辑，确保数据加载完成
          let retryCount = 0
          const maxRetries = 5
          
          while (retryCount < maxRetries && this.corpUsers.length === 0) {
            await new Promise(resolve => setTimeout(resolve, 100))
            retryCount++
            console.log(`等待员工数据加载，重试次数: ${retryCount}`)
          }
          
          if (this.corpUsers.length > 0) {
            await this.loadUsersByIds(userIds)
          } else {
            console.error('员工数据加载失败，无法匹配用户')
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.section-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  border: 0.5px solid rgba(0, 0, 0, 0.05);

  .section-header {
    background: #ffffff;
    padding: 12px 16px;
    border-bottom: none;
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      font-size: 16px;
      color: var(--ios-blue);
      background: rgba(0, 122, 255, 0.1);
      width: 28px;
      height: 28px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--ios-dark-gray);
      letter-spacing: -0.01em;
    }
  }

  .section-content {
    padding: 0 16px 16px 16px;
    background: #ffffff;
  }
}

.selected-users-info {
  margin-top: 12px;
  font-size: 13px;
  color: var(--ios-gray);
  font-weight: 500;
}

// 员工选择选项样式
.user-option-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .user-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .status-indicators {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: 8px;
    flex-shrink: 0;

    .status-offline {
      color: #F56C6C;
      font-size: 14px;
    }

    .warning-icon {
      color: #E6A23C;
      font-size: 14px;
    }
  }
}

// 禁用状态的选项样式
:deep(.el-select-dropdown__item.is-disabled) {
  color: #C0C4CC !important;
  cursor: not-allowed;

  .user-option-content {
    .user-name {
      color: #C0C4CC;
    }
  }
}
</style>
