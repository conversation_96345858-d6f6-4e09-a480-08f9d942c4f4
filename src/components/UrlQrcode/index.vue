<script setup>

import { ref, watch } from 'vue'
import QRCode from 'qrcode'

const props = defineProps({
  src: {
    type: String,
    default: ''
  }
})

const qrcode = ref('')
watch(() => props.src, (val) => {
  if (val) {
    QRCode.toDataURL(val)
      .then(url => {
        qrcode.value = url
      })
      .catch(err => {
        console.error(err)
      })
  } else {
    qrcode.value = ''
  }
}, {
  immediate: true
})
</script>

<template>
  <el-image :src="qrcode" v-bind="$attrs">1</el-image>
</template>

<style scoped lang="scss">

</style>
