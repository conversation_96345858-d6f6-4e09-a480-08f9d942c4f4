<script setup>
import { CTreeDrop } from '@wsfe/ctree'

</script>
<script>
export default {
  model: {
    prop: 'modelValue',
    event: 'update:modelValue'
  },
  props: {
    modelValue: [Array, String],
    options: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '店铺'
    },
    isSearch: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  emits: ['select'],
  computed: {
    innerValue: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
        this.$emit('select')
      }
    }
  },
  methods: {
  }
}
</script>
<template>
  <CTreeDrop
    v-model="innerValue"
    style="width: 190px"
    :dropdown-min-width="200"
    :data="options"
    :drop-placeholder="placeholder"
    title-field="label"
    :selectable="!multiple"
    clearable
    default-expand-all
    :checkable="multiple"
  >
    <template #display="scope">
      <div
        v-if="multiple"
        :class="[scope.checkedNodes.length ? 'c-tree-overflow': isSearch? 'c-tree-placeholder' : '']"
      >
        {{ scope.checkedNodes.length? scope.checkedNodes.map((node) => node.label).join('、') : placeholder }}
      </div>
      <div
        v-else
        :class="[scope.selectedNode ? 'c-tree-overflow': isSearch? 'c-tree-placeholder' : '']"
      >
        {{ scope.selectedNode? scope.selectedNode.label : placeholder }}
      </div>
    </template>
  </CTreeDrop>

</template>
