<template>
  <el-dialog
    class="task-message-dialog"
    :title="title"
    :visible.sync="dialogVisible"
    width="900px"
    top="5vh"
    append-to-body
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <div class="dialog-body">
      <WeChatMessageEditor
        v-model="localMessages"
        :max-count="maxCount"
        @error="handleEditorError"
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">保 存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import WeChatMessageEditor from './index.vue'

function cloneMessages(list) {
  return Array.isArray(list) ? list.map(item => ({ ...item })) : []
}

export default {
  name: 'TaskMessageDialog',
  components: {
    WeChatMessageEditor
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default: () => []
    },
    maxCount: {
      type: Number,
      default: 20
    },
    title: {
      type: String,
      default: '编辑发送内容'
    }
  },
  emits: ['update:visible', 'update:value', 'confirm', 'cancel'],
  data() {
    return {
      dialogVisible: false,
      localMessages: []
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.localMessages = cloneMessages(this.value)
        }
      }
    },
    dialogVisible(val) {
      if (!val && this.visible) {
        this.handleCancel()
      }
    },
    value: {
      deep: true,
      handler(val) {
        if (!this.visible) {
          this.localMessages = cloneMessages(val)
        }
      }
    }
  },
  methods: {
    handleEditorError(type, message) {
      if (message) {
        this.$message.error(message)
      }
      this.$emit('error', type, message)
    },
    handleCancel() {
      this.$emit('update:visible', false)
      this.$emit('cancel')
      this.dialogVisible = false
    },
    handleConfirm() {
      const payload = cloneMessages(this.localMessages)
      this.$emit('update:value', payload)
      this.$emit('confirm', payload)
      this.$emit('update:visible', false)
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.task-message-dialog {
  .dialog-body {
    min-height: 540px;
  }

  :deep(.wechat-message-editor) {
    min-height: 500px;
  }
}
</style>
