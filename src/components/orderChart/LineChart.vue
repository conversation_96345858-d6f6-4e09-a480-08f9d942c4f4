<template>
  <div>
    <div ref="chart" :class="className" :style="{height:height,width:width}" />
  </div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts/theme/macarons' // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    chartData: {
      type: Array,
      default: () => [],
      required: true
    },
    chartOption: {
      type: Object,
      default: () => {}
    },
    timeList: {
      type: Array,
      default: () => []
    },
    multi: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        if (val && this.chart) { this.setOptions(val) }
      }
    }
  },
  mounted() {
    setTimeout(() => {
      this.initChart()
    }, 500)
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions(chartData = []) {
      const option = {
        title: {
          text: this.title,
          left: 'center',
          textStyle: {
            fontSize: '16px',
            color: '#333'
          }
        },

        xAxis: {
          data: this.timeList,
          boundaryGap: false,
          axisTick: {
            show: false
          }

        },
        grid: {
          left: 20,
          right: 50,
          bottom: 20,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          axisTick: {
            show: false
          }
        }
      }
      if (this.multi) {
        const colors = ['#3888fa', '#FF005A']
        option.series = chartData.map((item, i) => {
          let data = item.data
          if (data && data.toString().includes('%')) {
            data = data.map(val => +val.replace('%', ''))
          }

          const series = {
            name: item.title,
            smooth: true,
            type: 'line',
            itemStyle: {
              color: colors[i],
              lineStyle: {
                color: colors[i],
                width: 2
              },
              areaStyle: {
                color: '#f3f8ff'
              }
            },
            data,
            animationDuration: 1500,
            animationEasing: 'quadraticOut'
          }
          return series
        })
        const selected = {}
        chartData.forEach(item => {
          if (item.show === false) {
            selected[item.title] = false
          }
        })
        option.legend = {
          left: 'right',
          data: chartData.map(item => item.title),
          selected
        }
      } else {
        option.series = [
          {
            name: this.title,
            smooth: true,
            type: 'line',
            itemStyle: {
              color: '#3888fa',
              lineStyle: {
                color: '#3888fa',
                width: 2
              },
              areaStyle: {
                color: '#f3f8ff'
              }
            },
            data: chartData,
            animationDuration: 1500,
            animationEasing: 'quadraticOut'
          }]
        if (this.title) {
          option.legend = { left: 'right', data: [this.title] }
        }
      }
      this.chart.setOption(Object.assign(option, this.chartOption))
    }
  }
}
</script>

<style>
</style>
