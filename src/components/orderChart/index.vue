<script setup>
import { ref, watch, nextTick } from 'vue'
import LineChart from './LineChart.vue'
import {
  getOrderChart,
  getTbOrder<PERSON>hart,
  getJdOrderChart,
  getMtOrderChart,
  getPdOrderChart,
  getDjkOrderChart,
  getUdsmartOrderChart,
  getTbProOrderChart,
  getJdProOrderChart,
  getTaobaoPrintOrderChart
} from '@/api/promotion/order'

const props = defineProps({
  visible: Boolean,
  type: {
    type: String,
    default: 'pddOrder'
  },
  typeList: {
    type: Object,
    default: () => {}
  },
  charts: {
    type: Array,
    default: () => []
  }
})

const lastQuery = ref(null)
watch(() => props.visible, (val) => {
  if (val && lastQuery.value) {
    getReport(lastQuery.value)
  }
})
// title
const lineTitle = ref('')
const chartLoading = ref(false)
const chartData = ref({})
const chartDataList = ref([])
const requestMap = {
  pddOrder: getOrder<PERSON>hart,
  tbOrder: getTbOrderChart,
  jdOrder: getJdOrderChart,
  mtOrder: getMtOrderChart,
  pdOrder: getPdOrderChart,
  djkOrder: getDjkOrderChart,
  udsmartOrder: getUdsmartOrderChart,
  tbproOrder: getTbProOrderChart,
  jdProOrder: getJdProOrderChart,
  taobaoPrintOrder: getTaobaoPrintOrderChart
}
const getReport = (queryParams) => {
  chartLoading.value = true
  requestMap[props.type](queryParams).then(res => {
    chartData.value = {}
    res.data.forEach(item => {
      Object.keys(item).forEach(key => {
        if (!chartData.value[key]) {
          chartData.value[key] = []
        }
        chartData.value[key].push(item[key])
      })
    })
    setChartData()
  }).finally(() => {
    chartLoading.value = false
    // firstIn = false
  })
}
function setChartData() {
  if (props.typeList && props.typeList.key) {
    chartDataList.value = chartData.value[props.typeList.key]
    lineTitle.value = props.typeList.label
    if (props.typeList.label.includes('率')) {
      lineTitle.value = props.typeList.label + '(%)'
      let arr = []
      if (chartData.value && chartData.value[props.typeList.key] && chartData.value[props.typeList.key].length) {
        arr = JSON.parse(JSON.stringify(chartData.value[props.typeList.key]))
      }
      chartDataList.value = arr.map(item => parseFloat(item.replace('%', '')))
    }
  }
}
defineExpose({
  fetchData(queryParams) {
    lastQuery.value = queryParams
    if (props.visible) {
      getReport(queryParams)
    }
  },
  resizeChart() {
    nextTick(() => {
      setChartData()
    })
  }
})

</script>

<template>
  <div class="chart-wrapper">
    <line-chart
      v-if="visible"
      v-loading="chartLoading"
      :chart-data="chartDataList || []"
      :title="lineTitle"
      :time-list="chartData.orderDate || []"
      height="180px"
    />
  </div>
</template>

<style scoped lang="scss">
    .chart-wrapper {
      background: #fff;
      height: 180px;
      border: 1px solid #e5e5e5;
      padding: 10px;
      margin-bottom: 10px;
    }
</style>
