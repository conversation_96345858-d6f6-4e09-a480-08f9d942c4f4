<script setup>
import { computed, ref, watch } from 'vue'
import { Message } from 'element-ui'
import { queryGoodsProfitConfig, updateGoodsProfitConfig, deleteGoodsProfitConfig } from '@/api/promotion/goods'

const props = defineProps({
  visible: Boolean,
  goods: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['update:visible', 'success'])

const open = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const tableData = ref([])
const loading = ref(false)
const saveLoading = ref(false)

// 初始化加载数据
watch(() => props.visible, (val) => {
  if (val) {
    fetchData()
  }
})

// 查询商品盈亏配置列表
const fetchData = async() => {
  loading.value = true
  try {
    const res = await queryGoodsProfitConfig({
      goodsId: props.goods.goodsId,
      platform: props.goods.platform,
      mediaPlatformType: props.goods.mediaPlatformType
    })
    if (res.code === 200 && res.data) {
      tableData.value = res.data.map(item => ({
        ...item,
        // 将金额从分转换为元，比例从千分比转换为百分比
        goodsExpressCost: item.goodsExpressCost ? item.goodsExpressCost / 100 : undefined,
        platformCommissionRate: item.platformCommissionRate ? item.platformCommissionRate / 10 : undefined,
        otherCost: item.otherCost ? item.otherCost / 100 : undefined,
        skuPrice: item.skuPrice ? item.skuPrice / 100 : undefined
      }))
    } else {
      tableData.value = []
    }
  } catch (error) {
    console.error('获取商品盈亏配置失败', error)
    Message.error('获取商品盈亏配置失败')
  } finally {
    loading.value = false
  }
}

// 添加新行
const addRow = () => {
  // 使用第一个商品的信息作为基础
  const goods = props.goods
  tableData.value.push({
    goodsId: goods.goodsId,
    platform: goods.platform,
    mediaPlatformType: goods.mediaPlatformType,
    skuName: '',
    skuPrice: undefined,
    goodsExpressCost: undefined,
    platformCommissionRate: undefined,
    otherCost: undefined
  })
}

// 删除行
const deleteRow = async(row, index) => {
  if (row.id) {
    // 如果有ID，说明是已存在的记录，需要调用删除API
    try {
      const res = await deleteGoodsProfitConfig([row.id])
      if (res.code === 200) {
        Message.success('删除成功')
        tableData.value.splice(index, 1)
      }
    } catch (error) {
      console.error('删除失败', error)
      Message.error('删除失败')
    }
  } else {
    // 如果没有ID，说明是本地新增的记录，直接从数组中删除
    tableData.value.splice(index, 1)
  }
}

// 保存所有配置
const submit = async() => {
  // 表单验证
  const invalidData = tableData.value.some(item => {
    return !item.skuName || !item.skuPrice
  })

  if (invalidData) {
    Message.warning('SKU名称和SKU单价为必填项')
    return
  }

  saveLoading.value = true
  try {
    const saveItems = tableData.value.map(item => ({
      id: item.id,
      goodsId: item.goodsId,
      platform: item.platform,
      mediaPlatformType: item.mediaPlatformType,
      skuName: item.skuName,
      skuPrice: Math.round(item.skuPrice * 100), // 元转为分
      goodsExpressCost: item.goodsExpressCost ? Math.round(item.goodsExpressCost * 100) : undefined, // 元转为分
      platformCommissionRate: item.platformCommissionRate ? Math.round(item.platformCommissionRate * 10) : undefined, // 百分比转为千分比
      otherCost: item.otherCost ? Math.round(item.otherCost * 100) : undefined // 元转为分
    }))

    const res = await updateGoodsProfitConfig({ saveItems })
    if (res.code === 200) {
      Message.success('保存成功')
      emit('success')
      close()
    }
  } catch (error) {
    console.error('保存失败', error)
    Message.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 关闭弹窗
const close = () => {
  open.value = false
  tableData.value = []
}
</script>

<template>
  <el-dialog
    title="商品盈亏配置录入"
    :visible.sync="open"
    width="900px"
    top="10vh"
    append-to-body
  >
    <div class="goods-profit-config">

      <div class="toolbar">
        <div>
          {{ goods.goodsName || '商品配置' }}
        </div>
        <el-button type="primary" size="mini" @click="addRow">
          <i class="el-icon-plus" /> 添加配置
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column label="SKU名称" prop="skuName">
          <template slot-scope="scope">
            <el-input v-model="scope.row.skuName" placeholder="请输入SKU名称" />
          </template>
        </el-table-column>

        <el-table-column label="SKU单价(元)" prop="skuPrice" width="120">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.skuPrice"
              :controls="false"
              :min="0"
              :precision="2"
              placeholder="单价"
              style="width: 100%"
            />
          </template>
        </el-table-column>

        <el-table-column label="商品+物流成本(元)" prop="goodsExpressCost" width="150">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.goodsExpressCost"
              :controls="false"
              :min="0"
              :precision="2"
              placeholder="成本"
              style="width: 100%"
            />
          </template>
        </el-table-column>

        <el-table-column label="抽佣+罚款均摊比例(%)" prop="platformCommissionRate" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.platformCommissionRate"
              :controls="false"
              :min="0"
              :max="100"
              :precision="1"
              placeholder="比例"
              style="width: 100%"
            />
          </template>
        </el-table-column>

        <el-table-column label="其他成本(元)" prop="otherCost" width="120">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.otherCost"
              :controls="false"
              :min="0"
              :precision="2"
              placeholder="其他成本"
              style="width: 100%"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="deleteRow(scope.row, scope.$index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button :loading="saveLoading" type="primary" @click="submit">保存</el-button>
      <el-button @click="close">关闭</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.goods-profit-config {
  .toolbar {
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
  }

  .batch-tip {
    margin-bottom: 15px;
    font-size: 14px;

    .color-primary {
      color: #409EFF;
    }
  }

  .el-input-number {
    width: 100%;
  }
}
</style>
