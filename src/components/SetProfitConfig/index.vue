<script setup>
import { computed, reactive, ref, watch } from 'vue'
import { updateMediaProfitConfig } from '@/api/promotion/media'
import { Message } from 'element-ui'
import { updateGoodsProfitConfig } from '@/api/promotion/goods'
const props = defineProps({
  visible: Boolean,
  items: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: 'media' // goods
  }
})
const emit = defineEmits(['success'])

const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})

watch(() => props.visible, (val) => {
  if (val) {
    if (props.items.length > 1) {
      profitForm.profit = undefined
      profitForm.profitRoi = undefined
      profitForm.profitBackRate = undefined
      profitForm.goodsExpressCost = undefined
      profitForm.platformCommissionRate = undefined
      profitForm.otherCost = undefined
    } else {
      const item = props.items[0]
      if (props.type === 'goods') {
        profitForm.goodsExpressCost = item.goodsExpressCost / 100 || undefined
        profitForm.platformCommissionRate = item.platformCommissionRate / 10 || undefined
        profitForm.otherCost = item.otherCost / 100 || undefined
      } else {
        profitForm.profitBackRate = item.profitBackRate ? item.profitBackRate / 10 : undefined
      }
    }
  }
})

const profitLoading = ref(false)

const profitForm = reactive({
  // goods
  goodsExpressCost: undefined,
  platformCommissionRate: undefined,
  otherCost: undefined,
  // media
  profitBackRate: undefined
})

const submit = async() => {
  profitLoading.value = true
  switch (props.type) {
    case 'media':
      await updateMediaProfitConfig({
        ids: props.items.map(item => item.id),
        profitBackRate: profitForm.profitBackRate * 10
      })
      break
    case 'goods':
      await updateGoodsProfitConfig({
        saveItems: props.items.map(item => ({
          goodsId: item.goodsId,
          platform: item.platform,
          mediaPlatformType: item.mediaPlatformType,
          goodsExpressCost: Math.round(profitForm.goodsExpressCost * 100),
          platformCommissionRate: Math.round(profitForm.platformCommissionRate * 10),
          otherCost: Math.round(profitForm.otherCost * 100)
        }))
      })
      break
  }
  Message.success('设置成功')
  emit('success')
  close()
  profitLoading.value = false
}

const close = () => {
  open.value = false
}
</script>

<template>
  <el-dialog
    title="设置成本参数"
    :visible.sync="open"
    width="500px"
    top="10vh"
    append-to-body
  >
    <el-form ref="form" :model="profitForm" label-width="110px" label-position="top">

      <template v-if="items.length > 1">
        批量设置 <span class="color-primary">{{ items.length }}</span> 条数据
      </template>
      <template v-else-if="items.length === 1">
        {{ type === 'media' ? items[0].advertiserName : items[0].goodsName }}
      </template>

      <el-form-item v-if="type === 'media'" label="充值返点" prop="profitBackRate">
        <el-input-number v-model="profitForm.profitBackRate" :controls="false" :min="0" :max="100" :precision="1" />
        <span class="ml5">%</span>
      </el-form-item>
      <template v-else>
        <el-form-item label="商品+物流成本" prop="goodsExpressCost">
          <div class="flex">
            <el-input-number v-model="profitForm.goodsExpressCost" :controls="false" :min="0" :precision="2" class="flex1" />
            <span class="ml5">元</span>
          </div>
        </el-form-item>
        <el-form-item label="抽佣+罚款均摊比例" label-width="140px" prop="platformCommissionRate">
          <div class="flex">
            <el-input-number v-model="profitForm.platformCommissionRate" :controls="false" :min="0" :max="100" :precision="1" class="flex1" />
            <span class="ml5">%</span>
          </div>
        </el-form-item>
        <el-form-item label="其他成本" prop="otherCost">
          <div class="flex">
            <el-input-number v-model="profitForm.otherCost" :controls="false" :min="0" :precision="2" class="flex1" />
            <span class="ml5">元</span>
          </div>
        </el-form-item>
      </template>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button v-loading="profitLoading" type="primary" @click="submit">确认</el-button>
      <el-button @click="close">取 消</el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
