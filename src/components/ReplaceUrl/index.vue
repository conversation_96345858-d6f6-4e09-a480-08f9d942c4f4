<script setup>
import { computed, reactive, ref, watch } from 'vue'
import { validURL } from '@/utils/validate'
import { upMonitor, batchChangeMonitorUrl } from '@/api/promotion/media'
import { Message } from 'element-ui'

const props = defineProps({
  visible: Boolean,
  form: {
    type: Object,
    default: () => ({})
  },
  selections: {
    type: Array,
    default: () => []
  },
  mediaType: {
    type: [Number, String],
    default: 1
  },
  batch: Boolean,
  // 是否来源于替换日志
  isFormLog: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['close'])
const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})
watch(() => props.visible, (val) => {
  if (val) {
    _mediaType.value = +props.mediaType
    reset()
  }
})

const _mediaType = ref(1)
const urlFormRef = ref(null)
const urlForm = reactive({
  url: [''],
  renewalUrl: '',
  exposureRenewalUrl: '',
  externalUrl: ''
})
const appletUrl = ref('')

const validateUrl = (rule, value, callback) => {
  if (value) {
    if (validURL(value)) {
      callback()
    } else {
      callback(new Error('请输入正确的链接'))
    }
  } else {
    callback(new Error('请输入一跳落地页'))
  }
}
const urlFormRules = {
  externalUrl: [
    { validator: validateUrl, trigger: 'blur' }
  ]
}

const replaceUrlLoading = ref(false)
const handleUpMonitor = (postData = {}) => {
  postData.url = postData.url.filter(i => i)

  if (props.isFormLog) {
    const data = {
      advertiserId: [props.selections[0].advertiserId],
      mediaPlatformType: _mediaType.value,
      ...postData,
      planIds: props.selections.map(item => item.businessId),
      traceId: props.selections[0].traceId
    }
    replaceUrlLoading.value = true
    batchChangeMonitorUrl(data).then(data => {
      if (data.code === 200) {
        Message.success(data.msg)
        emit('close', true)
        open.value = false
      }
    }).finally(() => {
      replaceUrlLoading.value = false
    })
  } else {
    const data = {
      advertiserId: props.batch ? props.selections.map(item => item.advertiserId) : [props.form.advertiserId],
      mediaPlatformType: _mediaType.value,
      ...postData
    }
    replaceUrlLoading.value = true
    upMonitor(data).then(data => {
      if (data.code === 200) {
        Message.success(data.msg)
        close()
      }
    }).finally(() => {
      replaceUrlLoading.value = false
    })
  }
}

const submit = () => {
  urlFormRef.value.validate(valid => {
    if (valid) {
      handleUpMonitor(urlForm)
    }
  })
}

const queryParams = reactive({
  goodsId: ''
})
const searchGoods = ref(null)

const close = () => {
  emit('close')
  open.value = false
}

const reset = () => {
  searchGoods.value = null
  queryParams.goodsId = ''
  urlForm.url = ['']
  urlForm.renewalUrl = ''
  urlForm.exposureRenewalUrl = ''
  urlForm.externalUrl = ''
  appletUrl.value = ''
  if (urlFormRef.value) { urlFormRef.value.clearValidate() }
}

</script>

<template>
  <el-dialog
    title="替换链接"
    :visible.sync="open"
    width="50%"
    top="10vh"
    append-to-body
  >

    <el-form ref="urlFormRef" :model="urlForm" :rules="urlFormRules" size="small" label-width="100px">
      <el-form-item label="一跳落地页" prop="externalUrl">
        <el-input
          v-model="urlForm.externalUrl"
          placeholder="请输入一跳落地页"
          clearable
        />
        <span style="color: #f4516c; display: flex; align-items: center;">
          <i class="el-icon-warning-outline" style="margin-right: 5px;" />
          注意：该操作将替换账户下所有广告的落地页！
        </span>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button :loading="replaceUrlLoading" type="primary" @click="submit">确 定</el-button>
      <el-button @click="close()">取 消</el-button>
    </span>
  </el-dialog>
</template>

<style scoped lang="scss">
.remove-icon{
  color: #ff4949;
  margin:0 10px;
  font-size: 16px;
  line-height: 16px;
  padding: 10px 0;
}
</style>
