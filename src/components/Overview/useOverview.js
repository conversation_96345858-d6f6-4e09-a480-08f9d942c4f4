
export const OVERVIEW_CONFIG = [
  { key: 'orderCount', label: '下单总数', decimals: 0 },
  { key: 'orderAmount', label: '下单总金额', decimals: 2 },
  { key: 'realOrderCount', label: '成交订单数', decimals: 0 },
  { key: 'realOrderAmount', label: '成交总金额', decimals: 2 },
  { key: 'chargeBack', label: '退单数量', decimals: 0 },
  { key: 'chargeAmount', label: '退单总金额', decimals: 2 },
  { key: 'chargeBackRate', label: '退单率', isPercentage: true },
  { key: 'conversionDeduct', label: '扣除回传数', decimals: 0 },
  { key: 'returnRate', label: '扣回传率', isPercentage: true }
]

export default function getOverviewData(data = {}, overviewConfig = OVERVIEW_CONFIG) {
  return overviewConfig.map(config => ({
    ...config,
    value: config.isPercentage ? (data[config.key] || '0%') : (data[config.key] || 0)
  }))
}
