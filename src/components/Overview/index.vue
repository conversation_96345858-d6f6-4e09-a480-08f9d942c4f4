<script setup>
import CountTo from 'vue-count-to'
import CollapseTransition from '@/components/Collapse'
defineProps({
  title: {
    type: String,
    default: ''
  },
  total: {
    type: [Number, String]
  },
  list: {
    type: Array,
    default: () => []
  },
  decimals: {
    type: Number,
    default: 0
  },
  divider: {
    type: Boolean,
    default: false
  },
  isPointer: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['setTag'])
function handleSetLineChartData(item) {
  emit('setTag', item)
}
</script>

<template>
  <collapse-transition>
    <div>
      <div v-if="title" class="overview-title">{{ title }} <span v-if="total" class="ml5"><span style="font-weight: normal">总计：</span>{{ total }}</span> </div>
      <div class="overview" :class="isPointer?'active':''" :style="{marginBottom: divider ? '0': '18px'}">
        <template v-for="item in list">
          <div :key="item.label" class="item" @click="handleSetLineChartData(item)">
            <div class="title">
              {{ item.label }}
              <el-tooltip v-if="item.tooltip" class="item" effect="dark" :content="item.tooltip" placement="top">
                <i class="el-icon-question" style="margin-left: 5px" />
              </el-tooltip>
            </div>
            <count-to v-if="typeof item.value === 'number'" :start-val="0" :end-val="item.value || 0" :duration="1000" class="value" :decimals="item.decimals !== undefined? item.decimals: decimals" />
            <span v-else style="font-size: 24px">{{ item.value }}</span>
          </div>
        </template>
      </div>
      <el-divider v-if="divider" />
    </div>
  </collapse-transition>
</template>

<style scoped lang="scss">
.overview-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 18px;
}
.overview {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  .item {
    margin-right: 60px;
    .title {
      font-size: 14px;
      color: #696969;
      margin-bottom: 10px;
    }
    .value {
      font-size: 24px;
      color: #000000;

    }
  }
}
.active{
  cursor: pointer;
}
::v-deep {
  .el-divider--horizontal {
    margin: 18px 0;
  }
}
</style>
