<script setup>
import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue'
import { getMonitorAutoConversionConfig, saveMonitorAutoConversionConfig } from '@/api/media/monitorAutoConversion'
import { queryImproveConversionList } from '@/api/data/monitor'
import { Message } from 'element-ui'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import SvgIcon from '@/components/SvgIcon/index.vue'
import useDicts from '@/hooks/useDicts'
import dayjs from 'dayjs'

const self = getCurrentInstance().proxy

// 获取字典数据
const { dictMap } = useDicts(['media_type', 'platform_type'])

const props = defineProps({
  form: {
    type: Array,
    default: () => ([])
  },
  type: {
    type: String,
    default: 'media' // plan,
  }
})

const emit = defineEmits(['save', 'cancel'])

// 显示信息
const info = computed(() => {
  if (props.form?.length !== 1) return false
  const form = props.form[0]
  switch (props.type) {
    case 'media':
    case 'plan':
      return {
        id: form.advertiserId,
        name: form.advertiserName
      }
    case 'goods':
      return {
        id: form.goodsId,
        name: form.goodsName
      }
    default:
      return false
  }
})

// 回传类型映射
const typeMap = {
  plan: { type: 1, idKey: 'planId' },
  media: { type: 2, idKey: 'advertiserId' },
  goods: { type: 3, idKey: 'goodsId' }
}

// 回传类型
const conversionType = computed(() => {
  return typeMap[props.type].type
})

// 回传id
const businessIdKey = computed(() => {
  return typeMap[props.type].idKey
})

const monitorAutoConversionRef = ref(null)
const monitorAutoConversionForm = reactive({
  id: null,
  enableConversion: false, // 是否开启回传限制(0:不开启;1:开启)
  type: 1, // 类型(1计划,2媒体账户)
  businessId: '', // 业务id根据type对应不同id
  goodsPlatformType: 1, // 商品的电商平台，固定为1
  mediaPlatformType: null, // 媒体的媒体平台
  conversionOrderCount: 10, // 每次回传订单数
  cost: null, // 消耗间隔
  roi: null, // ROI阈值
  goodsId: '', // 商品id
  goodsName: '', // 商品名称
  orderPrice: null, // 回传金额（显示用，单位：元）
  mallName: '', // 店铺名称
  goodsCategoryName: '' // 商品类目名称
})

const setDefaultMonitorAutoConversion = () => {
  monitorAutoConversionForm.type = conversionType.value

  if (props.form.length === 1) {
    const form = props.form[0]
    const businessId = form[businessIdKey.value]
    getMonitorAutoConversionConfig({
      businessId,
      type: conversionType.value,
      goodsPlatformType: form.platform,
      mediaPlatformType: form.mediaPlatformType || form.mediaType
    }).then(data => {
      if (data.data) {
        setMonitorAutoConversionForm(data.data)
      }
    })
  }
}

function setMonitorAutoConversionForm(data) {
  const formData = { ...data }
  // 将分转换为元显示
  if (formData.orderPrice) {
    formData.orderPrice = formData.orderPrice / 100
  }
  Object.assign(monitorAutoConversionForm, formData)
}

// 表单校验规则
const rules = {
  mediaPlatformType: [
    { required: true, message: '请选择媒体平台类型', trigger: 'change' }
  ],
  conversionOrderCount: [
    { required: true, message: '请输入每次回传订单数', trigger: 'blur' },
    { type: 'number', min: 1, max: 50, message: '每次回传订单数必须在1-50之间', trigger: 'blur' }
  ],
  // 暂时隐藏消耗间隔验证
  // cost: [
  //   { required: true, message: '请输入消耗间隔', trigger: 'blur' },
  //   { type: 'number', min: 0, message: '消耗间隔必须大于等于0', trigger: 'blur' }
  // ],
  roi: [
    { required: true, message: '请输入ROI阈值', trigger: 'blur' },
    { type: 'number', min: 0, max: 5, message: 'ROI阈值必须在0-5之间', trigger: 'blur' }
  ],
  goodsId: [
    { required: true, message: '请输入商品ID', trigger: 'blur' },
    { pattern: /^\d+$/, message: '商品ID必须为全数字', trigger: 'blur' }
  ],
  goodsName: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { max: 64, message: '商品名称最多64个字符', trigger: 'blur' }
  ],
  mallName: [
    { required: true, message: '请输入店铺名称', trigger: 'blur' },
    { max: 32, message: '店铺名称最多32个字符', trigger: 'blur' }
  ],
  goodsCategoryName: [
    { required: true, message: '请输入商品类目名称', trigger: 'blur' },
    { max: 32, message: '商品类目名称最多32个字符', trigger: 'blur' }
  ],
  orderPrice: [
    { required: true, message: '请输入回传金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '回传金额必须大于等于0', trigger: 'blur' }
  ]
}

const loading = ref(false)
const submit = () => {
  // 表单验证
  monitorAutoConversionRef.value.validate((valid) => {
    if (!valid) {
      return false
    }
    submitForm()
  })
}

const submitForm = () => {
  let postData = []

  postData = props.form.map(item => {
    const form = { ...monitorAutoConversionForm }
    form.businessId = item[businessIdKey.value]

    // 将元转换为分提交
    if (form.orderPrice) {
      form.orderPrice = Math.round(form.orderPrice * 100)
    }

    if (['plan', 'media'].includes(props.type)) {
      form.mediaPlatformType = item.mediaType
    } else if (props.type === 'goods') {
      form.goodsPlatformType = item.platform
      form.mediaPlatformType = item.mediaPlatformType
    }

    return form
  })

  loading.value = true
  saveMonitorAutoConversionConfig(postData).then(data => {
    Message.success('操作成功')
    emit('save')
  }).finally(() => {
    loading.value = false
  })
}

const cancel = () => {
  emit('cancel')
}

const close = () => {
  self.$tab.closePage()
}

// 清除表单验证
const clearValidate = () => {
  monitorAutoConversionRef.value && monitorAutoConversionRef.value.clearValidate()
}

// 回传明细弹窗相关
const logDialogVisible = ref(false)
const logLoading = ref(false)
const logList = ref([])
const logTotal = ref(0)
const logQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  advertiserId: '',
  planId: '',
  goodsId: '',
  goodsName: '',
  conversionState: null
})
const logQueryTime = ref([
  dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
  dayjs().format('YYYY-MM-DD HH:mm:ss')
])

// 打开回传明细弹窗
const openLogDialog = () => {
  logDialogVisible.value = true
  getLogList()
}

// 获取回传明细列表
const getLogList = () => {
  logLoading.value = true
  const query = Object.assign({}, logQueryParams)
  query.type = 3 // 多多自研回传日志
  switch (props.type) {
    case 'media':
      query.advertiserId = info.value.id
      break
    case 'plan':
      query.planId = info.value.id
      break
    case 'goods':
      query.goodsId = info.value.id
      break
    default:
      break
  }
  query.mediaType = monitorAutoConversionForm.mediaPlatformType

  // 处理时间范围
  if (logQueryTime.value && logQueryTime.value.length === 2) {
    query.startTime = logQueryTime.value[0]
    query.endTime = logQueryTime.value[1]
  }

  queryImproveConversionList(query).then(response => {
    logList.value = response.rows || []
    logTotal.value = response.total
  }).finally(() => {
    logLoading.value = false
  })
}

// 分页改变
const handleLogPageChange = () => {
  getLogList()
}

// 搜索回传明细
const handleLogQuery = () => {
  logQueryParams.pageNum = 1
  getLogList()
}

// 重置回传明细搜索
const resetLogQuery = () => {
  Object.assign(logQueryParams, {
    pageNum: 1,
    pageSize: 10,
    advertiserId: '',
    planId: '',
    goodsId: '',
    goodsName: '',
    conversionState: null
  })
  logQueryTime.value = [
    dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
    dayjs().format('YYYY-MM-DD HH:mm:ss')
  ]
  getLogList()
}

// 获取回传状态文本
const getConversionStateText = (state) => {
  const stateMap = {
    0: '未回传',
    1: '已回传'
  }
  return stateMap[state] || '未知'
}

// 格式化回传结果
const formatConversionResult = (conversionMessage) => {
  try {
    if (!conversionMessage) return ''
    const obj = JSON.parse(conversionMessage)
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return conversionMessage
  }
}

onMounted(() => {
  setDefaultMonitorAutoConversion()
})
</script>

<template>
  <div>
    <div v-if="info" class="flex">
      <BaseInfoCell :id="info.id" :name="info.name" label="名称:" type="info" class="flex1" style="margin-bottom: 20px" no-copy />
      <el-button v-if="form.length === 1 && monitorAutoConversionForm.id" type="primary" size="mini" style="height: 32px;" @click="openLogDialog">回传明细</el-button>
    </div>
    <el-form ref="monitorAutoConversionRef" :model="monitorAutoConversionForm" :rules="rules" label-width="140px">
      <el-form-item label="开启多多自研回传">
        <el-switch
          v-model="monitorAutoConversionForm.enableConversion"
          active-color="#13ce66"
          @change="clearValidate"
        />
      </el-form-item>

      <template v-if="monitorAutoConversionForm.enableConversion">

        <el-form-item label="每次回传订单数" prop="conversionOrderCount">
          <el-input-number
            v-model="monitorAutoConversionForm.conversionOrderCount"
            :min="1"
            :max="50"
            :controls="false"
          />
          <span class="ml10">单</span>
        </el-form-item>

        <!-- 暂时隐藏消耗间隔 -->
        <!-- <el-form-item label="消耗间隔" prop="cost">
          <el-input-number
            v-model="monitorAutoConversionForm.cost"
            :min="0"
            :precision="2"
            :controls="false"
          />
          <span class="ml10">元</span>
        </el-form-item> -->

        <el-form-item label="ROI阈值" prop="roi">
          <el-input-number
            v-model="monitorAutoConversionForm.roi"
            :min="0"
            :max="5"
            :precision="2"
            :controls="false"
          />
        </el-form-item>

        <el-form-item label="商品ID" prop="goodsId">
          <el-input
            v-model="monitorAutoConversionForm.goodsId"
            placeholder="请输入商品ID（全数字）"
            clearable
          />
        </el-form-item>

        <el-form-item label="商品名称" prop="goodsName">
          <el-input
            v-model="monitorAutoConversionForm.goodsName"
            placeholder="请输入商品名称（最多64字符）"
            maxlength="64"
            show-word-limit
            clearable
          />
        </el-form-item>

        <el-form-item label="回传金额" prop="orderPrice">
          <el-input-number
            v-model="monitorAutoConversionForm.orderPrice"
            :min="0"
            :precision="2"
            :controls="false"
          />
          <span class="ml10">元</span>
        </el-form-item>

        <el-form-item label="店铺名称" prop="mallName">
          <el-input
            v-model="monitorAutoConversionForm.mallName"
            placeholder="请输入店铺名称（最多32字符）"
            maxlength="32"
            show-word-limit
            clearable
          />
        </el-form-item>

        <el-form-item label="商品类目名称" prop="goodsCategoryName">
          <el-input
            v-model="monitorAutoConversionForm.goodsCategoryName"
            placeholder="请输入商品类目名称（最多32字符）"
            maxlength="32"
            show-word-limit
            clearable
          />
        </el-form-item>
      </template>

      <!-- 配置说明 -->
      <el-form-item v-if="monitorAutoConversionForm.enableConversion">
        <div class="summary-description">
          系统将根据设置的ROI阈值进行监测，
          当满足条件时自动回传 <span class="highlight">{{ monitorAutoConversionForm.conversionOrderCount || 0 }}</span> 单订单，
          每单回传金额为 <span class="highlight">{{ monitorAutoConversionForm.orderPrice || 0 }}</span> 元。
        </div>
      </el-form-item>

    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button :loading="loading" type="primary" @click="submit">保存</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>

    <!-- 回传明细弹窗 -->
    <el-dialog
      title="多多自研回传回传明细"
      :visible.sync="logDialogVisible"
      width="80%"
      :append-to-body="true"
    >
      <!-- 搜索表单 -->
      <el-form :model="logQueryParams" class="search-form" size="small" :inline="true" label-width="80px" style="margin-bottom: 20px;">
        <el-form-item prop="conversionState">
          <el-select v-model="logQueryParams.conversionState" placeholder="回传状态" clearable @change="handleLogQuery">
            <el-option label="未回传" :value="0" />
            <el-option label="已回传" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item prop="advertiserId">
          <el-input
            v-model.trim="logQueryParams.advertiserId"
            placeholder="广告主ID"
            clearable
            @keyup.enter.native="handleLogQuery"
          />
        </el-form-item>
        <el-form-item prop="planId">
          <el-input
            v-model.trim="logQueryParams.planId"
            placeholder="计划ID"
            clearable
            @keyup.enter.native="handleLogQuery"
          />
        </el-form-item>
        <el-form-item prop="goodsId">
          <el-input
            v-model.trim="logQueryParams.goodsId"
            placeholder="商品ID"
            clearable
            @keyup.enter.native="handleLogQuery"
          />
        </el-form-item>
        <el-form-item prop="goodsName">
          <el-input
            v-model.trim="logQueryParams.goodsName"
            placeholder="商品名称"
            clearable
            @keyup.enter.native="handleLogQuery"
          />
        </el-form-item>
        <el-form-item label="时间范围" prop="queryTime">
          <el-date-picker
            v-model="logQueryTime"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
            style="width: 350px;"
            @change="handleLogQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleLogQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetLogQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="logLoading"
        :data="logList"
        stripe
        border
        height="400"
      >
        <el-table-column label="平台类型" align="center" width="100" prop="platform">
          <template #default="scope">
            <svg-icon v-if="dictMap.platform_type[scope.row.platform]" :icon-class="dictMap.platform_type[scope.row.platform]" />
            <span> {{ dictMap.platform_type[scope.row.platform] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="媒体平台" align="center" width="100" prop="mediaPlatformType">
          <template #default="scope">
            <svg-icon v-if="dictMap.media_type[scope.row.mediaPlatformType]" :icon-class="dictMap.media_type[scope.row.mediaPlatformType]" />
            <span>{{ dictMap.media_type[scope.row.mediaPlatformType] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="账户信息" align="center" min-width="165" prop="advertiserName">
          <template #default="scope">
            <BaseInfoCell :id="scope.row.advertiserId" :name="scope.row.advertiserName" />
          </template>
        </el-table-column>
        <el-table-column label="计划信息" align="center" min-width="160" prop="planName">
          <template #default="scope">
            <BaseInfoCell :id="scope.row.planId" :name="scope.row.planName" />
          </template>
        </el-table-column>
        <el-table-column label="商品信息" align="center" prop="goods" min-width="160">
          <template #default="scope">
            <BaseInfoCell :id="scope.row.goodsId" :name="scope.row.goodsName" />
          </template>
        </el-table-column>
        <el-table-column label="订单编号" align="center" prop="orderSn" min-width="120" />
        <el-table-column label="回传状态" align="center" width="100" prop="conversionState">
          <template #default="scope">
            <el-tag :type="scope.row.conversionState === 1 ? 'success' : 'warning'">
              {{ getConversionStateText(scope.row.conversionState) }}
              <el-popover
                placement="top-start"
                width="400"
                trigger="hover"
                :disabled="!scope.row.conversionMessage"
                :content="formatConversionResult(scope.row.conversionMessage)"
              >
                <i slot="reference" class="el-icon-info" style="margin-left: 5px;" />
              </el-popover>
            </el-tag>
          </template>
        </el-table-column>

      </el-table>

      <div style="margin-top: 20px; text-align: right;">
        <el-pagination
          v-show="logTotal > 0"
          :total="logTotal"
          :page-size="logQueryParams.pageSize"
          :current-page="logQueryParams.pageNum"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="val => {logQueryParams.pageSize = val; handleLogPageChange()}"
          @current-change="val => {logQueryParams.pageNum = val; handleLogPageChange()}"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="logDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: right;
}

.ml10 {
  margin-left: 10px;
}

.summary-description {
  padding: 12px;
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-left: 4px solid #409EFF;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.8;
  color: #333;
}

.summary-description .highlight {
  color: #409EFF;
  font-weight: bold;
  background-color: #ecf5ff;
  padding: 1px 4px;
  border-radius: 3px;
}
</style>
