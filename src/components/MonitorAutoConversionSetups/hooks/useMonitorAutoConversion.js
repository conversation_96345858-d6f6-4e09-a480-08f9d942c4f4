import { ref } from 'vue'
import { Message } from 'element-ui'

export default function({ type, onSave }) {
  // 多多自研回传
  const monitorAutoConversionForm = ref([])
  const monitorAutoConversionDialogVisible = ref(false)

  const showMonitorAutoConversion = (row) => {
    monitorAutoConversionForm.value = [row]
    monitorAutoConversionDialogVisible.value = true
  }

  const batchUpdateMonitorAutoConversion = (selections) => {
    if (!selections || selections.length === 0) {
      Message.warning('请选择要设置项')
      return
    }
    if (selections.some(item => +item.mediaType !== 1 && +item.mediaType !== 4)) {
      Message.warning('只有抖音和快手的账户才能设置多多自研回传')
      return
    }
    monitorAutoConversionForm.value = selections
    monitorAutoConversionDialogVisible.value = true
  }

  const handleMonitorAutoConversionSave = () => {
    monitorAutoConversionDialogVisible.value = false
    onSave && onSave()
  }

  const handleMonitorAutoConversionCancel = () => {
    monitorAutoConversionDialogVisible.value = false
  }

  return {
    monitorAutoConversionType: type,
    monitorAutoConversionForm,
    monitorAutoConversionDialogVisible,
    showMonitorAutoConversion,
    batchUpdateMonitorAutoConversion,
    handleMonitorAutoConversionSave,
    handleMonitorAutoConversionCancel
  }
}
