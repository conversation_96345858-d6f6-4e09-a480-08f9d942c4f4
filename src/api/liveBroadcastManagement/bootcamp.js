
import request from '@/utils/request'

// 新增训练营
export function addBootcampInfo(data) {
  return request({
    url: '/liveAuiCamp/save',
    method: 'post',
    data
  })
}
// 删除训练营
export function deleteBootcampInfo(data) {
  return request({
    url: `/liveAuiCamp/mutiRemove`,
    method: 'delete',
    data
  })
}
// 修改训练营
export function editeBootcampInfo(data) {
  return request({
    url: '/liveAuiCamp/update',
    method: 'post',
    data
  })
}

// 查询训练营列表
export function queryBootcampInfo(campName) {
  return request({
    url: `/liveAuiCamp/page?campName=${campName}`,
    method: 'get'
  })
}

// 查询table训练营数据
export function queryBootcampList(campName) {
  return request({
    url: `/liveAuiCamp/list?campName=${campName}`,
    method: 'get'
  })
}

// 新增训练营营期
export function addBootcamp(data) {
  return request({
    url: `/liveAuiCampPeriod/save`,
    method: 'post',
    data
  })
}

// 编辑训练营营期
export function editeBootcamp(data) {
  return request({
    url: `/liveAuiCampPeriod/update`,
    method: 'post',
    data
  })
}
// 复制训练营营期
export function copyBootcamp(data) {
  return request({
    url: `/liveAuiCampPeriod/copy`,
    method: 'post',
    data
  })
}

// 训练营营期列表
export function getBootcampList(query) {
  return request({
    url: `/liveAuiCampPeriod/list`,
    method: 'get',
    params: query
  })
}

// 训练营营期分页列表
export function getBootcampListData(query) {
  return request({
    url: `/liveAuiCampPeriod/page`,
    method: 'get',
    params: query
  })
}

// 训练营营期详情
export function getBootcampDetail(id) {
  return request({
    url: `/liveAuiCampPeriod/getById?id=${id}`,
    method: 'get'
  })
}

// 训练营营期 删除
export function deleteBootcamp(data) {
  return request({
    url: `/liveAuiCampPeriod/mutiRemove`,
    method: 'delete',
    data
  })
}

// 训练营营期学员
export function getBootcampStudentsList(data) {
  return request({
    url: `/liveAuiCampPeriodStudentBiz/periodPage`,
    method: 'post',
    data
  })
}

// 训练营营期  我的学员
export function mineBootcampStudents(data) {
  return request({
    url: `/liveAuiCampPeriodStudentBiz/myPage`,
    method: 'post',
    data
  })
}

// 训练营营期  =====  课程章节列表
export function getBootcampCourseChapterList(periodId) {
  return request({
    url: `/liveAuiCampPeriodChapterBiz/queryChapterList?periodId=${periodId}`,
    method: 'get'
  })
}

// 训练营营期  =====  编辑章节
export function editeCourseChapter(data) {
  return request({
    url: `/liveAuiCampPeriodChapterBiz/updateChapter`,
    method: 'post',
    data
  })
}

// 训练营营期  =====  新增章节
export function addCourseChapter(data) {
  return request({
    url: `/liveAuiCampPeriodChapterBiz/saveChapter`,
    method: 'post',
    data
  })
}

// 训练营营期  =====  删除章节
export function deleteCourseChapter(chapterId) {
  return request({
    url: `/liveAuiCampPeriodChapterBiz/deleteChapter?chapterId=${chapterId}`,
    method: 'get'
  })
}

// 训练营营期  =====  章节排序
export function sortCourseChapter(data) {
  return request({
    url: `/liveAuiCampPeriodChapterBiz/sortChapter`,
    method: 'post',
    data
  })
}

// 训练营营期  =====  新增课节
export function addBootcampCourse(data) {
  return request({
    url: `/liveAuiCampPeriodClassBiz/saveClass`,
    method: 'post',
    data
  })
}

// 训练营营期  =====  编辑课节
export function editeBootcampCourse(data) {
  return request({
    url: `/liveAuiCampPeriodClassBiz/updateClass`,
    method: 'post',
    data
  })
}

// 训练营营期  =====  根据章节查询  课节
export function getCourseByChapter(query) {
  return request({
    url: `/liveAuiCampPeriodClassBiz/queryClassByChapterId`,
    method: 'get',
    params: query
  })
}

// 训练营营期  =====  移动课节(移动到其他章节下)
export function moveCourse(data) {
  return request({
    url: `/liveAuiCampPeriodClassBiz/moveSaveClass`,
    method: 'post',
    data
  })
}

// 训练营营期  =====  删除课节
export function deleteCourse(classId) {
  return request({
    url: `/liveAuiCampPeriodClassBiz/deleteClass?classId=${classId}`,
    method: 'get'
  })
}

// 训练营--------下拉列表
export function bootcampSelectData(query) {
  return request({
    url: `/liveAuiCampPeriodClassBiz/queryCampSelect`,
    method: 'get',
    params: query
  })
}

// 营期--------下拉列表
export function campSelectData(query) {
  return request({
    url: `/liveAuiCampPeriodClassBiz/queryPeriodSelect`,
    method: 'get',
    params: query
  })
}

// 分页查询其他营期的课程
export function searchCourse(data) {
  return request({
    url: `/liveAuiCampPeriodClassBiz/pageQueryOtherPeriodClass`,
    method: 'post',
    data
  })
}

// 复制课程
export function copyCourse(data) {
  return request({
    url: `/liveAuiCampPeriodClassBiz/copySaveClass`,
    method: 'post',
    data
  })
}

// 编辑课程
export function editeCourse(data) {
  return request({
    url: `/liveAuiCampPeriodClassBiz/updateClass`,
    method: 'post',
    data
  })
}

// 标签数据
export function loadTagsData() {
  return request({
    url: `/liveAuiCommonApi/listTag`,
    method: 'get'
  })
}

// 根据课节ID查询课节详情
export function courseDetailInfo(classId) {
  return request({
    url: `/liveAuiCampPeriodClassBiz/queryClassDetail?classId=${classId}`,
    method: 'get'
  })
}

// 修改课程直播时间
export function changeLiveTime(data) {
  return request({
    url: `/liveAuiCampPeriodClassBiz/updateClassTime`,
    method: 'post',
    data
  })
}

// 客户列表
export function customerList(data) {
  return request({
    url: `/liveAuiCampPeriodStudentBiz/customerPage`,
    method: 'post',
    data
  })
}

// 添加学员
export function addStudents(data) {
  return request({
    url: `/liveAuiCampPeriodStudentBiz/save`,
    method: 'post',
    data
  })
}

// 移除学员
export function removeStudents(id) {
  return request({
    url: `/liveAuiCampPeriodStudentBiz/delete/${id}`,
    method: 'delete'
  })
}

// 课程模式 ---- 查询课程列表
export function courseTypeList(query) {
  return request({
    url: `/liveAuiCampPeriodClassBiz/pageQueryClass`,
    method: 'get',
    params: query
  })
}

// 根据营期查询章节ID
export function queryChapterId(periodId) {
  return request({
    url: `/liveAuiCampPeriodChapterBiz/queryChapterList?periodId=${periodId}`,
    method: 'get'
  })
}

// 训练营学员--我的学员 分页查询
export function queryStudentsOrMineStudents(query) {
  return request({
    url: `/liveAuiCamp/getCampStudentPage`,
    method: 'get',
    params: query
  })
}

// 训练营学员--我的学员 统计数据
export function queryTotal(campId) {
  return request({
    url: `/liveAuiCamp/getCampStudentStatics?campId=${campId}`,
    method: 'get'
  })
}
