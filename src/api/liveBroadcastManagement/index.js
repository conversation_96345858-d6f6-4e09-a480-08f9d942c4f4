
import request from '@/utils/request'

// 新增课程分类
export function addCourseCategory(data) {
  return request({
    url: '/liveCourseCategory/save',
    method: 'post',
    data
  })
}
// 删除课程分类
export function deleteCourseCategory(data) {
  return request({
    url: '/liveCourseCategory/mutiRemove',
    method: 'delete',
    data
  })
}
// 修改课程分类
export function editeCourseCategory(data) {
  return request({
    url: '/liveCourseCategory/update',
    method: 'post',
    data
  })
}
// 课程分类详情
export function detailCourseCategory(id) {
  return request({
    url: `/liveCourseCategory/getById?id=${id}`,
    method: 'get'
  })
}
// 课程分类列表查询
export function queryCourseCategory(query) {
  return request({
    url: '/liveCourseCategory/list',
    method: 'get',
    params: query
  })
}
// 用户新增
export function addUser(data) {
  return request({
    url: '/liveAdminRootBiz/addSingleRobot',
    method: 'post',
    data
  })
}
// 编辑用户
export function editeUser(data) {
  return request({
    url: '/liveAdminRootBiz/editRobot',
    method: 'post',
    data
  })
}
// 删除用户
export function deleteUser(query) {
  return request({
    url: '/liveAdminRootBiz/deleteRobot',
    method: 'get',
    params: query
  })
}
// 用户批量新增
export function batchAddUser(query) {
  return request({
    url: '/liveAdminRootBiz/generateRandomRobots',
    method: 'get',
    params: query
  })
}
// 用户列表
export function getListUser(data) {
  return request({
    url: '/liveAdminRootBiz/retrieveRobotListPage',
    method: 'post',
    data
  })
}
// 互动消息应用配置 --- 保存
export function applicationConfigSave(data) {
  return request({
    url: '/liveMessageApp/save',
    method: 'post',
    data
  })
}
// 互动消息应用配置 --- 获取
export function getApplicationConfig() {
  return request({
    url: '/liveMessageApp/getById',
    method: 'get'
  })
}
// 互动消息应用配置 --- 修改
export function editeApplicationConfig(data) {
  return request({
    url: '/liveMessageApp/update',
    method: 'post',
    data
  })
}