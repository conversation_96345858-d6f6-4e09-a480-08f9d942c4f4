
import request from '@/utils/request'

// 新增分组
export function addGroup(data) {
  return request({
    url: '/liveAuiMaterialCategoryBiz/addCategory',
    method: 'post',
    data
  })
}
// 删除分组
export function deleteGroup(id) {
  return request({
    url: `/liveAuiMaterialCategoryBiz/deleteCategory?id=${id}`,
    method: 'delete'
  })
}
// 修改分组
export function editeGroup(data) {
  return request({
    url: '/liveAuiMaterialCategoryBiz/updateCategory',
    method: 'put',
    data
  })
}

// 查询分组
export function queryGroup(data) {
  return request({
    url: '/liveAuiMaterialCategoryBiz/listCategories',
    method: 'post',
    data
  })
}
// 获取上传视频凭证
export function uploadVideoAuth(data) {
  return request({
    url: '/liveAuiMaterialVideoBiz/getUploadVideoUrlAndAuth',
    method: 'post',
    data
  })
}
// 上传视频---保存
export function uploadVideoSuccess(data) {
  return request({
    url: '/liveAuiMaterialVideoBiz/saveLiveAuiVideo',
    method: 'post',
    data
  })
}
// 图片成功---保存
export function uploadImageSuccess(data) {
  return request({
    url: '/liveAuiMaterialImageBiz/save',
    method: 'post',
    data
  })
}
// 上传视频凭证刷新
export function uploadConfigRefesh(data) {
  return request({
    url: '/liveAuiMaterialVideoBiz/refreshUploadVideoUrlAndAuth',
    method: 'post',
    data
  })
}
// 获取上传图片凭证
export function uploadImgAuth(data) {
  return request({
    url: '/liveAuiMaterialImageBiz/getUploadImageUrlAndAuth',
    method: 'post',
    data
  })
}
// 查询视频列表
export function queryVideoList(data) {
  return request({
    url: '/liveAuiMaterialVideoBiz/pageLiveAuiVideo',
    method: 'post',
    data
  })
}
// 查询图片列表
export function queryImageList(data) {
  return request({
    url: '/liveAuiMaterialImageBiz/page',
    method: 'post',
    data
  })
}

// 创建模版
export function createTemplate(data) {
  return request({
    url: '/liveAuiMessageTemplate/save',
    method: 'post',
    data
  })
}

// 删除图片
export function deleteImage(id) {
  return request({
    url: `/liveAuiMaterialImageBiz/delete/${id}`,
    method: 'delete'
  })
}

// 删除视频
export function deleteVideo(id) {
  return request({
    url: `/liveAuiMaterialVideoBiz/delete/${id}`,
    method: 'delete'
  })
}

// 获取视频播放地址
export function getVideoSrc(data) {
  return request({
    url: `/liveAuiMaterialVideoBiz/getPlayInfo`,
    method: 'post',
    data
  })
}

// 图片重命名
export function updateImageName(data) {
  return request({
    url: '/liveAuiMaterialImageBiz/update',
    method: 'put',
    data
  })
}