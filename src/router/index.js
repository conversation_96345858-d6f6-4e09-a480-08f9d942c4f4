import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout/index.vue'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect.vue')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login.vue'),
    hidden: true
  },
  {
    path: '/social-login',
    component: () => import('@/views/socialLogin.vue'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register.vue'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404.vue'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401.vue'),
    hidden: true
  }, // {
  //   path: '',
  //   component: Layout,
  //   redirect: 'index',
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/index'),
  //       name: 'Index',
  //       meta: { title: '首页', icon: 'dashboard', affix: true }
  //     }
  //   ]
  // },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index.vue'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  }
  // iOS 风格样式展示页面
  // {
  //   path: '/demo',
  //   component: Layout,
  //   hidden: false,
  //   children: [
  //     {
  //       path: 'ios-style',
  //       component: () => import('@/views/demo/ios-style-demo'),
  //       name: 'IosStyleDemo',
  //       meta: {
  //         title: 'iOS 风格样式展示',
  //         icon: 'component',
  //         noCache: true
  //       }
  //     }
  //   ]
  // }
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/promotion/media-account',
    component: Layout,
    hidden: true,
    permissions: ['promotion:media:list'],
    children: [
      {
        path: 'index/:parentAccountId',
        component: () => import('@/views/promotion/media/media.vue'),
        name: 'MediaAccount',
        meta: { title: '媒体父账户', activeMenu: '/promotion/media' }
      }
    ]
  },
  {
    path: '/promotion/media-parent',
    component: Layout,
    hidden: true,
    permissions: ['promotion:media:list'],
    children: [
      {
        path: 'index/:parentAccountId(\\d+)',
        component: () => import('@/views/promotion/media/index.vue'),
        name: 'Media',
        meta: { title: '媒体父账户', activeMenu: '/promotion/media' }
      }
    ]
  },
  {
    path: '/promotion/media-list',
    component: Layout,
    hidden: true,
    permissions: ['promotion:media:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/promotion/media/listComponent/index.vue'),
        name: 'MediaList',
        meta: { title: '巨量列表', noCache: true, activeMenu: '/promotion/media' }
      },
      {
        path: 'ksIndex',
        component: () => import('@/views/promotion/media/listComponent/ksList.vue'),
        name: 'KsList',
        meta: { title: '快手列表', noCache: true, activeMenu: '/promotion/media' }
      },
      {
        path: 'txIndex',
        component: () => import('@/views/promotion/media/listComponent/txList.vue'),
        name: 'TxList',
        meta: { title: '腾讯列表', noCache: true, activeMenu: '/promotion/media' }
      }
    ]
  },
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole.vue'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser.vue'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data.vue'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log.vue'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable.vue'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  },
  {
    path: '/promotion/form-results',
    component: Layout,
    permissions: ['promotion:forms:results'],
    children: [
      {
        path: 'index/:formId',
        component: () => import('@/views/form/list/FormResults.vue'),
        name: 'FormResults',
        meta: { title: '表单回传信息', activeMenu: '/h5/formResults' }
      }
    ]
  },
  {
    path: '/h5/link-result',
    component: Layout,
    hidden: true,
    permissions: ['promotion:forms:results'],
    children: [
      {
        path: 'index/:linkId',
        component: () => import('@/views/wx/customerLinkCount/index.vue'),
        name: 'CustomerLinkCount',
        meta: { title: '获客添加用户', activeMenu: '/h5/customerLink' }
      }
    ]
  },
  {
    path: '/wx/customer-link-group',
    component: Layout,
    permissions: ['wx:customerLink:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/wx/customerLinkGroup/index.vue'),
        name: 'CustomerLinkGroup',
        meta: { title: '获客链接组', icon: 'link' }
      }
    ]
  },
  {
    path: '/resource',
    component: Layout,
    redirect: '/resource/files',
    name: 'Resource',
    meta: { title: '资源管理', icon: 'folder' },
    permissions: ['promotion:fileres:list'],
    children: [
      {
        path: 'files',
        component: () => import('@/views/resource/index.vue'),
        name: 'ResourceFiles',
        meta: { title: '文件管理', icon: 'document' }
      }
    ]
  }
]

// 防止连续点击多次路由报错
const routerPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
