<template>
  <div class="adaption-container">
    <el-form
      v-show="showSearch"
      ref="queryFormRef"
      class="search-form"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="70px"
      @submit.native.prevent
    >
      <el-form-item prop="mediaPlatformType">
        <el-select v-model="queryParams.mediaPlatformType" placeholder="媒体类型" clearable @change="handleQuery">
          <el-option label="抖音" :value="1">
            <svg-icon icon-class="抖音" />
            <span>抖音</span>
          </el-option>
          <el-option label="腾讯" :value="4">
            <svg-icon icon-class="腾讯" />
            <span> 腾讯</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="formId">
        <el-input
          v-model.trim="queryParams.formId"
          placeholder="表单ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="orderNo">
        <el-input
          v-model.trim="queryParams.orderNo"
          placeholder="商户订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="payNo">
        <el-input
          v-model.trim="queryParams.payNo"
          placeholder="支付订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="mobile">
        <el-input
          v-model.trim="queryParams.mobile"
          placeholder="手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="accountId">
        <el-input
          v-model.trim="queryParams.accountId"
          placeholder="账户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="planId">
        <el-input
          v-model.trim="queryParams.planId"
          placeholder="计划ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="corpId">
        <el-select v-model="queryParams.corpId" placeholder="授权企业" clearable filterable @change="handleQuery">
          <el-option
            v-for="(item, i) in authCorpList"
            :key="i"
            :label="item.corpName"
            :value="item.corpId"
            :disabled="!(item.isAppAuth && item.isCustomerAuth)"
          >
            {{ item.corpName }}
            <span v-if="!item.isAppAuth" class="text-danger"> <i class="el-icon-warning-outline" />应用未授权</span>
            <span v-if="!item.isCustomerAuth" class="text-danger"><i class="el-icon-warning-outline" />获客助手未授权</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="receiverName">
        <el-input
          v-model.trim="queryParams.receiverName"
          placeholder="接粉账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="name">
        <el-input
          v-model.trim="queryParams.name"
          placeholder="姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="convertFlag">
        <el-select
          v-model="queryParams.convertFlag"
          placeholder="回传状态"
          clearable
          @change="handleQuery"
        >
          <el-option label="无法回传" :value="0" />
          <el-option label="回传成功" :value="1" />
          <el-option label="扣取回传" :value="2" />
          <el-option label="手动回传" :value="5" />
        </el-select>
      </el-form-item>
      <el-form-item prop="payStatus">
        <el-select
          v-model="queryParams.payStatus"
          placeholder="支付状态"
          clearable
          @change="handleQuery"
        >
          <el-option label="未支付" :value="0" />
          <el-option label="支付成功" :value="1" />
          <el-option label="支付失败" :value="2" />
          <el-option label="已退款" :value="3" />
          <el-option label="退款中" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item prop="isAddWechat">
        <el-select
          v-model="queryParams.isAddWechat"
          placeholder="是否添加微信"
          clearable
          @change="handleQuery"
        >
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item prop="userName">
        <el-input
          v-model.trim="queryParams.userName"
          placeholder="用户账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <BusinessSelector :business.sync="queryParams.business" @query="handleQuery" />
      <DeptTreeSelector :dept-ids.sync="queryParams.deptIds" />
      <el-form-item v-has-Permi="['promotion:forms:pay-config']" prop="payConfigId">
        <el-select
          v-model="queryParams.payConfigId"
          placeholder="支付配置"
          clearable
          @change="handleQuery"
          @keyup.enter.native="handleQuery"
        >
          <el-option v-for="item in payConfigList" :key="item.id" :label="item.configName" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围" prop="resultRange">
        <el-date-picker
          v-model="resultRange"
          type="datetimerange"
          :picker-options="dateRangePickerOptions"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
          :clearable="false"
          :default-time="['00:00:00', '23:59:59']"
          @change="handleRangeChange"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:forms:results-export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:forms:results-settlement-export']"
          type="success"
          plain
          icon="el-icon-money"
          size="mini"
          @click="handleSettlementExport"
        >结算导出</el-button>
      </el-col>
      <el-col :span="2">
        <el-dropdown
          v-hasPermi="['promotion:forms:handle']"
          @command="handleBatchConvert"
        >
          <el-button type="primary" plain size="mini" icon="el-icon-refresh-right">
            批量回传
            <i class="el-icon-arrow-down el-icon--right" />
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="option in batchConvertOptions"
              :key="option.value"
              :command="option.value"
            >
              {{ option.label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>
      <el-col :span="1.5" class="fl-r">
        <el-button
          type="success"
          plain
          size="mini"
          icon="el-icon-data-line"
          @click="totalVisible = !totalVisible"
        >汇总数据
          <i
            :class="totalVisible ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
          />
        </el-button>
      </el-col>
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>

    <collapse-transition>
      <div v-show="totalVisible" class="statistics-card">
        <el-row :gutter="20">
          <!-- Submission Statistics -->
          <el-col :xs="24" :sm="8">
            <div class="statistics-section">
              <div class="section-header">
                <i class="el-icon-tickets" />
                <span>提交数据</span>
              </div>
              <div class="statistics-content">
                <div class="stat-item">
                  <span class="stat-label">提交数量</span>
                  <span class="stat-value" :class="{'zero-value': isZero(totalData.subCount)}">{{ formatNumber(totalData.subCount) }}</span>
                </div>
                <div class="stat-item combined-item">
                  <span class="stat-label">回传情况</span>
                  <div class="combined-values">
                    <div class="combined-value">
                      <span class="value-label">已回传:</span>
                      <span class="value-number" :class="{'zero-value': isZero(totalData.subConvertCount)}">{{ formatNumber(totalData.subConvertCount) }}</span>
                    </div>
                    <div class="combined-value">
                      <span class="value-label">扣回传:</span>
                      <span class="value-number" :class="{'zero-value': isZero(totalData.subDeductionCount)}">{{ formatNumber(totalData.subDeductionCount) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>

          <!-- Payment Statistics -->
          <el-col :xs="24" :sm="8">
            <div class="statistics-section">
              <div class="section-header">
                <i class="el-icon-money" />
                <span>支付数据</span>
              </div>
              <div class="statistics-content">
                <div class="stat-item combined-item">
                  <span class="stat-label">支付情况</span>
                  <div class="combined-values">
                    <div class="combined-value">
                      <span class="value-label">数量:</span>
                      <span class="value-number" :class="{'zero-value': isZero(totalData.payCount)}">{{ formatNumber(totalData.payCount) }}</span>
                    </div>
                    <div class="combined-value">
                      <span class="value-label">金额:</span>
                      <span class="value-number highlight" :class="{'zero-value': isZero(totalData.payAmount)}">{{ formatAmount(totalData.payAmount) }}</span>
                    </div>
                  </div>
                </div>
                <div class="stat-item combined-item">
                  <span class="stat-label">回传情况</span>
                  <div class="combined-values">
                    <div class="combined-value">
                      <span class="value-label">已回传:</span>
                      <span class="value-number" :class="{'zero-value': isZero(totalData.payConvertCount)}">{{ formatNumber(totalData.payConvertCount) }}</span>
                    </div>
                    <div class="combined-value">
                      <span class="value-label">扣回传:</span>
                      <span class="value-number" :class="{'zero-value': isZero(totalData.payDeductionCount)}">{{ formatNumber(totalData.payDeductionCount) }}</span>
                    </div>
                  </div>
                </div>
                <div class="stat-item combined-item">
                  <span class="stat-label">退款情况</span>
                  <div class="combined-values">
                    <div class="combined-value">
                      <span class="value-label">数量:</span>
                      <span class="value-number" :class="{'zero-value': isZero(totalData.refundCount)}">{{ formatNumber(totalData.refundCount) }}</span>
                    </div>
                    <div class="combined-value">
                      <span class="value-label">金额:</span>
                      <span class="value-number" :class="{'zero-value': isZero(totalData.refundAmount)}">{{ formatAmount(totalData.refundAmount) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>

          <!-- WeChat Statistics -->
          <el-col :xs="24" :sm="8">
            <div class="statistics-section">
              <div class="section-header">
                <i class="el-icon-chat-dot-square" />
                <span>微信数据</span>
              </div>
              <div class="statistics-content">
                <div class="stat-item combined-item">
                  <span class="stat-label">加微信情况</span>
                  <div class="combined-values">
                    <div class="combined-value">
                      <span class="value-label">已加微信:</span>
                      <span class="value-number" :class="{'zero-value': isZero(totalData.addCount)}">{{ formatNumber(totalData.addCount) }}</span>
                    </div>
                    <div class="combined-value">
                      <span class="value-label">未加微信:</span>
                      <span class="value-number" :class="{'zero-value': isZero(totalData.payNotAddCount)}">{{ formatNumber(totalData.payNotAddCount) }}</span>
                    </div>
                  </div>
                  <el-tooltip content="支付后是否添加微信" placement="top">
                    <i class="el-icon-question" />
                  </el-tooltip>
                </div>
                <div class="stat-item combined-item">
                  <span class="stat-label">回传情况</span>
                  <div class="combined-values">
                    <div class="combined-value">
                      <span class="value-label">已回传:</span>
                      <span class="value-number" :class="{'zero-value': isZero(totalData.addConvertCount)}">{{ formatNumber(totalData.addConvertCount) }}</span>
                    </div>
                    <div class="combined-value">
                      <span class="value-label">扣回传:</span>
                      <span class="value-number" :class="{'zero-value': isZero(totalData.addDeductionCount)}">{{ formatNumber(totalData.addDeductionCount) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </collapse-transition>
    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        v-bind="store.getters.tableHeight"
        border
        stripe
        :data="list"
        @selection-change="handleSelectionChange"
        @header-dragend="handleHeaderDragend"
      >
        <el-table-column type="selection" width="55" align="center" />
        <template v-for="(c, i) in columns">
          <template v-if="c.visible !== false">
            <!-- 表单信息 -->
            <template v-if="c.prop === 'formName'">
              <el-table-column :key="i" :label="c.label" align="left" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <BaseInfoCell :id="scope.row.formId" class="info-wrap" style="flex:1" :name="scope.row.formName" @nameClick="handleFormNameClick(scope.row)" />
                </template>
              </el-table-column>
            </template>

            <!-- 头像列特殊处理 -->
            <template v-else-if="c.prop === 'wechatInfo'">
              <el-table-column :key="i" :label="c.label" align="left" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <div class="flex">
                    <el-image
                      v-if="scope.row.wechatAvatar"
                      style="width: 32px; height: 32px; border-radius: 4px;margin-right: 10px;"
                      :src="scope.row.wechatAvatar"
                      :preview-src-list="[scope.row.wechatAvatar]"
                    />
                    <BaseInfoCell :id="scope.row.wechatGender === 1 ? '男' : scope.row.wechatGender === 2 ? '女' : ''" sub-label="" class="info-wrap" style="flex:1" :name="scope.row.wechatNickName" no-copy />
                  </div>
                </template>
              </el-table-column>
            </template>

            <!-- 媒体平台特殊处理 -->
            <template v-else-if="c.prop === 'mediaPlatformType'">
              <el-table-column :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <svg-icon
                    v-if="dictMap.media_type"
                    class-name="type-icon"
                    :icon-class="dictMap.media_type[scope.row.mediaPlatformType]"
                  />
                  <span v-else>-</span>
                  {{ dictMap.media_type[scope.row.mediaPlatformType] || '' }}
                </template>
              </el-table-column>
            </template>

            <!-- 回传信息特殊处理 -->
            <template v-else-if="c.prop === 'subConvertFlag'">
              <el-table-column :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <ConvertFlagColumn
                    :flag="scope.row.subConvertFlag"
                    :message="scope.row.subConvertMessage"
                    :result="scope.row"
                    type="subConvertFlag"
                    @refresh="getList"
                  />
                </template>
              </el-table-column>
            </template>

            <template v-else-if="c.prop === 'payConvertFlag'">
              <el-table-column :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <ConvertFlagColumn
                    :flag="scope.row.payConvertFlag"
                    :message="scope.row.payConvertMessage"
                    :result="scope.row"
                    type="payConvertFlag"
                    @refresh="getList"
                  />
                </template>
              </el-table-column>
            </template>

            <template v-else-if="c.prop === 'addConvertFlag'">
              <el-table-column :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <ConvertFlagColumn
                    :flag="scope.row.addConvertFlag"
                    :message="scope.row.addConvertMessage"
                    :result="scope.row"
                    type="addConvertFlag"
                    @refresh="getList"
                  />
                </template>
              </el-table-column>
            </template>

            <template v-else-if="c.prop === 'refundConvertFlag'">
              <el-table-column :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <ConvertFlagColumn
                    :flag="scope.row.refundConvertFlag"
                    :message="scope.row.refundConvertMessage"
                    :result="scope.row"
                    type="refundConvertFlag"
                    @refresh="getList"
                  />
                </template>
              </el-table-column>
            </template>

            <!-- 支付状态特殊处理 -->
            <template v-else-if="c.prop === 'payStatus'">
              <el-table-column :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <el-tag :type="payStatusColorMap[scope.row.payStatus]">
                    {{ payStatusMap[scope.row.payStatus] }}
                  </el-tag>
                </template>
              </el-table-column>
            </template>

            <!-- 会话状态特殊处理 -->
            <template v-else-if="c.prop === 'chatStatus'">
              <el-table-column :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <el-tag :type="scope.row.chatStatus === 1 ? 'success' : 'info'">
                    {{ scope.row.chatStatus === 1 ? '已发消息' : '未发消息' }}
                  </el-tag>
                </template>
              </el-table-column>
            </template>

            <!-- 用户填写信息特殊处理 -->
            <template v-else-if="c.prop === 'submitResult'">
              <el-table-column :key="i" :label="c.label" align="left" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <div v-if="scope.row.submitResult" class="submit-result-container">
                    <el-button
                      type="text"
                      size="mini"
                      class="view-more-btn"
                      @click="showDetails(scope.row)"
                    >
                      查看全部 ({{ parseSubmitResult(scope.row.submitResult).length }} 项)
                    </el-button>
                  </div>
                  <span v-else class="no-data">暂无数据</span>
                </template>
              </el-table-column>
            </template>

            <!-- 其他普通列 -->
            <CustomTableColumn v-else :key="i" :data="c" :render-map="renderMap" :sortable="!!c.sortable" />
          </template>
        </template>

        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <!-- <el-button
              size="mini"
              type="text"
              @click="showDetails(scope.row)"
            >用户提交信息</el-button> -->
            <el-button
              v-if="scope.row.payStatus === 1"
              v-hasPermi="['promotion:forms:results-refund']"
              size="mini"
              type="text"
              @click="handleRefund(scope.row)"
            >退款</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <ManualConvertDialog
      :visible.sync="batchConvertVisible"
      :type="batchConvertType"
      :result="batchConvertResult"
      :order-no-list="batchConvertOrderNos"
      multiple
      @refresh="handleBatchConvertRefresh"
    />

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :visible.sync="dialogVisible"
      title="用户提交信息"
      width="400px"
      append-to-body
    >
      <!-- <el-form>
        <el-form-item
          v-for="info in userJSON"
          :key="info.key"
          :label="info.title"
        >
          {{ info.value }}
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onHandle">已处理提交信息</el-button>
          <el-button @click="dialogVisible = false">关闭</el-button>
        </el-form-item>
      </el-form> -->
      <div class="submit-result-container">
        <div
          v-for="(item, index) in userJSON"
          :key="index"
          class="submit-result-item"
        >
          <div class="result-title">{{ item.title }}</div>
          <div class="result-value">{{ item.value }}</div>
        </div>
      </div>

    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'FormResults',
  dicts: ['form_report_event_ocean', 'form_report_event_tencent']
}
</script>
<script setup>
import { onMounted, reactive, ref } from 'vue'
import { Message, MessageBox } from 'element-ui'
import { exportResult, listResults, refundFormResult, getFormTotal, getUsablePayConfigList, settlementExport } from '@/api/form/form'
import { listAuthCorp } from '@/api/wx/authCorp'
import { dateRangePickerOptions } from '@/config'
import { deleteWhitespace } from '@/utils/StrUtil'
import { useRoute, useRouter } from 'vue-router/composables'
import dayjs from 'dayjs'
import { useStore } from '@/store'
import useDicts from '@/hooks/useDicts'
import useColumns from '@/hooks/useColumns'
import CollapseTransition from '@/components/Collapse'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import DeptTreeSelector from '@/components/DeptTreeSelect/DeptTreeSelector.vue'
import CustomTableColumn from '@/components/CustomTable/CustomTableColumn.vue'
import useStoreTableScroller from '@/hooks/useStoreTableScroller'
import ConvertFlagColumn from './components/ConvertFlagColumn.vue'
import ManualConvertDialog from './components/ManualConvertDialog.vue'
import BusinessSelector from '@/components/BusinessSelector/index.vue'

const { dictMap } = useDicts(['media_type'])
const route = useRoute()
const router = useRouter()
const store = useStore()
const loading = ref(false)
const showSearch = ref(true)
const form = ref({})
const tableRef = ref(null)
const totalVisible = ref(false)
const list = ref([])
const totalData = ref({})
const total = ref(0)
const payConfigList = ref([])
const authCorpList = ref([])
const multipleSelection = ref([])
const batchConvertVisible = ref(false)
const batchConvertType = ref('')
const batchConvertOrderNos = ref([])
const batchConvertResult = ref({})
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  mediaPlatformType: null,
  orderNo: null,
  payNo: null,
  receiverName: null,
  name: null,
  mobile: null,
  payStatus: null,
  isAddWechat: null,
  formId: null,
  deptIds: [],
  payConfigId: null
})

// Format amount to display as currency (¥)
const formatAmount = (amount) => {
  if (!amount && amount !== 0) return '¥0.00'
  return `¥${amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
}

// Format large numbers with thousand separators
const formatNumber = (num) => {
  if (num === undefined || num === null || num === 0) return '0'
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// Check if number is zero for styling
const isZero = (num) => {
  return num === undefined || num === null || num === 0
}

const resultRange = ref([
  dayjs(new Date()).startOf('day').valueOf(),
  dayjs(new Date()).endOf('day').valueOf()
])

const payStatusMap = ['未支付', '支付成功', '支付失败', '已退款', '退款中']
const payStatusColorMap = {
  0: 'info',
  1: 'success',
  2: 'danger',
  3: 'info',
  4: 'warning'
}
// const convertFlagMap = {
//   // null: '未回传',
//   0: '无法回传',
//   1: '已回传',
//   2: '扣取回传'
// }
// 1、企业微信获客助手页面 2、H5落地页、3微信小程序原生页
const formTypeMap = {
  1: '企业微信获客助手页面',
  2: 'H5落地页',
  3: '微信小程序原生页',
  4: '审核页'
}

const batchConvertOptions = [
  { label: '提交回传', value: 'subConvertFlag' },
  { label: '支付回传', value: 'payConvertFlag' },
  { label: '加微回传', value: 'addConvertFlag' },
  { label: '退款回传', value: 'refundConvertFlag' }
]

// 渲染函数映射
const renderMap = {
  formType: (row) => {
    return formTypeMap[row.formType] || '-'
  },
  wechatGender: (row) => {
    return row.wechatGender === 1 ? '男' : row.wechatGender === 2 ? '女' : '未知'
  },
  wechatAddTime: (row) => {
    return row.wechatAddTime ? dayjs(row.wechatAddTime).format('YYYY-MM-DD HH:mm:ss') : '-'
  },
  chatStatus: (row) => {
    return row.chatStatus === 1 ? '已发消息' : '未发消息'
  },
  payStatus: (row) => {
    return payStatusMap[row.payStatus] || '-'
  },
  payAmount: (val) => {
    return val ? (val / 100).toFixed(2) : '-'
  },
  payTime: (val) => {
    return val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '-'
  },
  payType: (val) => {
    return val ? (val === 1 ? '微信H5支付' : val === 2 ? '支付宝H5支付' : val === 3 ? '微信公众号支付' : '-') : '-'
  },
  createTime: (row) => {
    return row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
  },
  availableDownloadCount: (row) => {
    return row.availableDownloadCount === -1 ? '无限' : row.availableDownloadCount
  },
  business: (row) => {
    return row.business ? row.business : '-'
  },
  isOpenFeedback: (row) => {
    if (row.isOpenFeedback === undefined) return '-'
    return row.isOpenFeedback ? '开启' : '关闭'
  }
}

// 默认列配置 - 展开所有列
const defaultColumns = [
  { prop: 'mediaPlatformType', label: '媒体平台', width: 80 },
  { prop: 'formName', label: '表单名称', width: 200 },
  { prop: 'formType', label: '表单类型', width: 140, render: true },
  { prop: 'userInfo', label: '用户信息', width: 175, labelWidth: 50,
    set: [
      {
        label: '姓名',
        prop: 'name'
      },
      {
        label: '手机号',
        prop: 'mobile'
      }
    ]
  },
  { prop: 'wechatInfo', label: '微信信息', width: 180,
    set: [
      {
        label: '微信昵称',
        prop: 'wechatNickName'
      },
      {
        label: '性别',
        prop: 'wechatGender'
      },
      {
        label: '头像',
        prop: 'wechatAvatar'
      }
    ]
  },
  { prop: 'wechatAddTime', label: '添加时间', width: 160, render: true },
  { prop: 'chatStatus', label: '会话状态', width: 100, render: true },
  { prop: 'submitResult', label: '用户填写信息', width: 300 },
  { prop: 'receiveUserName', label: '接粉账号', width: 100, overflow: true },
  { prop: 'availableDownloadCount', label: '可下载次数', width: 100, render: true },
  { prop: 'payStatus', label: '支付状态', width: 100, render: true },
  { prop: 'payInfo', label: '支付信息', width: 230, labelWidth: 58,
    set: [
      {
        label: '支付金额',
        prop: 'payAmount',
        render: true
      },
      {
        label: '支付时间',
        prop: 'payTime',
        render: true
      },
      {
        label: '支付方式',
        prop: 'payType',
        render: true
      }
    ]
  },
  { prop: 'orderNo', label: '订单号信息', width: 273,
    set: [
      {
        label: '商户订单号',
        prop: 'orderNo',
        copy: true
      },
      {
        label: '支付订单号',
        prop: 'payNo',
        copy: true
      }
    ]
  },
  { prop: 'accountInfo', label: '账户计划信息', width: 230, labelWidth: 50,
    set: [
      {
        label: '账户ID',
        prop: 'advertiserId'
      },
      {
        label: '计划ID',
        prop: 'planId'
      }
    ]
  },
  { prop: 'isOpenFeedback', label: '开口回传', width: 100, render: true },
  { prop: 'subConvertFlag', label: '提交回传', width: 100 },
  { prop: 'payConvertFlag', label: '支付回传', width: 100 },
  { prop: 'addConvertFlag', label: '加微回传', width: 100 },
  { prop: 'refundConvertFlag', label: '退款回传', width: 100 },
  { prop: 'payConfig', label: '支付配置', width: 230, labelWidth: 60, permissions: ['promotion:forms:pay-config'],
    set: [
      {
        label: '配置ID',
        prop: 'payConfigId'
      },
      {
        label: '配置名称',
        prop: 'payConfigName'
      }
    ]
  },
  { prop: 'Dept', label: '公司信息', width: 160, labelWidth: 40,
    set: [
      { label: `公司`, prop: 'firstDeptName' },
      { label: `部门`, prop: 'deptName' }
    ]
  },
  { label: `用户账号`, prop: 'userName', width: '120' },
  { label: `商务负责人`, prop: 'business', width: '150', render: true, permissions: ['promotion:config:duoduo'] },
  { label: `商务名称`, prop: 'businessName', width: '120' },
  { prop: 'createTime', label: '创建时间', width: 160, render: true },
  { prop: 'submitResult', label: '用户填写信息', width: 130 }
]

// Parse submit result JSON string
const parseSubmitResult = (submitResultStr) => {
  try {
    if (!submitResultStr) return []
    const result = JSON.parse(submitResultStr)
    if (Array.isArray(result)) {
      return result
    }
    return []
  } catch (error) {
    console.error('解析用户填写信息失败:', error)
    return []
  }
}

// 使用自定义列钩子
const { columnsInstance, columns, operatedColumns, customList, handleHeaderDragend } = useColumns({
  defaultColumns,
  tableRef,
  name: 'formResults'
})

// 使用表格滚动记忆钩子
useStoreTableScroller(tableRef)

const getPayConfigList = () => {
  getUsablePayConfigList().then(res => {
    payConfigList.value = res.data
  })
}
function getList() {
  setQueryRange()
  loading.value = true
  listResults(queryParams).then((response) => {
    list.value = response.rows
    total.value = response.total
    loading.value = false
  })
  getFormTotal({
    ...queryParams,
    pageNum: undefined,
    pageSize: undefined
  }).then((response) => {
    totalData.value = response.data
  })
}

listAuthCorp({
  pageNum: 1,
  pageSize: 9999
}).then(response => {
  authCorpList.value = response.rows
})

const handleRangeChange = () => {
  getList()
}

const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

const handleBatchConvert = (type) => {
  if (!multipleSelection.value.length) {
    Message.warning('请先选择需要回传的记录')
    return
  }
  if (!type) return

  const reference = multipleSelection.value[0]
  const isSameForm = multipleSelection.value.every(item => item.mediaPlatformType === reference.mediaPlatformType)
  if (!isSameForm) {
    Message.warning('批量回传要求选择媒体类型相同的数据')
    return
  }

  const orderNos = multipleSelection.value
    .map(item => item.orderNo)
    .filter(orderNo => !!orderNo)

  if (!orderNos.length) {
    Message.warning('所选数据缺少商户订单号，无法回传')
    return
  }

  batchConvertResult.value = { ...reference }
  batchConvertType.value = type
  batchConvertOrderNos.value = orderNos
  batchConvertVisible.value = true
}

function handleBatchConvertRefresh() {
  if (tableRef.value && typeof tableRef.value.clearSelection === 'function') {
    tableRef.value.clearSelection()
  }
  multipleSelection.value = []
  getList()
}

const setQueryRange = () => {
  if (!resultRange.value) {
    resultRange.value = [
      dayjs(new Date()).startOf('day').valueOf(),
      dayjs(new Date()).endOf('day').valueOf()
    ]
  }
  queryParams.params = {
    beginTime: dayjs(resultRange.value[0]).format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs(resultRange.value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
}

onMounted(() => {
  const formId = route.params && route.params.formId
  if (formId) {
    queryParams.formId = formId
  }
  getPayConfigList()
  getList()
})

function handleQuery() {
  queryParams.pageNum = 1
  deleteWhitespace(queryParams, ['orderNo', 'payNo', 'mobile'])
  getList()
}

const queryFormRef = ref(null)
function resetQuery() {
  queryFormRef.value.resetFields()
  handleQuery()
}

const dialogVisible = ref(false)
const userJSON = ref({})
const showDetails = (row) => {
  form.value = row
  dialogVisible.value = true
  userJSON.value = JSON.parse(row.submitResult)
}

// const onHandle = () => {
//   handleForm(form.value.id).then(() => {
//     Message.success('处理成功')
//     dialogVisible.value = false
//     getList()
//   })
// }

function handleExport() {
  setQueryRange()
  const params = {
    ...queryParams,
    pageNum: undefined,
    pageSize: undefined
  }
  MessageBox.confirm('即将导出数据，是否继续？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    exportResult(params).then((res) => {
      Message.success(res.msg)
    })
  })
}

function handleSettlementExport() {
  setQueryRange()
  const params = {
    ...queryParams,
    pageNum: undefined,
    pageSize: undefined
  }
  MessageBox.confirm('即将导出数据，是否继续？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    settlementExport(params).then((res) => {
      Message.success(res.msg)
    })
  })
}

const handleRefund = (row) => {
  MessageBox.confirm('确认要对该订单进行退款操作吗？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    refundFormResult(row.orderNo).then(() => {
      Message.success('退款操作成功')
      getList()
    })
  })
}

const handleFormNameClick = (row) => {
  router.push({
    path: '/h5/form',
    query: {
      formId: row.formId
    }
  })
}
</script>

<style lang="scss" scoped>
.submitResult_style {
  display: flex;
  > span:nth-child(1) {
    color: #222;
    min-width: 40px;
    text-align: right;
  }
}

/* 用户填写信息样式 */
.submit-result-container {
  overflow: hidden;
  position: relative;

  .submit-result-item {
    margin-bottom: 6px;
    padding: 4px 6px;
    background-color: #f8f9fa;
    border-radius: 3px;
    font-size: 16px;
    line-height: 1.3;
    border-left: 2px solid #e4e7ed;

    .result-title {
      color: #606266;
      font-weight: 500;
      margin-bottom: 2px;
      font-size: 14px;
      display: block;
    }

    .result-value {
      color: #303133;
      word-break: break-all;
      display: block;
      max-height: 32px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    &:last-child {
      margin-bottom: 0;
    }

    &:nth-child(n+4) {
      display: none;
    }
  }

  .view-more-btn {
    color: #409eff;

    &:hover {
      color: #66b1ff;
    }
  }
}

.no-data {
  color: #c0c4cc;
  font-size: 12px;
}
::v-deep .tag_style {
  .el-tag--success {
    cursor: pointer;
  }
}

/* Statistics Card Styles */
.statistics-card {
  margin-bottom: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  background: #fff;
  border-radius: 4px;
  padding: 16px;

  .statistics-section {
    height: 100%;

    .section-header {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 12px;
      color: #303133;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 8px;

      i {
        margin-right: 5px;
        font-size: 16px;
      }
    }

    .statistics-content {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 10px;

      .stat-item {
        display: flex;
        flex-direction: column;
        font-size: 13px;
        position: relative;
        padding: 10px 12px;
        border-radius: 4px;
        background-color: #f8f9fa;
        transition: all 0.2s ease;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

        &:hover {
          background-color: #f2f6fc;
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        }

        .stat-label {
          color: #909399;
          font-size: 13px;
          margin-bottom: 6px;
          white-space: nowrap;
          font-weight: 500;
        }

        .stat-value {
          color: #303133;
          font-weight: 500;
          font-size: 15px;

          &.highlight {
            color: #f56c6c;
            font-weight: 600;
          }

          &.zero-value {
            color: #c0c4cc;
            font-weight: normal;
          }
        }

        i {
          position: absolute;
          right: 8px;
          top: 8px;
          color: #909399;
          font-size: 12px;
          cursor: help;
        }

        &.combined-item {
          min-height: 80px;

          .combined-values {
            display: flex;
            flex-direction: column;
            gap: 8px;
            width: 100%;
            padding-right: 16px; /* Space for tooltip icon */

            .combined-value {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .value-label {
                color: #606266;
                font-size: 12px;
                flex-shrink: 0;
                margin-right: 8px;
              }

              .value-number {
                font-weight: 500;
                color: #303133;
                text-align: right;

                &.highlight {
                  color: #f56c6c;
                  font-weight: 600;
                }

                &.zero-value {
                  color: #c0c4cc;
                  font-weight: normal;
                }
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: 992px) {
    .statistics-content {
      grid-template-columns: repeat(2, 1fr) !important;
    }
  }

  @media (max-width: 768px) {
    .el-col {
      margin-bottom: 16px;
    }

    .statistics-content {
      grid-template-columns: repeat(1, 1fr) !important;
    }
  }

  @media (max-width: 576px) {
    .statistics-content {
      grid-template-columns: repeat(1, 1fr) !important;

      .stat-item {
        &.combined-item {
          .combined-values {
            .combined-value {
              flex-direction: row;
              flex-wrap: wrap;
            }
          }
        }
      }
    }
  }
}
</style>
