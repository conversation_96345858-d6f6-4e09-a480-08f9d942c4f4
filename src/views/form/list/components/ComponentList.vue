<script setup>
import { computed } from 'vue'
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  list: {
    type: Array,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  },
  draggable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['add'])

const componentsListClass = computed(() => 'components-list ' + (props.disabled ? 'disabled' : ''))

const addComponent = (item) => {
  if (props.disabled) return
  emit('add', item)
}

const handleDragStart = (e, item) => {
  if (props.disabled) return
  e.dataTransfer.setData('drag-cmp', JSON.stringify(item))
}
</script>

<template>
  <div>
    <div v-if="title" class="components-title">{{ title }}</div>
    <div :class="componentsListClass">
      <div v-for="item in list" :key="item.label" class="component-item" :draggable="!props.disabled && draggable" @click="addComponent(item)" @dragstart="handleDragStart($event, item)">
        <div>
          <i :class="'component-icon el-icon-'+item.icon" />
        </div>
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.components-title {
  font-size: 14px;
  color: #333;
  margin: 8px 0;
}
.components-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 5px;
  .component-item {
    padding: 10px;
    border: 1px solid #DCDFE6;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    user-select: none;
    transition: all 0.3s;
    &:hover {
      box-shadow: 0 0 10px 0 #d7d7d7;
    }
    &:active {
      box-shadow: none;
      background: #F5F7FA;
    }
  }
  .component-icon {
    margin-bottom: 5px;
    font-size: 20px;
    color: #409EFF
  }
}

.disabled .component-item {
  cursor: not-allowed;
  opacity: 0.5;
  &:hover {
    box-shadow: none;
  }
  &:active {
    box-shadow: none;
    background: white;
  }
}
</style>
