<template>
  <div>
    <div ref="chart" :class="className" :style="{height:height,width:width}" />
  </div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts/theme/macarons'// echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    chartData: {
      type: Array,
      default: () => [],
      required: true
    },
    chartOption: {
      type: Object,
      default: () => {}
    },
    timeList: {
      type: Array,
      default: () => []
    },
    multi: {
      type: Boolean,
      default: false
    },
    selector: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        if (val && this.chart) { this.setOptions(val) }
      }
    }
  },
  mounted() {
    setTimeout(() => {
      this.initChart()
    }, 500)
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions(chartData = []) {
      const colors = ['#2563eb', '#f59e0b', '#10b981', '#ef4444', '#8b5cf6', '#ec4899', '#06b6d4', '#14b8a6']

      // 确保数据格式正确
      let processedData = []
      if (this.multi && Array.isArray(chartData)) {
        processedData = chartData.map(item => {
          // 确保数据项包含必要的属性
          if (!item || typeof item !== 'object') return { title: '', data: [] }

          let data = item.data || []
          // 处理百分比数据
          if (Array.isArray(data) && data.length > 0 && typeof data[0] === 'string' && data[0].includes('%')) {
            data = data.map(val => parseFloat(val.replace('%', '')))
          }

          return {
            ...item,
            title: item.title || '',
            data: Array.isArray(data) ? data : []
          }
        })
      } else {
        // 非多系列数据，确保是数组
        processedData = Array.isArray(chartData) ? chartData : []
      }

      const option = {
        title: {
          text: this.title,
          left: 'center',
          textStyle: {
            fontSize: '16px',
            fontWeight: 500,
            color: '#1e293b'
          },
          padding: [20, 0]
        },
        xAxis: {
          data: Array.isArray(this.timeList) ? this.timeList : [],
          boundaryGap: false,
          axisTick: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#e2e8f0'
            }
          },
          axisLabel: {
            color: '#64748b',
            fontSize: 12,
            padding: [8, 0]
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: this.title ? 60 : 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#e2e8f0',
          borderWidth: 1,
          padding: [10, 15],
          textStyle: {
            color: '#1e293b'
          },
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: '#2563eb',
              width: 2
            }
          },
          formatter: (params) => {
            if (!Array.isArray(params)) return ''

            let result = `<div style="font-weight: 500; margin-bottom: 8px;">${params[0].axisValue || ''}</div>`
            params.forEach(param => {
              if (!param) return
              result += `
                <div style="display: flex; justify-content: space-between; margin: 3px 0;">
                  <span style="margin-right: 15px;">
                    <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: ${param.color || colors[0]}; margin-right: 8px;"></span>
                    ${param.seriesName || ''}
                  </span>
                  <span style="font-weight: 500;">${param.value !== undefined ? param.value : ''}</span>
                </div>`
            })
            return result
          }
        },
        yAxis: {
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            color: '#64748b',
            fontSize: 12,
            padding: [0, 8]
          },
          splitLine: {
            lineStyle: {
              color: '#e2e8f0',
              type: 'dashed'
            }
          }
        }
      }

      if (this.multi) {
        const series = processedData.map((item, i) => ({
          name: item.title,
          smooth: true,
          type: 'line',
          symbolSize: 8,
          symbol: 'circle',
          itemStyle: {
            color: colors[i % colors.length],
            borderWidth: 2,
            borderColor: '#ffffff'
          },
          lineStyle: {
            width: 3,
            color: colors[i % colors.length],
            shadowColor: `${colors[i % colors.length]}33`,
            shadowBlur: 10
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: `${colors[i % colors.length]}15`
            }, {
              offset: 1,
              color: `${colors[i % colors.length]}03`
            }])
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              borderWidth: 3,
              shadowColor: `${colors[i % colors.length]}66`,
              shadowBlur: 10
            }
          },
          data: item.data,
          animationDuration: 1500,
          animationEasing: 'cubicInOut'
        }))

        option.series = series

        option.legend = {
          show: true,
          type: 'scroll',
          top: 0,
          right: 0,
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 20,
          icon: 'circle',
          textStyle: {
            fontSize: 13,
            color: '#64748b',
            fontWeight: 500
          },
          data: processedData.map(item => item.title),

          pageIconColor: '#2563eb',
          pageTextStyle: {
            color: '#64748b'
          },
          // 添加图例切换事件处理
          selectedMode: 'multiple',
          selectorLabel: {
            fontSize: 12,
            color: '#64748b'
          },
          selectorPosition: 'end'
        }
        if (this.selector) {
          option.legend.selector = [
            {
              type: 'all',
              title: '全选'
            },
            {
              type: 'inverse',
              title: '反选'
            }
          ]
        }
      } else {
        option.series = [{
          name: this.title || '',
          smooth: true,
          type: 'line',
          symbolSize: 8,
          symbol: 'circle',
          itemStyle: {
            color: colors[0],
            borderWidth: 2,
            borderColor: '#ffffff'
          },
          lineStyle: {
            width: 3,
            color: colors[0],
            shadowColor: 'rgba(37, 99, 235, 0.2)',
            shadowBlur: 10
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: 'rgba(37, 99, 235, 0.15)'
            }, {
              offset: 1,
              color: 'rgba(37, 99, 235, 0.03)'
            }])
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              borderWidth: 3,
              shadowColor: 'rgba(37, 99, 235, 0.5)',
              shadowBlur: 10
            }
          },
          data: Array.isArray(processedData) ? processedData : [],
          animationDuration: 1500,
          animationEasing: 'cubicInOut'
        }]
      }

      this.chart.setOption(Object.assign(option, this.chartOption))
    }
  }
}
</script>

<style>
</style>
