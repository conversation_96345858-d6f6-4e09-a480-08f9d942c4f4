<template>
  <div v-if="templateConfig" class="template-preview-wrapper">
    <div :class="templateConfig.containerClass || 'template-preview-container'" v-html="renderedHtml" />
  </div>

  <div v-else class="template-preview-placeholder">
    <div class="coming-soon">{{ getPlaceholderText(templateStyle) }}</div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'
import { useTemplatePreview, useTemplateRenderer } from '@/hooks/useTemplateRenderer.js'

const props = defineProps({
  templateStyle: {
    type: String,
    default: 'template1'
  },
  templateImage: {
    type: String,
    default: ''
  },
  templateGoodsName: {
    type: String,
    default: ''
  },
  templatePrice: {
    type: String,
    default: ''
  },
  templateButtonText: {
    type: String,
    default: '立即购买'
  },
  templateCompanyName: {
    type: String,
    default: ''
  }
})

// 构建模板数据
const templateData = computed(() => ({
  templateImage: props.templateImage,
  templateGoodsName: props.templateGoodsName,
  templatePrice: props.templatePrice,
  templateButtonText: props.templateButtonText,
  companyName: props.templateCompanyName
}))

// 使用统一的模板渲染器
const { getTemplateConfig, renderHtmlString } = useTemplateRenderer()
const { countdownTime } = useTemplatePreview(props.templateStyle, templateData.value)

// 获取模板配置
const templateConfig = computed(() => {
  return getTemplateConfig(props.templateStyle)
})

// 渲染的HTML内容
const renderedHtml = computed(() => {
  if (!templateConfig.value) return ''

  try {
    const result = renderHtmlString(props.templateStyle, {
      ...templateData.value,
      countdownTime: countdownTime.value
    })

    return result.html
  } catch (error) {
    console.error('模板预览渲染失败:', error)
    return '<div class="error-message">模板渲染失败</div>'
  }
})

// 获取占位符文本
const getPlaceholderText = (templateStyle) => {
  const textMap = {
    'template2': '简约风格模板敬请期待',
    'template3': '奢华品牌模板敬请期待'
  }
  return textMap[templateStyle] || '模板敬请期待'
}

// 动态注入样式
const dynamicStyles = computed(() => {
  if (!templateConfig.value) return ''

  try {
    const result = renderHtmlString(props.templateStyle, {
      ...templateData.value,
      countdownTime: countdownTime.value
    })

    return result.css
  } catch (error) {
    return ''
  }
})

// 注入CSS到页面
watch(dynamicStyles, (newStyles) => {
  if (newStyles) {
    let styleEl = document.getElementById('template-preview-styles')
    if (!styleEl) {
      styleEl = document.createElement('style')
      styleEl.id = 'template-preview-styles'
      document.head.appendChild(styleEl)
    }
    styleEl.textContent = newStyles
  }
}, { immediate: true })
</script>

<style scoped lang="scss">
.template-preview-wrapper {
  width: 100%;
  height: 100%;
  min-height: 730px;
  position: relative;
  display: flex;
  flex-direction: row;
}

.template-preview-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 2px dashed #ddd;
  border-radius: 8px;
  margin: 20px;
}

.template-preview-placeholder .coming-soon {
  font-size: 16px;
  color: #999;
  text-align: center;
}

.error-message {
  padding: 20px;
  text-align: center;
  color: #f56565;
  background: #fed7d7;
  border-radius: 8px;
  margin: 20px;
}

/* 全局样式重置，确保模板样式不被覆盖 */
:deep(.template-preview-container) {
  flex:1;
  display: flex;
  flex-direction: column;
  background: #fff;
  position: relative;
  width: 100%;
}
:deep(.template-page) {
  min-height: 100%;
  margin:0;
}
</style>
