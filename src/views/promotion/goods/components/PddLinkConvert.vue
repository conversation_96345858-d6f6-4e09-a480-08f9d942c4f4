<template>
  <div class="pdd-link-convert-container">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="媒体类型" prop="mediaType">
        <el-select v-model="form.mediaType" placeholder="请选择媒体类型" clearable @change="onMediaChange">
          <el-option
            v-for="dict in MediaTypes"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <svg-icon :icon-class="dict.label" />
            <span> {{ dict.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商品链接" prop="goodsLink">
        <el-input
          v-model="form.goodsLink"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4}"
          placeholder="请输入拼多多商品链接"
          clearable
        />
      </el-form-item>
    </el-form>
    <div v-if="convertResult" class="result-container">
      <div class="result-header">
        <el-divider content-position="left">转换结果</el-divider>
        <div class="result-actions">
          <el-button v-clipboard:copy="allLinksText" v-clipboard:success="clipboardSuccess" size="mini" type="primary" icon="el-icon-document-copy">
            复制全部链接
          </el-button>
        </div>
      </div>

      <!-- 抖音媒体类型 - 只显示 dpUrl 和 landingPageUrl -->
      <template v-if="form.mediaType === '1'">
        <el-form label-width="120px">
          <el-form-item v-if="convertResult.txOriginalUrlVO && convertResult.txOriginalUrlVO.dpUrl" label="直达链接">
            <UrlClipboard :url="convertResult.txOriginalUrlVO.dpUrl" />
          </el-form-item>
          <el-form-item v-if="convertResult.txOriginalUrlVO && convertResult.txOriginalUrlVO.landingPageUrl" label="一跳落地页">
            <UrlClipboard :url="convertResult.txOriginalUrlVO.landingPageUrl" />
          </el-form-item>
          <el-form-item v-if="monitorUrl" label="触点监测链接">
            <url-clipboard :url="monitorUrl" />
            <div class="color-danger">提示：当前商品广告必须配置该触点监测链接。</div>
          </el-form-item>
        </el-form>
      </template>

      <!-- 腾讯媒体类型 - 显示更多字段 -->
      <template v-else-if="form.mediaType === '4'">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="原始推广页" name="original">
            <el-form v-if="convertResult.txOriginalUrlVO" label-width="120px">
              <el-form-item v-if="convertResult.txOriginalUrlVO.landingPageUrl" label="一跳落地页">
                <UrlClipboard :url="convertResult.txOriginalUrlVO.landingPageUrl" />
              </el-form-item>
              <el-form-item v-if="convertResult.txOriginalUrlVO.iosId" label="苹果ID">
                <UrlClipboard :url="convertResult.txOriginalUrlVO.iosId" not-redirect />
              </el-form-item>
              <el-form-item v-if="convertResult.txOriginalUrlVO.androidId" label="安卓ID">
                <UrlClipboard :url="convertResult.txOriginalUrlVO.androidId" not-redirect />
              </el-form-item>
              <el-form-item v-if="convertResult.txOriginalUrlVO.dpUrl" label="直达链接">
                <UrlClipboard :url="convertResult.txOriginalUrlVO.dpUrl" />
              </el-form-item>
              <el-form-item v-if="convertResult.txOriginalUrlVO.universalLink" label="通用链接">
                <UrlClipboard :url="convertResult.txOriginalUrlVO.universalLink" />
              </el-form-item>
              <el-form-item v-if="convertResult.txOriginalUrlVO.monitorUrl" label="监测链接">
                <UrlClipboard :url="convertResult.txOriginalUrlVO.monitorUrl" />
                <div class="color-danger">提示：当前商品广告必须配置该监测链接。</div>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <el-tab-pane label="小程序转化" name="applet">
            <el-form v-if="convertResult.txAppletUrlVO" label-width="120px">
              <el-form-item v-if="convertResult.txAppletUrlVO.appletId" label="小程序ID">
                <UrlClipboard :url="convertResult.txAppletUrlVO.appletId" not-redirect />
              </el-form-item>
              <el-form-item v-if="convertResult.txAppletUrlVO.originalId" label="原始ID">
                <UrlClipboard :url="convertResult.txAppletUrlVO.originalId" not-redirect />
              </el-form-item>
              <el-form-item v-if="convertResult.txAppletUrlVO.appletUrl" label="小程序URL">
                <UrlClipboard :url="convertResult.txAppletUrlVO.appletUrl" not-redirect />
              </el-form-item>
              <el-form-item v-if="convertResult.txAppletUrlVO.monitorUrl" label="点击监测链接">
                <UrlClipboard :url="convertResult.txAppletUrlVO.monitorUrl" />
                <div class="color-danger">提示：当前商品广告必须配置该点击监测链接。</div>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </template>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="loading" @click="handleConvert">转换链接</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </div>
  </div>
</template>

<script>
import { getPddLinkConvert } from '@/api/promotion/goods'
import { getConfigKey } from '@/api/system/config'
import { parseParams } from '@/utils'
import UrlClipboard from '@/views/promotion/goods/components/UrlClipboard.vue'

export default {
  name: 'PddLinkConvert',
  components: {
    UrlClipboard
  },
  data() {
    return {
      form: {
        mediaType: '',
        goodsLink: ''
      },
      rules: {
        mediaType: [
          { required: true, message: '请选择媒体类型', trigger: 'change' }
        ],
        goodsLink: [
          { required: true, message: '请输入拼多多商品链接', trigger: 'blur' }
        ]
      },
      loading: false,
      convertResult: null,
      monitorUrl: '',
      activeTab: 'original',
      MediaTypes: [
        { label: '抖音', value: '1' },
        { label: '腾讯', value: '4' }
      ]
    }
  },
  dicts: ['media_type'],
  computed: {
    allLinksText() {
      if (!this.convertResult) return ''

      let text = '多多转链转换结果\n\n'

      if (this.form.mediaType === '1') {
        // 抖音媒体类型
        if (this.convertResult.txOriginalUrlVO) {
          if (this.convertResult.txOriginalUrlVO.dpUrl) {
            text += `直达链接: ${this.convertResult.txOriginalUrlVO.dpUrl}\n\n`
          }
          if (this.convertResult.txOriginalUrlVO.landingPageUrl) {
            text += `一跳落地页: ${this.convertResult.txOriginalUrlVO.landingPageUrl}\n\n`
          }
          if (this.monitorUrl) {
            text += `触点监测链接: ${this.monitorUrl}\n\n`
          }
        }
      } else if (this.form.mediaType === '4') {
        // 腾讯媒体类型
        text += '原始推广页:\n'
        if (this.convertResult.txOriginalUrlVO) {
          if (this.convertResult.txOriginalUrlVO.landingPageUrl) {
            text += `一跳落地页: ${this.convertResult.txOriginalUrlVO.landingPageUrl}\n`
          }
          if (this.convertResult.txOriginalUrlVO.iosId) {
            text += `苹果ID: ${this.convertResult.txOriginalUrlVO.iosId}\n`
          }
          if (this.convertResult.txOriginalUrlVO.androidId) {
            text += `安卓ID: ${this.convertResult.txOriginalUrlVO.androidId}\n`
          }
          if (this.convertResult.txOriginalUrlVO.dpUrl) {
            text += `直达链接: ${this.convertResult.txOriginalUrlVO.dpUrl}\n`
          }
          if (this.convertResult.txOriginalUrlVO.universalLink) {
            text += `通用链接: ${this.convertResult.txOriginalUrlVO.universalLink}\n`
          }
          if (this.convertResult.txOriginalUrlVO.monitorUrl) {
            text += `监测链接: ${this.convertResult.txOriginalUrlVO.monitorUrl}\n`
          }
        }

        text += '\n小程序转化:\n'
        if (this.convertResult.txAppletUrlVO) {
          if (this.convertResult.txAppletUrlVO.appletId) {
            text += `小程序ID: ${this.convertResult.txAppletUrlVO.appletId}\n`
          }
          if (this.convertResult.txAppletUrlVO.originalId) {
            text += `原始ID: ${this.convertResult.txAppletUrlVO.originalId}\n`
          }
          if (this.convertResult.txAppletUrlVO.appletUrl) {
            text += `小程序URL: ${this.convertResult.txAppletUrlVO.appletUrl}\n`
          }
          if (this.convertResult.txAppletUrlVO.monitorUrl) {
            text += `点击监测链接: ${this.convertResult.txAppletUrlVO.monitorUrl}\n`
          }
        }
      }

      return text
    }
  },
  methods: {
    handleConvert() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.loading = true
          if (this.form.mediaType === '1') this.getMonitorUrl()
          getPddLinkConvert({
            goodsLink: this.form.goodsLink,
            mediaType: this.form.mediaType
          }).then(response => {
            if (response.code === 200) {
              this.convertResult = response.data
              this.$message.success('链接转换成功')
            } else {
              this.$message.error(response.msg || '链接转换失败')
            }
          }).catch(() => {
            this.$message.error('链接转换失败')
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    getMonitorUrl() {
      getConfigKey('monitor_link').then(res => {
        this.monitorUrl = parseParams(res.msg, { goodsId: new URL(this.form.goodsLink).searchParams.get('goods_id'), platform: 1 })
      })
    },
    handleCancel() {
      this.$emit('cancel')
    },
    clipboardSuccess() {
      this.$message.success('复制成功')
    },
    reset() {
      this.form = {
        mediaType: '',
        goodsLink: ''
      }
      this.convertResult = null
      this.activeTab = 'original'
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
    },
    onMediaChange() {
      this.convertResult = null
      this.activeTab = 'original'
    }
  }
}
</script>

<style lang="scss" scoped>
.pdd-link-convert-container {
  padding: 20px;

  .result-container {
    margin-top: 20px;

    .result-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .result-actions {
        margin-left: 10px;
      }
    }
  }

  .dialog-footer {
    margin-top: 20px;
    text-align: center;
  }
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}
</style>
