<script setup>
import {
  computed,
  reactive,
  ref
} from 'vue'
import useDicts from '@/hooks/useDicts'
import { addGoods, selectDuoDuoMallList } from '@/api/promotion/goods'
import { Message } from 'element-ui'
import { isPdd, isPddPA, isPddCustom } from '@/utils/judge/platform'
import CompanySelect from '@/components/CtreeSelect/CompanySelect.vue'

const { dicts } = useDicts([
  'platform_type',
  'media_type',
  'goods_link_type_pdd',
  'goods_link_type_jd',
  'goods_link_type_pangda',
  'goods_link_type_tb',
  'goods_link_type_mt',
  'goods_link_type_djk',
  'goods_link_type_uds',
  'goods_link_type_tbpro',
  'goods_link_type_jdpro'
])

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'success'])

const goodsFormRef = ref(null)
const submitLoading = ref(false)
const duoduoMallList = ref([])

// 错误状态管理
const errorMap = ref({})
const hasValidationErrors = ref(false)

// 全局表单数据
const globalForm = reactive({
  platform: '1',
  linkType: '1',
  mallId: '',
  duoId: '',
  mallName: '',
  mediaPlatformType: 1
})

// 商品列表数据
const goodsList = ref([
  {
    goodsId: '',
    goodsName: '',
    goodsLink: '',
    minGroupPrice: null
  }
])

// 全局表单验证规则
const rules = {
  platform: [
    { required: true, message: '请选择平台类型', trigger: 'change' }
  ],
  mallId: [
    { required: true, message: '请选择店铺', trigger: 'change' }
  ],
  linkType: [
    { required: true, message: '请选择链接类型', trigger: 'change' }
  ]
}

// 单个商品验证并设置错误状态
const validateGoodsItem = (item, index) => {
  const rowKey = `row_${index}`
  const errors = []

  // 清除该行的旧错误
  delete errorMap.value[rowKey]

  if (!item.goodsId) {
    errors.push('商品ID不能为空')
  } else if (!/^\d+$/.test(item.goodsId)) {
    errors.push('商品ID只能是数字')
  }

  if (!item.goodsName || item.goodsName.trim().length === 0) {
    errors.push('商品名称不能为空')
  }

  if (!item.goodsLink) {
    errors.push('商品链接不能为空')
  } else if (!/^https?:\/\/.+/.test(item.goodsLink)) {
    errors.push('请输入正确的URL格式')
  }

  if (!item.minGroupPrice || item.minGroupPrice <= 0) {
    errors.push('最小拼团价必须大于0')
  }

  // 如果有错误，设置到errorMap中
  if (errors.length > 0) {
    errorMap.value[rowKey] = errors.join('，')
  }

  return errors
}

// 清除指定行的错误状态
const clearError = (index) => {
  const rowKey = `row_${index}`
  if (errorMap.value[rowKey]) {
    delete errorMap.value[rowKey]
    updateValidationState()
  }
}

// 更新整体验证状态
const updateValidationState = () => {
  hasValidationErrors.value = Object.keys(errorMap.value).length > 0
}

// 获取表格行的CSS类名
const getRowClassName = ({ rowIndex }) => {
  const rowKey = `row_${rowIndex}`
  return errorMap.value[rowKey] ? 'row-error' : ''
}

// 计算属性
const platformTypes = computed(() => {
  return dicts.value.platform_type.filter(item => item.value === '1')
})

const linkTypeOptions = computed(() => {
  const platformLinkTypes = {
    '1': dicts.value.goods_link_type_pdd,
    '3': dicts.value.goods_link_type_jd,
    '4': dicts.value.goods_link_type_pangda,
    '6': dicts.value.goods_link_type_mt,
    '8': dicts.value.goods_link_type_djk,
    '9': dicts.value.goods_link_type_tbpro,
    '10': dicts.value.goods_link_type_tbpro,
    '11': dicts.value.goods_link_type_jdpro
  }
  return platformLinkTypes[globalForm.platform] || dicts.value.goods_link_type_tb
})

const showMallSelect = computed(() => {
  return isPdd(globalForm.platform) || isPddPA(globalForm.platform) || isPddCustom(globalForm.platform)
})

const mediaTypeOptions = computed(() => {
  const platformMediaTypes = {
    '1': ['1', '4'], // 拼多多：巨量、腾讯
    '6': ['1', '2', '4'], // 美团
    '7': ['1', '2'], // 淘宝客
    '8': ['1', '2', '4', '5', '7', '8'], // 云台
    '9': ['1', '2', '3', '4', '5', '10'], // UDSmart
    '10': ['1', '2', '3', '4', '5', '10'], // 淘宝Pro
    '11': ['1', '2', '4'], // 京东Pro
    '13': ['1'], // 微信公众号
    '14': ['1'] // 微信小程序
  }

  const allowedTypes = platformMediaTypes[globalForm.platform] || ['1', '2', '3', '4', '5', '10']

  return dicts.value.media_type.filter(item => allowedTypes.includes(item.value))
})

// 获取多多进宝店铺列表
const getDuoduoMallList = async() => {
  try {
    const res = await selectDuoDuoMallList()
    if (res.code === 200) {
      duoduoMallList.value = res.data.map(item => ({
        id: item.mallId,
        label: item.mallName,
        duoduoId: item.duoDuoId,
        mallId: item.mallId,
        logo: item.logo
      }))
    }
  } catch (error) {
    console.error('获取店铺列表失败:', error)
  }
}

// 店铺变更处理
const handleMallChange = () => {
  if (globalForm.mallId) {
    const selectedMall = duoduoMallList.value.find(item => item.id === globalForm.mallId)
    if (selectedMall) {
      globalForm.duoId = selectedMall.duoduoId
      globalForm.mallName = selectedMall.label
    }
  } else {
    globalForm.duoId = ''
    globalForm.mallName = ''
  }
}

// 平台变更处理
const handlePlatformChange = () => {
  globalForm.linkType = '1'
  globalForm.mallId = ''
  globalForm.duoId = ''
  globalForm.mallName = ''
}

// 添加商品行
const addGoodsRow = () => {
  goodsList.value.push({
    goodsId: '',
    goodsName: '',
    goodsLink: '',
    minGroupPrice: null
  })
}

// 删除商品行
const removeGoodsRow = (index) => {
  if (goodsList.value.length > 1) {
    goodsList.value.splice(index, 1)
    // 重新构建错误映射，因为索引变化了
    rebuildErrorMap()
  }
}

// 重新构建错误映射（删除行时使用）
const rebuildErrorMap = () => {
  const newErrorMap = {}
  Object.keys(errorMap.value).forEach(key => {
    const match = key.match(/row_(\d+)/)
    if (match) {
      const oldIndex = parseInt(match[1])
      // 只保留仍然存在的行的错误
      if (oldIndex < goodsList.value.length) {
        newErrorMap[key] = errorMap.value[key]
      }
    }
  })
  errorMap.value = newErrorMap
  updateValidationState()
}

// 批量添加行
const addMultipleRows = (count = 5) => {
  for (let i = 0; i < count; i++) {
    addGoodsRow()
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

// 重置表单
const resetForm = () => {
  goodsFormRef.value?.resetFields()
  Object.assign(globalForm, {
    platform: '1',
    linkType: '1',
    mallId: '',
    duoId: '',
    mallName: '',
    mediaPlatformType: 1
  })
  goodsList.value = [{
    goodsId: '',
    goodsName: '',
    goodsLink: '',
    minGroupPrice: null
  }]
  // 清除所有错误状态
  errorMap.value = {}
  hasValidationErrors.value = false
}

// 提交表单
const handleSubmit = () => {
  goodsFormRef.value.validate(async(valid) => {
    if (!valid) return

    // 验证商品列表
    const validGoodsList = []
    errorMap.value = {} // 清空旧错误

    goodsList.value.forEach((item, index) => {
      const errors = validateGoodsItem(item, index)
      if (errors.length === 0) {
        validGoodsList.push(item)
      }
    })

    updateValidationState()

    if (hasValidationErrors.value) {
      Message.error('商品信息出错，请检查商品信息')
      return
    }

    if (validGoodsList.length === 0) {
      Message.error('请至少填写一个有效的商品信息')
      return
    }

    try {
      submitLoading.value = true

      // 构造提交数据
      const submitData = validGoodsList.map(item => ({
        platform: globalForm.platform,
        goodsId: item.goodsId,
        goodsName: item.goodsName,
        goodsLink: item.goodsLink,
        minGroupPrice: Math.round(item.minGroupPrice * 100), // 转换为分
        mallId: globalForm.mallId ? parseInt(globalForm.mallId) : undefined,
        mallName: globalForm.mallName,
        linkType: globalForm.linkType,
        mediaPlatformType: globalForm.mediaPlatformType,
        duoId: globalForm.duoId
      }))

      const response = await addGoods(submitData)

      if (response.code === 200 || response.code === 0) {
        let successMsg = `成功添加 ${submitData.length} 个商品`

        // 处理接口返回的错误信息
        if (response.data) {
          try {
            let serverErrors = response.data

            // 如果 response.data 是字符串，尝试解析为 JSON
            if (typeof response.data === 'string') {
              serverErrors = JSON.parse(response.data)
            }

            console.log('服务端错误信息:', serverErrors)

            // 清空旧的错误映射
            errorMap.value = {}

            // 将服务端错误映射到对应的商品行
            if (serverErrors && typeof serverErrors === 'object') {
              Object.keys(serverErrors).forEach(goodsId => {
                // 在原始商品列表中找到对应的索引
                const originalIndex = goodsList.value.findIndex(originalItem =>
                  String(originalItem.goodsId) === String(goodsId)
                )

                if (originalIndex !== -1) {
                  const rowKey = `row_${originalIndex}`
                  errorMap.value[rowKey] = serverErrors[goodsId]
                  console.log(`映射错误信息: ${rowKey} -> ${serverErrors[goodsId]}`)
                }
              })
            }

            updateValidationState()

            if (hasValidationErrors.value) {
              successMsg += '，有部分商品信息出错，请检查商品信息'
            }
          } catch (error) {
            console.warn('解析服务端错误信息失败:', error)
            // 如果是字符串类型的错误信息，直接显示
            if (typeof response.data === 'string' && response.data.includes('错误')) {
              successMsg += `，${response.data}`
            }
          }
        }

        Message.success(successMsg)

        // 如果没有错误，则关闭弹窗
        if (!hasValidationErrors.value) {
          emit('success')
          handleClose()
        }
      } else {
        // 处理请求失败的情况
        if (response.data) {
          try {
            let serverErrors = response.data

            // 如果 response.data 是字符串，尝试解析为 JSON
            if (typeof response.data === 'string') {
              serverErrors = JSON.parse(response.data)
            }

            console.log('服务端错误信息（失败）:', serverErrors)

            // 清空旧的错误映射
            errorMap.value = {}

            // 将服务端错误映射到对应的商品行
            if (serverErrors && typeof serverErrors === 'object') {
              Object.keys(serverErrors).forEach(goodsId => {
                const originalIndex = goodsList.value.findIndex(originalItem =>
                  String(originalItem.goodsId) === String(goodsId)
                )

                if (originalIndex !== -1) {
                  const rowKey = `row_${originalIndex}`
                  errorMap.value[rowKey] = serverErrors[goodsId]
                }
              })
            }

            updateValidationState()
            Message.error('添加商品失败，请检查商品信息')
          } catch (error) {
            Message.error(response.msg || '添加商品失败')
          }
        } else {
          Message.error(response.msg || '添加商品失败')
        }
      }
    } catch (error) {
      console.error('提交失败:', error)
      Message.error('添加商品失败，请重试')
    } finally {
      submitLoading.value = false
    }
  })
}

// 监听弹窗显示，获取数据
const handleDialogOpen = () => {
  if (showMallSelect.value) {
    getDuoduoMallList()
  }
}
</script>

<template>
  <el-dialog
    title="批量添加商品"
    :visible="visible"
    top="10vh"
    width="1000px"
    :before-close="handleClose"
    append-to-body
    @opened="handleDialogOpen"
  >
    <el-form
      ref="goodsFormRef"
      :model="globalForm"
      :rules="rules"
      label-width="100px"
      size="small"
    >
      <!-- 全局配置 -->
      <div class="global-config">
        <el-divider content-position="left">全局配置</el-divider>

        <el-row :gutter="16">
          <!-- 平台类型 -->
          <el-col :span="12">
            <el-form-item label="平台类型" prop="platform">
              <el-radio-group v-model="globalForm.platform" @change="handlePlatformChange">
                <el-radio
                  v-for="dict in platformTypes"
                  :key="dict.value"
                  :label="dict.value"
                  :value="dict.value"
                >

                  <svg-icon :icon-class="dict.label" />
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="媒体类型" prop="mediaPlatformType">
              <el-select
                v-model="globalForm.mediaPlatformType"
                placeholder="请选择媒体类型"
              >
                <el-option
                  v-for="option in mediaTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="parseInt(option.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="链接类型" prop="linkType">
              <el-select
                v-model="globalForm.linkType"
                disabled
                placeholder="请选择链接类型"
              >
                <el-option
                  v-for="option in linkTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col v-if="showMallSelect" :span="12">
            <el-form-item label="店铺" prop="mallId">
              <CompanySelect
                v-model="globalForm.mallId"
                :options="duoduoMallList"
                placeholder="请选择店铺"
                :is-search="false"
                @select="handleMallChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 商品列表 -->
      <div class="goods-list">
        <div class="goods-list-header">
          <el-divider content-position="left">商品信息</el-divider>
          <div class="goods-actions">
            <el-button size="mini" @click="addGoodsRow">增加一行</el-button>
            <el-button size="mini" @click="addMultipleRows">批量增加(5行)</el-button>
          </div>
        </div>

        <el-table :data="goodsList" border size="mini" max-height="400" :row-class-name="getRowClassName">
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column label="商品ID" width="120">
            <template #default="{ row, $index }">
              <div class="table-cell-wrapper">
                <el-input
                  v-model="row.goodsId"
                  placeholder="商品ID"
                  size="mini"
                  :class="{ 'input-error': errorMap[`row_${$index}`] }"
                  @input="clearError($index)"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="商品名称" min-width="200">
            <template #default="{ row, $index }">
              <div class="table-cell-wrapper">
                <el-input
                  v-model="row.goodsName"
                  placeholder="商品名称"
                  size="mini"
                  :class="{ 'input-error': errorMap[`row_${$index}`] }"
                  @input="clearError($index)"
                />
                <div v-if="errorMap[`row_${$index}`]" class="error-message">
                  {{ errorMap[`row_${$index}`] }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="商品链接" min-width="250">
            <template #default="{ row, $index }">
              <div class="table-cell-wrapper">
                <el-input
                  v-model="row.goodsLink"
                  placeholder="商品链接"
                  size="mini"
                  :class="{ 'input-error': errorMap[`row_${$index}`] }"
                  @input="clearError($index)"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="最小拼团价" width="160">
            <template #default="{ row, $index }">
              <div class="table-cell-wrapper">
                <el-input-number
                  v-model="row.minGroupPrice"
                  :min="0"
                  :precision="2"
                  size="mini"
                  style="width: 100%"
                  placeholder="价格"
                  :class="{ 'input-error': errorMap[`row_${$index}`] }"
                  @input="clearError($index)"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                :disabled="goodsList.length === 1"
                @click="removeGoodsRow($index)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          批量添加 ({{ goodsList.filter(item => item.goodsId && item.goodsName && item.goodsLink && item.minGroupPrice).length }} 个商品)
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}

.global-config {
  margin-bottom: 12px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .config-desc {
    font-size: 12px;
    color: #909399;
  }
}

.goods-list {

  .goods-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .goods-actions {
      display: flex;
      margin-left: 10px;
    }
  }
}

::v-deep .el-form-item {
  margin-bottom: 15px;
}

::v-deep .el-radio {
  display: inline-flex;
  align-items: center;
  height: auto;
  padding: 0;
  margin-right: 0;
}

::v-deep .el-textarea__inner {
  resize: vertical;
}

::v-deep .el-table .el-table__cell {
  padding: 4px 0;
}

::v-deep .el-table .el-input--mini .el-input__inner {
  height: 28px;
  line-height: 28px;
}

// 错误状态样式
.table-cell-wrapper {
  position: relative;
  width: 100%;
}

.error-message {
  color: #ff4949;
  font-size: 12px;
  margin-top: 2px;
  line-height: 1.2;
  word-break: break-all;
}

::v-deep .input-error {
  .el-input__inner {
    border-color: #ff4949 !important;
  }

  .el-input-number__increase,
  .el-input-number__decrease {
    border-left-color: #ff4949 !important;
  }
}

::v-deep .row-error {
  background-color: rgba(255, 73, 73, 0.03);

  &:hover {
    background-color: rgba(255, 73, 73, 0.06) !important;
  }
}
</style>
