<script setup>
import { ref, reactive, computed, nextTick, onMounted, watch } from 'vue'
import ResourceManager from '@/views/resource/index.vue'
import Slide from '@/components/Slide/index.vue'
import ImagesInputList from './ImagesInputList.vue'
import VueDragResize from 'vue-drag-resize'

import phoneHeader from '@/assets/images/phone-title.png'
import { data2Html, defaultImg } from '@/template/compileHtml'
import { addLanding, getGoodsFirstDeptDomainName, updateLanding } from '@/api/promotion/landing'
import { getConfigKey } from '@/api/system/config'
import { phoneSizes } from '@/config/phone'
import { LandingImageType } from '@/config'
import { Message, Notification } from 'element-ui'
import useLocalStorage from '@/hooks/useLocalStorage'
import useConfigs from '@/hooks/useConfigs'
import useUrlConfig from '@/components/URLGetter/useUrlConfig'
import { replaceUrl } from '@/utils'
import GoodsSelector from '@/components/GoodsSelector/index.vue'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import TemplatePreview from './TemplatePreview.vue'
import { isKs, isZh } from '@/utils/judge/media'
import { isJd } from '@/utils/judge/platform'

const MonitorConfigKeyMap = {
  1: 'landing_page_monitor_url',
  2: 'landing_page_monitor_url',
  3: 'landing_page_monitor_url',
  4: 'landing_page_monitor_url',
  5: 'tb_landing_page_monitor_url',
  6: 'landing_page_monitor_url',
  7: 'landing_page_monitor_url',
  8: 'landing_page_monitor_url',
  9: 'landing_page_monitor_url',
  10: 'landing_page_monitor_url',
  11: 'landing_page_monitor_url',
  12: 'landing_page_monitor_url',
  13: 'landing_page_monitor_url',
  14: 'landing_page_monitor_url'
}

const redirectOptions = [
  { label: '点击跳转', value: 1 },
  { label: '自动跳转', value: 2 }
]

const typeMap = {
  normal: '基础图片',
  img: '按钮图片',
  area: '点击区域',
  button: '普通按钮',
  backup: '弹窗'
}

const systemConfigs = useConfigs(['jd_jump_url', 'landing_page_show_url', 'jd_convert_wx'])
const { centralMonitorDomain } = useUrlConfig()

function scaleSize(num, scale) {
  return Math.round(num * scale)
}

function hasImages(form) {
  let has = false
  form.slides.forEach(item => {
    if (item) has = true
  })
  form.normalImages.forEach(item => {
    if (item) has = true
  })
  return has
}
function checkHttp(form) {
  let msg = ''
  form.slides.forEach(item => {
    if (item && item.startsWith('http://')) msg = '滚动'
  })
  form.normalImages.forEach(item => {
    if (item && item.startsWith('http://')) msg = '基础'
  })
  form.clickAreas.forEach(item => {
    if (item.type === 'img' && item.url.startsWith('http://')) { msg = '按钮' }
  })
  if (msg) {
    Message.warning(msg + '图片只能使用https协议图片')
  }
  return msg
}

function getImagesByType(list, type) {
  return list.filter(item => item.resourceType === type).map(item => item.resourceUrl)
}

const props = defineProps({
  goods: {
    type: Object,
    default: () => ({})
  },
  landing: {
    type: Object
  },
  platformType: {
    type: Number
  },
  pageBid: {
    type: String
  },
  pageBAdvertiserId: {
    type: String
  }
})

const emit = defineEmits(['close', 'success'])
const close = () => emit('close')

const { setStorage, getStorage } = useLocalStorage('domainName', { observe: false })
const handleDomainNameChange = (val) => setStorage('domainName', val)

const defaultPageForm = () => ({
  remark: '',
  oper: null,
  params: null,
  id: '',
  goodsId: null,
  mediaType: null,
  advertiserId: '',
  landingPageType: '1',
  redirectType: 1,
  title: '',
  content: '',
  buttonMargin: null,
  enableAbPage: false,
  refreshCdn: false,
  landingPageResourceList: [],
  companyName: '',
  clickAreas: [],
  config: '',
  backupGoodsId: null,
  platform: null,
  domainName: '',
  // 兼容旧版本字段
  slides: [],
  normalImages: [],
  autoRedirectTime: 0,
  iframe: '',
  isCallback: false,
  showAfterSuccess: true,
  // 模板页面新增字段
  pageType: 'template', // 'custom' | 'template'
  templateStyle: 'template1', // 模板样式选择
  templateImage: '', // 模板页面商品图片
  templateGoodsName: '', // 模板页面商品名称
  templatePrice: '', // 模板页面商品价格（默认为空，不显示价格）
  templateButtonText: '立即购买' // 模板页面按钮文字
})
const pageForm = reactive(defaultPageForm())
const isIframeUrlRight = computed(() => {
  if (!pageForm.iframe) return false
  const reg = /^(?:(http|https|ftp):\/\/)?((?:[\w-]+\.)+[a-z0-9]+)((?:\/[^/?#]*)+)?(\?[^#]+)?(#.+)?$/i
  return reg.test(pageForm.iframe)
})
const formRef = ref(null)
const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  landingPageType: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  advertiserId: [
    { required: false, message: '请输入账户ID', trigger: 'blur' }
  ],
  mediaType: [
    { required: false, message: '请选择媒体类型', trigger: 'change' }
  ]
}
const btnLoading = ref(false)
const createLanding = () => {
  if (pageForm.normalImages.length > 15) {
    Message.warning('基础图片最多上传15张')
    return
  }
  if (checkHttp(pageForm)) {
    return
  }
  formRef.value.validate(valid => {
    if (!valid) return
    const resourceList = getResourceList()
    const formData = {
      goodsId: isJd(props.goods.platform) ? props.goods.goodsSign : props.goods.goodsId,
      mediaType: props.platformType,
      advertiserId: pageForm.advertiserId,
      landingPageType: pageForm.landingPageType,
      redirectType: pageForm.redirectType === 1 ? pageForm.redirectType : pageForm.redirectType + pageForm.autoRedirectTime,
      title: pageForm.title,
      content: '',
      buttonMargin: pageForm.buttonMargin,
      pageAid: pageForm.pageAid,
      pageBid: pageForm.pageBid,
      enableAbPage: pageForm.enableAbPage,
      refreshCdn: pageForm.refreshCdn,
      landingPageResourceList: resourceList,
      companyName: pageForm.companyName,
      clickAreas: JSON.stringify(pageForm.clickAreas),
      config: '',
      backupGoodsId: pageForm.backupGoodsId,
      platform: pageForm.platform,
      remark: pageForm.remark,
      domainName: pageForm.domainName
    }
    const config = {}
    if (pageForm.id) {
      formData.id = pageForm.id
      formData.filePath = pageForm.filePath
    }
    if (props.pageBid) {
      formData.pageBid = props.pageBid
      formData.advertiserId = props.pageBAdvertiserId
      formData.landingPageType = '2'
    }
    if (backupGoods.value) {
      formData.backupGoodsId = backupGoods.value.goodsId
      config.backupGoods = {
        goodsId: backupGoods.value.goodsId,
        goodsName: backupGoods.value.goodsName,
        url: backupGoods.value.url,
        linkType: backupGoods.value.linkType,
        isCallback: pageForm.isCallback,
        showAfterSuccess: pageForm.showAfterSuccess
      }
    }
    if (pageForm.iframe) {
      config.iframe = pageForm.iframe
    }

    // 模板页面配置
    if (pageForm.pageType === 'template') {
      config.pageType = pageForm.pageType
      config.templateStyle = pageForm.templateStyle
      config.templateImage = pageForm.templateImage
      config.templateGoodsName = pageForm.templateGoodsName
      config.templatePrice = pageForm.templatePrice
      config.templateButtonText = pageForm.templateButtonText
    }

    formData.config = JSON.stringify(config)
    btnLoading.value = true
    getConfigKey(MonitorConfigKeyMap[props.goods.platform]).then(data => {
      if (data.code === 200) {
        let isReplace = ~['1', '3'].indexOf(props.goods.platform) || (props.goods.url && props.goods.url.includes('%22ckId%22%3A'))
        if (isJd(props.goods.platform) && isKs(props.goods.mediaPlatformType)) isReplace = false
        formData.content = data2Html(
          { form: pageForm,
            goods: { ...props.goods },
            monitorUrl: (isReplace && !isZh(props.goods.mediaPlatformType)) ? replaceUrl(data.msg, centralMonitorDomain.value) : data.msg,
            mediaType: props.platformType,
            advertiserId: pageForm.advertiserId,
            landingPageShowUrl: isReplace ? replaceUrl(systemConfigs.landing_page_show_url, centralMonitorDomain.value) : systemConfigs.landing_page_show_url,
            jdConvertWx: systemConfigs.jd_convert_wx,
            backupGoods: backupGoods.value,
            ...generateJumpMap(pageForm.clickAreas)
          }
        )
        // console.log(formData)
        // btnLoading.value = false
        // return
        if (pageForm.id) {
          if (props.landing.platform) {
            formData.platform = props.landing.platform
          }
          updateLanding(formData).then(response => {
            Message.success('修改成功')
            emit('success')
          }).finally(() => {
            btnLoading.value = false
          })
        } else {
          formData.platform = props.goods.platform
          addLanding(formData).then(response => {
            Message.success('新增成功')
            emit('success')
          }).finally(() => {
            btnLoading.value = false
          })
        }
      } else {
        Message.error('获取监测链接失败')
        btnLoading.value = false
      }
    })
  })
}

const getResourceList = () => {
  const resourceList = []
  pageForm.slides.forEach(item => {
    if (item) {
      resourceList.push({
        resourceType: LandingImageType.SLIDE,
        resourceUrl: item
      })
    }
  })
  pageForm.normalImages.forEach(item => {
    if (item) {
      resourceList.push({
        resourceType: LandingImageType.NORMAL,
        resourceUrl: item
      })
    }
  })
  return resourceList
}

// 机型尺寸
const phoneSize = ref(phoneSizes[1])
watch(phoneSize, (newVal, oldVal) => {
  redraw(newVal, oldVal)
})

// 生成按钮数据
const phoneContentRef = ref(null)
const phoneHeaderRef = ref(null)
const windowHeight = computed(() => {
  return phoneSize.value.height - phoneHeaderRef.value.offsetHeight
})
const generateJumpMap = (data) => {
  const map = { }
  data.forEach((item) => {
    if (!map[item.type]) map[item.type] = []
    if (item.type === 'img' && !item.url) return
    const drawItem = {
      ...item,
      left: Math.round((item.left / phoneSize.value.width) * 100) + '%',
      top: Math.round((item.top / phoneContentRef.value.offsetHeight) * 100) + '%',
      width: Math.round((item.width / phoneSize.value.width) * 100) + '%',
      height: Math.round((item.height / phoneContentRef.value.offsetHeight) * 100) + '%'
    }

    if (item.position === 'fixed') {
      const percent = Math.round((item.top / windowHeight.value) * 100)
      if (percent < 50) {
        drawItem.top = percent + '%'
      } else {
        drawItem.bottom = Math.round(((windowHeight.value - item.top - item.height) / windowHeight.value) * 100) + '%'
      }
    }
    map[item.type].push(drawItem)
  })
  return map
}

const resourceVisible = ref(false)
const resourceFormKey = ref('button')
const resourceSelectorProps = ref({})

const showResourceSelector = (key, options = {}) => {
  resourceFormKey.value = key
  resourceSelectorProps.value = {
    selectorMode: true,
    multiple: key === 'slides' || key === 'normalImages',
    maxCount: key === 'slides' || key === 'normalImages' ? 0 : 1,
    fileTypes: ['image'],
    ...options
  }
  resourceVisible.value = true
}

const handleResourceSelect = (selectedFiles) => {
  const key = resourceFormKey.value
  const files = Array.isArray(selectedFiles) ? selectedFiles : [selectedFiles]

  switch (key) {
    case 'slides':
    case 'normalImages':
      files.forEach(file => {
        const index = pageForm[key].findIndex(item => !item)
        if (index !== -1) {
          pageForm[key].splice(index, 1, file.resourceUrl || file.url)
        } else {
          pageForm[key].push(file.resourceUrl || file.url)
        }
      })
      break
    case 'button':
      if (files.length > 0) {
        selectItem.value.data.url = files[0].resourceUrl || files[0].url
      }
      break
    case 'templateImage':
      if (files.length > 0) {
        pageForm.templateImage = files[0].resourceUrl || files[0].url
      }
      break
  }

  resourceVisible.value = false
}

const handleResourceCancel = () => {
  resourceVisible.value = false
}

// 自定义域名
const domainOptions = ref([])
const fetchDomainName = () => {
  if (!props.goods.id) return
  getGoodsFirstDeptDomainName(props.goods.id).then(response => {
    if (response.data) {
      domainOptions.value = response.data.split(',')
      let url = ''
      if (props.landing && props.landing.filePath) {
        const reg = /^(?:(http|https|ftp):\/\/)?((?:[\w-]+\.)+[a-z0-9]+)((?:\/[^/?#]*)+)?(\?[^#]+)?(#.+)?$/i
        url = reg.exec(props.landing.filePath)[2] + '/'
      } else {
        url = getStorage('domainName')
      }
      pageForm.domainName = domainOptions.value.includes(url) ? url : ''
    } else {
      domainOptions.value = []
    }
  })
}

const analysisLanding = () => {
  if (!props.landing) return
  const {
    remark,
    oper,
    params,
    id,
    goodsId,
    mediaType,
    advertiserId,
    landingPageType,
    redirectType: originalRedirectType,
    title,
    content,
    buttonMargin,
    pageAid,
    pageBid,
    enableAbPage,
    refreshCdn,
    landingPageResourceList,
    companyName,
    clickAreas,
    config,
    backupGoodsId,
    platform
  } = props.landing
  let redirectType = +originalRedirectType
  const _config = config ? JSON.parse(config) : {}

  let autoRedirectTime = 0
  if (redirectType === '1') {
    redirectType = 1
  }
  if (redirectType !== 1) {
    autoRedirectTime = redirectType - 2
    redirectType = 2
  }

  Object.assign(pageForm, {
    remark,
    oper,
    params,
    id,
    goodsId,
    mediaType,
    advertiserId,
    landingPageType,
    redirectType,
    title,
    content,
    buttonMargin,
    pageAid,
    pageBid,
    enableAbPage,
    refreshCdn,
    companyName,
    backupGoodsId,
    platform,
    slides: getImagesByType(landingPageResourceList || [], LandingImageType.SLIDE),
    normalImages: getImagesByType(landingPageResourceList || [], LandingImageType.NORMAL),
    autoRedirectTime,
    clickAreas: [],
    domainName: '',
    // 模板页面字段
    pageType: _config.pageType || 'custom',
    templateStyle: _config.templateStyle || 'template1',
    templateImage: _config.templateImage || '',
    templateGoodsName: _config.templateGoodsName || '',
    templatePrice: _config.templatePrice || '', // 保持为空，不使用默认值
    templateButtonText: _config.templateButtonText || '立即购买'
  })

  const clickAreasTemp = clickAreas ? JSON.parse(clickAreas) : []

  // 旧版本处理
  clickAreasTemp.forEach((item, i) => {
    if (!item.type) { item.type = 'area' }
    if (item.type === 'button' && !item.position) { item.position = 'absolute' }
    if (!item.key) { item.key = new Date().getTime() }
    if (!item.zIndex) { item.zIndex = i }
  })
  pageForm.clickAreas = clickAreasTemp

  // 查找旧版本按钮图片并替换
  const btnImage = landingPageResourceList.find(item => (item.resourceType === LandingImageType.FIXED || item.resourceType === LandingImageType.BOTTOM))
  if (btnImage) {
    if (btnImage.resourceType === LandingImageType.FIXED) {
      const item = {
        key: new Date().getTime(),
        type: 'img',
        left: 0,
        top: 0,
        width: 375,
        height: 150,
        position: 'fixed',
        zIndex: pageForm.clickAreas.length + 1,
        url: btnImage.resourceUrl,
        load: false,
        flash: false
      }
      pageForm.clickAreas.push(item)
      nextTick(() => {
        handleItemSelect(item)
      })
      Notification({
        title: '提示',
        message: '因落地页创建逻辑调整，浮动类型的按钮图片展示受到影响，请重新调整按钮位置。',
        duration: 10000,
        customClass: 'z-index-99999',
        type: 'warning'
      })
    } else if (btnImage.resourceType === LandingImageType.BOTTOM) {
      pageForm.normalImages.push(btnImage.resourceUrl)
    }
  }

  // 获取副商品
  if (_config.backupGoods) {
    backupGoods.value = _config.backupGoods
    pageForm.isCallback = _config.backupGoods.isCallback
    pageForm.showAfterSuccess = _config.backupGoods.showAfterSuccess
  }

  pageForm.iframe = _config.iframe
  isRefresh.value = false
  setTimeout(() => {
    isRefresh.value = true
  }, 50)
}

onMounted(() => {
  analysisLanding()
  fetchDomainName()

  if (props.goods && !props.landing) {
    pageForm.title = props.goods.goodsName
    if (props.goods.goodsImageUrl) pageForm.normalImages = [props.goods.goodsImageUrl]

    // 设置模板页面字段的默认值
    pageForm.templateImage = props.goods.goodsImageUrl || ''
    pageForm.templateGoodsName = props.goods.goodsName || ''
    // 价格默认为空，不自动填入商品价格
  }
})

const onOperateStop = (newRect, oldRect) => {
  Object.assign(oldRect, newRect)
}

// 设置默认价格
const setDefaultPrice = () => {
  if (props.goods?.minGroupPrice) {
    pageForm.templatePrice = (props.goods.minGroupPrice / 100).toString()
  }
}

const redraw = (newSize, oldSize) => {
  pageForm.clickAreas.forEach(item => {
    if (newSize.width !== oldSize.width) {
      const scale = newSize.width / oldSize.width
      item.left = scaleSize(item.left, scale)
      item.top = scaleSize(item.top, scale)
      item.width = scaleSize(item.width, scale)
      item.height = scaleSize(item.height, scale)
    }
  })

  refreshDrag()
}

const isRefresh = ref(true)
const refreshDrag = () => {
  isRefresh.value = false
  nextTick(() => {
    isRefresh.value = true
  })
}

// const refreshDragDebounce = useDebounceFn(() => {
//   isRefresh.value = false
//   setTimeout(() => {
//     isRefresh.value = true
//   })
// }, 500)

// 添加按钮
const addClickItem = (type) => {
  let item = {
    key: new Date().getTime(),
    type,
    left: 0,
    top: pageForm.clickAreas.length * 50,
    width: 375,
    height: 150,
    position: 'absolute',
    zIndex: pageForm.clickAreas.length + 1
  }
  switch (type) {
    case 'img':
      item = {
        ...item,
        url: '',
        load: false,
        flash: true
      }
      break
    case 'button':
      item = {
        ...item,
        text: '立即购买',
        color: '#ffffff',
        background: '#e02e24',
        size: 18,
        flash: true
      }
      break
    case 'area':
      Message.warning('页面任意位置点击均可跳转，添加<点击区域>后仅<点击区域>可以点击跳转，建议拉大点击区域')
      break
    case 'backup':
      item = {
        ...item,
        descText: '您有优惠未享受即将过期！',
        descSize: 30,
        btnText: '立即购买',
        btnSize: 24,
        url: '',
        height: 375,
        top: 150,
        flash: false,
        zIndex: undefined
      }
      break
  }

  pageForm.clickAreas.push(item)
  nextTick(() => {
    handleItemSelect(item)
  })
  return item
}

// 组件属性操作
const selectItem = ref({ })

const handleItemSelect = (data) => {
  selectItem.value = {
    type: data.type,
    label: typeMap[data.type],
    data: data
  }
}
const removeSelectItem = () => {
  if (selectItem.value.data.backupGoodsId) {
    backupGoods.value = null
  }
  pageForm.clickAreas = pageForm.clickAreas.filter(item => item.key !== selectItem.value.data.key)
  selectItem.value = { }
}

const isSelect = (item) => {
  return selectItem.value.data && selectItem.value.data.key === item.key
}

const onButtonImgLoad = (event, item) => {
  if (!item.load) {
    item.load = true
    item.height = event.target.height
    isRefresh.value = false
    setTimeout(() => {
      isRefresh.value = true
    })
  }
}
const handlePositionChange = () => {
  if (selectItem.value.data.position === 'fixed') {
    selectItem.value.data.top = 0
  }
}

// 规则展示
const ruleVisible = ref()

// 选择商品
const goodsVisible = ref(false)
const showGoodsSelector = () => {
  goodsVisible.value = true
}

const backupGoods = ref(null)
const backupVisible = ref(true)
const handleBackupGoodsSelect = (goods) => {
  backupGoods.value = goods
  let backupItem = pageForm.clickAreas.find(item => item.type === 'backup')
  if (!backupItem) {
    backupItem = addClickItem('backup')
  }
  backupItem.backupGoodsId = goods.goodsId
  backupItem.backupGoodsName = goods.goodsName
}
const removeBackupGoods = () => {
  backupGoods.value = null
  pageForm.clickAreas = pageForm.clickAreas.filter(item => item.type !== 'backup')
}

</script>

<template>
  <div class="edit-landing-wrap">
    <div class="edit-form">
      <div class="landing-rule">
        <el-collapse v-model="ruleVisible">
          <el-collapse-item name="1">
            <template slot="title">
              <div class="landing-rule-title">自建地页规则</div>
            </template>
            <div class="landing-rule-item text-bold">落地页所有区域点击后均可跳转到目标商品。</div>
            <div class="landing-rule-item">基础图片：jpg，png，jpeg等图片格式，请选择宽度一致的图片，限15张。</div>
            <div class="landing-rule-item">按钮图片：jpg，png，jpeg等图片格式，请选择宽度一致的图片，限1张。</div>
            <div class="landing-rule-item color-danger text-bold">图片来源：请优先前往千牛素材库、拼多多素材库、京东素材库获取图片地址，以获得更加稳定高效的落地页体验。</div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <el-form ref="formRef" :model="pageForm" :rules="rules" label-width="100px" size="mini">
        <el-form-item label="标题" prop="title">
          <el-input v-model="pageForm.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="主体名称" prop="name">
          <el-input v-model="pageForm.companyName" filterable placeholder="请输入名称（可选）" style="width: 100%" clearable />
        </el-form-item>
        <el-form-item v-if="domainOptions.length" label="自定义域名" prop="domainName">
          <el-select
            v-model="pageForm.domainName"
            placeholder="未选择则使用默认域名"
            clearable
            style="width: 100%"
            @change="handleDomainNameChange"
          >
            <el-option
              v-for="item in domainOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="跳转方式" prop="redirectType">
          <el-radio-group v-model="pageForm.redirectType">
            <el-radio
              v-for="o in redirectOptions"
              :key="o.value"
              :label="o.value"
            >{{ o.label }}</el-radio>
          </el-radio-group>
          <template v-if="pageForm.redirectType === 2">
            , 延迟
            <el-input-number v-model="pageForm.autoRedirectTime" :min="0" :max="30" :controls="false" style="width: 60px" />
            秒后跳转
          </template>
        </el-form-item>
        <el-form-item label="页面类型" prop="pageType">
          <el-radio-group v-model="pageForm.pageType">
            <el-radio label="custom">自定义落地页</el-radio>
            <el-radio label="template">模板页面</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 模板页面配置项 -->
        <template v-if="pageForm.pageType === 'template'">
          <el-form-item label="模板样式" prop="templateStyle">
            <el-radio-group v-model="pageForm.templateStyle">
              <el-radio label="template1">
                经典电商模板
              </el-radio>
              <!-- <el-radio label="template2" disabled>简约风格模板</el-radio>
              <el-radio label="template3" disabled>奢华品牌模板</el-radio> -->

            </el-radio-group>
          </el-form-item>

          <el-form-item label="商品图片" prop="templateImage">
            <div class="image-btn">
              <el-input v-model="pageForm.templateImage" placeholder="默认使用商品主图" />
              <el-button style="margin-left: 10px" @click="showResourceSelector('templateImage')">选择图片</el-button>
            </div>
          </el-form-item>

          <el-form-item label="商品名称" prop="templateGoodsName">
            <el-input v-model="pageForm.templateGoodsName" placeholder="默认使用商品名称" />
          </el-form-item>

          <el-form-item label="商品价格" prop="templatePrice">
            <div style="display: flex; gap: 8px; align-items: center;">
              <el-input
                v-model="pageForm.templatePrice"
                placeholder="留空则不显示价格"
                style="flex: 1"
                clearable
              />
              <el-button
                size="small"
                type="primary"
                :disabled="!goods?.minGroupPrice"
                @click="setDefaultPrice"
              >
                默认价格
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="按钮文字" prop="templateButtonText">
            <el-input v-model="pageForm.templateButtonText" placeholder="立即购买" />
          </el-form-item>

        </template>

        <!-- 自定义落地页配置项 -->
        <template v-if="pageForm.pageType === 'custom'">
          <el-form-item label="基础图片" prop="normalImages">
            <ImagesInputList
              v-model="pageForm.normalImages"
              placeholder="请拷贝图片地址到此处"
              :file-size="1"
              :max="15"
              @upload="showResourceSelector('normalImages')"
              @remove="refreshDrag"
            />
          </el-form-item>
          <el-form-item label="轮播图" prop="slides">
            <ImagesInputList
              v-model="pageForm.slides"
              placeholder="请拷贝图片地址到此处"
              @upload="showResourceSelector('slides')"
            />
          </el-form-item>
          <el-form-item>
            <template #label>
              跳转组件
              <el-tooltip content="页面任意位置点击均可跳转，当添加<点击区域>后仅<点击区域>可以点击跳转" placement="top">
                <i class="el-icon-question" />
              </el-tooltip>
            </template>
            <el-row :gutter="10" style="margin-left: 0">
              <el-col :span="8">
                <div class="button-item normal-button" @click="addClickItem('button')">
                  <i class="el-icon-thumb color-danger" />
                  普通按钮
                </div>
              </el-col>
              <el-col :span="8">
                <div class="button-item img-button" @click="addClickItem('img')">
                  <i class="el-icon-picture color-success" />
                  图片按钮
                </div>
              </el-col>
              <el-col :span="8">
                <div class="button-item area-button" @click="addClickItem('area')">
                  <i class="el-icon-mouse color-primary" />点击区域</div>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="副商品" prop="backup">
            <template #label>
              副商品
              <el-tooltip content="选择副商品，用户退出或跳转后显示弹窗可跳转到该商品，更换链接后副商品将使用替换后主商品数据" placement="top">
                <i class="el-icon-question" />
              </el-tooltip>
            </template>
            <div :class="{'backup-active': !!backupGoods}">
              <div class="flex">
                <div v-if="backupGoods" class="overflow-text flex1" :title="backupGoods.goodsName">
                  <BaseInfoCell :id="backupGoods.goodsId" :name="backupGoods.goodsName" no-copy />
                </div>
                <div v-else>
                  <el-button @click="showGoodsSelector">选择商品</el-button>
                  <el-button @click="handleBackupGoodsSelect(goods)">选择当前商品</el-button>
                </div>
              </div>
              <div v-if="backupGoods" class="mb5">是否回传:  <el-switch
                v-model="pageForm.isCallback"
                class="mr5"
                :active-text="pageForm.isCallback ? '回传' : '不回传'"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />  </div>
              <div v-if="backupGoods" class="mb5">
                弹窗场景:
                <el-radio v-model="pageForm.showAfterSuccess" :label="false" disabled>弹窗挽留
                  <el-tooltip content="仅用户退出时弹窗" placement="top">
                    <i class="el-icon-question" />
                  </el-tooltip>
                </el-radio>
                <el-radio v-model="pageForm.showAfterSuccess" :label="true">买了又买
                  <el-tooltip content="用户从商品页面返回会弹窗" placement="top">
                    <i class="el-icon-question" />
                  </el-tooltip>
                </el-radio>
              </div>
              <div v-if="backupGoods">
                <el-button type="primary" @click="showGoodsSelector">更换商品</el-button>
                <el-button type="danger" @click="removeBackupGoods">移除</el-button>
                <el-button @click="backupVisible = !backupVisible">{{ backupVisible ? '隐藏': '显示' }}</el-button>
              </div>
            </div>
          </el-form-item>
        </template>
        <!-- <el-form-item label="内嵌网页">
          <el-input v-model="pageForm.iframe" placeholder="请输入网址" />
        </el-form-item> -->
        <el-form-item label="备注" prop="remark">
          <el-input v-model="pageForm.remark" />
        </el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" @click="close">取 消</el-button>
          <el-button :loading="btnLoading" type="primary" @click="createLanding">保 存</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="phone-wrap">
      <div class="phone-tip">
        <!-- <div class="phone-tip-item">尺寸:
          <el-select
            v-model="phoneSize"
            placeholder="请选择"
            size="mini"
            value-key="name"
            style="width:240px "
          >
            <el-option
              v-for="item in phoneSizes"
              :key="item.name"
              :label="`${item.name} (${item.width} × ${item.height})`"
              :value="item"
            />
          </el-select>
        </div> -->
        <div class="phone-tip-item">仅供参考，实际效果与用户手机型号相关。</div>
      </div>
      <div class="phone" :style="{width: phoneSize.width + 'px', height: phoneSize.height + 'px'}">
        <!--      <div class="phone">-->
        <div ref="phoneHeaderRef" class="phone-header">
          <el-image :src="phoneHeader" />
          <div class="phone-title overflow-text">{{ pageForm.title }}</div>
        </div>
        <div class="scroll-phone-container">
          <el-scrollbar class="scroll-phone-main">
            <div ref="phoneContentRef" class="phone-content">
              <!-- 模板页面预览 -->
              <template v-if="pageForm.pageType === 'template'">
                <TemplatePreview
                  :template-style="pageForm.templateStyle"
                  :template-image="pageForm.templateImage || (goods && goods.goodsImageUrl) || ''"
                  :template-goods-name="pageForm.templateGoodsName || (goods && goods.goodsName) || ''"
                  :template-price="pageForm.templatePrice || ''"
                  :template-button-text="pageForm.templateButtonText || '立即购买'"
                  :template-company-name="pageForm.companyName || ''"
                />
              </template>

              <!-- 自定义落地页预览 -->
              <template v-else>
                <Slide :images="pageForm.slides.filter(i => i)" />
                <img
                  v-for="(image, index) in pageForm.normalImages.filter(i => i)"
                  :key="index"
                  class="normal-image"
                  :src="image"
                  @load="refreshDrag"
                >
                <!-- <iframe
                v-if="isIframeUrlRight"
                frameborder="no"
                :src="pageForm.iframe"
                :style="{height: '720px',width:'100%',pointerEvents: 'none'}"
              /> -->
                <template v-if="isRefresh">
                  <template v-for="item in pageForm.clickAreas">
                    <VueDragResize
                      v-if="item.position === 'absolute'"
                      :key="item.key"
                      :parent-limitation="true"
                      :x="item.left"
                      :y="item.top"
                      :w="item.width"
                      :h="item.height"
                      :z="item.zIndex"
                      @clicked="handleItemSelect(item)"
                      @resizestop="onOperateStop($event, item)"
                      @dragstop="onOperateStop($event, item)"
                    >
                      <div v-if="item.type === 'area'" class="click-area area-button" :class="{'active-item': isSelect(item)}">
                        <i class="el-icon-mouse" />
                      </div>
                      <div v-else-if="item.type === 'button'" class="click-btn-wrap normal-button" :class="{'active-item': isSelect(item)}">
                        <div
                          class="click-btn"
                          :class="{'click-flash': item.flash}"
                          :style="{
                            backgroundColor: item.background,
                            color: item.color,
                            fontSize: item.size + 'px',
                          }"
                        >{{ item.text }}</div>
                      </div>
                      <template v-else-if="item.type === 'img'">
                        <div v-if="item.url" class="img-button" :class="{'active-item': isSelect(item)}">
                          <img
                            class="click-img"
                            :class="{'click-flash': item.flash}"
                            :src="item.url"
                            @load="onButtonImgLoad($event, item)"
                          >
                        </div>
                        <div v-else class="img-tooltip img-button" :class="{'active-item': isSelect(item)}">
                          请填写图片地址
                        </div>
                      </template>
                      <template v-else-if="item.type === 'backup'">
                        <div v-show="backupVisible" class="promotion-card" :class="{'active-item': isSelect(item),'click-flash': item.flash}">
                          <img
                            class="click-img"
                            :src="item.url || defaultImg"
                            @load="onButtonImgLoad($event, item)"
                          >
                          <div v-if="!item.url">
                            <div
                              class="promotion-card-desc"
                              :style="{
                                fontSize: item.descSize + 'px',
                              }"
                            >
                              {{ item.descText }}
                            </div>
                            <div
                              class="promotion-card-btn"
                              :style="{
                                fontSize: item.btnSize + 'px',
                              }"
                            >
                              {{ item.btnText }}
                            </div>
                          </div>
                        </div>
                      </template>
                    </VueDragResize>
                  </template>
                </template>
                <div class="company-name">{{ pageForm.companyName }}</div>
              </template>

              <!-- 公司名称显示 -->
              <div v-if="pageForm.companyName && pageForm.pageType === 'custom'" class="company-name">{{ pageForm.companyName }}</div>
            </div>
          </el-scrollbar>

          <!-- 自定义落地页的固定位置组件 -->
          <template v-if="pageForm.pageType === 'custom' && isRefresh">
            <template v-for="item in pageForm.clickAreas">
              <VueDragResize
                v-if="item.position === 'fixed'"
                :key="item.key"
                :parent-limitation="true"
                :x="item.left"
                :y="item.top"
                :w="item.width"
                :h="item.height"
                :z="item.zIndex"
                @clicked="handleItemSelect(item)"
                @resizestop="onOperateStop($event, item)"
                @dragstop="onOperateStop($event, item)"
              >
                <div v-if="item.type === 'button'" class="click-btn-wrap normal-button" :class="{'active-item': isSelect(item)}">
                  <div
                    class="click-btn"
                    :class="{'click-flash': item.flash}"
                    :style="{
                      backgroundColor: item.background,
                      color: item.color,
                      fontSize: item.size + 'px',
                    }"
                  >{{ item.text }}</div>
                </div>
                <template v-else-if="item.type === 'img'">
                  <div v-if="item.url" class="img-button" :class="{'active-item': isSelect(item)}">
                    <img
                      class="click-img"
                      :class="{'click-flash': item.flash}"
                      :src="item.url"
                      @load="onButtonImgLoad($event, item)"
                    >
                  </div>
                  <div v-else class="img-tooltip" :class="{'active-item': isSelect(item)}">
                    请填写图片地址
                  </div>
                </template>
              </VueDragResize>
            </template>
          </template>
        </div>
      </div>
    </div>
    <div class="item-operation">
      <h4 class="operation-title">
        自定义属性
      </h4>
      <el-form v-if="selectItem.type" :model="selectItem.data" label-width="90px" size="mini" class="operation-form" style="width: 390px">
        <el-form-item label="按钮类型">
          {{ selectItem.label }}
        </el-form-item>
        <template v-if="selectItem.type==='button'">
          <el-form-item label="内容">
            <el-input v-model="selectItem.data.text" />
          </el-form-item>
          <el-form-item label="尺寸">
            <el-slider v-model="selectItem.data.size" :min="12" :max="32" :step="1" />
          </el-form-item>
          <el-form-item label="文本颜色">
            <el-color-picker v-model="selectItem.data.color" />
          </el-form-item>
          <el-form-item label="背景色">
            <el-color-picker v-model="selectItem.data.background" />
          </el-form-item>
          <el-form-item label="跳动">
            <el-switch v-model="selectItem.data.flash" />
          </el-form-item>
          <el-form-item label="按钮定位">
            <el-radio-group v-model="selectItem.data.position" size="small" @input="handlePositionChange">
              <el-radio-button label="absolute">跟随页面滚动</el-radio-button>
              <el-radio-button label="fixed">屏幕固定位置</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </template>
        <template v-else-if="selectItem.type === 'img'">
          <el-form-item label="图片链接">
            <div class="image-btn">
              <el-input
                v-model="selectItem.data.url"
                placeholder="请拷贝图片地址到此处"
                clearable
              />
              <el-button style="margin-left: 10px" @click="showResourceSelector('button')">
                <i class="el-icon-upload" />
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="跳动">
            <el-switch v-model="selectItem.data.flash" />
          </el-form-item>
          <el-form-item label="按钮定位">
            <el-radio-group v-model="selectItem.data.position" size="small">
              <el-radio-button label="absolute">跟随页面滚动</el-radio-button>
              <el-radio-button label="fixed">屏幕固定位置</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </template>
        <template v-else-if="selectItem.type === 'backup'">
          <el-form-item label="自定义图片">
            <div class="image-btn">
              <el-input
                v-model="selectItem.data.url"
                placeholder="不选择则使用默认图片"
                clearable
              />
              <el-button style="margin-left: 10px" @click="showResourceSelector('button')">
                <i class="el-icon-upload" />
              </el-button>
            </div>
            <div v-if="selectItem.data.url" class="backup-img-tooltip">使用自定义图片时请自行调整图片内容</div>
          </el-form-item>
          <template v-if="!selectItem.data.url">
            <el-form-item label="促销文本">
              <el-input v-model="selectItem.data.descText" />
            </el-form-item>
            <el-form-item label="促销文本大小" label-width="98px">
              <el-slider v-model="selectItem.data.descSize" :min="12" :max="48" :step="1" />
            </el-form-item>
            <el-form-item label="按钮文字">
              <el-input v-model="selectItem.data.btnText" />
            </el-form-item>
            <el-form-item label="按钮文字大小" label-width="98px">
              <el-slider v-model="selectItem.data.btnSize" :min="12" :max="32" :step="1" />
            </el-form-item>
          </template>
          <el-form-item label="跳动">
            <el-switch v-model="selectItem.data.flash" />
          </el-form-item>
        </template>
        <el-form-item v-if="selectItem.data.zIndex !== undefined" label="显示层级">
          <el-input-number v-model="selectItem.data.zIndex" />
        </el-form-item>
        <el-form-item>
          <el-button type="warning" icon="el-icon-delete" @click="removeSelectItem">删 除</el-button>
        </el-form-item>
      </el-form>
      <div v-else class="operation-no-select">
        请选择跳转组件修改属性
      </div>
    </div>
    <el-dialog
      title="选择资源文件"
      :visible.sync="resourceVisible"
      width="90%"
      top="2vh"
      append-to-body
    >
      <ResourceManager
        v-bind="resourceSelectorProps"
        @confirm="handleResourceSelect"
        @cancel="handleResourceCancel"
      />
    </el-dialog>
    <GoodsSelector :visible.sync="goodsVisible" type="single" :default-query-params="{platform:goods.platform, mediaPlatformType: goods.mediaPlatformType}" @select="handleBackupGoodsSelect" />

  </div>
</template>

<style scoped lang="scss">
.edit-landing-wrap {
  padding:0 20px 20px ;
  display: flex;
}
.edit-form {
  box-sizing: border-box;
  width: 510px;
  min-width: 500px;
  margin-right: 80px;
  //height: calc(100vh - 100px);
  //overflow-y: auto;
  .landing-rule {
    margin: 0 0 20px 20px;

    .landing-rule-title {
      font-size: 16px;
      font-weight: bold;
    }

    .landing-rule-item {
      margin-bottom: 10px;
    }

    .red {
      color: #f56c6c;
      font-weight: bold;
    }
  }
  .clear-area-icon {
    width: 56px;
    display: inline-block;
    border: 1px solid #DCDFE6;
    border-radius: 5px;
    text-align: center;
    color: white;
    margin-right: 10px;
    cursor: pointer;
    .icon {
      opacity: 0;
    }
    &:hover {
      .icon {
        opacity: 1;
      }
    }
  }
  .images-main-item {
    width: 300px;
    margin-right: 12px;
  }
  .images-item {
    margin-bottom: 10px;
  }
}
.phone-wrap {
  position: relative;
  margin-right: 80px;
  user-select: none;
}
.phone {
  box-sizing: content-box;
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 405px;
  height: 840px;
  border: 10px solid #d1eaf8;
  border-radius: 50px ;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transform: scale(0.75,0.75);
  transform-origin: 0 0;
  .phone-header {
    position: relative;
    height: 82px;
    .phone-title {
      position: absolute;
      top:44px;
      left: 20px;
      width: 250px;
    }
  }

  .scroll-phone-container {
    box-sizing: border-box;
    position: relative;
    flex:1;
    width: 100%;
    overflow: hidden;
  }
  .scroll-phone-main {
    box-sizing: border-box;
    position: relative;
    width: 100%;
    height: 100%;
    //height: 735px; // 728
  }
  .phone-content {
    position: relative;
    overflow-y: hidden;
    overflow-x: hidden;
    min-height: 730px;
  }
  .normal-image {
    -webkit-user-drag: none; -moz-user-drag: none; -ms-user-drag: none; user-drag: none;
  }

  .normal-image, .click-img {
    width: 100%;
    display: block;
    border: 0;
    object-fit: cover;

  }
  .button-image-fixed {
    position: absolute;
    bottom:0;
    width: 100%;
  }
  .select-img {
    border: 2px dashed grey;
  }
}
.phone-tip {
  margin-bottom: 10px;
}
.phone-tip-item {
  color: #999;
  font-size: 14px;
  margin-bottom: 5px;
}
.image-btn {
  display: flex;
}
.dialog-file-res {
  height: calc(100vh - 250px);
  display: flex;
  flex-direction: column;
}
.click-area {
  width: 100%;
  height: 100%;
  border: 1px dashed #00afff;
  background: rgba(0, 0, 0, 0.25);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32px;
  color: white;
}
.click-btn-wrap{
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: row ;
  align-items: center;
}
.click-btn {
  flex: 1;
  background: #00afff;
  padding: 10px;
  text-align: center;
  border-radius: 10px;
  color: white;
}
.click-flash {
  animation-name: flash;
  animation-duration: 2s;
  animation-fill-mode: both;
  animation-iteration-count: infinite;
}

@keyframes flash{
  0%,50%,to {
    opacity: 1;
    transform: scale(1)
  }

  25%,75% {
    opacity: 1;
    transform: scale(.9)
  }
}
.company-name {
  text-align: center;
  font-size: 14px;
  color: #949494;
  line-height: 18px;
}
.item-operation {
  flex:1;
  .operation-title {
  }
  .operation-form {
    padding:10px 20px;
    border: 1px solid #DCDFE6;
  }
  .operation-no-select {
    height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #999;
    border: 1px solid #DCDFE6;
    user-select: none;
  }
}
.img-tooltip {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
  background: rgba(0, 0, 0, 0.25);
}

::v-deep .el-dialog__body {
  padding-top: 0;
  padding-bottom: 0;
}
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
.button-item {
  padding: 10px;
  text-align: center;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s;
  &:hover{
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.2);
  }
  &:active {
    transform: scale(0.98);
    background: rgba(0,0,0,.05);
  }
}
.button-item i {
  font-size: 18px;
}
.backup-active {
  border: 1px solid #DCDFE6;border-radius: 5px;padding: 10px
}
.promotion-card {
  position: relative;
  text-align: center;
  border: #409EFF dashed 1px;
  .promotion-card-desc {
    position: absolute;
    top: 18%;
    right: 20%;
    left: 20%;
    color: #96252b;
  }
  .promotion-card-btn {
    position: absolute;
    top: 71%;
    right: 0;
    left: 0;
    bottom: 0;
    font-size: 22px;
    color: #D53640;
  }
}
.backup-img-tooltip {
  font-size: 14px;
  color: grey;
}
.normal-button {
  border: #F56C6C dashed 1px;
}
.img-button {
  border: #67C23A dashed 1px;
}
.area-button {
  border: #409EFF dashed 1px;
}
.active-item {
  border-width: 3px;
  border-style: solid;
}

/* 模板预览样式 */
.template-preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 10px;
}

.template-preview-label {
  font-size: 12px;
  color: #666;
  text-align: center;
}

</style>
