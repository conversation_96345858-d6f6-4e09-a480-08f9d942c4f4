<template>
  <div class="app-container adaption-container">
    <!-- <div style="margin-bottom: 10px;border-bottom: 1px solid #DCDFE6">
      <Scroller :list="restrictList" @itemClick="handleScrollerClick" />
    </div> -->
    <el-form v-show="showSearch" ref="queryForm" class="search-form" :model="queryParams" size="small" :inline="true" label-width="80px">
      <!-- <el-form-item prop="platform">
        <el-select v-model="queryParams.platform" placeholder="平台类型" clearable @change="handleQuery">
          <el-option
            v-for="dict in platformTypes"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <svg-icon :icon-class="dict.label" />
            <span> {{ dict.label }}</span>
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item v-if="queryParams.platform" prop="linkType">
        <el-select v-model="queryParams.linkType" placeholder="链接类型" clearable @change="handleQuery">
          <el-option
            v-for="dict in (linkTypeMap[queryParams.platform] || dict.type.goods_link_type_tb)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <svg-icon :icon-class="dict.label" />
            <span> {{ dict.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="mediaPlatformType">
        <el-select v-model="queryParams.mediaPlatformType" placeholder="媒体类型" clearable @change="handleQuery">
          <el-option
            v-for="dict in dict.type.media_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <svg-icon :icon-class="dict.label" />
            <span> {{ dict.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="goodsIds">
        <el-input
          v-model.trim="queryParams.goodsIds"
          placeholder="商品ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item prop="pId">
        <el-input
          v-model.trim="queryParams.pId"
          placeholder="推广位ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item prop="goodsName">
        <el-input
          v-model.trim="queryParams.goodsName"
          placeholder="商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="mallId">
        <CompanySelect ref="mallSelect" v-model="queryParams.mallId" :options="duoduoMallList" placeholder="请选择店铺" :is-search="false" @select="handleQuery" />
      </el-form-item>
      <UserSearchInput :create-by.sync="queryParams.createBy" @query="handleQuery" />
      <el-form-item prop="remark">
        <el-input
          v-model.trim="queryParams.remark"
          placeholder="备注"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="currentLimitingState">
        <el-select v-model="queryParams.currentLimitingState" placeholder="推广限制" clearable @change="handleQuery">
          <el-option
            label="限制推广"
            :value="1"
          />
          <el-option
            label="未限制"
            :value="0"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-has-permi="['promotion:goods:duoId']" prop="duoId">
        <el-select v-model="queryParams.duoId" placeholder="多多进宝" clearable @change="handleQuery">
          <el-option
            v-for="dict in DuoDuoJinBao"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="status">
        <el-select v-model="queryParams.status" placeholder="是否停用" clearable @change="handleQuery">
          <el-option label="启用" :value="1" />
          <el-option label="停用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item prop="deptIds">
        <DeptTreeSelect v-model="queryParams.deptIds" :options="deptOptions" />
      </el-form-item>
      <el-form-item label="创建时间" prop="createStart">
        <el-date-picker
          v-model="createRange"
          :popper-class="device === 'mobile' ? 'mobile-date-picker' : ''"
          type="datetimerange"
          :picker-options="device === 'mobile' ? []:dateRangePickerOptions"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          :extra-create-range.sync="createRange"
          @search="handleQuery"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>

      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:goods:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleManualAdd"
        >添加商品</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          plain
          size="mini"
          :disabled="multiple"
          @click="batchUpdateConversion(selections)"
        >修改回传配置</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:goods:remove']"
          plain
          icon="el-icon-download"
          size="mini"
          :disabled="multiple"
          type="danger"
          @click="handleDelete()"
        >批量删除</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:goods:edit']"
          plain
          icon="el-icon-refresh"
          size="mini"
          :disabled="multiple"
          @click="handleBatchRefresh"
        >刷新商品</el-button>
      </el-col>
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>
    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="goodsList"
        v-bind="tableHeight"
        stripe
        border
        :row-class-name="handleRowClass"
        @header-dragend="handleHeaderDragend"
        @selection-change="handleSelectionChange"
        @sort-change="handleTableSort"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          type="index"
          label="序号"
          align="center"
          width="50"
        />
        <template v-for="(c,i) in columns">
          <el-table-column v-if="c.visible !==false" :key="i" :label="c.label" :align="c.align || 'center'" :prop="c.prop" :width="c.width" :sortable="c.sortable" :show-overflow-tooltip="c.overflow">
            <template #header="scope">
              <span>{{ scope.column.label }}</span>
              <el-tooltip v-if="c.tooltip" effect="dark" :content="c.tooltip" placement="top">
                <i class="el-icon-question color-primary" />
              </el-tooltip>
            </template>
            <template #default="scope">
              <template v-if="c.prop === 'Platform'">
                <div class="icon-flex">
                  <div class="type-icon-item">
                    <svg-icon v-if="mediaTypeMap[scope.row.mediaPlatformType]" class-name="type-icon" :icon-class="mediaTypeMap[scope.row.mediaPlatformType]" />
                    <div class="type-label">{{ mediaTypeMap[scope.row.mediaPlatformType] }}</div>
                  </div>
                  <div style="border: 1px solid #dfe6ec" />
                  <div class="type-icon-item">
                    <svg-icon v-if="platformTypeMap[scope.row.platform]" class-name="type-icon" :icon-class="platformTypeMap[scope.row.platform]" />
                    <div class="type-label">{{ platformTypeMap[scope.row.platform] }}</div>
                  </div>
                </div>
              </template>
              <template v-else-if="c.prop === 'GoodsInfo'">
                <div class="table-base-info">
                  <!-- <svg-icon v-if="scope.row.platform === '2' && !scope.row.goodsThumbnailUrl" class="info-img" icon-class="taobao" />
                  <svg-icon v-else-if="scope.row.platform === '6' && !scope.row.goodsThumbnailUrl" class="info-img" icon-class="美团" />
                  <svg-icon v-else-if="scope.row.platform === '8' && !scope.row.goodsThumbnailUrl" class="info-img" icon-class="云台" />
                  <svg-icon v-else-if="scope.row.platform === '9' && !scope.row.goodsThumbnailUrl" class="info-img" icon-class="UDSmart" />
                  <div v-else class="info-img">
                    <image-preview :url="scope.row.goodsThumbnailUrl" :width="50" :height="50" />
                  </div> -->
                  <BaseInfoCell :id="scope.row.goodsId" class="info-wrap" style="flex:1" :name="scope.row.goodsName" @nameClick="showDetail(scope.row)" />
                  <el-tooltip content="备案即将过期" placement="top">
                    <i v-show="promotionOverdue(scope.row)" class="el-icon-question text-danger ml10" style="font-size: 18px" />
                  </el-tooltip>
                  <el-tooltip content="备案已过期" placement="top">
                    <i v-show="promotionOverdue(scope.row,true)" class="el-icon-question text-danger ml10" style="font-size: 18px" />
                  </el-tooltip>
                </div>
              </template>
              <template v-else-if="c.prop === 'action'">
                <el-button
                  v-hasPermi="['promotion:goods:url']"
                  size="mini"
                  type="text"
                  icon="el-icon-paperclip"
                  @click="showGoodsUrl(scope.row)"
                >获取链接</el-button>
                <el-button
                  v-hasPermi="['promotion:landing:list']"
                  size="mini"
                  type="text"
                  icon="el-icon-s-open"
                  :disabled="!scope.row.status"
                  @click="showLandingList(scope.row)"
                >落地页</el-button>
                <el-button
                  v-if="scope.row.platform === '1'&&scope.row.linkType==='3'"
                  v-hasPermi="['promotion:pddCashgift:list']"
                  size="mini"
                  type="text"
                  icon="el-icon-s-open"
                  :disabled="!scope.row.status"
                  @click="showCashGiftList(scope.row)"
                >多多礼金</el-button>
                <el-dropdown
                  placement="bottom-start"
                  trigger="click"
                  :disabled="!scope.row.status"
                  @command="handleCommand($event, scope.row)"
                >
                  <el-button size="mini" type="text" icon="el-icon-more" style="margin: 0 7px">更多</el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      v-hasPermi="['promotion:goods:edit']"
                      command="refreshGoods"
                      icon="el-icon-refresh"
                    >刷新</el-dropdown-item>
                    <el-dropdown-item
                      icon="el-icon-edit"
                      command="editGoods"
                    >编辑</el-dropdown-item>
                    <el-dropdown-item
                      v-hasPermi="['promotion:goods:add']"
                      icon="el-icon-discount"
                      command="conversionProportion"
                    >回传比例</el-dropdown-item>
                    <!-- <el-dropdown-item
                      icon="el-icon-time"
                      command="autoConversion"
                    >扣后补回传</el-dropdown-item> -->
                    <template v-if="scope.row.platform === '1'">
                      <!-- <el-dropdown-item
                        v-hasPermi="['promotion:goods:replace']"
                        icon="el-icon-sort"
                        command="replaceLink"
                      >更换链接</el-dropdown-item>
                      <el-dropdown-item
                        v-if="[1,2,4].includes(scope.row.mediaPlatformType)"
                        v-hasPermi="['promotion:goods:updateSwitch']"
                        icon="el-icon-connection"
                        command="showSwitch"
                      >限推触发事件</el-dropdown-item> -->
                      <!-- <el-dropdown-item
                        v-if="[1,2,4].includes(scope.row.mediaPlatformType)"
                        v-hasPermi="['promotion:goods:updateSwitch']"
                        icon="el-icon-s-ticket"
                        command="showOrderSwitch"
                      >订单触发事件</el-dropdown-item> -->
                      <!-- <el-dropdown-item
                        v-hasPermi="['promotion:goods:updateRecordname']"
                        icon="el-icon-finished"
                        command="showUpdateRecordname"
                      >备案名称审核</el-dropdown-item> -->
                    </template>
                    <!-- <template v-if="scope.row.platform === '8'">
                      <el-dropdown-item
                        v-hasPermi="['promotion:goods:replace']"
                        icon="el-icon-sort"
                        command="replaceAliHealthLink"
                      >更换链接</el-dropdown-item>
                    </template> -->
                    <el-dropdown-item
                      icon="el-icon-s-order"
                      command="showUpdateRemark"
                    >添加备注</el-dropdown-item>
                    <!-- <el-dropdown-item
                      icon="  "
                      command="setRoi"
                    >设置盈亏参数</el-dropdown-item> -->
                    <!-- <el-dropdown-item
                      icon="el-icon-s-flag"
                      command="setProfitConfig"
                    >成本配置</el-dropdown-item> -->
                    <!-- <el-dropdown-item
                      v-hasPermi="['promotion:goods:list']"
                      icon="el-icon-plus"
                      command="landingPage"
                    >
                      巨量第三方落地页
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="scope.row.mediaPlatformType ===10 && scope.row.platform === '10'"
                      v-hasPermi="['promotion:goods:expertXhsCommissionRate']"
                      icon="el-icon-data-line"
                      command="xhsCommissionRate"
                    >
                      红猫佣金比例
                    </el-dropdown-item> -->
                    <!-- <el-dropdown-item
                      v-if="['1'].includes(scope.row.platform)"
                      v-hasPermi="['promotion:goods:authQuery']"
                      icon="el-icon-view"
                      command="filingInformation"
                    >
                      查看备案信息
                    </el-dropdown-item> -->
                    <!-- <el-dropdown-item
                      v-hasPermi="['promotion:goods:disabledGoodsConversion']"
                      icon="el-icon-sell"
                      command="disabledConversion"
                      style="color: #f56c6c"
                    >禁止回传</el-dropdown-item> -->
                    <el-dropdown-item
                      v-hasPermi="['promotion:goods:remove']"
                      icon="el-icon-delete"
                      command="handleDelete"
                      style="color: #f56c6c"
                    >删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <!-- <template v-if="scope.row.currentLimitingState !== 1 && scope.row.platform === '1'">
                <el-button
                  v-if=" ([0,1,4,7].includes(scope.row.putOnRecordState) || (scope.row.putOnRecordState===3 && promotionOverdue(scope.row, true))|| promotionOverdue(scope.row))"
                  v-hasPermi="['promotion:goods:detail']"
                  size="mini"
                  type="text"
                  icon="el-icon-s-order"
                  :disabled="!scope.row.status"
                  @click="showRecords(scope.row)"
                >备案</el-button>
                <el-button
                  v-if="[6].includes(scope.row.putOnRecordState)"
                  v-hasPermi="['promotion:config:duoduo']"
                  size="mini"
                  type="text"
                  icon="el-icon-s-order"
                  :disabled="!scope.row.status"
                  @click="showRecords(scope.row)"
                >备案</el-button>
                </template>
                <el-button
                  v-if="(scope.row.platform === '2' || scope.row.platform === '7') && [2].includes(scope.row.putOnRecordState)"
                  v-hasPermi="['promotion:goods:plantPageConvert']"
                  size="mini"
                  type="text"
                  icon="el-icon-s-order"
                  :disabled="!scope.row.status"
                  @click="showReview(scope.row)"
                >审核</el-button>
                <el-button
                  v-if="scope.row.platform === '6' && [2].includes(scope.row.putOnRecordState)"
                  v-hasPermi="['promotion:goods:meituanAuditor']"
                  size="mini"
                  type="text"
                  icon="el-icon-s-order"
                  :disabled="!scope.row.status"
                  @click="meituanAuditor(scope.row)"
                >审核</el-button>-->
              </template>
              <template v-else-if="c.prop === 'status'">
                <el-switch
                  v-model="scope.row.status"
                  class="mr5"
                  active-color="#DCDFE6"
                  inactive-color="#ff4949"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handleVaildChange(scope.row)"
                />
                <span v-if="!scope.row.status" class="color-danger text-bold">停用</span>
              </template>
              <template v-else-if="c.prop === 'url'">
                <a v-if="scope.row.platform === '1'" :href="scope.row.url" target="_blank" style="color: #00afff">链接</a>
                <a v-else-if="scope.row.goodsLink" :href="scope.row.goodsLink" target="_blank" style="color: #00afff">链接</a>
                <span v-else>-</span>
              </template>
              <template v-else-if="c.prop === 'pId'">
                <span>{{ scope.row.pId || '-' }}</span>
              </template>
              <template v-else-if="c.prop === 'autoRedirect'">
                <el-tag :type="scope.row.autoRedirect === 1 ? 'success' : 'info'">
                  {{ scope.row.autoRedirect === 1 ? '是' : '否' }}
                </el-tag>
              </template>
              <template v-else-if="c.prop === 'shopAttribute'">
                <el-tag :type="scope.row.shopAttribute ? 'success' : 'info'">
                  {{ scope.row.shopAttribute ? '开启' : '关闭' }}
                </el-tag>
              </template>
              <template v-else-if="c.prop === 'dailyOrderCount'">
                <span>{{ scope.row.dailyOrderCount || 0 }}</span>
              </template>
              <template v-else-if="c.prop === 'exposureMonitorUrl'">
                <span v-if="scope.row.exposureMonitorUrl">{{ scope.row.exposureMonitorUrl }}</span>
                <span v-else>-</span>
              </template>
              <template v-else-if="c.prop === 'clickMonitorUrl'">
                <span v-if="scope.row.clickMonitorUrl">{{ scope.row.clickMonitorUrl }}</span>
                <span v-else>-</span>
              </template>
              <template v-else-if="c.prop === 'weAppUrl'">
                <span v-if="scope.row.weAppUrl">{{ scope.row.weAppUrl }}</span>
                <span v-else>-</span>
              </template>
              <template v-else-if="c.prop === 'remark'">
                <span class="pointer" @click="showUpdateRemark(scope.row)">
                  <span v-if="scope.row.remark" style="white-space:pre;">{{ scope.row.remark }}</span>
                  <span v-else>—</span>
                </span>
              </template>
              <template v-else-if="c.prop === 'enableAmount'">
                <ConversionItem
                  :form.sync="scope.row"
                  item-type="amount"
                  :type="conversionType"
                  :disabled="!scope.row.status"
                  @setConversion="showConversion"
                  @switchConversion="getList"
                />
              </template>
              <template v-else-if="c.prop === 'conversionProportion'">
                <ConversionItem
                  :form.sync="scope.row"
                  item-type="order"
                  :type="conversionType"
                  :disabled="!scope.row.status"
                  @setConversion="showConversion"
                  @switchConversion="getList"
                />
              </template>
              <template v-else-if="c.prop === 'currentLimitingState'">
                <el-tag v-if="scope.row.currentLimitingState === 1" type="danger">
                  限制推广
                </el-tag>
                <span v-else>-</span>
              </template>
              <template v-else-if="c.prop === 'putOnRecordState'">
                <template v-if="scope.row.putOnRecordState!==undefined">
                  <dict-tag v-if="scope.row.putOnRecordState!==5" :options="dict.type.record_state" :value="scope.row.putOnRecordState" />
                  <el-tooltip v-else class="item" content="更新状态" placement="top">
                    <el-tag type="primary">
                      备案中
                      <i class="el-icon-refresh pointer" @click="getList" />
                    </el-tag>
                  </el-tooltip>
                </template>
              </template>
              <template v-else-if="c.prop === 'putOnRecordFailComment'">
                <el-tooltip v-if="scope.row.putOnRecordFailComment" class="item" :content="scope.row.putOnRecordFailComment" placement="top">
                  <i class="el-icon-warning" style="color:#F56C6C;font-size: 16px" />
                </el-tooltip>
                <span v-else>-</span>
              </template>
              <template v-else-if="c.prop === 'disabledGoodsConversion'">
                <el-button type="text" :style="{color: scope.row.disabledGoodsConversion?'#ff4d4f':'#a1a1a1'}" @click="disabledConversion(scope.row)">
                  {{ scope.row.disabledGoodsConversion ? '是': '否' }}
                </el-button>
              </template>
              <template v-else-if="c.prop === 'profit'">
                <el-button type="text" @click="setRoi(scope.row)">{{ scope.row.profit || '-.--' }}</el-button>
              </template>
              <template v-else-if="c.prop === 'profitRoi'">
                <el-button type="text" @click="setRoi(scope.row)">{{ scope.row.profitRoi || '-.--' }}</el-button>
              </template>
              <template v-else-if="c.prop === 'profitBackRate'">
                <el-button type="text" @click="setRoi(scope.row)">{{ scope.row.profitBackRate ? scope.row.profitBackRate / 10 + '%' : '--' }}</el-button>
              </template>
              <template v-else-if="c.prop === 'minGroupPrice'">
                <div style="text-align: left; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                  <span style="display: inline-block; width: 92px;">最小拼团价: </span>
                  <span>{{ scope.row.minGroupPrice ? formatPrice(scope.row.minGroupPrice) : '' }}</span>
                </div>
                <div style="text-align: left;">
                  <span style="display: inline-block; width: 92px;">最小单买价格: </span>
                  <span>{{ scope.row.minNormalPrice ? formatPrice(scope.row.minNormalPrice) : '' }}</span>
                </div>
              </template>
              <template v-else-if="c.prop === 'linkType'">
                <dict-tag :options="linkTypeMap[scope.row.platform]" :value="scope.row.linkType" />
              </template>
              <template v-else-if="c.prop === 'UserInfo'">
                <el-tooltip :disabled="!scope.row.nickName" effect="dark" :content="scope.row.nickName" placement="top">
                  <div style="text-align: left; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                    <span style="display: inline-block; width: 64px;">用户昵称: </span>
                    <span>{{ scope.row.nickName ?? '' }}</span>
                  </div>
                </el-tooltip>
                <div style="text-align: left;">
                  <span style="display: inline-block; width: 64px;">负责人: </span>
                  <span>{{ scope.row.createBy ?? '' }}</span>
                </div>
              </template>
              <template v-else>
                <RenderComponent v-if="c.render" :render="c.render(scope.row)" />
                <BaseInfoCell v-else-if="c.info" :id="scope.row[c.info.id]" :name="scope.row[c.info.name]" :label="c.info.label" :sub-label="c.info.subLabel" />
                <TableColumnSet v-else-if="c.set" :set="c.set" :row="scope.row" :label-width="c.labelWidth" />
                <template v-else>{{ scope.row[c.prop] }}</template>
                <ClipboardButton v-if="c.copy" :value="c.render ? c.render(scope.row) : scope.row[c.prop]" />
              </template>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加商品信息对话框 -->
    <el-dialog title="添加商品" :visible.sync="openCreate" top="3vh" width="1420px" append-to-body :close-on-click-modal="false" :fullscreen="device==='mobile'">
      <AddGoods v-if="openCreate" @success="handleCreateSuccess" @cancel="openCreate = false" />
    </el-dialog>
    <!-- 手动添加商品对话框 -->
    <ManualAddGoods :visible.sync="manualAddVisible" @success="handleManualAddSuccess" />
    <!-- 复制商品 -->
    <el-dialog v-loading="formLoading" title="复制商品" :visible.sync="openCopy" width="700px" append-to-body :fullscreen="device==='mobile'">
      <el-form v-if="openCopy" ref="copyForm" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="商品ID" prop="goodsId">
          {{ form.goodsId }}
        </el-form-item>
        <el-form-item label="商品名称" prop="goodsName">
          {{ form.goodsName }}
        </el-form-item>
        <el-form-item label="媒体类型" prop="mediaPlatformTypeArr">
          <el-checkbox-group v-model="form.mediaPlatformTypeArr">
            <el-checkbox
              v-for="dict in dict.type.media_type"
              :key="dict.value"
              :label="dict.value"
              :disabled="+dict.value === form.mediaPlatformType"
            >
              <svg-icon :icon-class="dict.label" />
              <span> {{ dict.label }}</span>
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-loading="submitLoading" type="primary" @click="submitCopy">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 显示商品信息 -->
    <el-drawer
      title="商品信息"
      :size="800"
      :visible.sync="infoDrawerVisible"
      destroy-on-close
    >
      <div class="info-drawer-body">
        <div class="main-info">
          <div class="info-title">
            {{ form.goodsName }}
          </div>
          <el-row>
            <el-col :span="8">
              <svg-icon
                v-if="form.platform === '2' && !form.goodsThumbnailUrl"
                style="width: 200px; height: 200px"
                icon-class="taobao"
              />
              <el-image
                v-else
                style="width: 200px; height: 200px"
                :src="form.goodsThumbnailUrl"
              />
            </el-col>
            <el-col :span="16">
              <el-descriptions :column="1" :label-style="desStyle" :content-style="desStyle">
                <el-descriptions-item label="平台类型">
                  <dict-tag :options="dict.type.platform_type" :value="form.platform" />
                </el-descriptions-item>
                <el-descriptions-item label="商品ID">
                  {{ form.goodsId }}
                </el-descriptions-item>
                <el-descriptions-item v-if="isTb(form.platform)" label="动态ID">
                  {{ form.numIid }}
                </el-descriptions-item>
                <!--          <el-descriptions-item label="商品Sign" prop="goodsSign">-->
                <!--            {{form.goodsSign}}-->
                <!--          </el-descriptions-item>-->
                <el-descriptions-item label="商品链接">
                  <a v-if="form.platform === '1'" :href="form.url" target="_blank" style="color: #00afff">链接</a>
                  <a v-else-if="form.goodsLink" :href="form.goodsLink" target="_blank" style="color: #00afff">链接</a>
                  <span v-else>-</span>
                </el-descriptions-item>
                <!--              <el-descriptions-item label="商品标签ID" >-->
                <!--                {{form.optId}}-->
                <!--              </el-descriptions-item>-->
                <el-descriptions-item label="商品标签名称">
                  {{ form.optName }}
                </el-descriptions-item>
                <el-descriptions-item label="商品描述">
                  {{ form.goodsDesc }}
                </el-descriptions-item>
              </el-descriptions>
            </el-col>
          </el-row>
          <el-divider />
          <el-descriptions :column="2" :label-style="desStyle" :content-style="desStyle">
            <el-descriptions-item label="最小拼团价">
              {{ formatPrice(form.minGroupPrice) }}元
            </el-descriptions-item>
            <el-descriptions-item label="最小单买价格">
              {{ formatPrice(form.minNormalPrice) }}元
            </el-descriptions-item>
            <el-descriptions-item label="店铺id">
              {{ form.mallId }}
            </el-descriptions-item>
            <el-descriptions-item label="店铺名称">
              {{ form.mallName }}
            </el-descriptions-item>
            <el-descriptions-item label="店铺类型">
              <dict-tag :options="dict.type.merchant_type" :value="form.merchantType" />
            </el-descriptions-item>
            <el-descriptions-item label="已售卖件数">
              {{ form.salesTip }}
            </el-descriptions-item>
            <el-descriptions-item label="服务分">
              {{ form.servTxt }}
            </el-descriptions-item>
            <el-descriptions-item label="推广计划类型">
              <dict-tag :options="dict.type.goods_plan_type" :value="form.planType" />
            </el-descriptions-item>
            <el-descriptions-item label="链接类型">
              <dict-tag :options="linkTypeMap[form.platform]" :value="form.linkType" />
            </el-descriptions-item>
            <el-descriptions-item label="商品标签ID">
              {{ form.optId }}
            </el-descriptions-item>
            <el-descriptions-item v-if="form.platform === '1'" label="备案状态">
              <dict-tag v-if="form.putOnRecordState!==undefined" :options="dict.type.record_state" :value="form.putOnRecordState" />
            </el-descriptions-item>
            <el-descriptions-item v-if="form.platform === '1'" label="失败原因">
              {{ form.putOnRecordFailComment }}
            </el-descriptions-item>
            <el-descriptions-item label="商品Sign" :span="2">
              {{ form.goodsSign }}
            </el-descriptions-item>
            <el-descriptions-item label="搜索id" :span="2">
              {{ form.searchId }}
            </el-descriptions-item>
            <el-descriptions-item label="淘积木链接" :span="2">
              <url-clipboard v-if="form.tjmUrl" :url="form.tjmUrl" />
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="淘积木原链接" :span="2">
              <url-clipboard v-if="form.originalTjmUrl" :url="form.originalTjmUrl" />
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="优惠券链接" :span="2">
              <url-clipboard v-if="form.couponUrl" :url="form.couponUrl" />
              <span v-else>-</span>
            </el-descriptions-item>
          </el-descriptions>
          <template v-if="form.platform === '1'">
            <el-divider />
            <h3>备案信息</h3>
            <el-descriptions :column="2" :label-style="desStyle" :content-style="desStyle">
              <el-descriptions-item label="商品名称" :span="2">
                {{ form.goodsName }}
              </el-descriptions-item>
              <el-descriptions-item label="商品ID">
                {{ form.goodsId }}
              </el-descriptions-item>
              <el-descriptions-item label="店铺名称">
                {{ form.mallName }}
              </el-descriptions-item>
              <el-descriptions-item label="店铺ID">
                {{ form.mallId }}
              </el-descriptions-item>
              <el-descriptions-item label="推广时间段">
                {{ parseTime(form.promotionStart,'{y}-{m}-{d}') }} - {{ parseTime(form.promotionEnd,'{y}-{m}-{d}') }}
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions :column="2" :label-style="desStyle" :content-style="desStyle" direction="vertical">
              <el-descriptions-item label="营业执照">
                <FileResSelector v-if="form.businessLicense" v-model="form.businessLicense" :edit="false" />
                <span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="商品图片素材">
                <FileResSelector v-if="form.goodsImageUrl" v-model="form.goodsImageUrl" :edit="false" /><span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="视频素材">
                <FileResSelector v-if="form.videoUrl" v-model="form.videoUrl" type="video" :edit="false" /><span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="视频预览码">
                <FileResSelector v-if="form.goodsThumbnailUrl" v-model="form.goodsThumbnailUrl" :edit="false" /><span v-else>-</span>
              </el-descriptions-item>
            </el-descriptions>
          </template>
        </div>
        <el-button class="info-drawer-action" type="danger" plain @click="infoDrawerVisible=false">关闭</el-button>
      </div>
    </el-drawer>
    <!--  修改商品  -->
    <el-dialog title="修改商品信息" :visible.sync="editVisible" width="770px" append-to-body top="10vh">
      <EditGoods v-if="editVisible" :goods="form" @success="handleEditSuccess" @cancel="editVisible=false" />
    </el-dialog>
    <!--  链接展示  -->
    <el-dialog title="推广链接" :visible.sync="urlVisible" width="900px" append-to-body top="10vh" class="dialog-pt-0">
      <URLGetter v-if="urlVisible" :goods="selectedGoods" show-personal-btn @openLanding="(goods) => {urlVisible = false;showLandingList(goods)}" />
    </el-dialog>
    <!-- 备案弹窗 -->
    <el-dialog
      title="商品备案"
      :visible.sync="recordVisible"
      width="700px"
      top="2vh"
      append-to-body
    >
      <template v-if="recordType === 'batch'">
        <el-table
          ref="goodsTableRef"
          :data="selections"
          border
          stripe
          height="200px"
          size="mini"
          class="mb20"
        >
          <el-table-column
            prop="goodsName"
            label="商品名称"
          >
            <template #default="scope">
              <div class="table-base-info">
                <BaseInfoCell :id="scope.row.goodsId" class="info-wrap flex1" :name="scope.row.goodsName" line-clamp="1" />
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="goodsName"
            label="店铺信息"
          >
            <template #default="scope">
              <div class="table-base-info">
                <BaseInfoCell :id="scope.row.mallId" class="info-wrap" :name="scope.row.mallName" />
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="goodsImageUrl"
            label="商品图片素材"
            width="100"
            align="center"
          >
            <template #default="{row}">
              <image-preview :src="row.goodsImageUrl" :width="40" :height="40" />
            </template>
          </el-table-column>
          <el-table-column
            prop="goodsThumbnailUrl"
            label="视频预览码"
            width="100"
            align="center"
          >
            <template #default="{row}">
              <image-preview :src="row.goodsThumbnailUrl" :width="40" :height="40" />
            </template>
          </el-table-column>
        </el-table>
      </template>
      <el-form ref="recordForm" :model="form" :rules="recordRules" label-width="120px">
        <template v-if="recordType === 'single'">
          <el-form-item label="商品名称" prop="goodsName">
            <el-input v-model="form.goodsName" placeholder="请输入商品名称" disabled />
          </el-form-item>
          <el-form-item label="商品ID" prop="goodsId">
            <el-input v-model="form.goodsId" placeholder="请输入商品ID" disabled />
          </el-form-item>
          <el-form-item label="店铺名称" prop="mallName">
            <el-input v-model="form.mallName" placeholder="请输入店铺名称" disabled />
          </el-form-item>
          <el-form-item label="店铺ID" prop="mallId">
            <el-input v-model="form.mallId" placeholder="请输入店铺id" disabled />
          </el-form-item>
        </template>
        <el-form-item v-if="checkRole(['admin'])" label="推广时间段" prop="promotionRange">
          <el-date-picker
            v-model="promotionRange"
            style="width: 400px;margin-right: 10px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="false"
            clearable
          />
        </el-form-item>
        <el-form-item v-if="recordType === 'single'" label="营业执照" prop="businessLicense">
          <FileResSelector v-if="form.businessLicense" v-model="form.businessLicense" :edit="false" default-selected="备案资源" />
          <span v-else>-</span>
        </el-form-item>
        <el-form-item v-if="recordType === 'single'" label="商品图片素材" prop="goodsImageUrl">
          <FileResSelector v-model="form.goodsImageUrl" default-selected="备案资源" />
        </el-form-item>
        <el-form-item label="视频素材" prop="videoUrl">
          <FileResSelector v-model="form.videoUrl" type="video" default-selected="备案资源" />
          <div>上传10秒左右，与商品相关的视频</div>
          <div v-if="recordType === 'batch'" class="color-danger">注意: 批量备案只能选择相同的商品，否则会备案失败</div>
        </el-form-item>
        <el-form-item v-if="recordType === 'single'" label="视频预览码" prop="goodsThumbnailUrl">
          <FileResSelector v-model="form.goodsThumbnailUrl" default-selected="备案资源" />
          <div>
            推广视频的预览图
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <span v-show="recordType === 'single'">
          <el-button v-hasPermi="['promotion:goods:auth']" v-loading="recordSubmitLoading" type="primary" @click="checkGoodsRecord(1)">拼多多备案</el-button>
          <el-button v-hasPermi="['promotion:goods:auth']" v-loading="recordSubmitLoading" type="warning" @click="checkGoodsRecord(2)">拒绝</el-button>
          <el-button v-hasPermi="['promotion:goods:restrict']" v-loading="recordSubmitLoading" type="primary" @click="saveGoodsRecord(1)">保存并备案</el-button>
          <el-button v-loading="recordSubmitLoading" type="primary" @click="saveGoodsRecord(0)">仅保存</el-button>
        </span>
        <span v-show="recordType === 'batch'">
          <el-button v-hasPermi="['promotion:goods:auth']" v-loading="recordSubmitLoading" type="primary" @click="handleBatchAuth('auth')">拼多多备案</el-button>
          <el-button v-hasPermi="['promotion:goods:restrict']" v-loading="recordSubmitLoading" type="primary" @click="handleBatchAuth('restrict')">保存并备案</el-button>
        </span>
        <el-button class="ml10" @click="cancelRecord">取 消</el-button>
      </div>
    </el-dialog>
    <!--  自建落地页  -->
    <el-drawer
      title="落地页列表"
      :visible.sync="landingListVisible"
      direction="rtl"
      size="100%"
      destroy-on-close
    >
      <LandingList :goods="form" :platform-type="form.mediaPlatformType" @close="landingListVisible=false" @refresh="handleRefresh" />
    </el-drawer>
    <!--  替换商品链接  -->

    <el-dialog
      title="替换商品链接"
      :visible.sync="replaceLinkVisible"
      width="80%"
      append-to-body
    >
      <ReplaceLink v-if="replaceLinkVisible" :goods="form" :platform-type="form.mediaPlatformType" @replace="handleRLClose" @close="replaceLinkVisible = false" />
    </el-dialog>
    <!--  替换阿里健康链接  -->
    <el-dialog
      title="替换阿里健康链接"
      :visible.sync="replaceAliHealthLinkVisible"
      width="80%"
      append-to-body
    >
      <ReplaceAliHealthLink v-if="replaceAliHealthLinkVisible" :goods="form" :platform-type="form.mediaPlatformType" @replace="handleRLClose" @close="replaceAliHealthLinkVisible = false" />
    </el-dialog>

    <!--  一键移交  -->
    <el-dialog
      title="选择一键移交目标用户"
      :visible.sync="transferVisible"
      width="50%"
      top="10vh"
      append-to-body
    >
      <el-form size="small" label-width="100px">
        <el-form-item label="移交订单数据">
          <div style="display: flex;align-items: center">
            <el-switch
              v-model="synchronousOrNot"
              active-color="#13ce66"
              style="margin-right: 10px"
            />
            {{ synchronousOrNot?'是':'否' }}
          </div>
        </el-form-item>
      </el-form>
      <TransferUser @select="handleTransferSelect" /></el-dialog>

    <!--  备案名称审核  -->
    <el-dialog
      title="备案名称审核"
      :visible.sync="recordNameVisible"
      width="500px"
      top="10vh"
      append-to-body
    >
      <el-tooltip class="item" effect="dark" :content="currentRecordGoods.recordGoodsName" placement="top">
        <p class="overflow-text">备案名称：<span style="font-weight:bold">{{ currentRecordGoods.recordGoodsName || '-' }}</span></p>
      </el-tooltip>
      <p style="text-align: center"><i class="el-icon-bottom" />修改为</p>
      <el-tooltip class="item" effect="dark" :content="currentRecordGoods.goodsName" placement="top">
        <p class="overflow-text">商品名称：<span style="font-weight:bold">{{ currentRecordGoods.goodsName || '-' }}</span></p>
      </el-tooltip>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleUpdateRecordname">确认</el-button>
        <el-button @click="recordNameVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!--  备注  -->
    <el-dialog
      title="添加备注"
      :visible.sync="remarkVisible"
      width="700px"
      top="10vh"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="60px">
        <el-form-item label="商品" prop="goodsName">
          {{ form.goodsName }}
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4}"
            placeholder="请输入内容"
            clearable
            resize="none"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveRemark">确认</el-button>
        <el-button @click="remarkVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!--  roi  -->
    <SetProfit :visible.sync="profitVisible" :items="profitItems" type="goods" @success="getList" />
    <GoodsProfitConfigDialog :visible.sync="profitConfigVisible" :goods="form" @success="getList" />

    <el-dialog
      :title="switchForm.type?'限推触发事件':'订单触发事件'"
      :visible.sync="switchVisible"
      :width="switchForm.pauseSwitch === 2 ? '80%' : '500px'"
      top="5vh"
      append-to-body
    >
      <div v-if="isInPool" type="danger" class="in-pool">提示：当前商品已添加到商品池中，设置{{ switchForm.type?'限推触发事件':'订单触发事件' }}将不会生效；如需触发当前事件请将本商品<ClipboardButton :value="form.goodsId" />从商品池移除。</div>
      <el-form ref="joinForm" :model="switchForm" label-width="90px">
        <el-form-item v-if="switchForm.type === 0" label="商品订单数" prop="goodsCount">
          <el-input-number v-model="switchForm.goodsCount" :min="0" :max="10000000" :precision="0" label="请输入配置商品订单数" :controls="false" style="width: 150px" />
          <el-tooltip effect="dark" content="当商品订单数超过该值时，触发下列动作" placement="top">
            <i class="el-icon-question color-primary" />
          </el-tooltip>
        </el-form-item>
        <el-form-item label="操作" prop="switch">
          <el-radio-group v-model="switchForm.pauseSwitch">
            <el-radio-button :label="0">关闭</el-radio-button>
            <el-radio-button :label="1">暂停计划
              <el-tooltip effect="dark" :content="switchForm.type?'开启后，当商品被限制推广时，自动暂停计划组或项目':'开启后，当订单超过设定订单数时，自动暂停计划组或项目'" placement="top">
                <i class="el-icon-question color-primary" />
              </el-tooltip>
            </el-radio-button>
            <el-radio-button :label="2">替换链接
              <el-tooltip effect="dark" :content="switchForm.type?'选择一个商品，当商品被限制推广时，自动使用选取的商品替代旧商品链接':'选择一个商品，当订单超过设定订单数时，自动使用选取的商品替代旧商品链接'" placement="top">
                <i class="el-icon-question color-primary" />
              </el-tooltip>
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <MediaAccountSelector v-if="switchVisible" v-show="switchForm.pauseSwitch === 1" :type="switchForm.type" :goods="form" @change="handleAccountSelect" />
        <ReplaceTable v-if="switchVisible" v-show="switchForm.pauseSwitch === 2" :type="switchForm.type" :goods="form" @replace="handleSwitchReplace" @close="handleSwitchReplaceCancel" />
      </el-form>
      <div v-if="switchForm.pauseSwitch !== 2" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSwitchReplace">确 定</el-button>
        <el-button @click="handleSwitchReplaceCancel">取 消</el-button>
      </div>
    </el-dialog>

    <!--  禁止回传  -->
    <el-dialog
      title="禁止回传"
      :visible.sync="conversionVisible"
      width="700px"
      top="10vh"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="70px">
        <el-form-item label="商品" prop="goodsName">
          {{ form.goodsName }}
        </el-form-item>
        <el-form-item label="禁止回传" prop="goodsConversionDisabled">
          <el-switch
            v-model="goodsConversionDisabled"
            :active-value="1"
            :inactive-value="0"
          />
          {{ goodsConversionDisabled?'是':'否' }}
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveConversion">确认</el-button>
        <el-button @click="conversionVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="请输入种草页地址或淘积木地址"
      :visible.sync="plantVisible"
      width="700px"
      top="10vh"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="88px">
        <el-form-item label="商品">
          {{ form.goodsName }}
          <ClipboardButton :value="form.goodsName" />
        </el-form-item>
        <el-form-item label="商品ID">
          {{ form.goodsId }}
          <ClipboardButton :value="form.goodsId" />
        </el-form-item>
        <el-form-item label="媒体类型">
          <svg-icon v-if="mediaTypeMap[form.mediaPlatformType]" class-name="type-icon" :icon-class="mediaTypeMap[form.mediaPlatformType]" />
          {{ mediaTypeMap[form.mediaPlatformType] }}
        </el-form-item>
        <el-form-item label="链接类型">
          <dict-tag :options="form.platform==='1'?dict.type.goods_link_type_pdd:dict.type.goods_link_type_tb" :value="form.linkType" />
        </el-form-item>
        <el-form-item v-if="form.platform === '2' && form.linkType === '2'" label="淘积木链接">
          {{ form.originalTjmUrl }}
          <ClipboardButton :value="form.originalTjmUrl" />
        </el-form-item>
        <el-form-item label="地址" prop="plantUrl">
          <el-input v-model="plantUrl" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="urlLoading" type="primary" @click="submitPlantUrl">确认</el-button>
        <el-button @click="plantVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="修改回传配置" :visible.sync="conversionDialogVisible" width="820px" top="5vh" append-to-body>
      <ConversionSetups v-if="conversionDialogVisible" :form="conversionForm" :form-type="conversionFormType" :type="conversionType" @save="handleConversionSave" @cancel="conversionDialogVisible = false" />
    </el-dialog>

    <el-dialog title="扣后补回传配置" :visible.sync="autoConversionDialogVisible" width="820px" top="5vh" append-to-body>
      <AutoConversionSetups v-if="autoConversionDialogVisible" :form="autoConversionForm" :type="autoConversionType" @save="handleAutoConversionSave" @cancel="handleAutoConversionCancel" />
    </el-dialog>
    <!-- 添加落地页 -->
    <landingComponent :visible.sync="landingVisible" :info="form" type="goods" @success="getList" />
    <!--商品提报列表  -->
    <productReportList v-model="productReportVisible" :list="productRow" />
    <!-- 查看备案信息 -->
    <FilingInfo v-if="filingVisible" :visible.sync="filingVisible" :form="filingInfo" />
    <!--  多多礼金  -->
    <el-drawer
      title="多多礼金"
      :visible.sync="cashGiftVisible"
      direction="rtl"
      size="100%"
      destroy-on-close
    >
      <cashGiftList v-if="cashGiftVisible" :goods="form" :platform-type="form.mediaPlatformType" @close="cashGiftVisible=false" @refresh="handleRefresh" />
    </el-drawer>

    <!-- 多多转链 -->
    <el-dialog
      title="多多转链"
      :visible.sync="pddLinkConvertVisible"
      width="800px"
      top="5vh"
      append-to-body
      :close-on-click-modal="false"
    >
      <PddLinkConvert v-if="pddLinkConvertVisible" @cancel="pddLinkConvertVisible = false" />
    </el-dialog>

    <el-dialog title="红猫达人佣金比例" :visible.sync="xhsCommissionRateVisible" width="400PX" append-to-body>
      <el-form>
        <el-form-item label="比例">
          <el-input-number v-model="xhsCommissionRate" :min="0" :max="100" :controls="false" />
          %
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" />
      <el-button type="primary" @click="submitXhsCommissionRate">确 定</el-button>
      <el-button @click="xhsCommissionRateVisible = false">取 消</el-button>
    </el-dialog>
  </div>
</template>

<script>
import {
  listGoods,
  getGoods,
  delGoods,
  updateGoods,
  refreshPddGoods,
  transfer,
  updateRecordname,
  restrictGoods,
  authGoods,
  updateSwitch,
  updateRemark,
  getGoodRestrict,
  taobaoPlantPageConvert,
  copyGoods,
  disabledGoodsConversion,
  handleMeituanAuditor,
  handleGoodsBra,
  setExpertXhsCommissionRate,
  getExpertXhsCommissionRate,
  selectDuoDuoMallList
} from '@/api/promotion/goods'
import { getGoodsPoolVOByGoodsId } from '@/api/promotion/pool'
import { mapGetters } from 'vuex'
import dayjs from 'dayjs'
import isToday from 'dayjs/plugin/isToday'
import { formatPrice, getExistingObj, idsToArr } from '@/utils'
import { loadPageSize } from '@/utils/beforeList'
import { urlReg } from '@/utils/validate'
import LandingList from './components/LandingList.vue'
import ReplaceLink from './components/ReplaceLink.vue'
import ReplaceAliHealthLink from './components/ReplaceAliHealthLink.vue'
import ReplaceTable from './components/ReplaceTable.vue'
import Scroller from './components/Scroller.vue'
import MediaAccountSelector from './components/MediaAccountSelector.vue'
import landingComponent from './components/landingPage.vue'
import productReportList from './components/productReportList.vue'
import ConversionSetups from '@/components/ConversionSetups/index.vue'
import AutoConversionSetups from '@/components/AutoConversionSetups/index.vue'
import FileResSelector from '@/components/FileResSelector/index.vue'
import TransferUser from '@/components/TransferUser/index.vue'
import DeptTreeSelect from '@/components/DeptTreeSelect/DeptTreeSelect.vue'
import CompanySelect from '@/components/CtreeSelect/CompanySelect.vue'
import URLGetter from '@/components/URLGetter/index.vue'
import SetProfit from '@/components/SetProfit/index.vue'
import GoodsProfitConfigDialog from '@/components/SetProfitConfig/GoodsProfitConfigDialog.vue'
import { checkRole } from '@/utils/permission'
import FilingInfo from './components/FilingInfo.vue'
import cashGiftList from './components/cashGiftList.vue'
import SavedSearches from '@/components/SavedSearches/index.vue'
import PddLinkConvert from './components/PddLinkConvert.vue'

dayjs.extend(isToday)
export default {
  name: 'Goods',
  components: {
    DeptTreeSelect,
    CompanySelect,
    MediaAccountSelector, LandingList, ReplaceLink, ReplaceTable, FileResSelector, TransferUser, Scroller,
    URLGetter,
    SetProfit,
    ConversionSetups,
    AutoConversionSetups,
    landingComponent,
    productReportList,
    FilingInfo, cashGiftList, SavedSearches, PddLinkConvert
  },
  dicts: ['goods_link_type', 'merchant_type', 'goods_plan_type', 'platform_type', 'media_type', 'record_state', 'goods_link_type_tb', 'goods_link_type_jd', 'goods_link_type_pdd', 'goods_link_type_djk', 'goods_link_type_uds', 'goods_link_type_pangda', 'goods_link_type_mt', 'duo_duo_jin_bao', 'goods_link_type_tbpro', 'goods_link_type_jdpro'],
  data() {
    return {
      // 遮罩层
      loading: false,
      urlLoading: false,
      // 表单遮罩
      formLoading: false,
      // 提交Loading
      submitLoading: false,
      recordSubmitLoading: false,
      // 选中数组
      ids: [],
      selections: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品信息表格数据
      goodsList: [],
      restrictList: [],
      // 多多进宝店铺列表
      duoduoMallList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      openCreate: false,
      manualAddVisible: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        oper: null,
        'params.key.key': null,
        id: null,
        pId: null, // 推广位ID
        companyId: null,
        deptId: null,
        deptIds: [],
        deptName: null,
        platform: null,
        goodsId: null,
        goodsIds: null,
        goodsSign: null,
        goodsName: null,
        goodsLink: null,
        goodsThumbnailUrl: null,
        goodsImageUrl: null,
        thumbnailUrl: null,
        videoUrl: null,
        goodsType: null,
        goodsDesc: null,
        minGroupPrice: null,
        minNormalPrice: null,
        optId: null,
        optName: null,
        mallId: null,
        mallName: null,
        merchantType: null,
        salesTip: null,
        servTxt: null,
        planType: null,
        promotionStart: null,
        promotionEnd: null,
        linkType: null,
        searchId: null,
        tjmUrl: null,
        couponUrl: null,
        schemaUrl: null,
        dpUrl: null,
        url: null,
        mobileUrl: null,
        landingPageUrl: null,
        replaceGoodsId: null,
        deleted: null,
        putOnRecordState: null,
        recordGoodsName: null,
        whetherTheNameIsTheSame: null,
        putOnRecordFailComment: null,
        mediaPlatformType: null,
        currentLimitingState: null,
        originalTjmUrl: null,
        duoId: null,
        firstDeptName: null,
        firstDeptId: null,
        createId: null,
        nickName: null,
        businessLicense: null,
        pauseSwitch: null,
        business: null,
        personInCharge: null,
        numIid: null,
        profit: null,
        profitRoi: null,
        profitBackRate: null,
        quantitySwitch: null,
        goodsCount: null,
        tbkUrlComplete: null,
        tbkUrlCount: null,
        disabledGoodsConversion: null,
        orderCount: null,

        shopAttribute: null, // 全店归因标识
        status: null, // 状态
        goodsPush: null, // 牧莎商品推送字段
        goodsIndex: null, // 商品序号
        exposureMonitorUrl: null, // 曝光监测链接
        clickMonitorUrl: null, // 点击监测链接
        ids: null,
        weAppUrl: null, // 微信小程序直达
        dailyOrderCount: null, // 当日
        monitorUniqueId: null, // 流量速pro的监测唯一id
        jdTraceEventType: null, // 京东pro行为
        jdTracePoint: null, // 京东pro归因触点
        jdTracePeriod: null, // 京东pro归因周期
        jdTraceRepeat: null, // 京东pro是否去重归因
        tbProSyn: null, // 淘宝pro使用
        goodsWhiteList: null, // 回传商品白名单
        goodsBlackList: null, // 回传商品黑名单
        itemSugarType: null // 淘宝pro UD建站使用的页面生成的评价与销量类型
      },
      // 表单参数
      form: {
        platform: '1',
        linkType: '1',
        numIid: ''
      },
      // 表单校验
      rules: {
        platform: [
          { required: true, message: '平台类型不能为空', trigger: 'change' }
        ],
        mediaPlatformTypeArr: [
          { type: 'array', required: true, message: '媒体类型不能为空', trigger: 'change' }
        ],
        goodsId: [
          { required: true, message: '商品ID不能为空', trigger: 'blur' }
        ],
        numIid: [
          { required: true, message: '商品虚拟ID不能为空', trigger: 'blur' }
        ],
        linkType: [
          { required: true, message: '链接类型不能为空', trigger: 'change' }
        ],
        companyId: [
          { required: true, message: '店铺不能为空', trigger: 'change' }
        ]
      },
      laterPay: false,
      editVisible: false,
      // 推广链接
      urlVisible: false,
      // 选中的商品
      selectedGoods: {},
      // 详情展示
      infoDrawerVisible: false,
      // 详情样式
      desStyle: { fontSize: '16px' },
      // 备案
      recordVisible: false,
      recordType: 'single',
      nameOptions: [
        {
          label: '正常',
          value: '1'
        }, {
          label: '异常',
          value: '0'
        }
      ],
      // 时间
      promotionRange: [],
      recordRules: {
        goodsId: [
          { required: true, message: '商品ID不能为空', trigger: 'blur' }
        ],
        goodsName: [
          { required: true, message: '商品名称不能为空', trigger: 'blur' }
        ],
        mallName: [
          { required: true, message: '店铺名称不能为空', trigger: 'blur' }
        ],
        goodsImageUrl: [
          { required: true, message: '商品图片素材不能为空', trigger: 'blur' }
        ],
        videoUrl: [
          { required: true, message: '视频素材不能为空', trigger: 'blur' }
        ],
        goodsThumbnailUrl: [
          { required: true, message: '视频预览码不能为空', trigger: 'blur' }
        ]
        // businessLicense: [
        //   { required: true, message: '营业执照不能为空', trigger: 'blur' }
        // ]
      },

      // 落地页列表
      landingListVisible: false,
      // 多多礼金
      cashGiftVisible: false,
      // 替换商品链接
      replaceLinkVisible: false,
      replaceAliHealthLinkVisible: false,
      transferVisible: false,
      synchronousOrNot: true,

      pageList: [],

      activeUrlType: '1',
      txAppletUrlOldVO: {},
      txAppletUrlVO: {},
      txOriginalUrlVO: {},

      recordNameVisible: false,
      currentRecordGoods: {},

      remarkVisible: false,

      // 计划
      switchVisible: false,
      isInPool: false,
      switchForm: {
        id: '',
        switch: 0,
        goodsCount: 0,
        type: 1
      },
      replaceSwitchData: null,
      switchMap: {
        0: '-',
        1: '暂停计划',
        2: '替换链接'
      },
      advertiserIds: [],

      profitVisible: false,
      profitConfigVisible: false,
      profitItems: [],

      openCopy: false,

      conversionVisible: false,
      goodsConversionDisabled: 0,

      plantVisible: false,
      plantUrl: '',

      createRange: [],

      landingVisible: false,
      // 商品提报列表
      productReportVisible: false,
      productRow: null,
      firstRequest: false,
      filingInfo: null,
      filingVisible: false,
      isBatch: false,
      // 多多转链
      pddLinkConvertVisible: false,

      xhsCommissionRate: 0,
      xhsCommissionRateVisible: false
    }
  },
  computed: {
    ...mapGetters([
      'device',
      'tableHeight'
    ]),
    linkTypeMap() {
      return {
        '1': this.dict.type.goods_link_type_pdd,
        '2': this.dict.type.goods_link_type_tb,
        '3': this.dict.type.goods_link_type_jd,
        '4': this.dict.type.goods_link_type_pangda,
        '5': this.dict.type.goods_link_type_tb,
        '6': this.dict.type.goods_link_type_mt,
        '7': this.dict.type.goods_link_type_tb,
        '8': this.dict.type.goods_link_type_djk,
        '9': this.dict.type.goods_link_type_uds,
        '10': this.dict.type.goods_link_type_tbpro,
        '11': this.dict.type.goods_link_type_jdpro
      }
    },
    platformTypes() {
      return this.dict.type.platform_type.filter(item => item.value !== '12')
    },
    mediaTypeMap() {
      return this.dict.type.media_type.reduce((obj, item) => {
        obj[item.value] = item.label
        return obj
      }, {})
    },
    platformTypeMap() {
      return this.dict.type.platform_type.reduce((obj, item) => {
        obj[item.value] = item.label
        return obj
      }, {})
    },
    DuoDuoJinBao() {
      const list = this.dict.type.duo_duo_jin_bao
      if (checkRole(['admin'])) {
        list.push({
          label: '--',
          value: '42737526'
        })
      }
      return list
    }
  },
  created() {
    loadPageSize(this.queryParams)
    this.getDuoduoMallList()
    // this.getList()
    // this.fetchGoodRestrict()
  },
  methods: {
    /** 查询商品信息列表 */
    getList() {
      // if (checkRole(['admin']) && !this.firstRequest) {
      //   this.firstRequest = true
      //   return
      // }
      this.loading = true
      listGoods(this.getQuery()).then(response => {
        this.goodsList = response.rows
        this.goodsList.forEach(item => {
          if (item.conversionJson && item.conversionJson !== '0') {
            item.conversionData = JSON.parse(item.conversionJson)
          }
        })
        this.total = response.total === -1 ? this.total : response.total
        // 对 Table 进行重新布局
        this.$refs.tableRef.doLayout()
        this.loading = false
      })
    },
    getQuery() {
      const query = Object.assign({}, this.queryParams)
      if (this.createRange && this.createRange.length > 0) {
        query.params = {
          createStart: this.createRange[0],
          createEnd: this.createRange[1]
        }
      }
      query.goodsIds = idsToArr(query.goodsIds)
      return query
    },
    handleTableSort(val) {
      const { prop, order } = val
      switch (prop) {
        case 'orderCount':
          if (order === 'ascending') {
            this.queryParams.orderCount = 2
          } else if (order === 'descending') {
            this.queryParams.orderCount = 1
          } else {
            delete this.queryParams.orderCount
          }
          break
      }
      this.getList()
    },
    fetchGoodRestrict() {
      // "params":{"goodPayTimeStart":1693152845,"goodPayTimeEnd":1693235645}
      getGoodRestrict({
        params: {
          updateTimeStart: dayjs().startOf('day').valueOf() / 1000,
          updateTimeEnd: Math.floor(dayjs().endOf('day').valueOf() / 1000)
        }
      }).then(response => {
        this.restrictList = response.rows || []
        this.restrictList.forEach(item => {
          item.title = item.goodsName
        })
      })
    },
    handleScrollerClick(item) {
      this.queryParams.goodsId = item.goodsId
      this.getList()
    },
    // 取消按钮
    cancel() {
      this.openCreate = false
      this.openCopy = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        pId: null,
        companyId: null,
        deptId: null,
        platform: '1',
        goodsId: null,
        goodsSign: null,
        goodsName: null,
        goodsLink: null,
        goodsThumbnailUrl: null,
        goodsImageUrl: null,
        thumbnailUrl: null,
        videoUrl: null,
        goodsType: null,
        goodsDesc: null,
        minGroupPrice: null,
        minNormalPrice: null,
        optId: null,
        optName: null,
        mallId: null,
        mallName: null,
        merchantType: null,
        salesTip: null,
        servTxt: null,
        planType: null,
        promotionStart: null,
        promotionEnd: null,
        linkType: '1',
        searchId: null,
        tjmUrl: null,
        couponUrl: null,
        schemaUrl: null,
        dpUrl: null,
        url: null,
        mobileUrl: null,
        landingPageUrl: null,
        replaceGoodsId: null,
        deleted: null,
        putOnRecordState: null,
        recordGoodsName: null,
        whetherTheNameIsTheSame: null,
        putOnRecordFailComment: null,
        mediaPlatformType: null,
        currentLimitingState: null,
        originalTjmUrl: null,
        duoId: null,
        firstDeptName: null,
        firstDeptId: null,
        createId: null,
        nickName: null,
        businessLicense: null,
        pauseSwitch: null,
        business: null,
        personInCharge: null,
        numIid: null,
        profit: null,
        profitRoi: null,
        profitBackRate: null,
        quantitySwitch: null,
        goodsCount: null,
        tbkUrlComplete: null,
        tbkUrlCount: null,
        disabledGoodsConversion: null,
        orderCount: null,
        shopAttribute: null,
        autoRedirect: null,
        status: null,
        goodsPush: null,
        goodsIndex: null,
        exposureMonitorUrl: null,
        clickMonitorUrl: null,
        weAppUrl: null,
        dailyOrderCount: null,
        monitorUniqueId: null,
        jdTraceEventType: null,
        jdTracePoint: null,
        jdTracePeriod: null,
        jdTraceRepeat: null,
        tbProSyn: null,
        goodsWhiteList: null,
        goodsBlackList: null,
        itemSugarType: null,
        mediaPlatformTypeArr: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.createRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selections = selection
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleTransfer() {
      this.transferVisible = true
      this.synchronousOrNot = true
    },
    handleTransferSelect(userId) {
      transfer({
        ids: this.ids,
        synchronousOrNot: this.synchronousOrNot,
        userId
      }).then(response => {
        this.transferVisible = false
        this.$message({
          message: '移交成功',
          type: 'success'
        })
        this.handleQuery()
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.openCreate = true
      this.title = '添加商品'
    },
    handleCreateSuccess() {
      this.openCreate = false
      this.handleQuery()
    },
    /** 手动添加按钮操作 */
    handleManualAdd() {
      this.manualAddVisible = true
    },
    handleManualAddSuccess() {
      this.manualAddVisible = false
      this.handleQuery()
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row ? row.id : this.ids
      this.$modal.confirm('删除后将影响该商品报表统计，是否确认删除所选商品?').then(function() {
        return delGoods(ids)
      }).then(() => {
        const len = Array.isArray(ids) ? ids.length : 1
        this.total = this.total - len
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('promotion/goods/export', this.getQuery(), `goods_${new Date().getTime()}.xlsx`)
    },
    handleExportCSV() {
      this.$confirm(this.ids.length > 0 ? `即将导出${this.ids.length}条数据，是否继续？` : '即将导出CSV数据，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        const query = getExistingObj(this.getQuery())
        if (this.ids.length > 0) { query.ids = this.selections.map(item => item.id) }
        this.download('promotion/goods/exportCsv', query, `goods_${new Date().getTime()}.xlsx`, {
          transformRequest: [(params) => { return JSON.stringify(params) }],
          headers: {
            'Content-Type': 'application/json;charset=utf-8'
          }})
        this._setupProxy.showTooltip('fileExport')
      })
    },
    // 显示详情
    showDetail(row) {
      this.reset()
      const id = row.id
      getGoods(id).then(response => {
        this.form = response.data
        this.infoDrawerVisible = true
      })
    },
    // 显示链接窗口
    showGoodsUrl(goods) {
      this.urlVisible = true
      this.selectedGoods = goods
    },

    // 获取多多进宝店铺列表
    getDuoduoMallList() {
      selectDuoDuoMallList().then((res) => {
        if (res.code === 200) {
          this.duoduoMallList = res.data.map(item => ({
            id: item.mallId,
            label: item.mallName,
            duoduoId: item.duoDuoId,
            mallId: item.mallId,
            logo: item.logo
          }))
        }
      }).catch(error => {
        console.error('获取店铺列表失败:', error)
      })
    },
    // 商品备案
    showRecords(row) {
      this.reset()
      this.promotionRange = []
      getGoods(row.id).then(response => {
        this.form = response.data
        this.recordVisible = true
        this.recordType = 'single'
      })
    },
    showReview(row) {
      this.form = row
      this.plantVisible = true
      this.plantUrl = ''
    },
    submitPlantUrl() {
      if (!urlReg.test(this.plantUrl)) {
        this.$message.warning('地址格式不正确')
        return
      }
      this.urlLoading = true
      taobaoPlantPageConvert({
        goodsId: this.form.goodsId,
        linkType: this.form.linkType,
        platform: this.form.platform,
        plantPageUrl: this.plantUrl
      }).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess('操作成功')
          this.plantVisible = false
          this.getList()
        }
      }).finally(() => {
        this.urlLoading = false
      })
    },
    checkGoodsRecord(oper) {
      this.$refs['recordForm'].validate(valid => {
        if (valid) {
          if (checkRole(['admin']) && this.promotionRange && this.promotionRange.length > 0) {
            this.form.promotionStart = this.promotionRange[0]
            this.form.promotionEnd = this.promotionRange[1]
          }
          this.form.oper = oper

          if (oper === 1) {
            this.recordSubmitLoading = true
            authGoods(this.form).then(response => {
              this.$modal.msgSuccess('备案成功')
              this.recordVisible = false
              this.getList()
            }).finally(() => {
              this.recordSubmitLoading = false
            })
          } else if (oper === 2) {
            this.$prompt('请输入拒绝理由', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              inputValidator: (value) => {
                if (!value) {
                  return '请输入拒绝理由'
                } else {
                  return true
                }
              }
            }).then(({ value }) => {
              this.recordSubmitLoading = true
              this.form.putOnRecordFailComment = value
              authGoods(this.form).then(response => {
                this.$modal.msgSuccess('拒绝成功')
                this.recordVisible = false
                this.getList()
              }).finally(() => {
                this.recordSubmitLoading = false
              })
            }).catch(() => {

            })
          }
        }
      })
    },
    saveGoodsRecord(oper) {
      this.$refs['recordForm'].validate(valid => {
        if (valid) {
          if (checkRole(['admin']) && this.promotionRange && this.promotionRange.length > 0) {
            this.form.promotionStart = this.promotionRange[0]
            this.form.promotionEnd = this.promotionRange[1]
          }

          this.form.oper = oper
          this.recordSubmitLoading = true

          if (oper === 1) {
            restrictGoods(this.form).then(response => {
              this.$modal.msgSuccess('备案成功')
              this.recordVisible = false
              this.getList()
            }).finally(() => {
              this.recordSubmitLoading = false
            })
          } else {
            updateGoods(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.recordVisible = false
              this.getList()
            }).finally(() => {
              this.recordSubmitLoading = false
            })
          }
        }
      })
    },
    // 美团审核
    meituanAuditor(row) {
      this.$confirm(`商品：${row.goodsName} 通过审核？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        handleMeituanAuditor({ id: row.id }).then(response => {
          this.$modal.msgSuccess('操作成功')
          this.getList()
        })
      }).catch(() => {
      })
    },
    showBatchAuth() {
      if (this.selections.some(item => item.platform !== '1')) {
        this.$modal.msgWarning('只有拼多多商品才能批量备案')
        return
      }
      if (this.selections.some(item => ![0, 1, 4, 6, 7].includes(item.putOnRecordState) &&
        !(item.putOnRecordState === 3 && this.promotionOverdue(item, true)) &&
        !this.promotionOverdue(item))) {
        this.$modal.msgWarning('只有未备案、备案失败的商品才能批量备案')
        return
      }

      this.promotionRange = []
      this.reset()
      this.recordVisible = true
      this.recordType = 'batch'
    },
    handleBatchAuth(type) {
      this.$refs['recordForm'].validate(valid => {
        if (valid) {
          this.recordSubmitLoading = true
          const batchAuth = type === 'restrict' ? restrictGoods : authGoods
          batchAuth(this.selections.map(item => {
            const temp = {
              ...item,
              oper: 1,
              videoUrl: this.form.videoUrl
            }
            if (checkRole(['admin']) && this.promotionRange && this.promotionRange.length > 0) {
              temp.promotionStart = this.promotionRange[0]
              temp.promotionEnd = this.promotionRange[1]
            }
            return temp
          })).then(response => {
            this.$modal.msgSuccess('备案成功')
            this.recordVisible = false
            this.getList()
          }).finally(() => {
            this.recordSubmitLoading = false
          })
        }
      })
    },
    getSelectionsBigDate() {
      let startTime = new Date()
      this.selections.forEach(item => {
        if (dayjs(startTime).isBefore(dayjs(item.promotionEnd))) {
          startTime = new Date(item.promotionEnd)
        }
      })
      return startTime
    },

    cancelRecord() {
      this.recordVisible = false
      this.reset()
    },
    // 自建落地页
    showLandingList(row) {
      this.reset()
      getGoods(row.id).then(response => {
        this.form = response.data
        this.landingListVisible = true
      })
    },
    showCashGiftList(row) {
      this.reset()
      getGoods(row.id).then(response => {
        this.form = response.data
        this.cashGiftVisible = true
      })
    },
    // 替换商品链接
    replaceLink(row) {
      this.reset()
      this.form = row
      this.replaceLinkVisible = true
    },
    replaceAliHealthLink(row) {
      this.reset()
      this.form = row
      this.replaceAliHealthLinkVisible = true
    },
    showUpdateRecordname(row) {
      this.recordNameVisible = true
      this.currentRecordGoods = row
    },
    handleUpdateRecordname() {
      updateRecordname(this.currentRecordGoods.id).then(response => {
        this.$message({
          type: 'success',
          message: '替换成功!'
        })
        this.getList()
      }).finally(() => {
        this.recordNameVisible = false
      })
    },
    showUpdateRemark(row) {
      this.form = row
      this.remarkVisible = true
    },
    saveRemark() {
      updateRemark({
        id: this.form.id,
        remark: this.form.remark
      }).then(response => {
        this.$message({
          type: 'success',
          message: '修改成功!'
        })
        this.remarkVisible = false
        this.getList()
      })
    },
    handleRLClose() {
      this.replaceLinkVisible = false
      this.replaceAliHealthLinkVisible = false
      this.reset()
      this.getList()
    },
    // 计划
    showSwitch(row, type) {
      this.advertiserIds = []
      this.replaceSwitchData = null
      this.reset()
      this.form = row
      this.switchForm = {
        id: row.id,
        mediaPlatformType: this.form.mediaPlatformType,
        type,
        pauseSwitch: type ? row.pauseSwitch : row.quantitySwitch
      }
      if (type === 0) {
        this.switchForm.goodsCount = row.goodsCount
      }
      this.switchVisible = true

      this.isInPool = false
      getGoodsPoolVOByGoodsId(row.id).then(res => {
        if (res.code === 200) {
          this.isInPool = !!res?.data.length
        }
      })
    },
    handleSwitchReplace(data) {
      let postData = {
        ...this.switchForm
      }
      if (!postData.goodsCount) {
        postData.goodsCount = null
      }
      if (data) {
        postData = Object.assign(postData, data)
      }
      if (this.switchForm.pauseSwitch === 1) {
        if (!this.advertiserIds || this.advertiserIds.length === 0) {
          this.$modal.msgWarning('请选择媒体账户')
          return
        }
        postData.advertiserIds = this.advertiserIds
      }
      updateSwitch(postData).then(response => {
        this.$modal.msgSuccess('修改成功')
        this.switchVisible = false
        this.getList()
      })
    },
    handleSwitchReplaceCancel() {
      this.replaceSwitchData = null
      this.switchVisible = false
    },
    handleAccountSelect(ids) {
      this.advertiserIds = ids
    },

    // 处理备案名
    handleRowClass({ row }) {
      let classNames = ''
      if (row.recordGoodsName && row.goodsName !== row.recordGoodsName) {
        classNames += 'record-warning '
      }
      if (row.promotionEnd) {
        const diff = dayjs(row.promotionEnd).diff(dayjs(new Date()), 'days') + 1
        if (diff <= 3 && diff > 0) {
          classNames += 'promotion-warning '
        }
        if (diff <= 0) {
          classNames += 'promotion-danger '
        }
      }

      return classNames
    },
    promotionOverdue(row, out = false) {
      if (!row.promotionEnd) return false
      const diff = dayjs(row.promotionEnd).diff(dayjs(new Date()), 'days') + 1
      if (out) {
        return diff <= 0
      }
      return diff <= 3 && diff > 0
    },

    setRoi(row) {
      this.profitVisible = true
      this.profitItems = Array.isArray(row) ? row : [row]
    },
    setProfitConfig(row) {
      this.profitConfigVisible = true
      this.form = row
    },

    handleCopyGoods(row) {
      this.form = row
      this.$set(this.form, 'mediaPlatformTypeArr', [])
      this.openCopy = true
    },
    submitCopy() {
      this.$refs['copyForm'].validate(valid => {
        if (valid) {
          this.formLoading = true
          copyGoods({ id: this.form.id, mediaPlatformTypeArr: this.form.mediaPlatformTypeArr }).then(response => {
            this.$message({
              type: 'success',
              message: response.msg || '复制成功!'
            })
            this.openCopy = false
            this.total = this.total + 1
            this.getList()
          }).finally(() => {
            this.formLoading = false
          })
        }
      })
    },
    disabledConversion(row) {
      this.conversionVisible = true
      this.form = row
      this.goodsConversionDisabled = row.disabledGoodsConversion
    },
    saveConversion(row) {
      // this.$confirm(`确认${this.disabledGoodsConversion? '设置':'取消'}该商品禁止回传?`, '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      disabledGoodsConversion({
        goodsId: this.form.goodsId,
        disabled: this.goodsConversionDisabled
      }).then(() => {
        this.$message.success('保存成功')
        this.conversionVisible = false
        this.getList()
      })

      // }).catch(() => {
      //
      // })
    },

    editGoods(row) {
      this.form = row
      this.editVisible = true
    },
    handleEditSuccess() {
      this.editVisible = false
      this.getList()
    },

    handleCommand(command, row) {
      switch (command) {
        case 'refreshGoods':
          this.refreshGoods(row.id)
          break
        case 'openGoodsLink':
          window.open(row.goodsLink)
          break
        case 'replaceLink':
          this.replaceLink(row)
          break
        case 'replaceAliHealthLink':
          this.replaceAliHealthLink(row)
          break
        case 'conversionProportion':
          this._setupProxy.showConversion(row)
          break
        case 'autoConversion':
          this._setupProxy.showAutoConversion(row)
          break
        case 'showSwitch':
          this.showSwitch(row, 1)
          break
        case 'showOrderSwitch':
          this.showSwitch(row, 0)
          break
        case 'showUpdateRecordname':
          this.showUpdateRecordname(row)
          break
        case 'showUpdateRemark':
          this.showUpdateRemark(row)
          break
        case 'setRoi':
          this.setRoi(row)
          break
        case 'setProfitConfig':
          this.setProfitConfig(row)
          break
        case 'showLandingList':
          this.showLandingList(row)
          break
        case 'editGoods':
          this.editGoods(row)
          break
        case 'disabledConversion':
          this.disabledConversion(row)
          break
        case 'handleDelete':
          this.handleDelete(row)
          break
        case 'landingPage':
          this.landingPage(row)
          break
        case 'xhsCommissionRate':
          this.handleXhsCommissionRate(row)
          break
        case 'filingInformation':
          this.filingInformation(row)
          break
        default:
          break
      }
    },

    // 刷新商品
    refreshGoods(target) {
      const ids = Array.isArray(target) ? target : [target]
      this.loading = true
      refreshPddGoods(ids).then(() => {
        this.$modal.msgSuccess('刷新成功')
        this.getList()
      }).finally(() => {
        this.loading = false
      })
    },

    handleBatchRefresh() {
      if (!this.ids || !this.ids.length) {
        this.$modal.msgWarning('请先选择商品')
        return
      }
      this.refreshGoods(this.ids)
    },

    // 刷新列表
    handleRefresh() {
      this.getList()
    },

    clipboardSuccess() {
      this.$modal.msgSuccess('复制成功')
    },
    landingPage(row) {
      this.landingVisible = true
      this.form = row
    },
    // 商品提报
    productReport(row) {
      const ids = this.selections.map(item => item.goodsId)
      this.$confirm('确认是否将该商品提报, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        handleGoodsBra({ goodsId: ids }).then(() => {
          this.$message({
            type: 'success',
            message: '提报成功!'
          })
          // this.getList()
        })
      }).catch(() => {

      })
    },
    // 商品提报列表
    productReportListHandle(row) {
      if (row) {
        this.productRow = row
      } else {
        this.productRow = null
      }
      this.productReportVisible = true
    },
    // 查看备案信息
    filingInformation(row) {
      this.filingInfo = row
      this.filingVisible = true
    },
    // 是否失败
    isFilingFail(row) {
      return [0, 1, 4, 7].includes(row.putOnRecordState)
    },

    // 多多转链
    handlePddLinkConvert() {
      this.pddLinkConvertVisible = true
    },
    handleXhsCommissionRate(row) {
      this.form = row
      getExpertXhsCommissionRate({ id: row.id }).then(res => {
        this.xhsCommissionRate = (res.data || 0) * 100
        this.xhsCommissionRateVisible = true
      })
    },

    submitXhsCommissionRate() {
      setExpertXhsCommissionRate({
        id: this.form.id,
        commissionRate: this.xhsCommissionRate / 100
      }).then(res => {
        this.$modal.msgSuccess('修改成功')
        this.xhsCommissionRateVisible = false
        this.getList()
      })
    },
    checkRole
  }
}
</script>

<script setup>
import useTooltipVisible from '@/hooks/useTooltipVisible'
import { getCurrentInstance, ref } from 'vue'
import useStoreTableScroller from '@/hooks/useStoreTableScroller'
import UrlClipboard from '@/views/promotion/goods/components/UrlClipboard.vue'
import ClipboardButton from '@/components/ClipboardButton/index.vue'
import { dateRangePickerOptions } from '@/config'
import AddGoods from '@/views/promotion/goods/components/AddGoods.vue'
import ManualAddGoods from '@/views/promotion/goods/components/ManualAddGoods.vue'
import useDeptOptions, { setDefaultIds, getDefaultIds } from '@/components/DeptTreeSelect/useDeptOptions'
import EditGoods from '@/views/promotion/goods/components/EditGoods.vue'
import TableColumnSet from '@/components/TableColumnSet/index.vue'
import useConversion from '@/components/ConversionSetups/hooks/useConversion'
import useAutoConversion from '@/components/AutoConversionSetups/hooks/useAutoConversion'
import ConversionItem from '@/components/ConversionSetups/ConversionItem.vue'
import { Message, MessageBox } from 'element-ui'
import { updateStatus } from '@/api/promotion/goods'
import useColumns from '@/hooks/useColumns'
import { parseTime } from '@/utils/ruoyi'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import RenderComponent from '@/components/RenderComponent/index.vue'
import BusinessSelector from '@/components/BusinessSelector/index.vue'
import { isTb } from '@/utils/judge/platform'
const { showTooltip } = useTooltipVisible()
const tableRef = ref(null)
const defaultColumns = [
  { prop: 'Platform', label: '类型', width: 105 },
  { prop: 'GoodsInfo', label: '商品信息', width: 240, align: 'left' },
  {
    label: `店铺信息`, prop: 'MallInfo', width: '200', align: 'left',
    info: { id: 'mallId', name: 'mallName' }
  },
  { prop: 'action', label: '操作', width: 260 },
  { prop: 'status', label: '停用', width: 110, tooltip: '停用后，该商品的所有操作将失效' },
  { prop: 'remark', label: '备注', width: 150 },
  { prop: 'url', label: '商品链接', width: 110 },
  // { prop: 'pId', label: '推广位ID', width: 120 },
  // { prop: 'autoRedirect', label: '熊猫良选自动跳转', width: 130, render: (row) => row.autoRedirect === 1 ? '是' : '否' },
  { prop: 'shopAttribute', label: '全店归因', width: 100, render: (row) => row.shopAttribute ? '开启' : '关闭' },
  { prop: 'enableAmount', label: '回传金额比例', width: 130, tooltip: '回传金额比例开关关闭时，100%回传' },
  { prop: 'conversionProportion', label: '回传订单配置', width: 130, tooltip: '回传订单配置开关关闭时，100%回传' },
  // { prop: 'currentLimitingState', label: '推广限制', width: 100 },
  // { prop: 'putOnRecordState', label: '备案状态', width: 110 },
  // { prop: 'putOnRecordFailComment', label: '失败原因', width: 100 },
  // {
  //   prop: 'disabledGoodsConversion',
  //   label: '禁止回传',
  //   width: 100,
  //   permissions: ['promotion:goods:disabledGoodsConversion']
  // },
  // {
  //   prop: 'pauseSwitch',
  //   label: '限推触发事件',
  //   width: 120,
  //   permissions: ['promotion:goods:updateSwitch'],
  //   tooltip: '商品限制推广后系统自动执行的操作',
  //   render: (row) => {
  //     return row.pauseSwitch === undefined ? '-' : {
  //       0: '-',
  //       1: '暂停计划',
  //       2: '替换链接'
  //     }[row.pauseSwitch]
  //   }
  // },
  // { prop: 'profit', label: '盈亏成本', width: 120, tooltip: '盈亏成本 = 拿货成本 + 运费 + 其它非广告消耗成本' },
  // { prop: 'profitRoi', label: '盈亏ROI', width: 120, tooltip: '设置预估的保本ROI' },
  // { prop: 'profitBackRate', label: `充值返点`, width: '120' },
  { prop: 'orderCount', label: '总成交订单数', width: 130, sortable: 'custom' },
  { prop: 'dailyOrderCount', label: '当日订单数', width: 130 },
  { prop: 'minGroupPrice', label: '最小价格', width: 180 },
  { prop: 'linkType', label: '链接类型', width: 120 },
  { prop: 'UserInfo', label: '用户信息', width: 180 },
  // {
  //   prop: 'business', label: '商务负责人', width: 150, permissions: ['promotion:config:duoduo'], render: (row) => {
  //     return row.business ? `${row.business} (${row.personInCharge})` : '-'
  //   }
  // },
  {
    prop: 'Dept', label: '公司信息', width: 160, labelWidth: 40,
    set: [
      { label: `公司`, prop: 'firstDeptName' },
      { label: `部门`, prop: 'deptName' }
    ]
  },
  // {
  //   prop: 'promotionStart', label: '推广日期', width: 160, labelWidth: 60,
  //   set: [
  //     { label: `开始时间`, prop: 'promotionStart' },
  //     { label: `结束时间`, prop: 'promotionEnd' }
  //   ]
  // },
  // { prop: 'exposureMonitorUrl', label: '曝光监测链接', width: 150, copy: true },
  // { prop: 'clickMonitorUrl', label: '点击监测链接', width: 150, copy: true },
  // { prop: 'weAppUrl', label: '微信小程序直达', width: 130 },
  { prop: 'createTime', label: '创建时间', width: 180 }
]

const { columnsInstance, columns, operatedColumns, customList, handleHeaderDragend } = useColumns({ defaultColumns, tableRef })
useStoreTableScroller(tableRef)

const self = getCurrentInstance().proxy
const deptOptions = useDeptOptions(() => {
  setDefaultIds(self)
  self.getList()
  // 由于CompanySelect组件已经在created中加载了数据，这里可以移除
  // self.$refs.mallSelect.getList({ deptIds: getDefaultIds() })
})

// 回传
const { conversionType,
  conversionForm,
  conversionDialogVisible,
  conversionFormType,
  showConversion,
  batchUpdateConversion,
  handleConversionSave
} = useConversion({
  type: 'goods',
  onSave: () => self.getList()
})

// 扣后补回传
const {
  autoConversionForm,
  autoConversionDialogVisible,
  autoConversionType,
  showAutoConversion,
  batchUpdateAutoConversion,
  handleAutoConversionSave,
  handleAutoConversionCancel
} = useAutoConversion({
  type: 'goods',
  onSave: () => self.getList()
})

// 启用禁用
const handleVaildChange = (row) => {
  MessageBox.confirm(`确认${row.status ? '启用' : '禁用'}商品?`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    updateStatus({
      id: row.id,
      status: row.status
    }).then(res => {
      Message.success('修改成功')
    })
  }).catch(() => {
    row.status = +!row.status
  })
}

const isNull = (value) => {
  return value === undefined || value === null
}
</script>

<style lang="scss" scoped>
.info-drawer-body {
  display: flex;
  flex-direction: column;
  padding: 0 20px;
  height: 100%;
  .main-info {
    overflow: auto;
    flex: 1
  }
  .info-title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 20px;
  }
  .info-drawer-action {
    width: 100%;
    align-self: end;
    margin-bottom: 20px;
  }
}
.url-wrap {
  overflow: hidden;
  /*将对象作为弹性伸缩盒子模型显示*/
  display: -webkit-box;
  /*设置子元素排列方式*/
  -webkit-box-orient: vertical;
  /*设置显示的行数，多出的部分会显示为...*/
  -webkit-line-clamp: 2;
}

.info-wrap {
  width: calc(100% - 60px);
  flex: 1;
}

.link-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  margin-bottom: 10px;
}
.in-pool {
  color: #f56c6c ;
  font-weight: bold;
  border-bottom: 1px solid #DCDFE6;
  padding-bottom: 10px;
  margin:0 10px 10px;
}

::v-deep .record-warning {
  color: #f56c6c;
}
::v-deep .promotion-warning {
  .el-table__cell {
    background: #fdf6ec !important;
  }
}
::v-deep .promotion-danger {
  .el-table__cell {
    background: #fef0f0 !important;
  }
}

::v-deep .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 10px;
}

.dialog-pt-0 ::v-deep .el-dialog__body {
 padding-top: 0;
}
.icon-flex {
  display: flex;
  justify-content: space-between;
  text-align: center;
  .type-icon {
    width:25px;
    height: 25px;
    font-size: 25px;
  }
  .type-label {
    font-size: 12px;
    color: #999;
  }
}

.submit-btn {
  display: inline-block;
  margin-right: 10px;
}

</style>
