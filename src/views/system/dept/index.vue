<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" class="search-form" :model="queryParams" size="small" :inline="true">
      <el-form-item prop="deptName">
        <el-input
          v-model.trim="filterDeptName"
          placeholder="部门名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="部门状态"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <BusinessSelector form-prop="createBy" :business.sync="queryParams.createBy" @query="handleQuery" />
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          :extra-filterDeptName.sync="filterDeptName"
          @search="handleQuery"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dept:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="toggleExpandAll"
        >展开/折叠</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-has-permi="['system:dept:rebate']"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-has-permi="['system:dept:transfer']"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="handleTransfer"
        >移交</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-has-permi="['system:rate:batch:editCommission']"
          plain
          icon="el-icon-edit"
          size="mini"
          @click="handleBatchRate"
        >修改返点</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-has-role="['admin']"
          plain
          icon="el-icon-edit"
          size="mini"
          @click="setDeptRates"
        >批量返点配置</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <vxe-table
      ref="xTable"
      :loading="loading"
      show-overflow
      :height="tableHeight"
      border="inner"
      row-id="deptId"
      :tree-config="{transform: true, rowField: 'deptId', parentField: 'parentId', expandAll: true}"
      :checkbox-config="{labelField: 'checkFlag'}"
      :data="deptList"
      :scroll-y="{enabled: true, gt: 40}"
      :loading-config="{text: '加载中...'}"
      empty-text="无数据"
      @checkbox-change="handleCheckboxChange"
      @checkbox-all="handleCheckboxChange"
    >
      <vxe-column type="checkbox" width="50" />
      <vxe-column field="deptName" title="部门名称" tree-node min-width="240" />
      <vxe-column field="deptId" title="部门编号" width="100">
        <template #default="{ row }">
          <el-tooltip :content="row.deptId" :disabled="row.deptId.length < 8" placement="top">
            <span :title="row.deptId">{{ row.deptId }}</span>
          </el-tooltip>
        </template>
      </vxe-column>
      <vxe-column field="orderNum" title="排序" width="80" />
      <vxe-column field="status" title="状态" width="100">
        <template #default="{ row }">
          <dict-tag :options="dict.type.sys_normal_disable" :value="row.status" />
        </template>
      </vxe-column>
      <vxe-column field="createTime" title="创建时间" width="180">
        <template #default="{ row }">
          <span>{{ parseTime(row.createTime) }}</span>
        </template>
      </vxe-column>
      <vxe-column field="status" title="操作" min-width="180">
        <template #default="{ row }">
          <el-button
            v-hasPermi="['system:dept:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
          >修改</el-button>
          <el-button
            v-hasPermi="['system:dept:add']"
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(row)"
          >新增</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command,row)" @visible-change="(visible) => calcIsRootDept(visible,row)">
            <el-button type="text" icon="el-icon-d-arrow-right">更多</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-show="row.parentId != 0"
                v-hasPermi="['system:dept:remove']"
                command="handleDelete"
                icon="el-icon-delete"
              >删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </vxe-column>
    </vxe-table>

    <!-- 添加或修改部门对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col v-if="form.parentId !== '0'" :span="24">
            <el-form-item label="上级部门" prop="parentId">
              <treeselect v-model="form.parentId" :options="deptOptions" :normalizer="normalizer" placeholder="选择上级部门" @select="handleDeptOptionChange" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="部门名称" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入部门名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="负责人" prop="leader">
              <el-input v-model="form.leader" placeholder="请输入负责人" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-if="type==='add'" v-model="form.phone" placeholder="请输入联系电话" maxlength="11" />
              <el-input v-else v-model="form.phone" :placeholder="formPlaceholder.phone?formPlaceholder.phone:'请输入联系电话'" maxlength="11" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-if="type==='add'" v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
              <el-input v-else v-model="form.email" :placeholder="formPlaceholder.email?formPlaceholder.email:'请输入邮箱'" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="isRootDept">
          <el-form-item
            v-hasPermi="['promotion:config:duoduo']"
            label="合同"
            prop="contract"
          >
            <el-select v-model="form.contract" clearable placeholder="请选择合同">
              <el-option
                v-for="dict in dict.type.contract"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <BusinessSelector label="商务负责人" :business.sync="form.createBy" placeholder="请选择负责人" />
          <!--          <CommissionRate v-if="type==='add'" :id="form.deptId" ref="rateRef" v-hasPermi="['system:rate:list']" />-->
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!--  返点确认   -->
    <el-dialog :title="'返点明细 - '+form.deptName" :visible.sync="rateResultVisible" width="600px" top="10vh" append-to-body>
      <CommissionRateResult :time-list="rateResult.timeList" @submit="handleRateSubmit" @cancel="rateResultVisible = false" />
    </el-dialog>
    <el-dialog v-if="rateDisplayVisible" :title="'返点明细 - '+form.deptName" :visible.sync="rateDisplayVisible" width="600px" top="10vh" append-to-body>
      <RebateDetail :time-list="rateResult.timeList" @submit="rateDisplayVisible = false" />
    </el-dialog>
    <!--  返点修改   -->
    <el-dialog :title="'修改返点 - '+form.deptName" :visible.sync="rateEditVisible" width="700px" top="10vh" append-to-body>
      <el-form ref="form" :model="form" label-width="100px">
        <CommissionRate v-if="rateEditVisible" :id="form.deptId" ref="rateRef" />
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitLoading" type="primary" @click="showRateResult">确 定</el-button>
        <el-button @click="rateEditVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <!--  设置税率   -->
    <el-dialog :title="'设置税率 - '+form.deptName" :visible.sync="taxVisible" width="350px" top="10vh" append-to-body>
      <el-form ref="form" :model="taxForm" label-width="110px">
        <el-form-item label="佣金提现税率" prop="commissionRate">
          <el-select
            v-model="taxForm.commissionRate"
            placeholder="请选择佣金提现税率"
            style="width: 200px"
          >
            <el-option
              v-for="n in 7"
              :key="n"
              :label="`${n-1}%`"
              :value="n-1"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="消耗返佣税率" prop="taxRate">
          <el-select
            v-model="taxForm.taxRate"
            placeholder="请选择消耗返佣税率"
            style="width: 200px"
          >
            <el-option
              v-for="n in 7"
              :key="n"
              :label="`${n-1}%`"
              :value="n-1"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" :loading="taxLoading" type="primary" @click="setTaxRate">确 定</el-button>
        <el-button size="mini" @click="taxVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 移交组件 -->
    <Transfer v-if="transferVisible" v-model="transferVisible" :dept-list="deptList" @Refresh="getList" />
    <BatchRate
      :visible.sync="batchRateVisible"
      :depts="secondDepts"
    />

    <el-dialog title="批量返点配置" :visible.sync="setDeptRatesVisible" width="80vw" top="10vh" append-to-body>
      <SetDeptRates v-loading="ratesLoading" :depts="rateList" :dept-names-not-find="deptNamesNotFind" @edit="showRateEdit" @batchEdit="handleBatchEdit" />
    </el-dialog>
  </div>
</template>

<script>
import { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild } from '@/api/system/dept'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { checkPermi } from '@/utils/permission'
import { getUserRateById } from '@/api/system/user'
import dayjs from 'dayjs'
import Transfer from './components/Transfer.vue'
import BatchRate from './components/BatchRate.vue'

export default {
  name: 'Dept',
  dicts: ['sys_normal_disable', 'duo_duo_jin_bao', 'contract'],
  components: { Treeselect, Transfer, BatchRate },
  data() {
    return {
      // 遮罩层
      loading: true,
      submitLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 表格树数据
      deptList: [],
      topDeptIds: [],
      originData: [],
      isFirst: true,
      allDeptList: [],
      // 部门树选项
      deptOptions: [],
      selectedDepts: [],
      // 弹出层标题
      title: '',
      // 弹窗类型
      type: '',
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        deptName: undefined,
        status: undefined,
        createBy: undefined,
        contract: undefined
      },
      lastQuery: {},
      filterDeptName: '',
      // 表单参数
      form: {},
      // 脱敏占位符
      formPlaceholder: {},
      // 表单校验
      rules: {
        parentId: [
          { required: true, message: '上级部门不能为空', trigger: 'blur' }
        ],
        deptName: [
          { required: true, message: '部门名称不能为空', trigger: 'blur' }
        ],
        orderNum: [
          { required: true, message: '显示排序不能为空', trigger: 'blur' }
        ],
        contract: [
          { required: true, message: '请选择合同', trigger: 'blur' }
        ],
        email: [
          {
            type: 'email',
            message: '请输入正确的邮箱地址',
            trigger: ['blur']
          }
        ],
        phone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ]
      },
      isRootDept: false,
      dropdownVisible: false,
      rateResultVisible: false,
      rateDisplayVisible: false,
      rateResult: {
        timeList: [],
        commissionStatus: 0
      },
      rateEditVisible: false,
      transferVisible: false,
      batchRateVisible: false,
      batchRate: {},
      secondDepts: []
    }
  },
  computed: {
    tableHeight() {
      return window.innerHeight - 245
    }
  },
  async created() {
    this.getList()
  },
  methods: {
    /** 查询部门列表 */
    async getList() {
      const query = { ...this.queryParams }
      this.loading = true
      this.lastQuery = query
      const response = await listDept(query)
      response.data.forEach(item => {
        if (item.pIds) {
          item.pidList = item.pIds.split(',').filter(p => p)
        }
      })
      this.deptList = this.originData = response.data
      if (this.isFirst) {
        this.topDeptIds = []
        this.allDeptList = this.deptList
        this.isFirst = false
        this.allDeptList.forEach(item => {
          if (item.parentId === '0') this.topDeptIds.push(item.deptId)
        })
      }
      this.deptList = this.filterDepts(this.originData, this.filterDeptName)

      this.loading = false
      if (this.isExpandAll) {
        this.$nextTick(() => {
          this.$refs.xTable.setAllTreeExpand(true)
        })
      }
    },
    isFilterChange() {
      let isChange = false
      Object.entries(this.lastQuery).forEach(([key, val]) => {
        if (this.queryParams[key] !== val) {
          isChange = true
        }
      })
      return isChange
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      }
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        deptId: undefined,
        parentId: undefined,
        deptName: undefined,
        orderNum: undefined,
        leader: undefined,
        phone: undefined,
        email: undefined,
        createBy: undefined,
        contract: undefined,
        status: '0'
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.isFilterChange()) {
        this.getList()
      }
      this.deptList = this.filterDepts(this.originData, this.filterDeptName)

      if (!this.isFilterChange() && this.isExpandAll) {
        this.$nextTick(() => {
          this.$refs.xTable.setAllTreeExpand(true)
        })
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.filterDeptName = ''
      this.handleQuery()
      this.$refs.xTable.clearCheckboxRow()
      this.selectedDepts = []
    },
    filterDepts(deptList, searchName) {
      if (!searchName) return deptList

      // 处理搜索关键词：分割并过滤空值
      const keywords = searchName.split(/[,，]/)
        .map(keyword => keyword.trim())
        .filter(keyword => keyword)

      if (keywords.length === 0) return deptList

      // 构建对象树
      const deptMap = new Map()
      deptList.forEach(dept => {
        if (!deptMap.has(dept.parentId)) {
          deptMap.set(dept.parentId, [])
        }
        deptMap.get(dept.parentId).push(dept)
        dept.checkFlag = false
      })

      // 用于存放找到的对象，包括匹配的deptName及其所有父子对象
      const matchedDepts = []

      // 递归函数，用于收集子对象
      function collectChildren(parentId) {
        const children = deptMap.get(parentId)
        if (children) {
          children.forEach(child => {
            matchedDepts.push(child)
            collectChildren(child.deptId) // 递归收集子对象的子对象
          })
        }
      }

      // 搜索匹配的对象，并收集其所有父子对象
      deptList.forEach(dept => {
        // 检查是否有任意关键词匹配
        const isMatch = keywords.some(keyword =>
          dept.deptName.toLowerCase().includes(keyword.toLowerCase())
        )

        if (isMatch) {
          matchedDepts.push(dept) // 添加匹配的对象
          collectChildren(dept.deptId) // 收集子对象
          // 收集父对象
          let parentId = dept.parentId
          while (parentId) {
            const parentDept = deptList.find(d => d.deptId === parentId)
            if (parentDept) {
              matchedDepts.push(parentDept)
              parentId = parentDept.parentId
            } else {
              break // 没有父对象或到达根节点
            }
          }
        }
      })
      // 返回找到的对象列表
      return Array.from(new Set(matchedDepts))
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      if (row !== undefined) {
        this.form.parentId = row.deptId
      }
      this.open = true
      this.title = '添加部门'
      this.type = 'add'
      listDept().then(response => {
        this.deptOptions = this.handleTree(response.data, 'deptId')
        if (row) {
          const parent = this.deptOptions.find(item => item.deptId === row.deptId)
          this.isRootDept = !!parent && parent.parentId === '0'
        } else {
          this.isRootDept = false
        }
      })
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      if (!this.isExpandAll) {
        this.$refs.xTable.setAllTreeExpand(true)
      } else {
        this.$refs.xTable.clearTreeExpand()
      }
      this.isExpandAll = !this.isExpandAll
    },
    /** 导出按钮操作 */
    handleExport(row) {
      this.$confirm('即将导出部门数据，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.download('/system/dept/export', {
        }, `部门_${new Date().getTime()}.xlsx`)
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      getDept(row.deptId).then(response => {
        this.form = Object.assign(this.form, response.data)
        this.open = true
        this.title = '修改部门'
        this.type = 'update'
        this.formPlaceholder = {
          phone: response.data.phone,
          email: response.data.email
        }
        this.form.phone = null
        this.form.email = null
        listDeptExcludeChild(row.deptId).then(response => {
          this.deptOptions = this.handleTree(response.data, 'deptId')
          if (this.deptOptions.length === 0) {
            const noResultsOptions = { deptId: this.form.parentId, deptName: this.form.parentName, children: [] }
            this.deptOptions.push(noResultsOptions)
          }
          const parent = this.deptOptions.find(item => item.deptId === row.parentId)
          this.isRootDept = !!parent && parent.parentId === '0'
        })
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // if (checkPermi(['system:rate:add']) && this.type === 'add' && this.isRootDept) {
          //   this.rateResultVisible = true
          //   this.rateResult = this.$refs['rateRef'].getData()
          // } else {
          this.saveDept()
          // }
        }
      })
    },
    handleRateSubmit() {
      this.rateResultVisible = false
      if (this.rateEditVisible) {
        this.submitRateForm()
      } else {
        this.saveDept()
      }
    },
    async saveDept() {
      if (!this.isRootDept && checkPermi(['promotion:config:duoduo'])) {
        delete this.form.contract
      }
      this.submitLoading = true
      if (this.form.deptId !== undefined) {
        this.form.phone = this.form.phone || null
        this.form.email = this.form.email || null
        // 修改
        await updateDept(this.form)
        this.$modal.msgSuccess('修改成功')
        this.open = false
        this.getList()
        this.submitLoading = false
      } else {
        // 新增
        if (this.isRootDept && checkPermi(['promotion:config:duoduo'])) {
          if (!this.form.createBy) {
            this.submitLoading = false
            this.$modal.msgError('商务负责人不能为空')
            return
          }
        }
        await addDept(this.form).catch(() => {
          this.submitLoading = false
        })
        // if (checkPermi(['system:rate:list']) && this.isRootDept) {
        //   await this.$refs['rateRef'].saveRate(res.data, true)
        // }
        this.$modal.msgSuccess('新增成功')
        this.open = false
        this.getList()
        this.submitLoading = false
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除名称为"' + row.deptName + '"的数据项？').then(function() {
        return delDept(row.deptId)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    showRateEdit(row) {
      this.rateEditVisible = true
      this.form = row
    },
    showRateResult() {
      this.rateResultVisible = true
      this.rateResult = this.$refs['rateRef'].getData()
    },
    showRate(row) {
      this.form = row
      let tabIndex = 1
      getUserRateById(row.deptId).then(res => {
        res.data.timeList?.forEach(item => {
          item.title = item.stepStartTime.split(' ')[0]
          item.name = tabIndex + ''
          tabIndex++
        })
        this.rateResult.timeList = res.data.timeList || [{
          title: dayjs(new Date()).format('YYYY-MM-DD'),
          stepStartTime: dayjs(new Date()).format('YYYY-MM-DD'),
          name: '1',
          userCommission: [] }]
        this.rateDisplayVisible = true
      })
    },
    submitRateForm() {
      this.submitLoading = true
      this.$refs['rateRef'].saveRate(this.form.deptId).then(() => {
        this.rateEditVisible = false
        this.$message.success('修改成功')
      }).finally(() => {
        this.submitLoading = false
      })
    },

    handleDeptOptionChange(node) {
      this.isRootDept = node.parentId === '0'
    },
    calcIsRootDept(visible, row) {
      if (visible) {
        this.dropdownVisible = this.topDeptIds.includes(row.parentId)
      }
    },
    handleTransfer() {
      this.transferVisible = true
    },
    handleCheckboxChange({ records }) {
      this.selectedDepts = records
    },
    handleBatchRate() {
      this.secondDepts = this.selectedDepts.filter(item => this.topDeptIds.includes(item.parentId))
      this.batchRateVisible = true
    },
    handleBatchEdit(depts) {
      console.log('depts', depts)
      this.secondDepts = depts
      this.batchRateVisible = true
    }
  }
}
</script>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { getDuoduo, updateCommissionRatio, updateDuoDuo, updateTaxRate } from '@/api/system/dept'
import { Message, MessageBox } from 'element-ui'
import CommissionRate from '@/views/system/user/components/CommissionRate.vue'
import CommissionRateResult from '@/views/system/user/components/CommissionRateResult.vue'
import RebateDetail from './components/RebateDetail.vue'
import BusinessSelector from '@/components/BusinessSelector/index.vue'
import SavedSearches from '@/components/SavedSearches/index.vue'
import SetDeptRates from './components/SetDeptRates.vue'

const self = getCurrentInstance().proxy

const handleCommand = (com, row) => {
  switch (com) {
    case 'showDdRatio':
      showDdRatio(row)
      break
    case 'showRateEdit':
      self.showRateEdit(row)
      break
    case 'showRate':
      self.showRate(row)
      break
    case 'showTaxRate':
      showTaxRate(row)
      break
    case 'handleDelete':
      self.handleDelete(row)
      break
  }
}

const ddRatioVisible = ref(false)
const ddRatioSwitch = ref(false)
const effectiveTime = ref(null)
const promotionRate = ref(0)
const deptId = ref(null)

const showDdRatio = (row) => {
  self.form = row
  promotionRate.value = (row.promotionRate === undefined || row.promotionRate === -1) ? 0 : row.promotionRate
  ddRatioSwitch.value = !(row.promotionRate === -1 || row.promotionRate === undefined)
  effectiveTime.value = row.effectiveTime ? new Date(row.effectiveTime * 1000) : null
  deptId.value = row.deptId
  ddRatioVisible.value = true
}
const submitDdRatioForm = () => {
  if (!effectiveTime.value) {
    Message({
      message: `请选择生效时间`,
      type: 'error'
    })
    return
  }
  updateCommissionRatio({
    deptId: deptId.value,
    promotionRate: ddRatioSwitch.value ? promotionRate.value : -1,
    effectiveTime: effectiveTime.value.getTime() / 1000
  }).then(response => {
    Message({
      message: `修改成功`,
      type: 'success'
    })
    self.getList()
    ddRatioVisible.value = false
  })
}

const taxVisible = ref(false)
const taxLoading = ref(false)
const taxForm = reactive({
  deptId: null,
  taxRate: null,
  commissionRate: null
})
const showTaxRate = (row) => {
  self.form = row
  taxVisible.value = true
  taxForm.deptId = row.deptId
  taxForm.taxRate = row.taxRate
  taxForm.commissionRate = row.commissionRate
}
const setTaxRate = () => {
  if (taxForm.taxRate === undefined || taxForm.commissionRate === undefined) {
    Message({
      message: `税率不能为空`,
      type: 'error'
    })
    return
  }
  taxLoading.value = true
  updateTaxRate({
    deptId: taxForm.deptId,
    taxRate: taxForm.taxRate,
    commissionRate: taxForm.commissionRate
  }).then(response => {
    Message({
      message: `修改成功`,
      type: 'success'
    })
    self.getList()
    taxVisible.value = false
    taxLoading.value = false
  })
}

const setDeptRatesVisible = ref(false)
const rateList = ref([])
const ratesLoading = ref(false)
const deptNamesNotFind = ref([])
const setDeptRates = () => {
  MessageBox.prompt('请输入部门名称', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: '请输入部门名称, 多个部门用回车或逗号隔开'
  }).then(async({ value }) => {
    const deptNames = value.split(/[\n\r,，]/).map(item => item.trim()).filter(item => item)
    // this.topDeptIds.includes(row.parentId)
    const depts = []
    deptNamesNotFind.value = []
    const deptMap = new Map()
    self.originData.forEach(item => {
      if (deptNames.includes(item.deptName) && self.topDeptIds.includes(item.parentId)) {
        depts.push(item)
      }
      deptMap.set(item.deptId, item)
    })
    depts.forEach(item => {
      if (deptMap.has(item.parentId)) {
        item.parentName = deptMap.get(item.parentId).deptName
      }
    })
    deptNames.forEach(item => {
      if (!depts.some(dept => dept.deptName === item)) {
        deptNamesNotFind.value.push(item)
      }
    })
    // 根据deptNames排序
    depts.sort((a, b) => {
      return deptNames.indexOf(a.deptName) - deptNames.indexOf(b.deptName)
    })
    setDeptRatesVisible.value = true
    ratesLoading.value = true
    rateList.value = []
    rateList.value = await getRateList(depts)
    ratesLoading.value = false
  })
}

const getRateList = async(depts) => {
  const list = []
  for (const dept of depts) {
    const rateDeptInfo = { ...dept }
    const res = await getUserRateById(dept.deptId)
    if (res.data.timeList && res.data.timeList.length > 0) {
      const lastItem = res.data.timeList[res.data.timeList.length - 1]
      Object.assign(rateDeptInfo, lastItem)
      rateDeptInfo.userCommissionStr = rateDeptInfo.userCommission.map(item => `${item.stepEnd !== 0 ? `${item.stepStart} ~ ${item.stepEnd} 元时, ` : `大于 ${rateDeptInfo.userCommission.length > 1 ? item.stepStart : item.stepEnd} 元时, `} ${item.commissionStatus === 2 ? '收佣为' : '返点为'} ${item.commissionRate}%`).join(';\n')
    }
    list.push(rateDeptInfo)
    await new Promise(resolve => setTimeout(resolve, 50))
  }
  return list
}

</script>

<style lang="scss" scoped>
.pid-list {
  height: 48px;
  .pid {
    margin-right: 10px;
    flex-shrink:0;
    &:first-child {
      color: #409EFF;
    }
  }
}
::v-deep .el-dialog__body {
  padding: 10px 20px;
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
::v-deep .vxe-checkbox--label {
  display: none;
}
</style>
