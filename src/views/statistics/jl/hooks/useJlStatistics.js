import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router/composables'
import { listJlStatistics, getJlStatisticsTotal, getJlStatisticsDetails } from '@/api/statistics/jl'
import { dateRangePickerOptions } from '@/config'
import { Message } from 'element-ui'
import dayjs from 'dayjs'
import { formatCurrency, formatNumber, formatPercent, dataToTotalString } from '@/utils/formatters'

let rowKey = 1

export default function useJlStatistics(dimension = 'advertiserId', initialParams = {}) {
  // 响应式数据
  const loading = ref(false)
  const statisticsList = ref([])
  const total = ref(0)
  const totalData = ref({})
  const showSearch = ref(true)
  const router = useRouter()

  // 查询参数
  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    beginTime: initialParams.beginTime || null,
    endTime: initialParams.endTime || null,
    advertiserId: initialParams.advertiserId || null,
    advertiserName: initialParams.advertiserName || null,
    projectId: initialParams.projectId || null,
    projectName: initialParams.projectName || null,
    promotionId: initialParams.promotionId || null,
    promotionName: initialParams.promotionName || null,
    deptIds: initialParams.deptIds || [],
    business: initialParams.business || null,
    createBy: initialParams.createBy || null,
    queryDimension: dimension
  })

  // 日期范围
  const dateRange = ref([
    dayjs().format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD')
  ])

  // 日期选择器配置
  const dateRangeOptions = dateRangePickerOptions

  // 计算属性 - 根据维度显示不同的搜索条件
  const searchFields = computed(() => {
    const baseFields = ['dateRange', 'deptIds', 'business', 'createBy']

    switch (dimension) {
      case 'advertiserId':
        return [...baseFields, 'advertiserId', 'advertiserName']
      case 'projectId':
        return [...baseFields, 'advertiserId', 'advertiserName', 'projectId', 'projectName']
      case 'promotionId':
        return [...baseFields, 'advertiserId', 'advertiserName', 'projectId', 'projectName', 'promotionId', 'promotionName']
      default:
        return baseFields
    }
  })

  // 获取列表数据
  const getList = async() => {
    loading.value = true
    try {
      // 设置时间范围
      if (dateRange.value && dateRange.value.length === 2) {
        queryParams.beginTime = dateRange.value[0] + ' 00:00:00'
        queryParams.endTime = dateRange.value[1] + ' 23:59:59'
      }

      // 并行请求：分页列表 + 汇总
      const listParams = { ...queryParams }
      const totalParams = { ...queryParams }
      // 汇总接口不需要分页参数
      delete totalParams.pageNum
      delete totalParams.pageSize

      const [listResponse, totalResponse] = await Promise.all([
        listJlStatistics(listParams),
        getJlStatisticsTotal(totalParams)
      ])

      statisticsList.value = listResponse.rows || []
      total.value = listResponse.total || 0

      // 为支持树形展示的数据添加必要属性
      if (statisticsList.value.length > 0) {
        statisticsList.value.forEach(item => {
          item.rowKey = rowKey++
          item.level = dimension
          item.hasChildren = true
        })
      }

      // 处理合计数据（部分列聚合后需要额外处理）
      if (totalResponse && totalResponse.data) {
        const t = totalResponse.data || {}

        // 聚合列字符串展示（与列配置顺序一致）
        const aggregated = {
          conversionInfo: dataToTotalString(
            formatNumber(t.convertCnt),
            formatCurrency(t.conversionCost),
            formatPercent(t.conversionRate)
          ),
          deepConversionInfo: dataToTotalString(
            formatNumber(t.deepConvertCnt),
            formatPercent(t.deepConvertRate),
            formatCurrency(t.deepConvertCost)
          ),
          validPlayInfo: dataToTotalString(
            formatNumber(t.validPlay),
            formatPercent(t.validPlayRate),
            formatCurrency(t.validPlayCost)
          ),
          realTotalConvertCost: dataToTotalString(
            formatCurrency(t.realTotalSubConvertCost),
            formatCurrency(t.realTotalPayConvertCost),
            formatCurrency(t.realTotalAddConvertCost)
          ),
          formSubInfo: dataToTotalString(
            formatNumber(t.formSubCount),
            formatNumber(t.formSubConvertCount),
            formatNumber(t.formSubDeductionCount)
          ),
          formAddInfo: dataToTotalString(
            formatNumber(t.formAddCount),
            formatNumber(t.formAddConvertCount),
            formatNumber(t.formAddDeductionCount)
          ),
          formPayInfo: dataToTotalString(
            formatNumber(t.formPayCount),
            formatCurrency(t.formPayAmount),
            formatNumber(t.formPayConvertCount),
            formatNumber(t.formPayDeductionCount)
          ),
          formPayRealInfo: dataToTotalString(
            formatNumber(t.formPayRealCount),
            formatCurrency(t.formPayRealAmount)
          ),
          formRefundInfo: dataToTotalString(
            formatNumber(t.formRefundCount),
            formatCurrency(t.formRefundAmount),
            formatNumber(t.formRefundConvertCount),
            formatNumber(t.formRefundDeductionCount)
          )
        }

        totalData.value = { ...t, ...aggregated }
      } else {
        totalData.value = {}
      }
    } catch (error) {
      console.error('获取数据失败:', error)
      Message.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }

  // 获取汇总数据 (暂未实现)
  const getTotalData = async() => {
    try {
      const params = { ...queryParams }
      delete params.pageNum
      delete params.pageSize
      const response = await getJlStatisticsTotal(params)
      totalData.value = response?.data || {}
    } catch (error) {
      console.error('获取汇总数据失败:', error)
    }
  }

  // 搜索
  const handleQuery = () => {
    queryParams.pageNum = 1
    getList()
  }

  // 重置搜索
  const resetQuery = () => {
    Object.keys(queryParams).forEach(key => {
      if (key === 'pageNum') queryParams[key] = 1
      else if (key === 'pageSize') queryParams[key] = 10
      else if (key === 'queryDimension') queryParams[key] = dimension
      else if (key === 'deptIds') queryParams[key] = []
      else queryParams[key] = null
    })

    dateRange.value = [
      dayjs().format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD')
    ]

    handleQuery()
  }

  // 分页处理
  const handlePagination = ({ page, limit }) => {
    queryParams.pageNum = page
    queryParams.pageSize = limit
    getList()
  }

  // 导出数据 (暂未实现)
  const handleExport = async() => {
    try {
      const exportParams = { ...queryParams }
      delete exportParams.pageNum
      delete exportParams.pageSize

      // await exportJlStatistics(exportParams)
      console.log('导出接口暂未实现', exportParams)
      Message.warning('导出功能暂未实现')
    } catch (error) {
      console.error('导出失败:', error)
      Message.error('导出失败')
    }
  }

  // 数据格式化工具函数（使用统一的格式化工具）
  // formatNumber, formatCurrency, formatPercent 已从 @/utils/formatters 导入

  // 汇总行计算
  const getSummaries = (param) => {
    const { columns } = param
    const sums = []

    columns.forEach((column, index) => {
      if (index === 0) {
        sums[index] = '合计'
        return
      }

      // 直接使用接口返回的 totalData 字段，以避免前端对聚合列的错误求和
      const prop = column.property
      const value = totalData.value ? totalData.value[prop] : undefined

      if (value === null || value === undefined) {
        sums[index] = '--'
        return
      }

      // 针对百分比/金额进行基本格式化，其余保持数字/文本
      if (prop && (/Rate$/.test(prop) || /Ctr$/.test(prop))) {
        sums[index] = formatPercent(value)
      } else if (prop && typeof value === 'number' && (/Cost$/.test(prop) || /Amount$/.test(prop))) {
        sums[index] = formatCurrency(value)
      } else {
        sums[index] = typeof value === 'number' ? formatNumber(value) : value
      }
    })

    return sums
  }

  // 树形数据展开加载
  const loadTreeData = async(tree, treeNode, resolve) => {
    try {
      const query = { ...queryParams }

      // 根据当前行数据设置查询条件，获取下级数据
      if (tree.level === 'advertiserId') {
        query.advertiserId = tree.advertiserId
      } else if (tree.level === 'projectId') {
        query.advertiserId = tree.advertiserId
        query.projectId = tree.cdpProjectId
      } else if (tree.level === 'promotionId') {
        query.advertiserId = tree.advertiserId
        query.projectId = tree.cdpProjectId
        query.promotionId = tree.cdpPromotionId
      }
      query.queryDimension = dimension

      // 移除分页参数
      delete query.pageNum
      delete query.pageSize

      const response = await getJlStatisticsDetails(query)
      const children = response.rows || []

      // 为子数据添加必要属性
      children.forEach(item => {
        item.rowKey = rowKey++
        item.level = query.queryDimension
      })

      resolve(children)
    } catch (error) {
      console.error('获取树形数据失败:', error)
      Message.error('获取详细数据失败')
      resolve([])
    }
  }

  // 维度钻取功能
  const drillDown = (row, targetDimension) => {
    const query = {
      tab: targetDimension,
      beginTime: queryParams.beginTime,
      endTime: queryParams.endTime
    }

    // 根据当前维度和目标维度设置查询参数
    if (dimension === 'advertiserId' && targetDimension === 'project') {
      query.advertiserId = row.advertiserId
      query.advertiserName = row.advertiserName
    } else if (dimension === 'advertiserId' && targetDimension === 'plan') {
      query.advertiserId = row.advertiserId
      query.advertiserName = row.advertiserName
    } else if (dimension === 'projectId' && targetDimension === 'plan') {
      query.advertiserId = row.advertiserId
      query.advertiserName = row.advertiserName
      query.projectId = row.cdpProjectId
      query.projectName = row.cdpProjectName
    }

    router.push({
      path: '/statistics/JlStatistics',
      query
    })
  }

  return {
    // 响应式数据
    loading,
    statisticsList,
    total,
    totalData,
    showSearch,
    queryParams,
    dateRange,
    dateRangeOptions,
    searchFields,

    // 方法
    getList,
    getTotalData,
    handleQuery,
    resetQuery,
    handlePagination,
    handleExport,
    getSummaries,
    loadTreeData,
    drillDown,

    // 工具函数
    formatNumber,
    formatCurrency,
    formatPercent
  }
}
