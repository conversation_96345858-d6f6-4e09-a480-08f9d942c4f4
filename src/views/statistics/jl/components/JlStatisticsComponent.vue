<template>
  <div class="app-container">
    <!-- 搜索条件 -->
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="search-form"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="时间范围" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="dateRangeOptions"
          @change="handleQuery"
        />
      </el-form-item>

      <el-form-item prop="advertiserId">
        <el-input
          v-model="queryParams.advertiserId"
          placeholder="账户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item prop="advertiserName">
        <el-input
          v-model="queryParams.advertiserName"
          placeholder="账户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- 项目搜索条件（项目和广告维度显示） -->
      <template v-if="dimension !== 'advertiserId'">
        <el-form-item prop="projectId">
          <el-input
            v-model="queryParams.projectId"
            placeholder="项目ID"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item prop="projectName">
          <el-input
            v-model="queryParams.projectName"
            placeholder="项目名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
      </template>

      <!-- 广告搜索条件（仅广告维度显示） -->
      <template v-if="dimension === 'promotionId'">
        <el-form-item prop="promotionId">
          <el-input
            v-model="queryParams.promotionId"
            placeholder="广告ID"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item prop="promotionName">
          <el-input
            v-model="queryParams.promotionName"
            placeholder="广告名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
      </template>

      <DeptTreeSelector
        v-model="queryParams.deptIds"
        :default-ids="queryParams.deptIds"
        multiple
        placeholder="请选择部门"
      />

      <BusinessSelector v-model="queryParams.business" />

      <el-form-item prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="负责人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          :extra-date-range.sync="dateRange"
          :storage-key="storageKey"
          @search="handleQuery"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb-4">
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>

    <!-- 数据表格 -->
    <div>
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="statisticsList"
        :summary-method="getSummaries"
        show-summary
        row-key="rowKey"
        stripe
        border
        lazy
        :load="loadTreeData"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        @header-dragend="handleHeaderDragend"
      >
        <el-table-column
          type="index"
          label="序号"
          align="center"
          width="50"
        />

        <template v-for="(c, i) in columns">
          <template v-if="c.visible !== false">
            <!-- 信息列特殊处理 -->
            <template v-if="c.info">
              <el-table-column
                :key="i"
                :label="c.label"
                :width="c.width"
                :fixed="c.fixed"
                align="left"
              >
                <template #default="scope">
                  <!-- 账户级别显示钻取按钮 -->
                  <template v-if="c.prop === 'advertiserInfo' && (scope.row.level === 'advertiserId' || !scope.row.level)">
                    <template v-if="scope.row.hasChildren">

                      <BaseInfoCell
                        :id="scope.row[c.info.id]"
                        :name="scope.row[c.info.name]"
                        style="width: 85%;"
                      />
                    </template>
                    <span v-else class="color-primary">{{ scope.row.timePeriod }}</span>
                  </template>
                  <!-- 其他信息列 -->
                  <BaseInfoCell
                    v-else-if="c.prop !== 'advertiserInfo' || scope.row.hasChildren"
                    :id="scope.row[c.info.id]"
                    :name="scope.row[c.info.name]"
                    style="width: 85%;"
                  />
                  <span v-else class="color-primary">{{ scope.row.timePeriod }}</span>
                </template>
              </el-table-column>
            </template>

            <!-- 普通列 -->
            <el-table-column
              v-else
              :key="`col-${i}`"
              :label="c.label"
              :prop="c.prop"
              :width="c.width"
              :align="c.align || 'center'"
              :sortable="c.sortable"
            >
              <template slot-scope="scope">
                <span v-if="c.render">{{ c.render(scope.row[c.prop], scope.row) }}</span>
                <TableColumnSet v-else-if="c.set" :set="c.set" :row="scope.row" :label-width="c.labelWidth" />
                <span v-else>{{ scope.row[c.prop] ?? '-' }}</span>
              </template>
            </el-table-column>
          </template>
        </template>
      </el-table>
    </div>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="handlePagination"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import useJlStatistics from '../hooks/useJlStatistics'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import DeptTreeSelector from '@/components/DeptTreeSelect/DeptTreeSelector.vue'
import BusinessSelector from '@/components/BusinessSelector/index.vue'
import SavedSearches from '@/components/SavedSearches/index.vue'
import Pagination from '@/components/Pagination/index.vue'
import RightToolbar from '@/components/RightToolbar/index.vue'
import TableColumnSet from '@/components/TableColumnSet/index.vue'
import useColumns from '@/hooks/useColumns'
import { accountColumns, projectColumns, planColumns } from '../config/config'

// Props
const props = defineProps({
  // 统计维度：advertiserId(账户) | projectId(项目) | promotionId(广告)
  dimension: {
    type: String,
    default: 'advertiserId',
    validator: (value) => ['advertiserId', 'projectId', 'promotionId'].includes(value)
  },
  // 初始查询参数
  initialParams: {
    type: Object,
    default: () => ({})
  }
})

// Table ref
const tableRef = ref(null)

// 计算属性
const columnsConfig = computed(() => {
  switch (props.dimension) {
    case 'advertiserId':
      return accountColumns
    case 'projectId':
      return projectColumns
    case 'promotionId':
      return planColumns
    default:
      return accountColumns
  }
})

const storageKey = computed(() => `jl_${props.dimension}_statistics`)

// Columns
const { columnsInstance, columns, operatedColumns, customList, handleHeaderDragend } = useColumns({
  defaultColumns: columnsConfig.value,
  tableRef,
  name: storageKey.value
})

// Statistics
const {
  loading,
  statisticsList,
  total,
  showSearch,
  queryParams,
  dateRange,
  dateRangeOptions,
  getList,
  handleQuery,
  resetQuery,
  handlePagination,
  getSummaries,
  loadTreeData,
  drillDown
} = useJlStatistics(props.dimension, props.initialParams)

// 初始化数据
onMounted(() => {
  getList()
})
</script>

<style scoped>
.drill-down-actions {
  display: flex;
  gap: 8px;
}
</style>
