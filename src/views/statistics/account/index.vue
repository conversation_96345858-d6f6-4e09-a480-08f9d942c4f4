<template>
  <div class="app-container adaption-container">
    <el-form v-show="showSearch" ref="queryForm" class="search-form" :model="queryParams" size="small" :inline="true" label-width="70px">
      <el-form-item
        v-if="!defaultMediaType"
        prop="mediaType"
      >
        <el-select
          v-model="queryParams.mediaType"
          placeholder="媒体类型"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.media_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <svg-icon :icon-class="dict.label" />
            <span> {{ dict.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="goodsPlatform">
        <el-select
          v-model="queryParams.goodsPlatform"
          placeholder="商品平台"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.platform_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            <svg-icon :icon-class="dict.label" />
            <span> {{ dict.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <template v-if="queryParams.mediaType === '1'">
        <el-form-item prop="statusFirst">
          <el-select
            v-model="queryParams.statusFirst"
            placeholder="一级计划状态"
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="dict in dict.type.ocean_promotion_status_first"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
              <svg-icon :icon-class="dict.label" />
              <span> {{ dict.label }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="queryParams.statusFirst ==='PROMOTION_STATUS_DISABLE'" prop="statusSecondList">
          <el-select
            v-model="queryParams.statusSecondList"
            placeholder="二级计划状态"
            clearable
            multiple
            collapse-tags
            collapse-tags-tooltip
            @change="handleQuery"
          >
            <el-option
              v-for="dict in dict.type.ocean_promotion_status_second"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
              <svg-icon :icon-class="dict.label" />
              <span> {{ dict.label }}</span>
            </el-option>
          </el-select>
        </el-form-item>
      </template>
      <el-form-item prop="advertiserIds">
        <el-input
          v-model.trim="queryParams.advertiserIds"
          placeholder="账户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="advertiserName">
        <el-input
          v-model.trim="queryParams.advertiserName"
          placeholder="账户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="projectId">
        <el-input
          v-model.trim="queryParams.projectId"
          placeholder="项目ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="projectName">
        <el-input
          v-model.trim="queryParams.projectName"
          placeholder="项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="planIds">
        <el-input
          v-model.trim="queryParams.planIds"
          placeholder="计划ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="planName">
        <el-input
          v-model.trim="queryParams.planName"
          placeholder="计划名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <UserSearchInput :create-by.sync="queryParams.createBy" @query="handleQuery" />
      <el-form-item prop="deptIds">
        <DeptTreeSelect v-model="queryParams.deptIds" :options="deptOptions" />
      </el-form-item>
      <el-form-item label="操作时间">
        <el-date-picker
          v-model="dateRange"
          :popper-class="device === 'mobile' ? 'mobile-date-picker' : ''"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :picker-options="device === 'mobile' ? {}: pickerOptions"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          :extra-date-range.sync="dateRange"
          @search="handleQuery"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:planstatistics:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport('all')"
        >汇总导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['promotion:planstatistics:export']"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport('day')"
        >分日导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          plain
          size="mini"
          :disabled="multiple"
          @click="batchUpdateConversion(selections)"
        >修改回传比例</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          plain
          size="mini"
          icon="el-icon-time"
          :disabled="multiple"
          @click="batchUpdateAutoConversion(selections)"
        >扣后补回传</el-button>
      </el-col>
      <el-col v-hasPermi="['promotion:media:updateStatus']" :span="1.5">
        <el-button size="mini" type="primary" :disabled="multiple" @click="handleOpen">批量开启</el-button>
      </el-col>
      <el-col v-hasPermi="['promotion:media:updateStatus']" :span="1.5">
        <el-button size="mini" type="warning" :disabled="multiple" @click="handleSuspend">批量暂停</el-button>
      </el-col>
      <el-col v-has-permi="['media:warn:saveOrUpdate']" :span="1.5">
        <el-button size="mini" type="primary" @click="showPreWarning(selections)">批量设置预警</el-button>
      </el-col>
      <el-col
        v-has-permi="['promotion:media:edit']"
        :span="1.5"
      >
        <el-button
          type="primary"
          plain
          size="mini"
          :disabled="multiple"
          icon="el-icon-edit"
          @click="showBatchBidDialog"
        >批量修改出价</el-button>
      </el-col> -->
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>
    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        v-bind="tableHeight"
        :data="statisticsList"
        :summary-method="getSummaries"
        show-summary
        row-key="rowKey"
        stripe
        border
        @header-dragend="handleHeaderDragend"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
        />
        <el-table-column
          type="index"
          label="序号"
          align="center"
          width="50"
        />
        <template v-for="(c,i) in columns">
          <template v-if="c.visible !== false">
            <el-table-column v-if="c.prop === 'mediaType'" :key="i" label="媒体类型" align="center" prop="mediaType" :width="c.width">
              <template slot-scope="scope">
                <svg-icon v-if="mediaTypeMap[scope.row.mediaType]" :icon-class="mediaTypeMap[scope.row.mediaType]" />
                <dict-tag :options="dict.type.media_type" :value="scope.row.mediaType" style="display: inline-block;margin-left: 5px" />
              </template>
            </el-table-column>
            <el-table-column v-else-if="c.prop === 'goodsPlatform'" :key="i" label="商品平台" align="center" prop="goodsPlatform" :width="c.width">
              <template slot-scope="scope">
                <template v-if="scope.row.orderCount">
                  <svg-icon v-if="dict.label.platform_type[scope.row.goodsPlatform]" :icon-class="dict.label.platform_type[scope.row.goodsPlatform]" />
                  <span style="padding-left: 5px">{{ dict.label.platform_type[scope.row.goodsPlatform] }}</span>
                </template>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column v-else-if="c.prop === 'remark'" :key="i" label="备注" align="center" show-overflow-tooltip prop="remark" :width="c.width">
              <template slot-scope="scope">
                <div v-if="!scope.row.timePeriod" class="remark-item pointer overflow-text" @click="editRemark(scope.row)">
                  {{ scope.row.remark || '-' }}
                  <i class="remark-btn el-icon-edit color-primary" />
                </div>
              </template>
            </el-table-column>
            <el-table-column v-else-if="c.prop === 'JLStatus'" :key="i" :label="c.label" align="left" :prop="c.prop" :width="c.width">
              <template #default="scope">
                <PlanStatus :plan="scope.row" />
              </template>
            </el-table-column>
            <el-table-column v-else-if="c.prop === 'advertiserName'" :key="i" :label="c.label" align="left" :prop="c.prop" :width="c.width">
              <template #default="scope">
                <BaseInfoCell v-if="!scope.row.timePeriod" :id="scope.row.advertiserId" :name="scope.row.advertiserName" />
                <span v-else>{{ scope.row.timePeriod }}</span>
              </template>
            </el-table-column>
            <el-table-column v-else-if="c.prop === 'planName'" :key="i" :label="c.label" align="left" :prop="c.prop" :width="c.width">
              <template #default="scope">
                <BaseInfoCell v-if="!scope.row.timePeriod" :id="scope.row.planId" :name="scope.row.planName" />
              </template>
            </el-table-column>
            <el-table-column v-else-if="c.prop === 'projectName'" :key="i" :label="c.label" align="left" :prop="c.prop" :width="c.width">
              <template #default="scope">
                <BaseInfoCell v-if="!scope.row.timePeriod" :id="scope.row.projectId" :name="scope.row.projectName" />
              </template>
            </el-table-column>
            <el-table-column v-else-if="c.prop === 'enableAmount'" :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
              <template #header>
                <span>回传金额比例</span>
                <el-tooltip effect="dark" content="优先使用计划报表比例，未设置则使用账户报表中比例；未启用则使用100%比例回传。" placement="top">
                  <i class="el-icon-question color-primary" />
                </el-tooltip>
              </template>
              <template #default="scope">
                <ConversionItem
                  v-if="scope.row.id"
                  :form.sync="scope.row"
                  item-type="amount"
                  :type="conversionType"
                  @setConversion="showConversion"
                  @switchConversion="getList"
                />
              </template>
            </el-table-column>
            <el-table-column v-else-if="c.prop === 'conversionProportion'" :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
              <template #header>
                <span>回传订单配置</span>
                <el-tooltip effect="dark" content="优先使用计划报表比例，未设置则使用账户报表中比例；未启用则使用100%比例回传。" placement="top">
                  <i class="el-icon-question color-primary" />
                </el-tooltip>
              </template>
              <template #default="scope">
                <ConversionItem
                  v-if="scope.row.id"
                  :form.sync="scope.row"
                  item-type="order"
                  :type="conversionType"
                  @setConversion="showConversion"
                  @switchConversion="getList"
                />
              </template>
            </el-table-column>
            <template v-else-if="c.prop === 'action'">
              <el-table-column :key="i" :label="c.label" align="center" :prop="c.prop" :width="c.width">
                <template #default="scope">
                  <template v-if="scope.row.id">
                    <el-button
                      v-show="!scope.row.timePeriod"
                      v-hasPermi="['promotion:advertising:list']"
                      size="mini"
                      type="text"
                      icon="el-icon-film"
                      @click="showAdvertising(scope.row)"
                    >
                      查看广告
                    </el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-menu"
                      @click="showCode(scope.row)"
                    >
                      预览码
                    </el-button>
                    <el-button
                      v-if="scope.row.orderCount && orderPathMap[scope.row.goodsPlatform]"
                      size="mini"
                      type="text"
                      icon="el-icon-s-shop"
                      @click="toOrder(scope.row)"
                    >
                      查看订单
                    </el-button>
                    <el-dropdown
                      placement="bottom-start"
                      trigger="click"
                      @command="handleCommand($event, scope.row)"
                    >
                      <el-button size="mini" type="text" icon="el-icon-more" style="margin: 0 7px">更多</el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          icon="el-icon-discount"
                          command="conversionProportion"
                        >回传比例</el-dropdown-item>
                        <!-- <el-dropdown-item
                          icon="el-icon-time"
                          command="updateAutoConversion"
                        >扣后补回传</el-dropdown-item> -->
                        <!-- <el-dropdown-item
                          v-if="scope.row.mediaType === 1 || scope.row.mediaType === 4"
                          icon="el-icon-monitor"
                          command="updateMonitorAutoConversion"
                        >多多自研回传</el-dropdown-item> -->
                        <!-- <el-dropdown-item
                          v-has-permi="['promotion:order:privatelyOwned']"
                          icon="el-icon-s-shop"
                          command="showCommodityInformation"
                        >推广商品</el-dropdown-item> -->
                        <!-- <el-dropdown-item
                          v-if="scope.row.mediaType === 1 || scope.row.mediaType === 2"
                          icon="el-icon-s-order"
                          command="showModificationRecord"
                        >操作日志</el-dropdown-item> -->
                        <el-dropdown-item
                          v-has-permi="['media:warn:saveOrUpdate']"
                          icon="el-icon-warning-outline"
                          command="showPreWarning"
                        >设置账户预警</el-dropdown-item>
                        <!-- <el-dropdown-item
                          v-if="scope.row.mediaType === 1 || scope.row.mediaType === 2 || scope.row.mediaType === 4"
                          icon="el-icon-link"
                          command="showPlanning"
                        >计划信息</el-dropdown-item> -->
                        <!-- <el-dropdown-item
                          icon="el-icon-circle-plus-outline"
                          command="setSupplementary"
                        >补单</el-dropdown-item> -->
                        <el-dropdown-item
                          v-if="scope.row.goodsPlatform === 8"
                          icon="el-icon-setting"
                          command="showIntelligence"
                        >智能提量</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>
                </template>
              </el-table-column>
            </template>
            <CustomTableColumn v-else :key="i" :data="c" :render-map="renderMap" />
          </template>
        </template>
      </el-table>

    </div>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[10, 50, 100, 200, 500]"
      @pagination="getList"
    />
    <el-dialog
      title="广告列表"
      :visible.sync="advertisingVisible"
      width="70%"
      top="5vh"
      append-to-body
    >
      <Advertising v-if="advertisingVisible" :plan-id="planId" :advertiser-id="advertiserId" />
    </el-dialog>
    <el-dialog
      :visible.sync="codeVisible"
      width="300px"
      append-to-body
      title="预览二维码"
    >
      <div class="code-title">计划名称：{{ currentRow.planName }}</div>
      <el-image :src="codeUrl" />
    </el-dialog>

    <el-dialog title="修改回传配置" :visible.sync="conversionDialogVisible" width="820px" top="10vh" append-to-body>
      <ConversionSetups v-if="conversionDialogVisible" :form="conversionForm" :form-type="conversionFormType" :type="conversionType" @save="handleConversionSave" @cancel="conversionDialogVisible = false" />
    </el-dialog>

    <!-- 扣后补回传 -->
    <el-dialog title="扣后补回传配置" :visible.sync="autoConversionDialogVisible" width="820px" top="10vh" append-to-body>
      <AutoConversionSetups v-if="autoConversionDialogVisible" :form="autoConversionForm" :type="autoConversionType" @save="handleAutoConversionSave" @cancel="autoConversionDialogVisible = false" />
    </el-dialog>

    <!-- 多多自研回传 -->
    <el-dialog title="多多自研回传配置" :visible.sync="monitorAutoConversionDialogVisible" width="820px" top="10vh" append-to-body>
      <MonitorAutoConversionSetups v-if="monitorAutoConversionDialogVisible" :form="monitorAutoConversionForm" :type="monitorAutoConversionType" @save="handleMonitorAutoConversionSave" @cancel="monitorAutoConversionDialogVisible = false" />
    </el-dialog>

    <!--  商品推广 -->
    <CommodityInformation :visible.sync="ciVisible" :form="form" :date-range="dateRange" />
    <ModificationRecord :visible.sync="recordVisible" :form="form" :date-range="dateRange" />
    <PlanningInformation :visible.sync="planningVisible" :form="form" />

    <!--  账户预警 -->
    <el-dialog
      title="账户预警"
      :visible.sync="preWarningVisible"
      width="600px"
      top="10vh"
      append-to-body
      @close="preWarningVisible=false"
    >
      <PreWarning v-if="preWarningVisible" :form="preWarningForm" business-type="3" @save="handlePreWarningSave" @cancel="preWarningVisible=false" />
    </el-dialog>
    <!-- <SetSupplementary :visible.sync="supplementaryVisible" type="plan" :form="form" @success="getList" /> -->
    <!-- 智能提量 -->
    <Intelligence v-if="intelligenceVisible" :visible.sync="intelligenceVisible" :form="intelligenceRow" @refresh="getList" />
    <el-dialog
      title="批量修改出价"
      :visible.sync="batchBidVisible"
      width="400px"
      append-to-body
    >
      <el-form :model="batchBidForm" label-width="80px">
        <el-form-item label="新出价" prop="newBid">
          <el-input-number v-model="batchBidForm.newBid" :min="0" :precision="2" label="请输入新出价" :controls="false" style="margin: 0 10px" />
          元
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitLoading" @click="handleBatchBid">确认</el-button>
        <el-button @click="batchBidVisible = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import {
  listStatistics,
  getStatisticsTotal,
  getPlanStatisticsDetails,
  queryPreviewCode
} from '@/api/statistics/plan'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import { mapGetters } from 'vuex'
import Advertising from '@/components/Advertising/index.vue'
import { dateRangePickerOptions } from '@/config'
import { loadPageSize } from '@/utils/beforeList'
// import SetSupplementary from '@/views/promotion/statistics/components/SetSupplementary'
import clearDirectLinks from '@/hooks/useClearDirectLinks'
import { checkRole } from '@/utils/permission'
import { idsToArr } from '@/utils'
import { batchUpdateBid } from '@/api/promotion/media'

let rowKey = 1
export default {
  name: 'AccountStatistics',
  components: {
    Advertising,
    BaseInfoCell
    // SetSupplementary
  },
  props: {
    defaultMediaType: {
      type: [String, Number],
      default: null
    },
    routeAdvertiserId: {
      type: [String, Number],
      default: null
    }
  },
  dicts: ['media_type', 'platform_type', 'ocean_promotion_status_first', 'ocean_promotion_status_second'],
  computed: {
    ...mapGetters([
      'device',
      'permissions',
      'roles',
      'tableHeight'
    ]),
    mediaTypeMap() {
      return this.dict.type.media_type.reduce((obj, item) => {
        obj[item.value] = item.label
        return obj
      }, {})
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 媒体账户报表表格数据
      statisticsList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mediaType: null,
        goodsPlatform: null,
        advertiserIds: null,
        advertiserName: null,
        planIds: null,
        planName: null,
        projectId: null,
        projectName: null,
        deptIds: [],
        createBy: null
      },
      pickerOptions: dateRangePickerOptions,
      // 日期范围
      dateRange: [
        dayjs().format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD')
      ],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        advertiserId: [
          { required: true, message: 'ID不能为空', trigger: 'blur' }
        ],
        statDatetime: [
          { required: true, message: '数据起始时间不能为空', trigger: 'blur' }
        ],
        inventory: [
          { required: true, message: '投放广告位不能为空', trigger: 'blur' }
        ],
        creativeMaterialMode: [
          { required: true, message: '创意类型不能为空', trigger: 'blur' }
        ],
        landingType: [
          { required: true, message: '推广目的类型不能为空', trigger: 'change' }
        ],
        pricing: [
          { required: true, message: '出价类型不能为空', trigger: 'blur' }
        ],
        imageMode: [
          { required: true, message: '素材类型不能为空', trigger: 'blur' }
        ],
        cost: [
          { required: true, message: '消耗不能为空', trigger: 'blur' }
        ],
        show: [
          { required: true, message: '展示数不能为空', trigger: 'blur' }
        ],
        avgShowCost: [
          { required: true, message: '平均千次展现费用不能为空', trigger: 'blur' }
        ],
        click: [
          { required: true, message: '点击数不能为空', trigger: 'blur' }
        ]
      },
      // 合计数据
      totalData: {
        'advertiserName': '合计',
        'cost': 0,
        'show': 0,
        'avgShowCost': 0,
        'click': 0,
        'avgClickCost': 0,
        'realOrderCount': 0,
        'orderCountDeduct': 0,
        'realOrderAmount': 0,
        'realOrderAmountDeduct': 0,
        'amountAfterDeducting': 0,
        'clickRate': '0%',
        'roi': 0,
        'realRoi': 0,
        'deductRoi': 0,
        'convert': 0,
        'convertCost': 0,
        'cvtRate': '0%',
        'deepConvert': null,
        'deepConvertCost': 0,
        'dctRate': '0%'
      },

      advertisingVisible: false,
      advertiserId: '',
      planId: '',

      ciVisible: false,
      recordVisible: false,
      planningVisible: false,
      preWarningVisible: false,
      preWarningForm: {},
      supplementaryVisible: false,
      firstRequest: false,
      intelligenceVisible: false,
      intelligenceRow: null,
      advertAuth: ['1', '2', ' 4']
    }
  },
  created() {
    if (this.defaultMediaType) this.queryParams.mediaType = this.defaultMediaType
    if (this.routeAdvertiserId) {
      this.queryParams.advertiserIds = this.routeAdvertiserId
    }
    loadPageSize(this.queryParams)
  },
  mounted() {
  },
  methods: {
    /** 查询媒体账户报表列表 */
    getList() {
      // if (checkRole(['admin']) && !this.firstRequest && !this.routeAdvertiserId) {
      //   this.firstRequest = true
      //   return
      // }
      if (this.loading) return
      this.loading = true
      const query = { ...this.addDateRange(this.queryParams, this.dateRange) }
      query.advertiserIds = idsToArr(query.advertiserIds)
      query.planIds = idsToArr(query.planIds)
      query.params.groupBy = 'advertiser_id'
      Promise.all([listStatistics(query), getStatisticsTotal(query)]).then(([listResponse, totalResponse]) => {
        this.statisticsList = listResponse.rows
        this.statisticsList.forEach(item => {
          item.hasChildren = true
          item.statusSecond = item.statusSecond ? JSON.parse(item.statusSecond) : '-'
          item.rowKey = rowKey++
        })
        this.total = listResponse.total === -1 ? this.total : listResponse.total
        const totalData = totalResponse.data
        if (totalData) {
          this.totalData = totalData
          this.totalData.advertiserName = `合计 (${this.total}) `
          this.$nextTick(() => {
            this.$refs['tableRef'].doLayout()
          })
        }
      }).finally(() => {
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [
        dayjs().format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD')
      ]
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport(type) {
      this.$confirm('即将导出数据，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.download(`promotion/planstatistics/${type === 'all' ? 'export' : 'split_export'}`, {
          ...this.addDateRange(this.queryParams, this.dateRange)
        }, `statistics_${new Date().getTime()}.xlsx`)
        this._setupProxy.showTooltip('fileExport')
      })
    },
    getSummaries(param) {
      const { columns } = param
      const sums = []
      columns.forEach((column, index) => {
        sums[index] = this.totalData[column.property]
      })

      return sums
    },
    // 广告
    showAdvertising({ planId, advertiserId }) {
      this.advertisingVisible = true
      this.planId = planId
      this.advertiserId = advertiserId
    },

    loadData(node, treeNode, resolve) {
      getPlanStatisticsDetails({
        planId: node.planId,
        advertiserId: node.advertiserId,
        time: this.dateRange[1],
        mediaType: node.mediaType
      }).then(response => {
        if (response.code === 200) {
          const list = response.data.map(item => {
            item.rowKey = rowKey++
            item.hasChildren = false
            return item
          })
          resolve(list)
        } else {
          resolve([])
        }
      })
    },
    showCommodityInformation(row) {
      this.form = row
      this.ciVisible = true
    },
    showModificationRecord(row) {
      this.form = row
      this.recordVisible = true
    },
    showPlanning(row) {
      this.form = row
      this.planningVisible = true
    },

    showPreWarning(data) {
      this.preWarningForm = data
      this.preWarningVisible = true
    },
    handlePreWarningSave() {
      this.getList()
      this.preWarningVisible = false
    },
    setSupplementary(row) {
      this.supplementaryVisible = true
      this.form = row
    },

    showIntelligence(row) {
      this.intelligenceRow = row
      this.intelligenceVisible = true
    },
    handleCommand(command, row) {
      switch (command) {
        case 'showCommodityInformation':
          this.showCommodityInformation(row)
          break
        case 'showModificationRecord':
          this.showModificationRecord(row)
          break
        case 'showPlanning':
          this.showPlanning(row)
          break
        case 'showPreWarning':
          this.showPreWarning(row)
          break
        case 'setSupplementary':
          this.setSupplementary(row)
          break
        case 'conversionProportion':
          this._setupProxy.showConversion(row)
          break
        case 'updateAutoConversion':
          this._setupProxy.showAutoConversion(row)
          break
        case 'updateMonitorAutoConversion':
          this._setupProxy.showMonitorAutoConversion(row)
          break
        case 'showIntelligence':
          this.showIntelligence(row)
          break
        case 'clearDirectLinks':
          clearDirectLinks({
            advertiserId: row.advertiserId,
            planId: row.planId,
            type: 2
          })
          break
      }
    },

    // 跳转页面
    toPage(name, params) {
      this.$router.push({
        name,
        params
      })
    },
    // 复制成功
    clipboardSuccess() {
      this.$modal.msgSuccess('复制成功')
    }

  }
}
</script>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router/composables'
import useColumns from '@/hooks/useColumns'
import useQueryCallback from '@/hooks/useQueryCallback'
import { MaxDangerRate } from '@/utils/render'
import DeptTreeSelect from '@/components/DeptTreeSelect/DeptTreeSelect.vue'
import useStoreTableScroller from '@/hooks/useStoreTableScroller'
import useTooltipVisible from '@/hooks/useTooltipVisible'
import CommodityInformation from '@/components/CommodityInformation/index.vue'
import ModificationRecord from './components/ModificationRecord/index.vue'
import PlanStatus from './components/PlanStatus.vue'
import CustomTableColumn from '@/components/CustomTable/CustomTableColumn.vue'
import PlanningInformation from '@/views/statistics/plan/components/PlanningInformation.vue'
import PreWarning from '@/views/promotion/media/components/PreWarning.vue'
import useDeptOptions, { getDefaultIds, setDefaultIds } from '@/components/DeptTreeSelect/useDeptOptions'
import ConversionItem from '@/components/ConversionSetups/ConversionItem.vue'
import useConversion from '@/components/ConversionSetups/hooks/useConversion'
import useAutoConversion from '@/components/AutoConversionSetups/hooks/useAutoConversion'
import useMonitorAutoConversion from '@/components/MonitorAutoConversionSetups/hooks/useMonitorAutoConversion'

import ConversionSetups from '@/components/ConversionSetups/index.vue'
import AutoConversionSetups from '@/components/AutoConversionSetups/index.vue'
import MonitorAutoConversionSetups from '@/components/MonitorAutoConversionSetups/index.vue'
import { updateRemark } from '@/api/statistics/plan'
import { Message, MessageBox } from 'element-ui'
import { isJl } from '@/utils/judge/media'
import Intelligence from './components/Intelligence.vue'
import SavedSearches from '@/components/SavedSearches/index.vue'
import { updateStatus } from '@/api/statistics/common'
const renderMap = {
  chargeBackRate: (row) => {
    return MaxDangerRate(row.chargeBackRate)
  }
}

const defaultColumns = [
  { label: `媒体类型`, prop: 'mediaType', width: '100' },
  { label: `账户名称`, prop: 'advertiserName', width: '210' },
  { label: `计划名称`, prop: 'planName', width: '200' },
  { label: `项目名称`, prop: 'projectName', width: '200' },
  { label: `操作`, prop: 'action', width: '180', overflow: true },
  { label: `备注`, prop: 'remark', width: '100' },
  { label: `商品平台`, prop: 'goodsPlatform', width: '100' },
  { label: `巨量计划状态`, prop: 'JLStatus', width: '160' },
  { label: `回传金额比例`, prop: 'enableAmount', width: '120', tooltip: '回传金额比例开关关闭时，100%回传' },
  { label: `回传订单配置`, prop: 'conversionProportion', width: '120', tooltip: '回传订单配置开关关闭时，100%回传' },
  { label: `公司部门信息`, prop: 'CompanyDept', width: '160', labelWidth: 40,
    set: [
      { label: `公司`, prop: 'firstDeptName' },
      { label: `部门`, prop: 'deptName' }
    ] },
  // { label: `所属部门`, prop: 'deptName', width: '180', overflow: true },
  // { label: `所属公司`, prop: 'firstDeptName', width: '180', overflow: true },
  { label: `消耗`, prop: 'cost', width: '120' },
  { label: `展示数`, prop: 'show', width: '120' },
  { label: `平均千次展现费用`, prop: 'avgShowCost', width: '150' },
  { label: `点击数`, prop: 'click', width: '120' },
  { label: `点击率`, prop: 'clickRate', width: '120' },
  { label: `平均点击单价`, prop: 'avgClickCost', width: '130' },
  { label: `平均客单价`, prop: 'avgPrice', width: '150' },
  { label: `转化数`, prop: 'convert', width: '120' },
  { label: `转化成本`, prop: 'convertCost', width: '120' },
  { label: `转化率`, prop: 'cvtRate', width: '120' },
  { label: `实际回传率`, prop: 'returnRate', width: '120' },
  { label: `下单订单数`, prop: 'orderCount', width: '130' },
  { label: `下单总金额`, prop: 'orderAmount', width: '130', tooltip: '已支付的订单金额' },
  { label: `下单ROI`, prop: 'roi', width: '120', tooltip: '已支付订单金额除以今日消耗' },
  { label: `成交订单数`, prop: 'realOrderCount', width: '130' },
  { label: `成交总金额`, prop: 'realOrderAmount', width: '130', tooltip: '已支付减去已退款的订单金额' },
  { label: `成交ROI`, prop: 'realRoi', width: '120', tooltip: '(已支付减去已退款) 除以今日消耗' },
  { label: `盈亏成本`, prop: 'profit', width: '120', tooltip: '盈亏成本 = 拿货成本 + 运费 + 其它非广告消耗成本' },
  { label: `盈亏ROI`, prop: 'profitRoi', width: '120', tooltip: '设置预估的保本ROI' },
  { label: `利润(成本)`, prop: 'profitAmount', width: '120', tooltip: '利润 = (客单价 - 盈亏成本) * 成交订单数 - 消耗' },
  { label: `利润(ROI)`, prop: 'profitRoiAmount', width: '120', tooltip: '利润 = 成交总金额 - 消耗 * 盈亏ROI' },
  { label: `下单成本`, prop: 'orderCost', width: '120' },
  { label: `成交成本`, prop: 'transactionCost', width: '120' },
  { label: `媒体下单金额`, prop: 'orderAmountMedia', width: '130' },
  { label: `媒体ROI`, prop: 'mediaRoi', width: '120' },
  { label: `扣除回传后成本`, prop: 'deductTheReturnCost', width: '145' },
  { label: `扣除回传后订单数`, prop: 'orderCountDeduct', width: '150' },
  { label: `扣除回传金额后金额`, prop: 'amountAfterDeducting', width: '170' },
  { label: `扣除回传后ROI`, prop: 'deductRoi', width: '140' },
  { label: `扣除回传数`, prop: 'conversionDeduct', width: '120' },
  { label: `扣除回传金额`, prop: 'realOrderAmountDeduct', width: '130' },
  { label: `退单量`, prop: 'chargeBack', width: '120' },
  { label: `退款总金额`, prop: 'totalRefundAmount', width: '120' },
  { label: `退单率`, prop: 'chargeBackRate', width: '120', render: true },
  // { label: `用户昵称`, prop: 'nickName', width: '160', overflow: true },
  // { label: `负责人`, prop: 'createBy', width: '160' },
  { label: `用户信息`, prop: 'UserInfo', width: '200', labelWidth: 50,
    set: [
      { label: `昵称`, prop: 'nickName' },
      { label: `负责人`, prop: 'createBy' }
    ]
  },
  { label: `播放量`, prop: 'totalPlay', width: '100' },
  { label: `3秒播放数`, prop: 'playDuration3s', width: '110' },
  { label: `3秒播放率`, prop: 'playDuration3sRate', width: '110' },
  { label: `25%进度播放数`, prop: 'play25FeedBreak', width: '140' },
  { label: `25%进度播放率`, prop: 'play25FeedBreakRate', width: '140' },
  { label: `50%进度播放数`, prop: 'play50FeedBreak', width: '140' },
  { label: `50%进度播放率`, prop: 'play50FeedBreakRate', width: '140' },
  { label: `75%进度播放数`, prop: 'play75FeedBreak', width: '140' },
  { label: `75%进度播放率`, prop: 'play75FeedBreakRate', width: '140' },
  { label: `更新时间`, prop: 'updateTime', width: '100' }
]

const self = getCurrentInstance().proxy
const tableRef = ref(null)
const { columnsInstance, columns, operatedColumns, customList, handleHeaderDragend } = useColumns({ defaultColumns, tableRef, name: self.defaultMediaType && useRoute().name + '_plan' })

const queryCallback = useQueryCallback({
  params: ['advertiserId', 'beginTime', 'endTime'],
  callback: (query) => {
    self.queryParams.advertiserIds = query.advertiserId
    self.dateRange = [
      dayjs(query.beginTime).format('YYYY-MM-DD'),
      dayjs(query.endTime).format('YYYY-MM-DD')
    ]
    self.getList()
  }
})

const deptOptions = useDeptOptions(() => {
  setDefaultIds(self)
  queryCallback(self.getList)
})
useStoreTableScroller(tableRef)

// 列表选择
const selections = ref([])
const multiple = ref(true)
const handleSelectionChange = (selection) => {
  selections.value = selection
  multiple.value = !selection.length
}
// 二维码
const codeVisible = ref(false)
const codeUrl = ref('')
const currentRow = ref({})
const showCode = (row) => {
  currentRow.value = row
  queryPreviewCode(row.id).then(response => {
    if (response.code === 200) {
      codeUrl.value = response.data.previewUrl
      codeVisible.value = true
    }
  })
}

const { showTooltip } = useTooltipVisible()

// 回传
const { conversionType,
  conversionForm,
  conversionDialogVisible,
  conversionFormType,
  showConversion,
  batchUpdateConversion,
  handleConversionSave
} = useConversion({
  type: 'media',
  onSave: () => self.getList()
})

// 扣后补回传
const {
  autoConversionForm,
  autoConversionDialogVisible,
  autoConversionType,
  showAutoConversion,
  batchUpdateAutoConversion,
  handleAutoConversionSave
} = useAutoConversion({
  type: 'plan',
  onSave: () => self.getList()
})

// 多多自研回传
const {
  monitorAutoConversionForm,
  monitorAutoConversionDialogVisible,
  monitorAutoConversionType,
  showMonitorAutoConversion,
  batchUpdateMonitorAutoConversion,
  handleMonitorAutoConversionSave
} = useMonitorAutoConversion({
  type: 'plan',
  onSave: () => self.getList()
})

// 备注
const editRemark = (row) => {
  MessageBox.prompt('请输入备注', '提示', {
    inputValue: row.remark,
    closeOnClickModal: false
  }).then(({ value }) => {
    updateRemark({
      advertiserId: row.advertiserId,
      planId: row.planId,
      remark: value
    }).then((res) => {
      self.getList()
      Message.success('更新成功')
    })
  }).catch(() => {})
}
// 判断数组中的媒体类型是否为巨量、快手、腾讯
function isMediaType() {
  const mediaTypeList = [1, 2, 4]
  let state = false
  selections.value.forEach(item => {
    if (!mediaTypeList.includes(+item.mediaType)) {
      state = true
    }
  })
  return state
}
function handleOpen() {
  if (isMediaType()) {
    self.$modal.msgError('只能选择媒体类型为抖音、快手、腾讯的计划')
    return
  }
  self.$confirm('确定要开启选中的项目吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const newArr = []
    selections.value.forEach(item => {
      const existingItem = newArr.find(list => list.advertiserId === item.advertiserId)
      if (existingItem) {
        existingItem.ids.push(item.planId)
      } else {
        const list = {
          advertiserId: item.advertiserId,
          mediaType: item.mediaType,
          statusType: 0,
          type: +item.mediaType === 1 ? 3 : 2,
          ids: [item.planId]
        }
        newArr.push(list)
      }
    })
    updateStatus(newArr).then(response => {
      self.$modal.msgSuccess('开启成功')
      self.getList()
    })
  }).catch(() => {})
}
function handleSuspend() {
  if (isMediaType()) {
    self.$modal.msgError('只能选择媒体类型为抖音、快手、腾讯的计划')
    return
  }
  self.$confirm('确定要暂停选中的项目吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const newArr = []
    selections.value.forEach(item => {
      const existingItem = newArr.find(list => list.advertiserId === item.advertiserId)
      if (existingItem) {
        existingItem.ids.push(item.planId)
      } else {
        const list = {
          advertiserId: item.advertiserId,
          mediaType: item.mediaType,
          statusType: 1,
          type: +item.mediaType === 1 ? 3 : 2,
          ids: [item.planId]
        }
        newArr.push(list)
      }
    })
    updateStatus(newArr).then(response => {
      self.$modal.msgSuccess('暂停成功')
      self.getList()
    })
  }).catch(() => {})
}

// 跳转订单
const orderPathMap = {
  1: '/order/order',
  2: '/order/tbOrder',
  3: '/order/jdOrder',
  10: '/order/tbProOrder',
  11: '/order/jdProOrder',
  8: '/order/health',
  9: '/order/udsOrder',
  6: '/order/mtOrder'
}
const router = useRouter()
const toOrder = (row) => {
  router.push({
    path: orderPathMap[row.goodsPlatform],
    query: {
      advertiserId: row.advertiserId,
      planId: row.planId,
      beginTime: self.dateRange[0],
      endTime: self.dateRange[1]
    }
  })
}

// 批量修改出价
const batchBidVisible = ref(false)
const submitLoading = ref(false)
const batchBidForm = ref({
  newBid: 0
})

// 显示批量修改出价对话框
const showBatchBidDialog = () => {
  batchBidVisible.value = true
}
// 批量修改出价
const handleBatchBid = async() => {
  if (!batchBidForm.value.newBid) {
    self.$message.error('请输入新出价')
    return
  }

  const selectItems = selections.value.filter(item => item.mediaType === 1)
  if (selectItems.length === 0) {
    self.$message.error('请选择媒体类型为抖音的账户')
    return
  }

  // 构造参数
  const advertiserIds = {}
  selectItems.forEach(item => {
    if (!advertiserIds[item.advertiserId]) {
      advertiserIds[item.advertiserId] = []
    }
    if (item.planId) {
      advertiserIds[item.advertiserId].push(item.planId)
    }
  })
  const params = {
    advertiserIds,
    mediaType: 1,
    newBid: batchBidForm.value.newBid
  }
  submitLoading.value = true
  try {
    const res = await batchUpdateBid(params)
    self.$message.success(res.msg || '批量修改出价成功')
    batchBidVisible.value = false
    self.getList()
  } catch (e) {
    // 错误处理
  } finally {
    submitLoading.value = false
  }
}
</script>
<style lang="scss" scoped>
.code-title {
  padding-bottom: 10px;margin-bottom:10px;border-bottom: 1px solid #bdbdbd
}
::v-deep .el-dialog__body {
  //padding-top: 0;
}
.conversion-footer {
  display: flex;
  justify-content: space-between;
}
.dialog-conversion-rate {
  display: flex;
  align-items: center;
  gap:10px;
  height: 40px;
}
.remark-item {
  position: relative;
  .remark-btn {
    position: absolute;
    right: 0;
    top: 2px;
    font-size: 18px;
    background: #f4f6f9;
    opacity: 0;
  }
  &:hover .remark-btn {
    opacity: 1;
    cursor: pointer;
  }
}
</style>
