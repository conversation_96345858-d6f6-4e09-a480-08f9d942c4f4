<template>
  <div class="plan-status">
    <div class="plan-first-status">
      <!-- 状态圆点 -->
      <div class="status-point" :style="{ backgroundColor: statusColorMap[plan.statusFirst] }" />
      <span class="status-text">{{ dictMap.ocean_promotion_status_first[plan.statusFirst] }}</span>
    </div>
    <div class="plan-second-status">
      <span v-if="plan.statusFirst === 'PROMOTION_STATUS_DISABLE' && plan.statusSecond.length === 1" class="status-text secondary">{{ dictMap.ocean_promotion_status_second[plan.statusSecond[0]] }}</span>
      <el-popover
        v-else-if="plan.statusFirst === 'PROMOTION_STATUS_DISABLE' && plan.statusSecond.length > 1"
        placement="top"
        width="300"
        trigger="hover"
      >
        <div class="plan-second-status-popover">
          <el-tag v-for="status in plan.statusSecond" :key="status" type="primary">{{ dictMap.ocean_promotion_status_second[status] }}</el-tag>
        </div>
        <span slot="reference" class="status-text secondary"> {{ dictMap.ocean_promotion_status_second[plan.statusSecond[0]] }}等{{ plan.statusSecond.length }}个 </span>
      </el-popover>
      <span v-else class="status-text secondary">--</span>
    </div>
  </div>
</template>
<script setup>
import useDicts from '@/hooks/useDicts'
const { dictMap } = useDicts(['ocean_promotion_status_first', 'ocean_promotion_status_second'])

defineProps({
  plan: {
    type: Object,
    default: () => ({})
  }
})

const statusColorMap = {
  PROMOTION_STATUS_ENABLE: '#67C23A',
  PROMOTION_STATUS_DISABLE: '#909399',
  PROMOTION_STATUS_FROZEN: '#E6A23C',
  PROMOTION_STATUS_DONE: '#409EFF',
  PROMOTION_STATUS_DELETED: '#F56C6C'
}
</script>
<style scoped lang="scss">

.plan-first-status {
  display: flex;
  align-items: center;
  gap: 5px;

  .status-point {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.9);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 4px;
      height: 4px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
    }
  }

  .status-text {
    font-weight: 500;
    font-size: 14px;
    color: #2c3e50;
  }
}

.plan-second-status {
  display: flex;
  align-items: center;
  padding-left: 10px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 14px;
    background: linear-gradient(to bottom, #e8eaed, #f0f1f4);
    border-radius: 2px;
  }

  .status-text {
    font-size: 12px;
    color: #5a6c7d;

    &.secondary {
      color: #8590a6;
      font-weight: 400;
    }
  }
}

.plan-second-status-popover {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
  }
.status-text {
  line-height: 1.4;
  transition: color 0.2s ease;
}

</style>
