<script setup>
import { computed, reactive, ref, watch } from 'vue'
import { getModificationRecord } from '@/api/statistics/plan'
import dayjs from 'dayjs'
const props = defineProps({
  visible: Boolean,
  form: {
    type: Object,
    default: () => ({})
  },
  dateRange: {
    type: Array,
    default: () => []
  }
})
const emit = defineEmits(['update:visible'])

const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})

const list = ref([])
const fetchList = () => {
  list.value = []

  const params = {
    advertiserId: props.form.advertiserId,
    startTime: dayjs(props.dateRange[0]).format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs(props.dateRange[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
  }

  if (props.form.mediaType === 1) {
    params.planId = props.form.planId
  }

  getModificationRecord(params).then(response => {
    list.value = response.data
    pageState.total = list.value.length
  })
}
// 手动分页
const pageState = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})
const showList = computed(() => {
  return list.value.slice((pageState.pageNum - 1) * pageState.pageSize, pageState.pageNum * pageState.pageSize)
})
const handlePageChange = (page) => {
  pageState.pageNum = page.page
  pageState.pageSize = page.limit
}

watch(() => props.visible, (val) => {
  if (val) {
    fetchList()
  }
})
</script>

<template>
  <el-dialog
    title="操作日志"
    :visible.sync="open"
    width="1200px"
    top="5vh"
    append-to-body
  >
    <div class="table-wrap">
      <el-table
        :data="showList"
        style="width: 100%"
        height="100%"
      >
        <el-table-column label="操作对象名称" align="left" prop="object_name" width="150" show-overflow-tooltip />
        <el-table-column label="操作对象类型" align="left" prop="object_type" width="250" show-overflow-tooltip />
        <el-table-column label="操作内容" align="left" prop="content_title" width="250" show-overflow-tooltip />
        <el-table-column label="操作前后内容" align="left" prop="content_log" show-overflow-tooltip />
        <el-table-column label="操作时间" align="left" prop="create_time" width="160" show-overflow-tooltip />
      </el-table>
    </div>

    <pagination
      v-show="pageState.total>0"
      :total="pageState.total"
      :page.sync="pageState.pageNum"
      :limit.sync="pageState.pageSize"
      @pagination="handlePageChange"
    />
  </el-dialog>
</template>

<style scoped lang="scss">
.table-wrap {
  height: calc(100vh - 350px);
}
</style>
