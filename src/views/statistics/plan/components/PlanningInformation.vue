<script setup>
import { computed, ref, watch } from 'vue'
import { selectPlanningInformation } from '@/api/statistics/plan'
import ClipboardButton from '@/components/ClipboardButton/index.vue'
const props = defineProps({
  visible: Boolean,
  form: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['update:visible'])

const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})

const planInfo = ref({})
const fetchPlan = () => {
  planInfo.value = {}

  const params = {
    advertiserId: props.form.advertiserId,
    planId: props.form.planId
  }

  selectPlanningInformation(params).then(response => {
    planInfo.value = response.data
  })
}

watch(() => props.visible, (val) => {
  if (val) {
    fetchPlan()
  }
})
</script>

<template>
  <el-dialog
    title="计划信息"
    :visible.sync="open"
    width="800px"
    append-to-body
  >
    <div class="info-wrap">
      <el-form v-if="planInfo">
        <el-form-item label="计划名称">
          {{ planInfo.planName }}
        </el-form-item>
        <el-form-item label="状态">
          {{ planInfo.statusName }}
        </el-form-item>
        <el-form-item label="直达链接">
          <div v-for="(item, index) in planInfo.openUrl" :key="index" class="flex" style="border-bottom: 1px solid #f0f0f0;">
            <div class="overflow-text flex1" :title="item">{{ item }}</div>
            <ClipboardButton :value="item" />
          </div>
        </el-form-item>
        <el-form-item label="落地页链接">
          <div class="flex">
            <div class="overflow-text flex1" :title="planInfo.landingPageUrl">{{ planInfo.landingPageUrl }}</div>
            <ClipboardButton :value="planInfo.landingPageUrl" />
          </div>
        </el-form-item>
        <el-form-item label="监控链接">
          <div class="flex">
            <div class="overflow-text flex1" :title="planInfo.supervisoryControlUrl">{{ planInfo.supervisoryControlUrl }}</div>
            <ClipboardButton :value="planInfo.supervisoryControlUrl" />
          </div>
        </el-form-item>
      </el-form>
      <div v-else>无数据</div>
    </div>

  </el-dialog>
</template>

<style scoped lang="scss">
.info-wrap {
  padding: 0 20px;
}
</style>
