<script>
export default {
  dicts: ['media_type']
}

</script>
<script setup>
import { computed, onMounted, ref, getCurrentInstance } from 'vue'
import {
  goreport
} from '@/api/statistics/plan'
const { proxy } = getCurrentInstance()
const props = defineProps({
  visible: Boolean,
  form: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['update:visible', 'refresh'])
const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入每天智能回传数量'))
  } else {
    if (value < 1 || value > 10) {
      callback(new Error('每天智能回传数量范围为1~10'))
    }
    callback()
  }
}
const dataForm = ref({
  num: '',
  open: true
})
const rules = {
  num: [
    { required: true, validator: validatePass, trigger: 'blur' }
  ],
  open: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}
const open = computed({
  get: () => props.visible,
  set: () => emit('update:visible', false)
})

function mediaTypeFilter(val) {
  const list = proxy.dict.type.media_type.find(item => item.value == val)

  return list ? list.label : null
}
function onInput(key, event) {
  // 限制输入首位为数值
  if (event.match(/^\d+/)) {
    dataForm.value[key] = event.replace(/\D/g, '')
  } else {
    dataForm.value[key] = ''
  }
}
const loading = ref(false)
const handleSubmit = () => {
  loading.value = true
  const data = {
    planId: props.form.planId,
    mediaType: props.form.mediaType,
    advertiserId: props.form.advertiserId,
    num: dataForm.value.num,
    open: dataForm.value.open
  }
  goreport(data).then((res) => {
    open.value = false
    emit('refresh')
  }).finally(() => {
    loading.value = false
  })
}
onMounted(() => {
  if (props.form && props.form.intelligentStartingQuantity) {
    dataForm.value.num = props.form.intelligentStartingQuantity.num
    dataForm.value.open = props.form.intelligentStartingQuantity.open
  }
})
</script>

<template>
  <el-dialog
    title="智能提量"
    :visible.sync="open"
    width="700px"
    top="10vh"
    append-to-body
  >

    <el-form ref="ruleForm" :model="dataForm" :rules="rules" label-width="140px" class="demo-ruleForm">
      <div class="presentation_info">
        <div class="list">
          <div>媒体计划ID：</div>
          <div>
            {{ form&&form.planId }}
          </div>
        </div>
        <div class="list">
          <div>渠道ID：</div>
          <div v-if="form&&form.mediaType">
            {{ mediaTypeFilter(form.mediaType) }}
          </div>
        </div>
        <div class="list">
          <div>账户ID：</div>
          <div>
            {{ form&&form.advertiserId }}
          </div>
        </div>
      </div>
      <el-form-item label="每天智能回传数量:" prop="num">
        <el-input v-model="dataForm.num" @input="onInput('num', $event)" />
      </el-form-item>
      <el-form-item label="状态:" prop="open">
        <el-radio-group v-model="dataForm.open">
          <el-radio :label="true">开启</el-radio>
          <el-radio :label="false">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="footer">
        <el-button v-loading="loading" type="primary" @click="handleSubmit">确定</el-button>
        <el-button @click="open=false">取消</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<style scoped lang="scss">
.presentation_info {
  padding: 16px 16px 12px 16px;
  margin-bottom: 12px;
  background: #FAFAFA;
  .list {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    >div{
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: 14px;
      >:first-child{
        color: rgba(0,0,0,0.55);

      }
      >:last-child{
        color: rgba(0,0,0,0.75);
      }
    }
  }
}
.footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px 0 0;
}
</style>
