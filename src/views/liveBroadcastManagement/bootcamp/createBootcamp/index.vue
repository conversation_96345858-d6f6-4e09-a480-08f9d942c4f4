<template>
  <div class="w-100">
    <div style="width: 80%">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- 基础配置 -->
        <div class="titleBox">基础配置</div>
        <el-form-item label="营期名称" prop="periodName" required>
          <el-input
            v-model="form.periodName"
            placeholder="请输入营期名称"
            style="width: 600px"
            :maxlength="32"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="营期简介" prop="description" required>
          <el-input
            v-model="form.description"
            placeholder="请输入营期简介"
            type="textarea"
            rows="4"
            style="width: 600px"
            :maxlength="256"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="封面" prop="imageIdStr" required>
          <div class="flex">
            <el-image v-if="form.imageIdStr" :src="chooeseImg" style="width: 318px; height: 200px" fit="contain" :preview-src-list="[chooeseImg]" />
            <el-image v-else :src="posterImg" style="width: 318px; height: 200px" fit="contain" />
            <div class="ml-10 mt-20">
              <el-button
                class="addBtn"
                type="primary"
                @click="handleChooeseImg"
              >
                添加图片
              </el-button>
              <div class="tips">
                <div>*（建议按照下方要求添加图片）</div>
                <div>图片尺寸：“750px*420px”</div>
                <div>图片比例：16:9</div>
                <div>图片格式：png、jpg、jpeg格式</div>
                <div>图片大小：小5M</div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="营期详情" prop="detailDescription" required>
          <editor
            v-model="form.detailDescription"
            :min-height="200"
            style="width: 600px"
          />
        </el-form-item>
        <el-form-item label="标签">
          <div>
            <div class="tagsBox">
              <el-tag v-for="(item,index) in form.listTag" :key="index" type="success" :closable="true" @close="tagDel(item)" class="tags">{{ item.tagName }}</el-tag>
            </div>
            <el-button icon="el-icon-plus" type="primary" @click="handleChooeseTags">请选择标签</el-button>
            <div class="tips">* 根据使用场景做标签记录，扫码添加的客户，可自动打上标签</div>
          </div>
        </el-form-item>
        <div class="titleBox">开课设置</div>
        <el-form-item label="招生时间" prop="rangeTime1" required>
          <el-date-picker
            v-model="form.rangeTime1"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择招生开始时间"
            style="width: 440px"
            start-placeholder="选择招生开始时间"
            end-placeholder="选择招生结束时间"
            :picker-options="pickerOptions"
            @input="changeTime1"
          />
        </el-form-item>
        <el-form-item label="开课时间" prop="curriculumTimeType" required>
          <el-radio-group v-model="form.curriculumTimeType" @change="changeCurriculumTimeType">
            <el-radio :label="0">期限</el-radio>
            <el-radio :label="1">永久</el-radio>
          </el-radio-group>
          <div v-if="form.curriculumTimeType == 0" class="flexBox">
            <el-date-picker
              v-model="form.rangeTime2"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 440px"
              start-placeholder="选择开课开始时间"
              end-placeholder="选择开课结束时间"
              :picker-options="pickerOptions"
              @input="changeTime2"
            />
          </div>
          <div v-else>
            <el-date-picker
              v-model="form.curriculumStartTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择开课开始时间"
              style="width: 240px"
              :picker-options="pickerOptions"
            />
          </div>
        </el-form-item>
        <el-form-item label="课程目录模式" prop="courseMode" required>
          <el-radio-group v-model="form.courseMode">
            <el-radio :label="0">课节模式</el-radio>
            <el-radio :label="1">章节模式</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="titleBox">售卖设置</div>
        <el-form-item label="上架设置" prop="publishStatus" required>
          <el-radio-group v-model="form.publishStatus">
            <el-radio v-for="(item,index) in dict.type.live_publish_status" :key="index" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
          <div class="tipsword">* 仅上架中的营期才允许学员购买。但站点投放中关联的训练营营期不受是否上架影响。</div>
        </el-form-item>
      </el-form>
      <div style="margin-top: 50px;margin-left:150px;">
        <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </div>

    <!-- 选择图片 -->
    <chooeseImage :visible.sync="chooeseImageShow" :is-create-template="false" @chooeseImageSuccess="chooeseSuccess" />

    <!-- 选择标签 -->
    <chooeseTags :visible.sync="chooeseTagsShow" :default-chooese="form.listTag" @chooeseTagsSuccess="chooeseTagsSuccess" />

  </div>
</template>

<script>
import { getDetailInfo, getImageInfo } from '@/api/liveBroadcastManagement/messageTemplate'
import { addBootcamp, editeBootcamp, copyBootcamp, getBootcampDetail } from '@/api/liveBroadcastManagement/bootcamp'
import chooeseImage from '@/views/liveBroadcastManagement/components/chooeseImage.vue'
import chooeseTags from '@/views/liveBroadcastManagement/components/chooeseTags.vue'
import posterImg from '@/assets/images/bootcampChooese.png'
import { throttle } from '@/utils'
export default {
  name: 'CreateBootcamp',
  dicts: ['live_publish_status'],
  components: { chooeseImage, chooeseTags },
  data() {
    return {
      chooeseImageShow: false, // 选择图片弹窗
      chooeseImg: '', // 选择的图片
      posterImg,
      total: 0,
      dataList: [],
      // 表单参数
      form: {
        rangeTime1: [],
        rangeTime2: [],
        campIdStr: '', // 训练营分组ID
        periodName: '', // 营期名称
        description: '', // 营期简介
        imageIdStr: '', // 封面(背景图id)
        detailDescription: '', // 营期详情（详情介绍）
        recruitStartTime: '', // 招生开始时间
        recruitEndTime: '', // 招生结束时间
        curriculumTimeType: 0, // 开课时间类型 0——期限，1——永久
        curriculumStartTime: '', // 开课开始时间
        curriculumEndTime: '', // 开课结束时间
        courseMode: 0, // 课程目录模式 0——课节，1——章节
        publishStatus: '0', // 上架状态[0 下架，1 稍后上架，2 上架 ]  关联数据字典 live_publish_status  0 下架，1 稍后上架，2 上架
        campPeriodStatus: 0, // 关联数据字典 live_camp_period_status 0 未开始，1 招生中，2 等待开营，3 开营中，4 已结束
        listTag: []// 标签
      },
      // 表单校验
      rules: {
        periodName: [
          { required: true, message: '营期名称不能为空', trigger: ['blur', 'change'] }
        ],
        description: [
          { required: true, message: '营期简介不能为空', trigger: ['blur', 'change'] }
        ],
        imageIdStr: [
          { required: true, message: '请选择直播封面', trigger: ['blur', 'change'] }
        ],
        detailDescription: [
          { required: true, message: '营期详情不能为空', trigger: ['blur', 'change'] }
        ],
        curriculumTimeType: [
          { required: true, message: '请选择开课时间类型', trigger: 'change' }
        ],
        curriculumStartTime: [
          { required: true, message: '请选择开课开始时间', trigger: ['blur', 'change'] }
        ],
        curriculumEndTime: [
          { required: true, message: '请选择开课结束时间', trigger: ['blur', 'change'] }
        ],
        courseMode: [
          { required: true, message: '请选择课程目录模式', trigger: 'change' }
        ],
        rangeTime1: [
          { required: true, message: '请选择招生时间', trigger: ['blur', 'change'] }
        ],
        curriculumTime: [
          { required: true, message: '请选择招生时间', trigger: ['blur', 'change'] }
        ],
        publishStatus: [
          { required: true, message: '请选择上架设置', trigger: 'change' }
        ]
      },
      pickerOptions: {
        disabledDate(time) {
          const todayStart = new Date()
          todayStart.setHours(0, 0, 0, 0)// 今天零点
          // 禁用今天零点之前的日期
          return time.getTime() < todayStart.getTime()
        }
      },
      submitLoading: false,

      // 选择标签
      chooeseTagsShow: false
    }
  },
  beforeRouteEnter(to, from, next) {
    // 组件实例还没创建，用 next(callback) 访问实例
    next(vm => {
      vm.initData(to)
    })
  },
  beforeRouteUpdate(to, from, next) {
    // 路由复用组件时触发
    this.initData(to)
    next()
  },
  mounted(){
    // 浏览器控制按钮前进后退触发函数
    if (window.history && window.history.pushState) {
      history.pushState(null, null, document.URL)
      window.addEventListener('popstate', this.popstate, false)
    }
  },
  destroyed(){
    window.removeEventListener('popstate', this.popstate, false);
  },
  methods: {
    popstate(){
      this.$tab.closeOpenPage({ path: '/bootcamp/createBootcamp' })
      this.$router.push('/bootcamp/index')
    },
    initData(route) {
      this.form = {
        rangeTime1: [],
        rangeTime2: [],
        campIdStr: '', // 训练营分组ID
        periodName: '', // 营期名称
        description: '', // 营期简介
        imageIdStr: '', // 封面(背景图id)
        detailDescription: '', // 营期详情（详情介绍）
        recruitStartTime: '', // 招生开始时间
        recruitEndTime: '', // 招生结束时间
        curriculumTimeType: 0, // 开课时间类型 0——期限，1——永久
        curriculumStartTime: '', // 开课开始时间
        curriculumEndTime: '', // 开课结束时间
        courseMode: 0, // 课程目录模式 0——课节，1——章节
        publishStatus: '0', // 上架状态[0 下架，1 稍后上架，2 上架 ]  关联数据字典 live_publish_status  0 下架，1 稍后上架，2 上架
        campPeriodStatus: 0, // 关联数据字典 live_camp_period_status 0 未开始，1 招生中，2 等待开营，3 开营中，4 已结束
        listTag: []// 标签
      }
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
      this.form.campIdStr = localStorage.getItem('bootcampIdStr')
      // 如果有 id 参数，可以请求详情
      if (route.query?.id) {
        this.loadDetailInfo(this.$route.query?.id)
      }
    },
    // 删除标签
    tagDel(data){
      if(this.form.listTag[0].id){
        let arr = JSON.parse(JSON.stringify(this.form.listTag))
        this.form.listTag = arr.filter(item => item.id != data.id)
      }else{
        let arr = JSON.parse(JSON.stringify(this.form.listTag))
        this.form.listTag = arr.filter(item => item.tagId != data.tagId)
      }
    },
    // 选择标签
    handleChooeseTags() {
      this.chooeseTagsShow = true
    },
    // 招生时间
    changeTime1() {
      if (this.form.rangeTime1 && this.form.rangeTime1.length == 2) {
        this.form.recruitStartTime = this.form.rangeTime1[0]
        this.form.recruitEndTime = this.form.rangeTime1[1]
      } else {
        this.form.recruitStartTime = ''
        this.form.recruitEndTime = ''
      }
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    // 开课期限时间
    changeTime2() {
      if (this.form.rangeTime2 && this.form.rangeTime2.length == 2) {
        this.form.curriculumStartTime = this.form.rangeTime2[0]
        this.form.curriculumEndTime = this.form.rangeTime2[1]
      } else {
        this.form.curriculumStartTime = ''
        this.form.curriculumEndTime = ''
      }
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    // 加载编辑直播间详情
    loadDetailInfo(id) {
      getBootcampDetail(id).then(res => {
        if (res.code == 200) {
          this.form = res.data
          this.form.rangeTime1 = [this.form.recruitStartTime, this.form.recruitEndTime]
          this.form.rangeTime2 = [this.form.curriculumStartTime, this.form.curriculumEndTime]
          this.form.curriculumTimeType = Number(this.form.curriculumTimeType)
          this.form.courseMode = Number(this.form.courseMode)
          this.form.imageIdStr = this.form.imageId
          this.form.idStr = this.form.id
          this.form.campIdStr = this.form.campId
          // 封面图片回显
          this.loadImageInfo()
        }
      })
    },
    // 加载图片回显
    loadImageInfo() {
      getImageInfo(this.form.imageId).then(res => {
        this.chooeseImg = res.data.url
      })
    },
    // 切换开课时间类型
    changeCurriculumTimeType() {
      if (this.form.curriculumTimeType == 1) {
        this.form.curriculumStartTime = null
        this.form.curriculumEndTime = null
      } else if (this.form.curriculumTimeType == 0) {
        this.form.curriculumStartTime = null
        this.form.curriculumEndTime = null
      }
    },
    // 选择图片
    handleChooeseImg() {
      this.chooeseImageShow = true
    },
    // 选择图片回调
    chooeseSuccess(data) {
      this.form.imageIdStr = data.id
      this.chooeseImg = data.url
    },
    // 选择标签
    chooeseTagsSuccess(data) {
      const arr = []
      data.forEach(item => {
        const obj = {
          tagId: item.tagId,
          tagName: item.tagName
        }
        arr.push(obj)
      })
      this.form.listTag = arr
    },
    // 取消按钮
    cancel() {
      this.$tab.closeOpenPage({ path: '/bootcamp/createBootcamp' })
      this.$router.push('/bootcamp/index')
    },
    /** 提交按钮 */
    submitForm: throttle(function() {
      const that = this
      that.$refs['form'].validate((valid) => {
        if (valid) {
          // 先判断是否为空
          if (that.form.curriculumTimeType == 0 && !that.form.curriculumStartTime) {
            that.$modal.msgWarning('开课开始时间不能为空！')
            return
          }
          if (that.form.curriculumTimeType == 0 && !that.form.curriculumEndTime) {
            that.$modal.msgWarning('开课结束时间不能为空！')
            return
          }
          if (that.form.curriculumTimeType == 1 && !that.form.curriculumStartTime) {
            that.$modal.msgWarning('开课结束时间不能为空！')
            return
          }
          // 验证时间都不能小于当前时间
          const recruitStartTime = that.isValidTime(that.form.recruitStartTime)
          const recruitEndTime = that.isValidTime(that.form.recruitEndTime)
          const curriculumStartTime = that.isValidTime(that.form.curriculumStartTime)
          let curriculumEndTime
          if (that.form.curriculumTimeType == 0) {
            curriculumEndTime = that.isValidTime(that.form.curriculumEndTime)
          }
          
          if(!recruitStartTime){
            that.$modal.msgWarning("招生开始时间不能小于当前时间！");
            return
          }
          if (!recruitEndTime) {
            that.$modal.msgWarning('招生结束时间不能小于当前时间！')
            return
          }
          if (!curriculumStartTime) {
            that.$modal.msgWarning('开课开始时间不能小于当前时间！')
            return
          }
          if (that.form.curriculumTimeType == 0 && !curriculumEndTime) {
            that.$modal.msgWarning('开课结束时间不能小于当前时间！')
            return
          }
          // 开课开始时间不能小于招生结束时间
          const boolRange = that.isStartBeforeEnd(that.form.recruitEndTime, that.form.curriculumStartTime)
          if (!boolRange) {
            that.$modal.msgWarning('开课开始时间不能小于招生结束时间！')
            return
          }

          that.submitLoading = true
          if (that.form.id && that.$route.query?.type != 'copy') {
            // console.log(JSON.parse(JSON.stringify(that.form)), "编辑===form🔥🔥🔥🔥🔥🔥🔥")
            editeBootcamp(that.form).then((res) => {
              if (res.code == 200) {
                that.$modal.msgSuccess('修改成功')
                that.$router.push('/bootcamp/index')
              }
              that.submitLoading = false
            }).catch(() => {
              that.submitLoading = false
            })
          } else if (that.form.id && that.$route.query?.type == 'copy') {
            // console.log(JSON.parse(JSON.stringify(that.form)), "复制===form🔥🔥🔥🔥🔥🔥🔥")
            copyBootcamp(that.form).then((res) => {
              if (res.code == 200) {
                that.$modal.msgSuccess('复制成功')
                that.$tab.closeOpenPage({ path: '/bootcamp/createBootcamp' })
                that.$router.push('/bootcamp/index')
              }
              that.submitLoading = false
            }).catch(() => {
              that.submitLoading = false
            })
          } else {
            // console.log(JSON.parse(JSON.stringify(that.form)), "新增===form🔥🔥🔥🔥🔥🔥🔥")
            addBootcamp(that.form).then((res) => {
              if (res.code == 200) {
                that.$modal.msgSuccess('新增成功')
              }
              that.submitLoading = false
              that.$tab.closeOpenPage({ path: '/bootcamp/createBootcamp' })
              that.$router.push('/bootcamp/index')
            }).catch(() => {
              that.submitLoading = false
            })
          }
        }
      })
    }, 1500),
    isStartBeforeEnd(startTime, endTime) {
      // 将字符串转为 Date 对象
      const start = new Date(startTime.replace(/-/g, '/')) // 兼容 Safari
      const end = new Date(endTime.replace(/-/g, '/'))
      // 判断大小
      return start <= end // true 表示合法，false 表示 startTime > endTime
    },
    isValidTime(targetTime) {
      // 转换成 Date 对象，兼容 Safari
      const time = new Date(targetTime.replace(/-/g, '/'))
      const now = new Date()
      // 当前时间
      const minAllowedTime = new Date(now.getTime())
      // 返回 true 表示合法，false 表示不合法
      return time >= minAllowedTime
    }
  }
}
</script>
<style lang="scss" scoped>
.tagsBox{
  max-width: 600px;
  display: flex;
  flex-wrap: wrap;
}
.tags{
  margin-right: 5px;
  margin-bottom: 5px;
  cursor: pointer;
}
.flexBox{
  display: flex;
  margin-top: 5px;
}
.tipsword{
  font-size: 12px;
  color: gray;
}
.tips {
  font-size: 12px;
  color: gray;
  line-height: 20px;
  margin-top: 10px;
}
.titleBox {
  border-left: 3px solid #007aff;
  text-indent: 10px;
  font-weight: bold;
  margin-bottom: 20px;
}
::v-deep .el-radio-group .el-radio{
  margin-bottom: 0px !important;
}
.mt-20{
  margin-top: 30px;
}
</style>

