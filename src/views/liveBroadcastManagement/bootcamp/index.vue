<template>
  <div class="containerBox">
    <div class="flex align-center justify-between">
      <div class="flex align-center">
        <i class="el-icon-data-line" style="font-size: 50px" />
        <div class="ml-5">
          <p style="font-weight: bold;">训练营</p>
          <p>训练营是一种注重课程服务、学员学习效果的线上轻教学模式。通过课程、作业打卡、学习社群的组合方式，能够提高教学质量与口碑，对学员进行精细化运营，提升流量转化效果</p>
        </div>
      </div>
      <div>
        <el-button type="primary" @click="waiteDevelop">违规通知</el-button>
      </div>
    </div>

    <el-tag class="tips" type="success">请仔细核查课程内容，遵守相关规定，禁止虚假宣传、夸大功效、诱导消费、网络水军等非法营销行为。关于规范私域直播秩序的公告</el-tag>

    <div class="boxContent">
      <div class="menuBox">
        <div class="categoryBox">
          <div>
            <el-input
              v-model="queryBootCampParams.campName"
              placeholder="请输入训练营名称"
            >
              <el-button slot="append" icon="el-icon-search" @click="getDataList" />
            </el-input>
          </div>
          <el-button class="addBtn" plain icon="el-icon-plus" @click="handleAddBootcamp">
            新建训练营
          </el-button>
          <div>
            <div
              v-for="(item, index) in bootcampList"
              :key="index"
              class="boxItem"
              :class="activeIndex === index ? 'boxItemActive' : ''"
              @click="groupClick(item, index)"
            >
              <template>
                <el-popover
                  v-if="item.campName.length > 11"
                  placement="left-start"
                  title=""
                  width="200"
                  trigger="hover"
                  :content="item.campName"
                >
                  <div slot="reference" class="textOne">{{ item.campName }}</div>
                </el-popover>
                <div v-else class="textOne">{{ item.campName }}</div>
              </template>
              <span v-if="item.periodCount > 0">{{ item.periodCount }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="content">
        <div v-if="activeBootcampData.campName" class="campBox">
          <div>{{ activeBootcampData.campName }}</div>
          <div class="opration">
            <el-button type="text" icon="el-icon-edit" style="margin-right: 20px" @click="handleEdite(activeBootcampData)">编辑</el-button>
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-button
                  type="text"
                  icon="el-icon-more"
                >更多</el-button>
                <i class="el-icon-arrow-down el-icon--right" style="color:#409efe;" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button type="text" style="width:100%" @click="toBootcampStudents(activeBootcampData,'bootcamp')">训练营学员</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button type="text" style="width:100%" @click="toBootcampStudents(activeBootcampData,'mineStudents')">我的学员</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button type="text" style="width:100%" @click="handleDelete(activeBootcampData)">删除</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div class="queryBox">
          <el-form ref="queryForm" class="queryBoxContent" :model="queryParams" :inline="true">
            <div>
              <el-form-item>
                <el-button v-if="activeBootcampData.id" type="primary" icon="el-icon-plus" @click="handleAdd">新增营期</el-button>
                <el-button v-else type="info" icon="el-icon-plus">新增营期</el-button>
              </el-form-item>
            </div>
            <div>
              <el-form-item>
                <el-select
                  v-model="queryParams.publishStatus"
                  placeholder="请选择上架状态"
                  style="width:160px"
                  clearable
                >
                  <el-option
                    v-for="dict in dict.type.live_publish_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                    {{ dict.label }}
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-select
                  v-model="queryParams.campPeriodStatus"
                  placeholder="请选择营期状态"
                  style="width:160px"
                  clearable
                >
                  <el-option
                    v-for="dict in dict.type.live_camp_period_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                    {{ dict.label }}
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.periodName"
                  placeholder="请输入营期名称"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </div>
          </el-form>
        </div>
    
        <el-table ref="elTable" v-loading="loading" :data="dataList" height="calc(100% - 190px)">
          <el-table-column
            label="期数"
            align="center"
            prop="sort"
            width="100"
          />
          <el-table-column
            label="营期名称"
            align="center"
            prop="periodName"
            width="180px"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="营期状态" align="center" prop="campPeriodStatus" width="140px">
            <template slot-scope="scope">
              <dict-tag type="primary" :options="dict.type.live_camp_period_status" :value="Number(scope.row.campPeriodStatus)" />
            </template>
          </el-table-column>
          <el-table-column label="招生时间" align="center" prop="recruitStartTime" width="180px">
            <template slot-scope="scope">
              <div>{{ scope.row.recruitStartTime }} ~ {{ scope.row.recruitEndTime }}</div>
            </template>
          </el-table-column>
          <el-table-column label="开营时间" align="center" prop="curriculumStartTime" width="170px">
            <template slot-scope="scope">
              <div>{{ scope.row.curriculumStartTime }} ~ {{ scope.row.curriculumEndTime ? scope.row.curriculumEndTime :'永久' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="创建人" align="center" prop="createBy" width="170px" />
          <el-table-column label="创建时间" align="center" prop="createTime" width="180px" />
          <el-table-column label="上架状态" align="center" prop="publishStatus" width="120px">
            <template slot-scope="scope">
              <dict-tag type="primary" :options="dict.type.live_publish_status" :value="Number(scope.row.publishStatus)" />
            </template>
          </el-table-column>
          <el-table-column label="上下架时间" align="center" prop="publishTime" width="160px">
            <template slot-scope="scope">
              <div v-if="scope.row.publishStatus == 2">{{ scope.row.publishTime }}</div>
              <div v-if="scope.row.publishStatus == 0">{{ scope.row.soldOutTime }}</div>
            </template>
          </el-table-column>
          <el-table-column label="更新人" align="center" prop="updateBy" width="170px" />
          <el-table-column label="更新时间" align="center" prop="updateTime" width="180px" />
          <el-table-column label="操作" align="center" width="240px" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" @click="shareFunc(scope.row)">分享给学员</el-button>
              <el-button type="text" @click="toDetailPage(scope.row)">营期详情</el-button>
              <el-dropdown>
                <span class="el-dropdown-link">
                  <el-button
                    type="text"
                  >更多</el-button>
                  <i class="el-icon-arrow-down el-icon--right" style="color:#409efe;" />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>
                    <el-button type="text" style="width:100%" @click="copyBootcamp(scope.row)">复制营期</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text" style="width:100%" @click="editeBootcamp(scope.row)">编辑营期</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text" style="width:100%" @click="deleteFunc(scope.row)">删除</el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 添加或修改训练营 -->
    <el-dialog :title="title" :visible.sync="openBootcamp" width="650px" append-to-body>
      <el-form
        ref="bootCampform"
        :model="addQueryParams"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="训练营名称" prop="campName">
          <el-input
            v-model="addQueryParams.campName"
            placeholder="请输入训练营名称"
            :maxlength="32"
          />
        </el-form-item>
        <el-form-item label="完课设置" prop="lightVideoType">
          <el-radio-group v-model="addQueryParams.lightVideoType">
            <el-radio :label="0">按固定时长</el-radio>
            <el-radio :label="1">按时长比例</el-radio>
          </el-radio-group>
          <div class="flexBox" v-if="addQueryParams.lightVideoType == 0 || addQueryParams.lightVideoType == 1 ">
            <div>当学员观看直播或回放超过</div>
            <el-input class="inputBox" v-if="addQueryParams.lightVideoType == 0" type="text" @input="(v)=>(addQueryParams.lightVideoFixedDuration=v.replace(/[^\d.]/g,''))" v-model="addQueryParams.lightVideoFixedDuration" />
            <el-input class="inputBox" v-if="addQueryParams.lightVideoType == 1" type="text" @input="(v)=>(addQueryParams.lightVideoDurationRatio=v.replace(/[^\d.]/g,''))" v-model="addQueryParams.lightVideoDurationRatio" />
            <div v-if="addQueryParams.lightVideoType == 0">分钟视为完课</div>
            <div v-if="addQueryParams.lightVideoType == 1">%视为完课</div>
          </div>
          <div class="tipsWord" v-if="addQueryParams.lightVideoType == 0">温馨提示: 时长输入范围为 1~1440 可输入小数 最多两位小数</div>
          <div class="tipsWord" v-if="addQueryParams.lightVideoType == 1">温馨提示: 时长比例输入范围为 1~100 可输入小数 最多两位小数</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAddForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 分享链接 -->
    <share-link :data-share="dataShare" :visible.sync="showShareLink" />
  </div>
</template>

<script>
import { addBootcampInfo, deleteBootcampInfo, editeBootcampInfo, queryBootcampList } from '@/api/liveBroadcastManagement/bootcamp.js'
import { courseListData, deleteLive, upOrDownLive } from '@/api/liveBroadcastManagement/messageTemplate'
import { getBootcampList, getBootcampListData, deleteBootcamp } from '@/api/liveBroadcastManagement/bootcamp'
import { listConfig } from '@/api/system/config'
import { bus } from '@/utils/bus.js'
import ShareLink from '../components/shareLink.vue'
export default {
  name: 'Bootcamplist',
  dicts: ['live_publish_status', 'live_camp_period_status'],
  components: { ShareLink },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      dataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        periodName: '',
        publishStatus: '',
        campPeriodStatus: '',
        campIdStr: ''
      },
      // 分享数据
      dataShare: {},
      showShareLink: false,
      bootcampList: [],

      openBootcamp: false,
      // 查询参数
      queryBootCampParams: {
        campName: ''
      },
      addQueryParams: {
        campName: null,
        lightVideoType: null, // 完课类型[0 固定时长,1 时长比例]
        lightVideoFixedDuration: null, // 固定时长 单位：分钟，取值范围1~1440 可输入小数 最多两位小数
        lightVideoDurationRatio: null // 时长比例 取值范围1~100 可输入小数 最多两位小数
      },
      // 表单校验
      rules: {
        campName: [
          { required: true, message: '训练营名称不能为空', trigger: ['blur', 'change'] }
        ],
        lightVideoType: [
          { required: true, message: '请选择完课设置', trigger: ['blur', 'change'] }
        ]
      },
      isEdit: false,
      currentRow: null,
      title: '添加训练营',
      clickIndex: 1,
      activeBootcampData: {}, // 高亮数据
      activeIndex: 0
    }
  },
  created() {
    this.queryBootCampParams.campName = ''
    this.getDataList()
  },
  methods: {
    // 新增营期
    handleAdd() {
      localStorage.setItem('bootcampIdStr', this.activeBootcampData.id)
      this.$router.push({
        path: '/bootcamp/createBootcamp'
      })
    },
    // 营期详情
    toDetailPage(data) {
      this.$router.push({
        path: '/bootcamp/detail?id='+data.id+'&courseMode='+data.courseMode
      })
    },
    // 分享给学员
    shareFunc(data) {
      this.dataShare = data
      this.showShareLink = true
    },
    // 待开发
    waiteDevelop() {
      this.$modal.msgWarning('待开发!')
    },
    // 训练营学员 ---- 我的学员
    toBootcampStudents(data,str){
      localStorage.setItem('bootcampData',JSON.stringify(data))
      this.$router.push(`/bootcamp/bootcampStudents?type=${str}`)
    },
    // 复制营期
    copyBootcamp(data) {
      this.$router.push(`/bootcamp/createBootcamp?id=${data.id}&type=copy`)
    },
    // 删除营期
    deleteFunc(row) {
      this.$modal
        .confirm('当前删除营期名称为"' + row.periodName + '"，删除后，将无法恢复和正常使用，会影响到用户正常访问，且视频文件无法恢复或找回！')
        .then(function() {
          return deleteBootcamp([row.id])
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功!')
        })
        .catch(() => {})
    },
    // 编辑直播间
    editeBootcamp(data) {
      this.$router.push('/bootcamp/createBootcamp?id=' + data.id)
    },
    // 选择分类
    chooeseCategory(data) {
      this.queryParams.courseGroupId = data.id
      this.getList()
    },
    // 新建直播
    addLive() {
      this.$router.push('/courseManagement/createLiveRecord')
    },
    /** 列表 */
    getList() {
      this.loading = true
      getBootcampListData(this.queryParams).then(res => {
        this.dataList = res.data.rows
        this.total = res.data.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        periodName: '',
        publishStatus: '',
        campPeriodStatus: '',
        campIdStr: null
      }
      this.queryParams.campIdStr = localStorage.getItem('bootcampIdStr')
      this.handleQuery()
    },
    // ======================================================================================================================================================
    // ======================================================================================================================================================
    // ======================================================================================================================================================
    // ======================================================================================================================================================
    // 点击训练营
    groupClick(data, index) {
      this.activeIndex = index
      this.activeBootcampData = data
      this.queryParams.campIdStr = data.id
      this.getList()
    },
    /** 查询资源类别列表 */
    getDataList() {
      this.loading = true
      queryBootcampList(this.queryBootCampParams.campName).then((res) => {
        this.bootcampList = res.data || [];
        if(res.data.length){
          this.activeBootcampData = res.data[this.activeIndex]
          this.queryParams.campIdStr = res.data[this.activeIndex].id
          this.getList()
        } else {
          this.queryParams.campIdStr = ''
          this.activeBootcampData = {}
          this.dataList = []
        }    
        setTimeout(() => {
          if (this.$refs.elTable) {
            this.$refs.elTable.doLayout();
          }
        },1000);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.addQueryParams = {
        campName: null,
        lightVideoType: null,
        lightVideoFixedDuration: null,
        lightVideoDurationRatio: null
      }
      this.$refs.bootCampform.resetFields()
      this.openBootcamp = false
    },
    /** 新增训练营操作 */
    handleAddBootcamp() {
      this.title = '添加训练营'
      this.openBootcamp = true
    },
    /** 修改按钮操作 */
    handleEdite(row) {
      this.addQueryParams = JSON.parse(JSON.stringify(row))
      this.addQueryParams.lightVideoType = Number(this.addQueryParams.lightVideoType)
      this.addQueryParams.idStr = this.addQueryParams.id
      this.title = '修改训练营'
      this.openBootcamp = true
    },
    /** 提交按钮 */
    submitAddForm() {
      
      let boolMis = this.isValidInput(this.addQueryParams.lightVideoFixedDuration,1,1440)
      let boolRatio = this.isValidInput(this.addQueryParams.lightVideoDurationRatio,1,100)

      if (this.addQueryParams.lightVideoType == 0 && !boolMis) {
        this.$modal.msgWarning('固定时长输入错误')
        return
      }

      if (this.addQueryParams.lightVideoType == 1 && !boolRatio) {
        this.$modal.msgWarning('时长比例输入错误')
        return
      }

      this.$refs['bootCampform'].validate((valid) => {
        if (valid) {
          if (this.addQueryParams.id) {
            editeBootcampInfo(this.addQueryParams).then(() => {
              this.$modal.msgSuccess("修改训练营成功");
              this.getDataList();
              this.addQueryParams = {
                campName: null,
                lightVideoType: null,
                lightVideoFixedDuration: null,
                lightVideoDurationRatio: null
              }
              this.$refs.bootCampform.resetFields()
              this.openBootcamp = false
            })
          } else {
            addBootcampInfo(this.addQueryParams).then(() => {
              this.$modal.msgSuccess('新增训练营成功')
              this.getDataList()
              this.addQueryParams = {
                campName: null,
                lightVideoType: null,
                lightVideoFixedDuration: null,
                lightVideoDurationRatio: null
              }
              this.$refs.bootCampform.resetFields()
              this.openBootcamp = false
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除 ' + row.campName + ' 训练营？')
        .then(function() {
          return deleteBootcampInfo([row.id])
        })
        .then(() => {
          this.getDataList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    isValidInput(value, min, max) {
      if (value === '' || value === null || value === undefined) return false
      // 转成字符串统一处理
      const str = String(value).trim()
      // 匹配整数或小数（最多两位小数）
      const reg = /^(?:\d+)(?:\.\d{1,2})?$/
      if (!reg.test(str)) return false
      const num = parseFloat(str)
      if (isNaN(num)) return false
      return num >= min && num <= max
    }
  }
}
</script>
<style lang="scss" scoped>
.containerBox{
  height: calc(100vh - 170px);
}
.tipsWord{
  margin-bottom: 10px;
  color: #606266;
  font-size: 12px;
}
.tips{
  margin-top: 30px;
}
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
.boxContent{
  display: flex;
  padding-top: 30px;
  height: calc(100% - 178px);
}
.menuBox{
  margin-right: 10px;
  height: 100%;
  overflow-y: scroll;
}
.content{
  flex: 1;
  max-width: calc(100% - 270px);
  .queryBox{
    width: 100%;
    .queryBoxContent{
      display: flex;
      justify-content: space-between;
    }
  }
}
::v-deep .el-table.el-table--medium .el-table__body-wrapper td{
  font-size: 14px;
  color: #606266;
}
.boxx{
  width: 100%;
  display: flex;
}
.campBox{
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f7f8fa;
  padding: 20px;
  margin-bottom: 20px;
}

.categoryBox {
  width: 250px;
  border: 1px solid #eee;
  padding: 10px;
  height: 100%;
  overflow: scroll;
  box-sizing: border-box;
}
.addBtn {
  margin-top: 10px;
  margin-bottom: 10px;
}
.boxItem {
  position: relative;
  height: 40px;
  width: 100%;
  line-height: 40px;
  cursor: pointer;
  padding: 0 10px;
  border-radius: 6px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}

.textOne {
  width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.deleteIcon {
  position: absolute;
  right: 6px;
  color: #007aff;
  top: 12px;
}
.editeIcon {
  position: absolute;
  right: 30px;
  color: #007aff;
  top: 12px;
}

.boxItemActive {
  background-color: #ecfaf6;
  color: #0abf89;
}

.category-container {
  min-width: 355px;
  padding: 10px;
  border: 1px solid #eaeaea;
}
.operation-header {
  display: flex;
  margin-bottom: 22px;
}

.category-table-wrap {
  height: calc(100vh - 250px);
}
.category-col {
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: opacity 0.3s;
  &:hover .category-name {
    text-decoration: underline;
  }
  .category-name {
    flex: 1;
    //line-height: 56.5px;
    padding: 0 10px;
  }
  .category-r {
    display: flex;
    align-items: center;
  }
  .category-operation {
    opacity: 0;
  }
  .el-icon-arrow-right {
    opacity: 1;
  }
  &:hover {
    .category-operation {
      opacity: 1;
    }
    .el-icon-arrow-right {
      opacity: 0;
    }
  }
}
::v-deep .el-table__header-wrapper .el-checkbox {
  display: none;
}
::v-deep .el-form-item--small.el-form-item {
  margin: 0;
}
.flexBox{
  display: flex;
}
.inputBox{
  width: 150px;
  margin-left: 20px;
  margin-right:20px;
}
::v-deep .el-radio-group .el-radio{
  margin-bottom: 0px !important;
}
// 表格高度不一致，将高度撑开
::v-deep .el-table__fixed-right {
  height: 100% !important;
}
</style>

