<template>
  <div class="app-container">

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          @click="handleAdd"
        >添加学员</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="dataList" ref="singleSelectTable">
      <el-table-column type="index" align="center" label="序号" width="55" />
      <el-table-column
        label="头像"
        align="center"
        prop="avatar"
        width="100px"
      >
        <template slot-scope="scope">
          <el-image :src="scope.row.avatar || userDefault" class="headImg"></el-image>
        </template>
      </el-table-column>
      <el-table-column
        label="用户昵称"
        align="center"
        prop="userName"
        width="140px"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="性别"
        align="center"
        width="80px"
        prop="gender"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.gender == 0">未知</div>
          <div v-if="scope.row.gender == 1">男</div>
          <div v-if="scope.row.gender == 2">女</div>
        </template>
      </el-table-column>
      <el-table-column
        label="手机号码"
        align="center"
        prop="mobile"
        width="140px"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="邮箱"
        align="center"
        width="80px"
        prop="email"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.customerType == 1">微信用户</div>
          <div v-if="scope.row.customerType == 2">企业用户</div>
        </template>
      </el-table-column>
      <el-table-column
        label="上课时长(分钟)"
        align="center"
        prop="attendingClassesDuration"
        width="140px"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="完课次数"
        align="center"
        prop="finishClassTimes"
        width="140px"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="到课次数"
        align="center"
        prop="attendingClassesTimes"
        width="140px"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="最后登录IP"
        align="center"
        prop="loginIp"
        width="140px"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="最后登录地址"
        align="center"
        prop="loginAddress"
        width="140px"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="最后登录时间"
        align="center"
        prop="loginDate"
        width="140px"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="跟进人ID"
        align="center"
        prop="followStatus"
        width="140px"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="跟进状态"
        align="center"
        prop="followStatus"
        width="140px"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.chatStatus == 0">待跟进</div>
          <div v-if="scope.row.chatStatus == 1">跟进中</div>
          <div v-if="scope.row.chatStatus == 2">待签单</div>
          <div v-if="scope.row.chatStatus == 3">已签单</div>
          <div v-if="scope.row.chatStatus == 4">复购</div>
          <div v-if="scope.row.chatStatus == 5">无效</div>
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        width="140px"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="操作" fixed="right" width="140px" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleDelete(scope.row)"
          >移除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增模版 -->
     <chooeseStudents :visible.sync="showCreate" @addSuccess="addSuccess" />
  </div>
</template>

<script>
import chooeseStudents from "../../components/chooeseStudents.vue";
import userDefault from '@/assets/images/userDefault.png'
import { getBootcampStudentsList,mineBootcampStudents,removeStudents } from '@/api/liveBroadcastManagement/bootcamp'
import store from '@/store'
export default {
  name: 'mineStudents',
  components: { chooeseStudents },
  data() {
    return {
      userDefault,
      submitLoading:false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      dataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        campPeriodId: null,
        followId: null,
      },
      // 添加学员
      showCreate: false,
      editeOrCopyTitle: '',
      openEdite: false,
      copyForm: {
        templateName: '',
        idStr: ''
      }
    }
  },
  created() {
    this.queryParams.campPeriodId = this.$route.query.id
    this.getList()
  },
  methods: {
    // 添加学员成功回调
    addSuccess(){
      this.getList()
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true
      this.queryParams.followId = this.$store.state.user.userInfo.userId
      mineBootcampStudents(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.showCreate = true
    },
    handleDelete(row) {
      this.$modal.confirm('是否确认移除名为"' + row.userName + '"的学员？').then(function() {
        return removeStudents([row.id])
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('移除成功')
      }).catch(() => {})
    },
  }
}
</script>
<style lang="scss" scoped>
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
.headImg {
  width: 60px;
  height: 60px;
}
</style>
