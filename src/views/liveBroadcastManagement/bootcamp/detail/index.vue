<template>
  <div class="app-container">
    <div class="dataInfo">
      <div class="left">
        <img src="@/assets/images/courseImg.png" style="width: 164px;height: 120px;" fit="contain" alt="">
      </div>
      <div class="right">
        <div class="titleBox">{{ bootcampData.periodName }}</div>
        <div class="tips">招生时间：{{ bootcampData.recruitStartTime }} ~ {{ bootcampData.recruitEndTime }} 开营时间：{{ bootcampData.curriculumStartTime }} ~ {{ bootcampData.curriculumEndTime }}</div>
        <div class="btnBox">
          <el-button type="primary" @click="toShare">分享链接</el-button>
        </div>
      </div>
    </div>
    <el-tabs v-model="activeName" style="margin-top: 5px;" @tab-click="handleClick">
      <el-tab-pane label="营期学员" name="campStudents" />
      <el-tab-pane label="我的学员" name="mineStudents" />
      <el-tab-pane label="课程" name="course" />
    </el-tabs>
    <div class="pageRight">
      <component :is="activeComponent" :key="index" />
    </div>

    <!-- 分享链接 -->
    <share-link :data-share="dataShare" :visible.sync="showShareLink" />
  </div>
</template>

<script>
import CampStudents from './campStudents.vue'
import MineStudents from './mineStudents.vue'
import ShareLink from '../../components/shareLink.vue'
import Course from './course.vue'
import { getBootcampDetail } from '@/api/liveBroadcastManagement/bootcamp'
export default {
  name: 'Index',
  components: {
    CampStudents,
    MineStudents,
    Course,
    ShareLink
  },
  data() {
    return {
      activeName: 'campStudents',
      showShareLink: false,
      dataShare: {},
      bootcampData: {},
      index:1
    }
  },
  computed: {
    activeComponent() {
      let componentStr = ''
      if (this.activeName === 'campStudents') {
        componentStr = 'campStudents'
      }
      if (this.activeName === 'mineStudents') {
        componentStr = 'mineStudents'
      }
      if (this.activeName === 'course') {
        componentStr = 'course'
      }
      return componentStr
    }
  },
  activated(){
    this.index++
    const id = this.$route.query.id
    const type = this.$route.query.type
    if (id) {
      this.loadBootcampDetail(id)
    }
    if (type == 'course') {
      this.activeName = 'course'
    }else{
      this.activeName = 'campStudents'
    }
  },
  methods: {
    // 营期详情
    loadBootcampDetail(id) {
      getBootcampDetail(id).then(res => {
        if (res.code == 200) {
          this.bootcampData = res.data
        }
      })
    },
    // 营期
    toShare() {
      this.showShareLink = true
    },
    handleClick(tab) {
      this.activeName = tab.name
    }
  }
}
</script>
<style lang="scss" scoped>
.dataInfo{
  padding: 20px;
  display: flex;
  .right{
    margin-left: 40px;
    .titleBox{
      font-weight: bold;
    }
    .tips{
      font-size: 12px;
      color: #666;
    }
    div{
      margin-top: 5px;
    }
    .btnBox{
      margin-top: 10px;
    }
  }
}
</style>

