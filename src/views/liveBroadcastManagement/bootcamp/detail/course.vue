<template>
  <div class="app-container">
    
    <!-- 章节模式 -->
    <div>
      <div style="margin-bottom: 10px" v-if="courseMode == 1">
        <el-button type="primary" icon="el-icon-plus" @click="addCourseChapterFunc">添加章节</el-button>
      </div>
      <el-collapse accordion v-model="activeName" @change="getCourseDataList" v-if="courseMode == 1">
        <el-collapse-item v-for="(item,index) in courseChapterDataList" :key="index" :name="index">
          <template slot="title">
            <div class="titleBox">
              <div>
                <i v-if="activeName === index" class="header-icon el-icon-arrow-down" />
                <i v-else class="header-icon el-icon-arrow-right" />
                {{ item.chapterName }}
              </div>
              <div>
                <template  v-if="courseChapterDataList.length > 1">
                  <el-button type="text" v-if="index == 0" @click.stop="sotMoveChapter(item,index,'down')">下移</el-button>
                  <el-button type="text" v-if="index == (courseChapterDataList.length - 1)" @click.stop="sotMoveChapter(item,index,'up')">上移</el-button>
                  <el-button type="text" v-if="index != (courseChapterDataList.length - 1) && index != 0" @click.stop="sotMoveChapter(item,index,'up')">上移</el-button>
                  <el-button type="text" v-if="index != (courseChapterDataList.length - 1) && index != 0" @click.stop="sotMoveChapter(item,index,'down')">下移</el-button>
                </template>
                <el-button type="text" style="margin-left: 10px;" @click.stop="editeChapter(item)">编辑</el-button>
                <el-button type="text" @click.stop="deleteChapter(item)">删除</el-button>
              </div>
            </div>
          </template>
          <div>
            <div style="margin-bottom: 10px">
              <el-button type="primary" plain icon="el-icon-plus" @click="handleAddChapter">新增课节</el-button>
              <el-button type="primary" plain icon="el-icon-document-copy" @click="handleCopyCourse">复制课节</el-button>
              <el-button type="info" icon="el-icon-sort" v-if="!moveCourseParmas.classIds.length" plain @click="moveTips">移动课节</el-button>
              <el-button type="primary" icon="el-icon-sort" v-if="moveCourseParmas.classIds.length" plain @click="handleMove">移动课节</el-button>
              <!-- <el-button type="primary" plain icon="el-icon-plus" @click="handleBatchAddChapter">批量新增课节</el-button> -->
            </div>
            <el-table ref="singleSelectTable" v-loading="loading" :data="courseDataList" @selection-change="handleSelectionChange">
              <el-table-column type="selection" align="center" :selectable="handleSelectable" width="55" />
              <el-table-column
                label="次序"
                align="center"
                prop="classSort"
                width="60"
              />
              <el-table-column
                label="课节名称"
                align="center"
                prop="className"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="主讲老师"
                align="center"
                prop="anchor"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="课节类型"
                align="center"
                prop="classTypeName"
                :show-overflow-tooltip="true"
              />
              <el-table-column label="状态" align="center" prop="liveStatusName" width="100" />
              <el-table-column label="直播时间" align="center" prop="liveTime" width="170">
                <template slot-scope="scope">
                  <div class="boxxx" @click="handleEditeLiveTime(scope.row)"  v-if="isValidTime(scope.row.liveTime) && scope.row.liveStatus == 0">
                    {{ scope.row.liveTime }}
                    <i class="el-icon-edit" />
                  </div>
                  <div class="boxxx disableBox"  v-else>
                    {{ scope.row.liveTime }}
                    <i class="el-icon-edit" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="创建人"
                align="center"
                prop="createBy"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="创建时间"
                align="center"
                prop="createTime"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="修改人"
                align="center"
                prop="updateBy"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="修改时间"
                align="center"
                prop="updateTime"
                :show-overflow-tooltip="true"
              />
              <el-table-column label="操作" align="center" class-name="small-padding">
                <template slot-scope="scope">
                  <el-button
                    v-if="!scope.row.liveTime"
                    type="text"
                    disabled
                  >分享链接</el-button>
                  <el-button
                    v-if="scope.row.liveTime"
                    type="text"
                    @click="shareLink(scope.row)"
                  >分享链接</el-button>
                  <el-dropdown>
                    <span class="el-dropdown-link">
                      <el-button
                        type="text"
                      >更多</el-button>
                      <i class="el-icon-arrow-down el-icon--right" style="color:#409efe;" />
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item v-if="scope.row.liveStatus == 2">
                        <el-button type="text" style="width:100%" icon="el-icon-download" @click="handleDownload(scope.row)">下载直播消息</el-button>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <el-button type="text" style="width:100%" icon="el-icon-edit" @click="handleEdite(scope.row)">编辑</el-button>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <el-button type="text" style="width:100%" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
  
            <pagination
              v-show="total>0"
              :total="total"
              :page.sync="courseParmas.pageNum"
              :limit.sync="courseParmas.pageSize"
              @pagination="getCourseDataList"
            />
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>


    <!-- 课节模式 -->
    <div v-if="courseMode == 0">
      <div style="margin-bottom: 10px">
        <el-button type="primary" plain icon="el-icon-plus" @click="handleAddChapter">新增课节</el-button>
        <el-button type="primary" plain icon="el-icon-document-copy" @click="handleCopyCourse">复制课节</el-button>
      </div>
      <el-table v-loading="loading" :data="courseDataList">
        <el-table-column
          label="次序"
          align="center"
          prop="classSort"
          width="60"
        />
        <el-table-column
          label="课节名称"
          align="center"
          prop="className"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="主讲老师"
          align="center"
          prop="anchor"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="课节类型"
          align="center"
          prop="classTypeName"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="状态" align="center" prop="liveStatusName" width="100" />
        <el-table-column label="直播时间" align="center" prop="liveTime" width="170">
          <template slot-scope="scope">
            <div class="boxxx" @click="handleEditeLiveTime(scope.row)"  v-if="isValidTime(scope.row.liveTime) && scope.row.liveStatus == 0">
              {{ scope.row.liveTime }}
              <i class="el-icon-edit" />
            </div>
            <div class="boxxx disableBox"  v-else>
              {{ scope.row.liveTime }}
              <i class="el-icon-edit" />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="创建人"
          align="center"
          prop="createBy"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="修改人"
          align="center"
          prop="updateBy"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="修改时间"
          align="center"
          prop="updateTime"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="操作" align="center" class-name="small-padding">
          <template slot-scope="scope">
            <el-button
              type="text"
              disabled
              v-if="!scope.row.liveTime"
            >分享链接</el-button>
            <el-button
              type="text"
              v-if="scope.row.liveTime"
              @click="shareLink(scope.row)"
            >分享链接</el-button>
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-button
                  type="text"
                >更多</el-button>
                <i class="el-icon-arrow-down el-icon--right" style="color:#409efe;"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="scope.row.liveStatus == 2">
                  <el-button type="text" style="width:100%" icon="el-icon-download" @click="handleDownload(scope.row)">下载直播消息</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button type="text" style="width:100%" icon="el-icon-edit" @click="handleEdite(scope.row)">编辑</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button type="text" style="width:100%" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="courseParmas.pageNum"
        :limit.sync="courseParmas.pageSize"
        @pagination="getCourseDataList"
      />
    </div>

    <!-- 修改外部名称 -->
    <el-dialog title="修改课程名称" :visible.sync="editeCourseNameBool" width="600px" append-to-body>
      <el-form ref="editeCourseNameForm" :model="editeCourseNameForm" :rules="rules" label-width="80px">
        <el-row>
          <el-col>
            <el-form-item label="课程名称" prop="chapterName">
              <el-input v-model="editeCourseNameForm.chapterName" :maxlength="32" placeholder="请输入章节名称" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitLoading" @click="submitChapter">确 定</el-button>
        <el-button @click="cancelSubmit">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 编辑直播时间 -->
    <el-dialog title="编辑直播时间" :visible.sync="editeCourseLiveTimeBool" width="600px" append-to-body>
      <el-form ref="editeCourseLiveTime" :model="editeCourseLiveTime" :rules="rules" label-width="80px">
        <div style="display:flex;">
          <el-date-picker
            v-model="editeCourseLiveTime.date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择开始日期"
            style="width: 200px;margin-right: 10px"
            :picker-options="pickerOptions"
          ></el-date-picker>
          <el-time-select
            v-model="editeCourseLiveTime.time"
            :picker-options="timeOption"
            placeholder="请手动修改/选择时间">
          </el-time-select>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitLoading" @click="submitLiveTime">确 定</el-button>
        <el-button @click="cancelSubmitLiveTime">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 新增章节 -->
    <el-dialog :title="courseChapterTitle" :visible.sync="openCourseChapter" width="600px" append-to-body>
      <el-form ref="addChapterParmas" :model="addChapterParmas" :rules="rules" label-width="80px">
        <el-row>
          <el-col>
            <el-form-item label="章节名称" prop="chapterName">
              <el-input v-model="addChapterParmas.chapterName" :maxlength="32" placeholder="请输入章节名称" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitLoading" @click="submitChapter">确 定</el-button>
        <el-button @click="cancelSubmit">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 复制课程----选择课程 -->
    <chooeseCourse :visible.sync="showCopy" :chapter-id="chapterId" @refresh="getList" />

    <!-- 移动课节 -->
    <el-dialog title="移动课节" :visible.sync="moveCourseBool" width="500px" append-to-body>
      <el-form ref="moveCourse" :model="moveCourseParmas" :rules="rules" label-width="80px">
        <el-form-item label="章节">
          <el-select
            v-model="moveCourseParmas.targetChapterId"
            placeholder="请选择目标章节"
            clearable
            style="width: 300px"
          >
            <el-option
              v-for="(item, index) in courseChapterDataList"
              :key="index"
              :label="item.chapterName"
              :value="item.chapterId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitLoading" @click="submitMove">确 定</el-button>
        <el-button @click="cancelMove">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 分享链接 -->
    <share-link :data-share="dataShare" :visible.sync="showShareLink" />
  </div>
</template>

<script>
import userSelector from '@/views/system/notice/components/UserSelector.vue'
import { getBootcampCourseChapterList, editeCourseChapter,addCourseChapter,deleteCourseChapter, moveCourse,
  sortCourseChapter, getCourseByChapter, deleteCourse, changeLiveTime,courseTypeList,queryChapterId } from '@/api/liveBroadcastManagement/bootcamp'
import chooeseCourse from "../../components/chooeseCourse.vue";
import ShareLink from '../../components/shareLink.vue';
export default {
  name: 'Course',
  components: { userSelector, chooeseCourse, ShareLink },
  data() {
    return {
      activeName: 0,
      submitLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      dataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateName: null
      },
      // 表单校验
      rules: {
        chapterName: [
          { required: true, message: '请输入章节名称', trigger: 'change' }
        ]
      },
      // 用户选择器
      // userSelectorVisible: false,

      // 创建消息
      showCreate: false,
      editeOrCopyTitle: '',
      openEdite: false,
      copyForm: {
        templateName: '',
        idStr: ''
      },

      // 章节
      courseChapterDataList: [],
      openCourseChapter: false,
      addChapterParmas: {
        periodId: null,
        chapterName: null
      },
      courseChapterTitle: '',

      // 章节下的课程
      courseParmas: {
        chapterId: '',
        pageNum: 1,
        pageSize: 10
      },
      courseDataList: [],

      // 复制课程
      showCopy: false,
      chapterId: '',
      moveCourseParmas: {
        targetChapterId: '',
        classIds: []
      },
      moveCourseBool: false,

      // 分享
      dataShare: {},
      showShareLink: false,

      // 修改课节名称
      editeCourseNameBool: false,
      editeCourseNameForm: {
        chapterName: ''
      },

      // 修改直播时间
      editeCourseLiveTimeBool: false,
      editeCourseLiveTime: {
        classId: '',
        date: '',
        time: ''
      },
      timeOption:{},
      pickerOptions: {
        disabledDate(time) {
          const todayStart = new Date();
          todayStart.setHours(0, 0, 0, 0); // 今天零点
          // 禁用今天零点之前的日期（昨天及更早）
          return time.getTime() < todayStart.getTime();
        }
      },
      courseMode: null,
    }
  },
  created(){
    this.addChapterParmas.periodId = this.$route.query.id
    this.courseMode = this.$route.query.courseMode
    this.getList()//查询章节
  },
  methods: {
    // 下载直播间消息
    handleDownload(data){
      this.downloadJSON('/liveAuiCommonApi/downloadRoomMessage', {'roomId':data.roomId}, `直播互动消息_${new Date().getTime()}.xlsx`)
    },
    // 取消修改直播时间
    cancelSubmitLiveTime(){
      this.editeCourseLiveTime = {
        classId: '',
        date: '',
        time: ''
      }
      this.editeCourseLiveTimeBool = false
    },
    // 提交修改直播时间
    submitLiveTime(){
      if(!this.editeCourseLiveTime.date){
        this.$modal.msgWarning("请选择日期!");
        return
      }
      if(!this.editeCourseLiveTime.time){
        this.$modal.msgWarning("请选择时间!");
        return
      }
      let parmas = {
        classId: this.editeCourseLiveTime.classId,
        liveTime: this.editeCourseLiveTime.date + ' ' + this.editeCourseLiveTime.time + ':00'
      }
      this.submitLoading = true
      changeLiveTime(parmas).then(res => {
        if(res.code == 200){
          this.$modal.msgSuccess("修改成功!");
          this.submitLoading = false
          this.editeCourseLiveTimeBool = false
          this.getList()
        }
      }).catch(()=>{
        this.submitLoading = false
      })
    },
    // 批量新增课节
    handleBatchAddChapter(){ },
    // 修改直播时间
    handleEditeLiveTime(data){
      this.editeCourseLiveTime.classId = data.classId
      if(data.liveTime){
        this.editeCourseLiveTime.time = data.liveTime ? data.liveTime.split(' ')[1].substring(0,5) : ''
        this.editeCourseLiveTime.date = data.liveTime ? data.liveTime.split(' ')[0] : ''
      }else{
        this.getDate()
      }
      this.updateTimeOption()
      this.editeCourseLiveTimeBool = true
    },
    // 获取当前日期
    getDate(){
      const d = new Date()
      const pad = (n) => n.toString().padStart(2, '0')
      this.editeCourseLiveTime.date = `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())}`
    },
    // 选择
    updateTimeOption() {
      const now = new Date()
      let hour = now.getHours()
      let minute = now.getMinutes()
      // 向上取整到 15 分钟刻度
      minute = Math.ceil(minute / 15) * 15
      if (minute === 60) {
        minute = 0
        hour = (hour + 1) % 24
      }
      const pad = (n) => n.toString().padStart(2, '0')
      const startTime = `${pad(hour)}:${pad(minute)}`
      this.timeOption = {
        start: startTime,
        step: '00:15',
        end: '23:45'
      }
    },
    // 分享
    shareLink(data) {
      const cloneData = JSON.parse(JSON.stringify(data))
      cloneData['type'] = 'CAMP_COURSE_LIVE'
      cloneData['periodId'] = this.$route.query.id
      this.dataShare = cloneData
      this.showShareLink = true
    },
    // 移动课节
    handleMove() {
      this.moveCourseBool = true
    },
    // 取消移动
    cancelMove() {
      this.moveCourseParmas.targetChapterId = ''
      this.moveCourseBool = false
    },
    // 确定移动
    submitMove() {
      moveCourse(this.moveCourseParmas).then(res => {
        if (res.code == 200) {
          this.$modal.msgSuccess('移动成功!')
          this.cancelMove()
          this.getCourseDataList()
        }
      })
    },
    // 请先勾选课节
    moveTips() {
      this.$modal.msgWarning('请先勾选课节!')
    },
    // 待开发
    waiteDevelop() {
      this.$modal.msgWarning('待开发!')
    },
    // 复制课程
    handleCopyCourse() {
      let that = this
      if(that.courseMode == 1){
        that.chapterId = that.courseChapterDataList[that.activeName].chapterId
        that.showCopy = true
      }else{
        queryChapterId(that.$route.query.id).then(res => {
          if(res.code == 200){
            that.chapterId = res.data[0].chapterId
            that.showCopy = true
          }
        })
      }
    },
    // 新增课节
    handleAddChapter() {
      let that = this
      if(that.courseMode == 1){
        localStorage.setItem('chapterId', that.courseChapterDataList[that.activeName].chapterId)
        that.$router.push('/bootcamp/bootcampChapter?periodId=' + that.$route.query.id+'&courseMode='+that.$route.query?.courseMode)
      }else{
        queryChapterId(that.$route.query.id).then(res => {
          if(res.code == 200){
            localStorage.setItem('chapterId', res.data[0].chapterId)
            that.$router.push('/bootcamp/bootcampChapter?periodId=' + that.$route.query.id+'&courseMode='+that.$route.query?.courseMode)
          }
        })
      }
    },
    // 下移 上移
    sotMoveChapter(data, index, str) {
      const that = this
      const name = str === 'up' ? '上移' : '下移'
      if (str === 'up') {
        const upData = this.courseChapterDataList[index - 1]
        const pamas1 = {
          chapterSourceIdId: data.chapterId,
          chapterTargetIdId: upData.chapterId,
          sourceSort: data.sort,
          targetSort: upData.sort
        }
        that.$modal.confirm('是否确认 ' + name + data.chapterName + ' 章节？').then(function() {
          sortCourseChapter(pamas1).then(() => {
            that.getList()
            that.$modal.msgSuccess(name + '成功')
          }).catch(() => {

          })
        })
      } else {
        const downData = this.courseChapterDataList[index + 1]
        const pamas2 = {
          chapterSourceIdId: data.chapterId,
          chapterTargetIdId: downData.chapterId,
          sourceSort: data.sort,
          targetSort: downData.sort
        }
        that.$modal.confirm('是否确认 ' + name + data.chapterName + ' 章节？').then(function() {
          sortCourseChapter(pamas2).then(() => {
            that.getList()
            that.$modal.msgSuccess(name + '成功')
          }).catch(() => {

          })
        })
      }
    },
    // 编辑章节
    editeChapter(data) {
      this.addChapterParmas = JSON.parse(JSON.stringify(data))
      this.openCourseChapter = true
    },
    // 删除章节
    deleteChapter(data) {
      this.$modal.confirm('是否确认删除 ' + data.chapterName + ' 章节？').then(function() {
        return deleteCourseChapter(data.chapterId)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    // 取消新增章节
    cancelSubmit() {
      this.addChapterParmas = {
        periodId: this.$route.query.id,
        chapterName: null
      }
      this.$refs.addChapterParmas.resetFields()
      this.openCourseChapter = false
    },
    // 新增章节
    addCourseChapterFunc() {
      this.courseChapterTitle = '新增章节'
      this.openCourseChapter = true
    },
    // 提交章节
    submitChapter() {
      const that = this
      that.$refs['addChapterParmas'].validate((valid) => {
        if (valid) {
          that.submitLoading = true
          if (that.addChapterParmas.chapterId) {
            const parmas = {
              chapterId: that.addChapterParmas.chapterId,
              chapterName: that.addChapterParmas.chapterName
            }
            editeCourseChapter(parmas).then((res) => {
              if (res.code == 200) {
                that.$modal.msgSuccess('修改成功')
                that.submitLoading = false
                that.addChapterParmas = {
                  periodId: that.$route.query.id,
                  chapterName: null
                }
                that.$refs.addChapterParmas.resetFields()
                that.openCourseChapter = false
                that.getList()
              }
            }).catch(() => {
              that.submitLoading = false
            })
          } else {
            addCourseChapter(that.addChapterParmas).then((res) => {
              if (res.code == 200) {
                that.$modal.msgSuccess('新增成功')
                that.submitLoading = false
                that.addChapterParmas = {
                  periodId: that.$route.query.id,
                  chapterName: null
                }
                that.$refs.addChapterParmas.resetFields()
                that.openCourseChapter = false
                that.getList()
              }
            }).catch(() => {
              that.submitLoading = false
            })
          }
        }
      })
    },
    // 删除记录
    toDeletePath() {
      this.$router.push('/courseManagement/deleteRecord')
    },
    /** 章节列表 */
    getList() {
      this.loading = true
      if(this.courseMode == 1){
        // 加载章节
        getBootcampCourseChapterList(this.$route.query.id).then(res => {
          this.courseChapterDataList = res.data
          this.getCourseDataList()
          this.loading = false
        })
      }else{
        this.getCourseTypeDataList()
      }
    },
    // 章节模式----课程列表
    getCourseDataList() {
      this.courseParmas.chapterId = this.courseChapterDataList[this.activeName]?.chapterId
      if (this.courseParmas.chapterId) {
        getCourseByChapter(this.courseParmas).then(res => {
          this.courseDataList = res.rows
          this.total = res.total
        })
      }
    },
    // 课程模式----课程列表
    getCourseTypeDataList(){
      let parmas = {
        pageNum: this.courseParmas.pageNum,
        pageSize: this.courseParmas.pageSize,
        periodId: this.$route.query.id
      }
      courseTypeList(parmas).then(res => {
        this.loading = false
        this.courseDataList = res.rows
        this.total = res.total
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.moveCourseParmas.classIds = selection.map(item => item.classId)
    },
    handleSelectable(row) {
      return row.convertFlag !== 1
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.showCreate = true
    },
    /** 删除操作 */
    handleDelete(row) {
      this.$modal.confirm('当前删除名称为"' + row.className + '"的课程，删除后，将无法恢复和正常使用，会影响到用户正常访问，且视频文件无法恢复或找回！').then(function() {
        return deleteCourse([row.classId])
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    // 编辑课节
    handleEdite(data){
      let that = this
      // 是否编辑课节开始日期时间
      let editeBool = this.isValidTime(data.liveTime) && data.liveStatus == 0 ? 1 : 0;
      if(that.courseMode == 1){
        localStorage.setItem('chapterId',that.courseChapterDataList[that.activeName].chapterId)
        that.$router.push('/bootcamp/bootcampChapter?periodId='+that.$route.query.id+'&classId='+data.classId+'&courseMode='+that.$route.query?.courseMode+'&editeTime='+editeBool+'&liveStatusName='+data.liveStatusName)
      }else{
        queryChapterId(that.$route.query.id).then(res => {
          if(res.code == 200){
            localStorage.setItem('chapterId',res.data[0].chapterId)
            that.$router.push('/bootcamp/bootcampChapter?periodId='+that.$route.query.id+'&classId='+data.classId+'&courseMode='+that.$route.query?.courseMode+'&editeTime='+editeBool+'&liveStatusName='+data.liveStatusName)
          }
        })
      }
    },
    // 是否可以编辑直播时间
    isValidTime(targetTime) {
      if(!targetTime) return true
      // 转换成 Date 对象，兼容 Safari
      const time = new Date(targetTime.replace(/-/g, '/'))
      const now = new Date()
      // 当前时间减去 5 分钟
      const minAllowedTime = new Date(now.getTime() - 5 * 60 * 1000)
      // 返回 true 表示合法，false 表示不合法
      return time >= minAllowedTime
    },
  }
}
</script>
<style lang="scss" scoped>
.boxxx{
  cursor: pointer;
  color: #409efe;
}
.disableBox{
  color: #909399;
  cursor: not-allowed;
}
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
.titleBox{
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-right: 10px;
}
</style>
