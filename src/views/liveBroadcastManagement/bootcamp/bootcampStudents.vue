<template>
  <div class="containerBox">

    <div v-if="type === 'bootcamp'">
      <el-tag class="tips" type="warning" size="large">* 汇总所有报名了训练营或直接加入班级群的学员数据。您可以手动调整学员的营期和班级。</el-tag>
      <div class="contentBox">
        <img src="@/assets/images/logoi.png" alt="">
        <div>
          <div class="title">训练营学员</div>
          <div class="name">所属训练营：{{ bootcampData.campName }}</div>
        </div>
      </div>
    </div>

    <div v-if="type === 'mineStudents'">
      <el-tag class="tips" type="warning" size="large">* 汇总所有您作为跟进人的训练营学员数据。您可以手动调整学员的营期和班级。</el-tag>
      <div class="contentBox">
        <img src="@/assets/images/logoi.png" alt="">
        <div>
          <div class="title">我的学员</div>
          <div class="name">所属训练营：{{ bootcampData.campName }}</div>
        </div>
      </div>
    </div>

    <div class="boxTab">
      <div v-for="(item,index) in tabList" :key="index" :class="activeIndex == index ? 'activeTab itemTab':'itemTab'" @click="tabClick(item,index)">
        <div class="tabName">{{ item.label }}</div>
        <div class="tabContent">{{ item.num }}</div>
      </div>
    </div>

    <div class="queryBox">
      <el-form ref="queryForm" class="queryBoxContent" :model="queryParams" :inline="true">
        <!-- <el-form-item label="是否分配营期">
          <el-select
            v-model="queryParams.publishStatus"
            placeholder="请选择是否分配营期"
            style="width:160px"
            clearable
          >
            <el-option
              v-for="dict in dict.type.live_publish_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="归属状态">
          <el-select
            v-model="queryParams.campPeriodStatus"
            placeholder="请选择归属状态"
            style="width:160px"
            clearable
          >
            <el-option
              v-for="dict in dict.type.live_camp_period_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="客户创建时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 250px;"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button type="primary" plain @click="downloadFunc">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table ref="elTable" v-loading="loading" :data="dataList">
      <el-table-column label="序号" align="center" type="index" />
      <el-table-column label="训练营名称" align="center" prop="periodName" />
      <el-table-column label="客户ID" align="center" prop="customerId" width="160" />
      <el-table-column label="到课次数" align="center" prop="attendingClassesTimes" />
      <el-table-column label="完课次数" align="center" prop="finishClassTimes" />
      <el-table-column label="客户ID" align="center" prop="customerId" width="160" />
      <el-table-column label="客户ID" align="center" prop="customerId" width="160" />
      <el-table-column label="手机号" align="center" prop="mobile" />
      <el-table-column label="微信昵称" align="center" prop="userName" />
      <el-table-column label="UnionId" align="center" prop="wechatUnionId" />
      <el-table-column label="跟进人" align="center" prop="followName" />
      <el-table-column label="跟进状态" align="center" prop="followStatus">
        <template slot-scope="scope">
          <dict-tag type="primary" :options="dict.type.live_follow_status" :value="Number(scope.row.followStatus)" />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getDataList"
    />
  </div>
</template>

<script>
import { queryStudentsOrMineStudents,queryTotal } from '@/api/liveBroadcastManagement/bootcamp.js'
export default {
  name: 'Bootcamplist',
  dicts: ['live_follow_status'],
  data() {
    return {
      dateRange: [],
      activeIndex: 0,
      tabList:[
        {label: '总学员人数',num: 0},
        {label: '今日新增',num: 0},
        {label: '待分配营期',num: 0},
      ],
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      dataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        campId: '',
        followId: '',
        createStartTime: '',
        createEndTime: ''
      },
      type: null,
      bootcampData:{}
    }
  },
  beforeRouteEnter(to, from, next) {
    // 组件实例还没创建，用 next(callback) 访问实例
    next(vm => {
      vm.initData(to)
    })
  },
  beforeRouteUpdate(to, from, next) {
    // 路由复用组件时触发
    this.initData(to)
    next()
  },
  methods: {
    // 默认进入页面请求
    initData(){
      this.type = this.$route.query.type
      this.bootcampData = JSON.parse(localStorage.getItem('bootcampData')) || {};
      this.getDataList()
      this.getTabInfo()
    },
    // 统计数据获取
    getTabInfo(){
      queryTotal(this.queryParams.campId).then(res => {
        this.tabList[0].num = res.data.totalStudent
        this.tabList[1].num = res.data.todayAddStudent
        this.tabList[2].num = res.data.waitDistributeStudent
      })
    },
    // 导出
    downloadFunc() {
      this.downloadJSON('/liveAuiCamp/export', { ...this.queryParams }, `学员信息_${new Date().getTime()}.xlsx`)
    },
    // tab点击 
    tabClick(data,index){
      this.activeIndex = index
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.createStartTime = this.dateRange[0] + ' 00:00:00'
        this.queryParams.createEndTime = this.dateRange[1] + ' 23:59:59'
      } else {
        this.queryParams.createStartTime = null
        this.queryParams.createEndTime = null
      }
      this.queryParams.pageNum = 1
      this.getDataList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        campId: '',
        followId: '',
        createStartTime: '',
        createEndTime: ''
      }
      this.handleQuery()
    },
    /** 查询资源类别列表 */
    getDataList() {
      this.loading = true
      this.bootcampData = JSON.parse(localStorage.getItem('bootcampData')) || {};
      this.queryParams.campId = this.bootcampData.id
      if(this.type ==='mineStudents'){
        this.queryParams.followId = this.$store.state.user.userInfo.userId
      }else{
        this.queryParams.followId = null
      }
      queryStudentsOrMineStudents(this.queryParams).then((res) => {
        this.dataList = res.data.rows || [];
        this.total = res.data.total;
        this.loading = false;
      });
    },
  }
}
</script>
<style lang="scss" scoped>
.tips{
  margin-bottom: 30px;
}
.queryBox{
  margin-bottom: 20px;
  margin-top: 40px;
}
.contentBox{
  display: flex;
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  img{
    display: block;
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }
  .title{
    font-weight: bold;
    font-size: 16px;
  }
  .name{
    font-size: 14px;
    color: #606266;
  }
}
.boxTab{
  display: flex;
  margin-bottom: 20px;
  font-weight: 400;
  cursor: pointer;
  .itemTab{
    color: #606266;
    width: 140px;
    padding: 8px;
    margin-right: 12px;
    border-radius: 4px;
    border: 2px solid #ebeef5;
    .tabName{
      font-weight: 500;
    }
  }
  .activeTab{
    color: #0abf89;
    border: 2px solid #0abf89;
  }
}
</style>

