<template>
  <div class="w-100">
    <div style="width: 50%">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- 基础配置 -->
        <div class="titleBox">基础配置</div>
        <el-form-item label="课节名称" prop="title" required>
          <el-input
            v-model="form.title"
            placeholder="请输入课节名称"
            style="width: 100%"
            :maxlength="256"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="开始日期时间">
          <div style="display:flex;">
            <el-date-picker
              v-model="form.date"
              type="date"
              :disabled="editeBool"
              value-format="yyyy-MM-dd"
              placeholder="选择开始日期"
              style="width: 200px;margin-right: 10px"
              :picker-options="pickerOptions"
            />
            <el-time-select
              v-model="form.time"
              :disabled="editeBool"
              :picker-options="timeOption"
              placeholder="请手动修改/选择时间"
            />
          </div>
        </el-form-item>
        <el-form-item label="课程时长" prop="classDuration" required>
          <el-input
            :disabled="finished"
            v-model="form.classDuration"
            placeholder="请输入课程时长"
            style="width: 200px"
            type="number"
          />
        </el-form-item>
        <el-form-item label="直播暖场" prop="warmType" required>
          <el-radio-group v-model="form.warmType" @change="changeWarmType" :disabled="finished">
            <el-radio :label="0">无配置</el-radio>
            <el-radio :label="1">暖场视频</el-radio>
            <el-radio :label="2">暖场图片</el-radio>
          </el-radio-group>
          <div v-if="form.warmType === 1" class="flex">
            <div v-if="form.warmUpVideoId">
              <video
                ref="videoRef"
                :src="videoSrc"
                :poster="videoPoster"
                controls
                width="318px"
                style="height: 200px;"
              >
                您的浏览器不支持 video 标签。
              </video>
            </div>
            <img v-else :src="videoImg" style="width: 318px; height: 172px">
            <div class="ml-10">
              <el-button
                class="addBtn"
                type="primary"
                style="margin-top: 30px;"
                :disabled="finished"
                @click="handleChooeseVideo('warmUpVideoId')"
              >
                添加暖场视频
              </el-button>
              <div class="tips">
                <div>
                  支持mp4、avi、wmv、mov、flv、rmvb、3gp、m4v、mkv格式；
                </div>
                <div>文件最大不超过20G，新视频上传后需要转码；</div>
                <div>请尽可能提前上传，转码未完成时学员端无法观看</div>
              </div>
            </div>
          </div>
          <div v-if="form.warmType === 2" class="flex">
            <el-image :src="form.warmUpImageId ? chooeseImg : posterImg" style="width: 318px; height: 200px" fit="contain" :preview-src-list="form.warmUpImageId ? [chooeseImg] : [posterImg]" />
            <div class="ml-10">
              <el-button
                class="addBtn"
                type="primary"
                style="margin-top: 30px;"
                :disabled="finished"
                @click="handleChooeseImg"
              >
                添加图片
              </el-button>
              <div class="tips">
                <div>*（建议按照下方要求添加图片）</div>
                <div>图片尺寸：“750px*420px”</div>
                <div>图片比例：16:9</div>
                <div>图片格式：png、jpg、jpeg格式</div>
                <div>图片大小：小5M</div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="主讲老师" prop="anchorId" required>
          <el-select
            v-model="form.anchorId"
            placeholder="请选择上课老师"
            style="width: 100%"
            :disabled="finished"
            @change="changeAnchorId"
          >
            <el-option
              v-for="(dict,index) in dropdownList"
              :key="index"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          <div style="color: gray; fontSize: 12px;">* 单场直播仅可配置一位主讲老师，讲师不可以进入直播客户端上课，但会在营期课程列表显示老师头像和外显昵称信息</div>
        </el-form-item>
        <el-form-item label="助教设置" required>
          <div class="flex">
            <div>
              <el-tag v-for="(item,index) in checkedNodes" :key="index" style="margin-right: 6px; cursor: pointer;">{{ item.label }}</el-tag>
            </div>
            <el-button
              class="addBtn"
              type="primary"
              :disabled="finished"
              @click="addTeachingAssistant = true"
            >
              请选择助教
            </el-button>
          </div>
          <div style="color: gray; fontSize: 12px;">* 单场直播可配置多位助教</div>
        </el-form-item>
        <el-form-item label="课程内容" prop="notice" required>
          <el-input
            :disabled="finished"
            v-model="form.notice"
            type="textarea"
            :rows="4"
            :maxlength="256"
            show-word-limit
            placeholder="请输入课程内容"
            style="width: 100%"
          />
        </el-form-item>
        <!-- 直播内容设置 -->
        <div class="titleBox">直播内容设置</div>
        <el-form-item label="内容来源" prop="contentSource" required>
          <el-radio-group v-model="form.contentSource" @change="changeContentSource" :disabled="finished">
            <el-radio :label="0">自定义</el-radio>
            <el-radio :label="1">直播模版</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.contentSource == 0" label="课程视频" required>
          <div class="flex">
            <div v-if="form.courseVideoId">
              <video
                ref="videoRef"
                :src="courseVideoSrc"
                :poster="courseVideoPoster"
                controls
                width="318px"
                style="height: 200px;"
              >
                您的浏览器不支持 video 标签。
              </video>
            </div>
            <img v-else :src="videoImg" style="width: 318px; height: 172px">
            <div class="ml-10">
              <el-button
                class="addBtn"
                type="primary"
                style="margin-top: 30px;"
                :disabled="finished"
                @click="handleChooeseVideo('courseVideoId')"
              >
                添加课程视频
              </el-button>
              <div class="tips">
                <div>
                  1、支持mp4、avi、wmv、mov、flv、rmvb、3gp、m4v、mkv格式；
                </div>
                <div>2、文件最大不超过20G，新视频上传后需要转码；</div>
                <div>
                  请至少提60分钟创建课程，否则可能会影响视频按时开始播放
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="form.contentSource == 1" label="直播模版" required>
          <el-select
            v-model="form.liveTemplateId"
            placeholder="请选择直播模版"
            :disabled="finished"
            style="width: 100%"
          >
            <el-option
              v-for="(dict,index) in templateListData"
              :key="index"
              :label="dict.templateName"
              :value="dict.liveTemplateId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div style="margin-top: 50px;margin-left:150px;">
        <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </div>

    <!-- 助教选择 -->
    <el-dialog
      title="添加助教"
      :visible="addTeachingAssistant"
      width="60%"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div class="flex myBox">
        <div
          class="flex-1"
          style="border: 1px solid #ccc; margin-right: 10px; padding: 20px"
        >
          <div style="border-bottom: 1px solid #ccc; margin-bottom: 10px">
            <el-input
              v-model="form.categoryName"
              prefix-icon="el-icon-search"
              placeholder="请搜索"
              style="width: 100%; border: none !important"
            />
          </div>
          <el-tree
            ref="userTree"
            :data="userTreeData"
            show-checkbox
            node-key="id"
            filter
            :default-checked-keys="defaultChecked"
            :props="defaultProps"
            :check-strictly="true"
            @check="handleCheckChange"
          />
        </div>
        <div class="flex-1" style="border: 1px solid #ccc; padding: 20px">
          <div
            style="
              border-bottom: 1px solid #ccc;
              line-height: 36px;
              margin-bottom: 10px;
            "
          >
            已选择的助教({{checkedNodes.lenght}})
          </div>
          <div>
            <p v-for="(item,index) in checkedNodes" :key="index">{{ item.label }}</p>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 选择视频 -->
    <chooeseVideo :visible.sync="showCreate" :is-create-template="false" @chooeseVideo="chooeseVideoFunc" />

    <!-- 选择图片 -->
    <chooeseImage :visible.sync="chooeseImageShow" :is-create-template="false" @chooeseImageSuccess="chooeseSuccess" />
  </div>
</template>

<script>
import { addRecordCourseData, editeRecordCourseData, getUserTreeList, searchTemplateList, courseSelectList, msgListData, getDetailInfo, getCourseMsgList, getImageInfo } from '@/api/liveBroadcastManagement/messageTemplate'
import chooeseImage from '@/views/liveBroadcastManagement/components/chooeseImage.vue'
import { addBootcampCourse,editeBootcampCourse, courseDetailInfo } from '@/api/liveBroadcastManagement/bootcamp'
import chooeseVideo from "../../components/chooeseVideo.vue";
import posterImg from "@/assets/images/courseImg.png";
import videoImg from "@/assets/images/courseVideo.png";
import { getVideoSrc } from "@/api/sourceMaterial/index";
import { throttle } from '@/utils';
import Vue from 'vue';
export default {
  name: 'CreateLiveRecord',
  dicts: ['category_type'],
  components: { chooeseVideo, chooeseImage },
  data() {
    return {
      courseVideoSrc: '', // 课程视频地址
      courseVideoPoster: '', // 课程视频地址
      videoSrc: '', // 暖场视频地址
      videoPoster: '', // 暖场视频封面
      chooeseImageShow: false, // 选择图片弹窗
      showCreate: false, // 选择课程弹窗
      chooeseImg: '', // 选择的图片
      posterImg,
      videoImg,
      total: 0,
      dataList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        systemed: null,
        categoryName: null,
        categoryType: null,
        status: null
      },
      defaultChecked: [],
      // 表单参数
      form: {
        date: '',
        time: '',
        periodId: '', // 营期id
        chapterId: '', // 章节id
        liveMode: 2, // 直播模式 默认2
        title: '', // 课节名称 256字符
        notice: '', // 课程内容 256字符
        liveTime: '', // 开始时间
        classDuration: 30, // 课程时长
        warmType: 0, // 暖场类型 0无暖场 1暖场视频 2暖场图片
        warmUpVideoId: '', // 暖场视频ID
        warmUpImageId: '', // 暖场图片ID
        anchorId: '', // 主讲Id
        anchor: '', // 主讲名称
        assistantIds: [], // 助教用户ID
        contentSource: 0, // 内容来源
        courseVideoId: '', // 课程视频ID(内容来源是自定义的时候设置)
        liveTemplateId: '', // 直播模板ID(内容来源是直播模板的时候设置)
        anchorNick: '' // 主播nick
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: '课节名称不能为空', trigger: ['blur', 'change'] }
        ],
        notice: [
          { required: true, message: '课程内容不能为空', trigger: ['blur', 'change'] }
        ],
        liveTime: [
          { required: true, message: '课程分组不能为空', trigger: 'change' }
        ],
        classDuration: [
          { required: true, message: '课程时长不能为空', trigger: ['blur', 'change'] }
        ],
        warmType: [
          { required: true, message: '请选择暖场类型', trigger: 'change' }
        ],
        stopTime: [
          { required: true, message: '请选择直播结束时间', trigger: ['blur', 'change'] }
        ],
        liveImageId: [
          { required: true, message: '请选择直播封面', trigger: 'change' }
        ],
        warmUpVideoId: [
          { required: true, message: '请选择暖场视频', trigger: 'change' }
        ],
        anchorId: [
          { required: true, message: '请选择讲师', trigger: 'change' }
        ],
        assistantIds: [
          { required: true, message: '请选择助教', trigger: 'change' }
        ],
        contentSource: [
          { required: true, message: '请选择内容来源', trigger: 'change' }
        ],
        listedStatus: [
          { required: true, message: '请设置上下架状态', trigger: 'change' }
        ]
      },
      addTeachingAssistant: false, // 助教弹窗
      userTreeData: [], // 树形数据
      dropdownList: [], // 下拉
      defaultProps: {
        children: 'children',
        label: 'label',
        disabled: (data, node) => {
          return data.type !== 1 // 非 type === 1 的节点禁用勾选
        }
      },
      checkedNodes: [], // 已勾选助教数据
      checkedKeys: [], // 已勾选助教ID
      templateListData: [], // 直播模版
      courseSelectData: [],
      chooeseVideoType: '', // 判断当前是选择暖场视频还是课程视频
      chooeseVideoId: null,
      messageList: [],
      pickerOptions: {
        disabledDate(time) {
          const todayStart = new Date()
          todayStart.setHours(0, 0, 0, 0) // 今天零点
          // 禁用今天零点之前的日期（昨天及更早）
          return time.getTime() < todayStart.getTime()
        }
      },
      submitLoading: false,
      timeOption: {},

      // 编辑时，只有未开播的课节可以编辑时间，已开播或者开播中或者开播前5分钟都不能编辑课节
      editeBool: false,
      finished: false,
    }
  },
  watch: {
    'form.assistantIds': {
      handler(newVal) {
        this.getUserTree1()// 讲师
      },
      deep: true
    },
    'form.anchorId': {
      handler(newVal) {
        this.getUserTree2()// 助教
      },
      deep: true
    }
  },
  created() {
    if(this.$route.query?.classId){
      // 编辑
      this.loadDetailInfo(this.$route.query?.classId)
      this.editeBool = this.$route.query?.editeTime == 1 ? false : true;
    }else{
      this.getUserTree1();//讲师
      this.getUserTree2();//助教
      this.getDate()//默认取当前日期
    }
    if(this.$route.query?.liveStatusName === '已结束'){
      this.editeBool = true
      this.finished = true
    }
    this.getTemplate()// 可用直播模版
    this.getCourseSelectList()// 课程下拉列表
    this.updateTimeOption()// 选择时间

    // 浏览器控制按钮前进后退触发函数
    if (window.history && window.history.pushState) {
      history.pushState(null, null, document.URL)
      window.addEventListener('popstate', this.popstate, false)
    }
  },
  destroyed(){
    window.removeEventListener('popstate', this.popstate, false);
  },
  methods: {
    popstate(){
      let courseMode = this.$route.query?.courseMode
      let periodId = this.$route.query?.periodId
      this.$tab.closeOpenPage({ path: '/bootcamp/bootcampChapter' })
      this.$router.push('/bootcamp/detail?type=course&id='+periodId+'&courseMode='+courseMode)
    },
    // 获取当前日期
    getDate() {
      const d = new Date()
      const pad = (n) => n.toString().padStart(2, '0')
      this.form.date = `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())}`
    },
    // 时间刻度选择
    updateTimeOption() {
      const now = new Date()
      let hour = now.getHours()
      let minute = now.getMinutes()
      // 向上取整到 15 分钟刻度
      minute = Math.ceil(minute / 15) * 15
      if (minute === 60) {
        minute = 0
        hour = (hour + 1) % 24
      }
      const pad = (n) => n.toString().padStart(2, '0')
      const startTime = `${pad(hour)}:${pad(minute)}`
      this.timeOption = {
        start: startTime,
        step: '00:15',
        end: '23:45'
      }
    },
    // 加载编辑直播间详情
    loadDetailInfo(id){
      courseDetailInfo(id).then(res => {
        if(res.code == 200){
          let resInfo = res.data
          this.form = JSON.parse(JSON.stringify(resInfo))
          Vue.set(this.form, 'title', resInfo.title);
          Vue.set(this.form, 'date', resInfo.date);
          Vue.set(this.form, 'time', resInfo.time);
          this.form.title = resInfo.className
          this.checkedKeys = resInfo.assistantList.map(item => item.assistantId)
          // 加载暖场视频回显
          if(resInfo.warmUpVideoId){
            let params1 = {id: resInfo.warmUpVideoId}
            this.loadVideoInfoData(params1,1)
          }
          // 封面图片回显
          if(resInfo.warmUpImageId){
            this.loadImageInfo()
          }
          // 开始日期时间回显
          if(resInfo.liveTime){
            let dateTime = resInfo.liveTime.split(' ')
            this.form.date = dateTime[0]
            this.form.time = dateTime[1].substring(0,5)
          }
          // 助教回显
          if(resInfo.assistantList.length){
            this.form.assistantIds = resInfo.assistantList.map(item => item.assistantId)
            let checkedNodes = []
            resInfo.assistantList.forEach(item => {
              let obj = {
                label: item.assistantName
              }
              checkedNodes.push(obj)
            })
            this.checkedNodes = checkedNodes
            // 内容来源--- 自定义 ----课程回显
            if(resInfo.contentSource == 0){
              this.loadCourseMessageList()
              let params2 = {id: resInfo.courseVideoId}
              this.loadVideoInfoData(params2,2)
            }
            // 加载助教数据
            this.getUserTree2()
          }
          console.log(JSON.parse(JSON.stringify(this.form)), "编辑详情===🔥🔥🔥")
        }
      })
    },
    // 加载图片回显
    loadImageInfo() {
      getImageInfo(this.form.warmUpImageId).then(res => {
        this.chooeseImg = res.data.url
      })
    },
    // 加载课程消息
    loadCourseMessageList() {
      getCourseMsgList(this.form.courseVideoId).then(res => {
        this.messageList = res.data
      })
    },
    // 切换暖场类型
    changeWarmType() {
      if (this.form.contentSource == 2) {
        this.form.warmUpVideoId = null
        this.videoPoster = null
        this.videoSrc = null
      } else if (this.form.contentSource == 1) {
        this.form.warmUpImageId = null
        this.chooeseImg = null
      } else {
        this.form.warmUpVideoId = null
        this.form.warmUpImageId = null
      }
    },
    // 切换内容来源
    changeContentSource() {
      if (this.form.contentSource == 1) {
        this.form.courseVideoId = null
      } else if (this.form.contentSource == 0) {
        this.form.courseVideoId = null
        this.form.liveTemplateId = null
      }
    },
    // 选择课程分组
    getCourseSelectList() {
      courseSelectList().then(res => {
        this.courseSelectData = res.data
      })
    },
    // 选择图片
    handleChooeseImg() {
      this.chooeseImageShow = true
    },
    // 选择图片回调
    chooeseSuccess(data) {
      this.form.warmUpImageId = data.id
      this.chooeseImg = data.url
    },
    // 选择视频
    handleChooeseVideo(str) {
      this.chooeseVideoType = str
      this.showCreate = true
    },
    // 选择视频回调
    chooeseVideoFunc(data) {
      if (data.videoId) {
        if (this.chooeseVideoType === 'warmUpVideoId') {
          this.form.warmUpVideoId = data.id
          const parmar = { videoId: data.videoId }
          this.loadVideoInfoData(parmar, 1)
        } else if (this.chooeseVideoType === 'courseVideoId') {
          this.chooeseVideoId = data.videoId
          this.loadMessageList()
          this.form.courseVideoId = data.id
          const parmar = { videoId: data.videoId }
          this.loadVideoInfoData(parmar, 2)
        }
      }
    },
    // 获取视频信息
    loadVideoInfoData(data, type) {
      getVideoSrc(data).then(res => {
        if (res.code == 200) {
          const videoObj = res.data?.playInfoList.filter(item => item.specification === 'Original')
          if (type == 1) {
            this.videoSrc = videoObj[0].playURL
            this.videoPoster = videoObj[0].coverURL
          }
          if (type == 2) {
            this.courseVideoSrc = videoObj[0].playURL
            this.courseVideoPoster = videoObj[0].coverURL
          }
        }
      })
    },
    // 消息内容下拉列表
    loadMessageList() {
      msgListData(this.chooeseVideoId).then(res => {
        this.messageList = res.rows
      })
    },
    // 直播模版下拉
    getTemplate() {
      searchTemplateList().then(res => {
        this.templateListData = res.data
      })
    },
    // 讲师下拉选择
    changeAnchorId(e) {
      const obj = this.dropdownList.filter(item => item.value === e)
      if (obj.length) {
        this.form.anchorNick = obj[0].label
      }
    },
    // 助教选择确认
    handleConfirm() {
      this.form.assistantIds = this.checkedKeys
      this.addTeachingAssistant = false
    },
    // 助教选择取消
    handleCancel() {
      this.addTeachingAssistant = false
    },
    handleClose() {
      this.addTeachingAssistant = false
    },
    // 勾选 选择
    handleCheckChange(checkedNodes, checkedKeys) {
      this.checkedNodes = checkedKeys['checkedNodes']
      this.checkedKeys = checkedKeys['checkedKeys']
    },
    /** 讲师 */
    getUserTree1() {
      let parmas
      if (this.form.assistantIds) {
        parmas = {
          userIds: this.form.assistantIds
        }
      } else {
        parmas = {}
      }
      getUserTreeList(parmas).then((response) => {
        this.dropdownList = []
        this.collectType1Nodes(response.data[0])
      })
    },
    /** 助教 */
    getUserTree2() {
      let parmas
      if (this.form.anchorId) {
        parmas = {
          userIds: [this.form.anchorId]
        }
      } else {
        parmas = {}
      }
      getUserTreeList(parmas).then((response) => {
        this.userTreeData = response.data
      })
    },
    // 取消按钮
    cancel() {
      let courseMode = this.$route.query?.courseMode
      let periodId = this.$route.query?.periodId
      this.$tab.closeOpenPage({ path: '/bootcamp/bootcampChapter' })
      this.$router.push('/bootcamp/detail?type=course&id='+periodId+'&courseMode='+courseMode)
    },
    /** 提交按钮 */
    submitForm: throttle(function() {
      const that = this

      that.$refs['form'].validate((valid) => {
        if (valid) {
          if (!that.form.date) {
            return that.$modal.msgWarning('请选择开始日期！')
          }

          if (!that.form.time) {
            return that.$modal.msgWarning('请选择开始时间！')
          }

          that.form.liveTime = that.form.date + ' ' + that.form.time + ':00'

          if (!that.checkedKeys.length) {
            return that.$modal.msgWarning('请选择助教！')
          }

          if (that.form.contentSource == 0 && !that.form.courseVideoId) {
            return that.$modal.msgWarning('请选择课程视频！')
          }

          if (that.form.contentSource == 1 && !that.form.liveTemplateId) {
            return that.$modal.msgWarning('请选择直播模版！')
          }

          that.form.chapterId = localStorage.getItem('chapterId') // 章节id
          that.form.periodId = that.$route.query.periodId // 营期id

          that.submitLoading = true
          let courseMode = that.$route.query?.courseMode
          let periodId = that.$route.query?.periodId
          // 编辑
          if( that.form.classId ){
            console.log(JSON.parse(JSON.stringify(that.form)), "编辑===form🔥🔥🔥🔥🔥🔥🔥")
            editeBootcampCourse(that.form).then((res) => {
              if(res.code == 200){
                that.$modal.msgSuccess("编辑成功");
              }
              that.submitLoading = false
              that.$tab.closeOpenPage({ path: '/bootcamp/bootcampChapter' })
              that.$router.push('/bootcamp/detail?type=course&id='+periodId+'&courseMode='+courseMode)
            }).catch(()=>{
              that.submitLoading = false
            })
          }else{
            console.log(JSON.parse(JSON.stringify(that.form)), "新增===form🔥🔥🔥🔥🔥🔥🔥")
            addBootcampCourse(that.form).then((res) => {
              if(res.code == 200){
                that.$modal.msgSuccess("新增成功");
              }
              that.submitLoading = false
              that.$tab.closeOpenPage({ path: '/bootcamp/bootcampChapter' })
              that.$router.push('/bootcamp/detail?type=course&id='+periodId+'&courseMode='+courseMode)
            }).catch(()=>{
              that.submitLoading = false
            })
          }
        }
      })
    }, 1500),
    isStartBeforeEnd(startTime, endTime) {
      // 将字符串转为 Date 对象
      const start = new Date(startTime.replace(/-/g, '/')) // 兼容 Safari
      const end = new Date(endTime.replace(/-/g, '/'))
      // 判断大小
      return start <= end // true 表示合法，false 表示 startTime > endTime
    },
    isValidTime(targetTime) {
      // 转换成 Date 对象，兼容 Safari
      const time = new Date(targetTime.replace(/-/g, '/'))
      const now = new Date()
      // 当前时间减去 5 分钟
      const minAllowedTime = new Date(now.getTime() - 5 * 60 * 1000)
      // 返回 true 表示合法，false 表示不合法
      return time >= minAllowedTime
    },
    // 组装讲师下拉数据
    collectType1Nodes(node) {
      if (node.type === 1) {
        this.dropdownList.push({
          label: node.label,
          value: node.id
        })
      }
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => this.collectType1Nodes(child))
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.tips {
  font-size: 12px;
  color: gray;
  line-height: 20px;
  margin-top: 10px;
}
.titleBox {
  border-left: 3px solid #007aff;
  text-indent: 10px;
  font-weight: bold;
  margin-bottom: 20px;
}
.myBox{
  min-height: 300px;
}
::v-deep .myBox .el-input__inner {
  border: none !important;
  outline: none !important;
}
::v-deep .el-radio-group .el-radio{
  margin-bottom: 0px !important;
}
</style>

