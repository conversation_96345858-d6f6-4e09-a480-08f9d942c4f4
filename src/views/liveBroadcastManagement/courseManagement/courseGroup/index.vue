<template>
  <div class="app-container">
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="分类名称">
        <el-input
          v-model="queryParams.categoryName"
          placeholder="请输入分类名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="分组类型">
        <el-select
          v-model="queryParams.categoryType"
          placeholder="请选择"
          style="width:100px"
          clearable
        >
          <el-option
            v-for="dict in dict.type.category_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否系统类型">
        <el-select
          v-model="queryParams.systemed"
          placeholder="请选择"
          style="width:100px"
          clearable
        >
          <el-option :value="true" label="是">是</el-option>
          <el-option :value="false" label="否">否</el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="启用禁用">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择"
          style="width:100px"
          clearable
        >
          <el-option :value="0" label="启用">启用</el-option>
          <el-option :value="1" label="禁用">禁用</el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:notice:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="序号" type="index" width="55" align="center" />
      <el-table-column
        label="分类名称"
        align="center"
        prop="categoryName"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="分组类型" align="center" prop="categoryType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.category_type" :value="scope.row.categoryType" />
        </template>
      </el-table-column>
      <el-table-column label="是否系统类型" align="center" prop="systemed" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.systemed" type="success">是</el-tag>
          <el-tag v-else type="danger">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == 0" type="success">启用</el-tag>
          <el-tag v-if="scope.row.status == 1" type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建者" align="center" prop="createBy" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="分类名称" prop="categoryName" required>
          <el-input v-model="form.categoryName" placeholder="请输入分类名称" :maxlength="32" />
        </el-form-item>
        <el-form-item label="分组类型" prop="categoryType" required>
          <el-select v-model="form.categoryType" placeholder="请选择分组类型">
            <el-option
              v-for="dict in dict.type.category_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否系统类型" required>
          <el-radio-group v-model="form.systemed">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="启用禁用" required>
          <el-radio-group v-model="form.status">
            <el-radio :label="0">启用</el-radio>
            <el-radio :label="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addCourseCategory, deleteCourseCategory, editeCourseCategory, detailCourseCategory, queryCourseCategory } from '@/api/liveBroadcastManagement'
import { throttle } from '@/utils'
export default {
  name: 'CourseGroup',
  dicts: ['category_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      dataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        systemed: null,
        categoryName: null,
        categoryType: null,
        status: null
      },
      // 表单参数
      form: {
        status: 0,
        systemed: true,
        categoryName: null,
        categoryType: null,
        deleted: 0
      },
      // 表单校验
      rules: {
        categoryName: [
          { required: true, message: '分类名称不能为空', trigger: ['blur', 'change'] }
        ],
        categoryType: [
          { required: true, message: '分组类型不能为空', trigger: 'change' }
        ]
      },
      // 用户选择器
      userSelectorVisible: false,
      submitLoading: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询公告列表 */
    getList() {
      this.loading = true
      queryCourseCategory(this.queryParams).then(response => {
        this.dataList = response.data.rows
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        status: 0,
        systemed: true,
        categoryName: null,
        categoryType: null,
        deleted: 0
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        systemed: null,
        categoryName: null,
        categoryType: null,
        status: null
      }
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.noticeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加分类'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      detailCourseCategory(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改分类'
      })
    },
    /** 提交按钮 */
    submitForm: throttle(function() {
      const that = this
      that.$refs['form'].validate(valid => {
        if (valid) {
          that.submitLoading = true
          if (that.form.id !== undefined) {
            editeCourseCategory(that.form).then(response => {
              that.$modal.msgSuccess('修改成功')
              that.open = false
              that.submitLoading = false
              that.getList()
            }).catch(() => {
              that.submitLoading = false
            })
          } else {
            addCourseCategory(that.form).then(response => {
              that.$modal.msgSuccess('新增成功')
              that.open = false
              that.submitLoading = false
              that.getList()
            }).catch(() => {
              that.submitLoading = false
            })
          }
        }
      })
    }, 1500),
    /** 删除按钮操作 */
    handleDelete(row) {
      const noticeIds = row.id
      this.$modal.confirm('是否确认删除分类"' + row.categoryName + '"数据项？').then(function() {
        return deleteCourseCategory([noticeIds])
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
</style>

