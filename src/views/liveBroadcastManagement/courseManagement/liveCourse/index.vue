<template>
  <div>
    <div class="flex align-center justify-between">
      <div class="flex align-center">
        <i class="el-icon-data-line" style="font-size: 50px" />
        <div class="ml-5">
          <p style="font-weight: bold;">直播课程</p>
          <p>快速创建课程，公开课直播｜低价课直播｜0元课直播｜正价课直播｜大班课｜小班课</p>
          <p>营销直播间/服务直播间，优惠券｜精品课推荐｜快捷卡片</p>
        </div>
      </div>
      <div>
        <el-button type="primary" @click="waiteDevelop">违规通知</el-button>
        <el-button type="primary">直播概览</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="addLive">新建直播</el-button>
      </div>
    </div>

    <el-tag class="mt-10" type="success">请仔细核查课程内容，遵守相关规定，禁止虚假宣传、夸大功效、诱导消费、网络水军等非法营销行为。关于规范私域直播秩序的公告</el-tag>

    <div class="boxContent">
      <!-- <div class="menuBox">
        <Category @selectCategory="chooeseCategory" />
      </div> -->
      <div class="content">
        <el-form ref="queryForm" class="mt-10" :model="queryParams" :inline="true">
          <el-form-item label="直播名称">
            <el-input
              v-model="queryParams.roomName"
              placeholder="请输入直播名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="直播时间">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 250px;"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <el-form-item label="直播状态">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择"
              style="width:160px"
              clearable
            >
              <el-option
                v-for="dict in dict.type.live_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上架状态">
            <el-select
              v-model="queryParams.listedStatus"
              placeholder="请选择"
              style="width:160px"
              clearable
            >
              <el-option :value="0" label="上架">上架</el-option>
              <el-option :value="1" label="暂不上架">暂不上架</el-option>
              <el-option :value="2" label="下架">下架</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="课程类型">
            <el-select
              v-model="queryParams.courseType"
              placeholder="请选择"
              style="width:160px"
              clearable
            >
              <el-option :value="0" label="直播课程">直播课程</el-option>
              <el-option :value="1" label="录播直播">录播直播</el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="dataList">
          <el-table-column fixed="left" label="序号" type="index" width="55" align="center" />
          <el-table-column
            fixed="left"
            label="直播名称"
            align="center"
            prop="title"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="课程类型" align="center" prop="courseTypeName" />
          <!-- <el-table-column label="直播类型" align="center" prop="systemed" width="100">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.systemed">是</el-tag>
              <el-tag type="danger" v-else>否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="课程分组" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.status == 0">启用</el-tag>
              <el-tag type="danger" v-if="scope.row.status == 1">禁用</el-tag>
            </template>
          </el-table-column> -->
          <!-- <el-table-column label="直播模式" align="center" prop="createBy"/> -->
          <el-table-column label="直播状态" align="center" prop="status">
            <template slot-scope="scope">
              <dict-tag type="primary" :options="dict.type.live_status" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="直播开始时间" align="center" prop="startedAt" width="160px" />
          <el-table-column label="直播结束时间" align="center" prop="stoppedAt" width="160px" />
          <!-- <el-table-column label="上架状态" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.status == 0">启用</el-tag>
              <el-tag type="danger" v-if="scope.row.status == 1">禁用</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="上/下架时间" align="center" prop="createTime" width="100">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="所属流量池" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.status == 0">启用</el-tag>
              <el-tag type="danger" v-if="scope.row.status == 1">禁用</el-tag>
            </template>
          </el-table-column> -->
          <el-table-column label="创建人" align="center" prop="createBy" />
          <el-table-column label="创建时间" align="center" prop="createTime" width="160px" />
          <el-table-column label="上架状态" align="center" prop="listedStatus">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.listedStatus == 0" type="success">上架</el-tag>
              <el-tag v-if="scope.row.listedStatus == 1" type="warning">暂不上架</el-tag>
              <el-tag v-if="scope.row.listedStatus == 2" type="danger">下架</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="上下架时间" align="center" prop="listedTime" width="160px" />
          <!-- <el-table-column label="更新人" align="center" prop="status">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.status == 0">启用</el-tag>
              <el-tag type="danger" v-if="scope.row.status == 1">禁用</el-tag>
            </template>
          </el-table-column> -->
          <!-- <el-table-column label="更新时间" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.status == 0">启用</el-tag>
              <el-tag type="danger" v-if="scope.row.status == 1">禁用</el-tag>
            </template>
          </el-table-column> -->
          <el-table-column label="操作" align="center" width="240" class-name="small-padding" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" @click="toLive(scope.row)">去直播</el-button>
              <!-- <el-button type="text" @click="handleUpdate(scope.row)">我的邀课链接</el-button> -->
              <el-button type="text" @click="shareFunc(scope.row)">分享链接</el-button>
              <el-button type="text" @click="waiteDevelop(scope.row)">服务配置</el-button>
              <el-dropdown>
                <span class="el-dropdown-link">
                  <el-button
                    type="text"
                  >更多</el-button>
                  <i class="el-icon-arrow-down el-icon--right" style="color:#409efe;" />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>
                    <el-button v-if="scope.row.listedStatus != 2 && scope.row.status != 1" type="text" style="width:100%" @click="upOrDownFunc(scope.row,'下架')">下架</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button v-if="scope.row.listedStatus != 0 && scope.row.status != 1" type="text" style="width:100%" @click="upOrDownFunc(scope.row,'上架')">上架</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text" style="width:100%" @click="deleteFunc(scope.row)">删除</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text" style="width:100%" @click="copyLive(scope.row)">复制直播间</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text" style="width:100%" @click="editeLive(scope.row)">编辑直播间</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button v-if="scope.row.status == 2" type="text" style="width:100%" @click="downloadLiveMsg(scope.row)">下载直播消息</el-button>
                  </el-dropdown-item>
                  <!-- <el-dropdown-item>
                    <el-button type="text" style="width:100%">直播统计</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text" style="width:100%">我的直播统计</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text" style="width:100%">直播大屏</el-button>
                  </el-dropdown-item> -->
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 分享链接 -->
    <share-link :data-share="dataShare" :visible.sync="showShareLink" />
  </div>
</template>

<script>
import { courseListData, deleteLive, upOrDownLive } from '@/api/liveBroadcastManagement/messageTemplate'
import { listConfig } from '@/api/system/config'
import { throttle } from '@/utils';
import ShareLink from '../../components/shareLink.vue';
import { getConfigKey } from "@/api/system/config";
import axios from 'axios'
import { getToken } from '@/utils/auth'
export default {
  name: 'CourseGroup',
  dicts: ['category_type', 'live_status'],
  components: { ShareLink },
  data() {
    return {
      dateRange: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      dataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: '',
        listedStatus: '',
        courseType: '',
        roomName: ''
      },
      // 表单校验
      rules: {
        categoryName: [
          { required: true, message: '分类名称不能为空', trigger: ['blur', 'change'] }
        ],
        categoryType: [
          { required: true, message: '分组类型不能为空', trigger: 'change' }
        ]
      },
      dataShare:{},
      showShareLink: false,
      
      // 场控地址
      liveUrl: "",
      baseUrl: "",
    }
  },
  created() {
    this.getList()
    this.getLiveUrl()
  },
  methods: {
    // 场控token 请求地址
    getLiveUrl(){
      getConfigKey("live_aui_token_api").then(res => {
        if (res.code === 200) {
          this.baseUrl = res.msg;
        }
      });
      // 获取场控地址
      getConfigKey("live_aui_control_address").then(res => {
        if (res.code === 200) {
          this.liveUrl = res.msg;
        }
      });
    },
    // 分享给学员
    shareFunc(data) {
      this.dataShare = data
      this.showShareLink = true
    },
    // 进入直播
    toLive(data){
      let token = 'Bearer ' + getToken()
      let path = `${this.liveUrl.split('?')[0]}?roomId=${data.id}&token=${token}`
      window.open(path)
      // axios.post(this.baseUrl,{roomId: data.id,authorization: token},{
      //   headers: {
      //     'Content-Type': 'application/json'
      //   }
      // }).then(res => {
      //   let path = `${this.liveUrl.split('?')[0]}?roomId=${data.id}&token=${res.data.data}`
      //   window.open(path)
      // })
    },
    // 待开发
    waiteDevelop() {
      this.$modal.msgWarning('待开发!')
    },
    // 下载直播间消息
    downloadLiveMsg(data) {
      this.downloadJSON('/liveAuiCommonApi/downloadRoomMessage', {'roomId':data.id}, `直播间消息_${new Date().getTime()}.xlsx`)
    },
    // 复制直播间
    copyLive(data) {
      this.$router.push(`/courseManagement/createLiveRecord?id=${data.id}&type=copy`)
    },
    // 删除直播间
    deleteFunc(row) {
      this.$modal
        .confirm('当前删除课程名称为"' + row.title + '"，删除后，将无法恢复和正常使用，会影响到用户正常访问，且视频文件无法恢复和找回！')
        .then(function() {
          return deleteLive(row.id)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功!')
        })
        .catch(() => {})
    },
    // 直播间上下架
    upOrDownFunc(row, str) {
      this.$modal
        .confirm('是否确认"' + str + '"直播间？')
        .then(function() {
          return upOrDownLive(row.id)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess(str + '成功!')
        })
        .catch(() => {})
    },
    // 编辑直播间
    editeLive(data) {
      this.$router.push('/courseManagement/createLiveRecord?id=' + data.id)
    },
    // 选择分类
    chooeseCategory(data) {
      this.queryParams.courseGroupId = data.id
      this.getList()
    },
    // 新建直播
    addLive() {
      this.$router.push('/courseManagement/createLiveRecord')
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true
      courseListData(this.queryParams).then(response => {
        this.dataList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.startTime = this.dateRange[0] + ' 00:00:00'
        this.queryParams.endTime = this.dateRange[1] + ' 23:59:59'
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        status: '',
        listedStatus: '',
        courseType: '',
        roomName: ''
      }
      this.handleQuery()
    }
  }
}
</script>
<style lang="scss" scoped>
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
.boxContent{
  display: flex;
  margin-top: 10px;
}
.menuBox{
  margin-right: 10px;
}
.content{
  width: 100%;
}
::v-deep .el-table.el-table--medium .el-table__body-wrapper td{
  font-size: 14px;
  color: #606266;
}
</style>

