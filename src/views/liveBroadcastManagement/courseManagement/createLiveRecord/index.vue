<template>
  <div class="w-100">
    <div style="width: 80%">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- 基础配置 -->
        <div class="titleBox">基础配置</div>
        <el-form-item label="直播名称" prop="title" required>
          <el-input
            v-model="form.title"
            placeholder="请输入直播名称"
            style="width: 100%"
            :maxlength="256"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="直播公告" prop="notice" required>
          <el-input
            v-model="form.notice"
            type="textarea"
            :rows="4"
            placeholder="请输入直播公告"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="课程分组" prop="courseGroupId" required>
          <el-select
            v-model="form.courseGroupId"
            placeholder="请选择课程分组"
            style="width: 100%"
          >
            <el-option
              v-for="(dict,index) in courseSelectData"
              :key="index"
              :label="dict.categoryName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="直播简介" prop="liveIntroduction" required>
          <editor
            v-model="form.liveIntroduction"
            :min-height="200"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime" required>
          <el-date-picker
            v-model="form.startTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择开始时间"
            style="width: 100%"
            :picker-options="pickerOptions"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="stopTime" required>
          <el-date-picker
            v-model="form.stopTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择结束时间"
            style="width: 100%"
            :picker-options="pickerOptions"
          />
        </el-form-item>
        <el-form-item label="直播封面" prop="liveImageId" required>
          <div class="flex">
            <el-image :src="form.liveImageId ? chooeseImg : posterImg" style="width: 318px; height: 200px" fit="contain" :preview-src-list="form.liveImageId ? [chooeseImg] : [posterImg]" />
            <div class="ml-10">
              <el-button
                class="addBtn"
                type="primary"
                @click="handleChooeseImg"
              >
                添加图片
              </el-button>
              <div class="tips">
                <div>*（建议按照下方要求添加图片）</div>
                <div>图片尺寸：“750px*420px”</div>
                <div>图片比例：16:9</div>
                <div>图片格式：png、jpg、jpeg格式</div>
                <div>图片大小：小5M</div>
                <div>暖场视频</div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="暖场视频" prop="warmUpVideoId" required>
          <div class="flex">
            <div v-if="form.warmUpVideoId">
              <video
                ref="videoRef"
                :src="videoSrc"
                :poster="videoPoster"
                controls
                width="318px"
                style="height: 200px;"
              >
                您的浏览器不支持 video 标签。
              </video>
            </div>
            <img v-else :src="videoImg" style="width: 318px; height: 172px">
            <div class="ml-10">
              <el-button
                class="addBtn"
                type="primary"
                @click="handleChooeseVideo('warmUpVideoId')"
              >
                添加暖场视频
              </el-button>
              <div class="tips">
                <div>
                  支持mp4、avi、wmv、mov、flv、rmvb、3gp、m4v、mkv格式；
                </div>
                <div>文件最大不超过20G，新视频上传后需要转码；</div>
                <div>请尽可能提前上传，转码未完成时学员端无法观看</div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="讲师设置" prop="anchorId" required>
          <el-select
            v-model="form.anchorId"
            placeholder="请选择课程分组"
            style="width: 100%"
            @change="changeAnchorId"
          >
            <el-option
              v-for="(dict,index) in dropdownList"
              :key="index"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="助教设置" required>
          <div class="flex">
            <div>
              <el-tag v-for="(item,index) in checkedNodes" :key="index" style="margin-right: 6px; cursor: pointer;">{{ item.label }}</el-tag>
            </div>
            <el-button
              class="addBtn"
              type="primary"
              @click="addTeachingAssistant = true"
            >
              选择助教
            </el-button>
          </div>
          <div style="color: gray; fontSize: 12px;">* 单场直播可配置多位助教</div>
        </el-form-item>
        <!-- 直播内容设置 -->
        <div class="titleBox">直播内容设置</div>
        <el-form-item label="内容来源" prop="contentSource" required>
          <el-radio-group v-model="form.contentSource" @change="changeContentSource">
            <el-radio :label="0">自定义</el-radio>
            <el-radio :label="1">直播模版</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.contentSource == 0" label="课程视频" required>
          <div class="flex">
            <div v-if="form.courseVideoId">
              <video
                ref="videoRef"
                :src="courseVideoSrc"
                :poster="courseVideoPoster"
                controls
                width="318px"
                style="height: 200px;"
              >
                您的浏览器不支持 video 标签。
              </video>
            </div>
            <img v-else :src="videoImg" style="width: 318px; height: 172px">
            <div class="ml-10">
              <el-button
                class="addBtn"
                type="primary"
                @click="handleChooeseVideo('courseVideoId')"
              >
                添加课程视频
              </el-button>
              <div class="tips">
                <div>
                  1、支持mp4、avi、wmv、mov、flv、rmvb、3gp、m4v、mkv格式；
                </div>
                <div>2、文件最大不超过20G，新视频上传后需要转码；</div>
                <div>
                  请至少提60分钟创建课程，否则可能会影响视频按时开始播放
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="form.contentSource == 0" label="消息内容设置">
          <el-select
            v-model="form.categoryType"
            placeholder="请选择消息内容"
            style="width: 100%"
          >
            <el-option
              v-for="(item,index) in messageList"
              :key="index"
              :label="item.roomName"
              :value="item.liveId"
            />
          </el-select>
          <div class="tips">
            <div>
              *
              若使用真实直播下载的的视频回放生成录播直播课，则使用该直播的【直播消息下载】功能下载的消息，原则上可以直接用于生成用于录播直播的消息内容
            </div>
            <div>
              *
              若原直播中间有中断（主要为OBS断流，螳螂直播原则上不受影响），则回放视频会自动截除中断时间的视频
            </div>
            <div>
              *
              直播中断，但IM消息不会停止，故若视频中断，则课程回放可能与直播IM消息产生无法对应的情况，故若直播课程有中断，建议不要使用该课程回放用作生成录播直播
            </div>
            <div>
              *
              若坚持使用该直播回放生成录播直播，建议按实际回放效果，调整直播IM消息时间（【直播消息下载】下载的IM消息中，直播中断时的IM将会被标记）
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="form.contentSource == 1" label="模版设置" required>
          <el-select
            v-model="form.liveTemplateId"
            placeholder="请选择直播模版"
            style="width: 100%"
          >
            <el-option
              v-for="(dict,index) in templateListData"
              :key="index"
              :label="dict.templateName"
              :value="dict.liveTemplateId"
            />
          </el-select>
        </el-form-item>
        
        <div class="titleBox">其他基础配置</div>
        <el-form-item label="标签设置">
          <el-button
            class="addBtn"
            type="primary"
            icon="el-icon-plus"
            @click="addTagsInfo"
          >
            新增标签规则
          </el-button>
          <div class="tips">* 建议课程开始前完成配置，规则仅对配置后行为生效，历史及后续相同行为不打标签。</div>
          <!-- 展示标签 -->
          <div>
            <el-table :data="form.userBehaviorTagDtos" border style="width: 100%; margin-top: 10px" v-if="form.userBehaviorTagDtos && form.userBehaviorTagDtos.length">
              <el-table-column label="序号" type="index" width="55" align="center" />
              <el-table-column label="用户行为" width="300" prop="liveUserBehaviorValue" align="center">
                <template #default="scope">
                  <span>{{getLabelName(dict.type.live_user_behavior,scope.row.liveUserBehaviorValue)}}</span>
                </template>
              </el-table-column>
              <el-table-column label="标签" prop="" align="center">
                <template #default="scope">
                  <div>
                    <el-tag v-for="(item,index) in scope.row.tagIds" :key="index" type="success" :closable="false" class="tags">{{ item.tagName }}</el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row,scope.$index)"
                    >修改</el-button
                  >
                  <el-button
                    type="text"
                    icon="el-icon-delete"
                    @click="deleteTas(scope.row,scope.$index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>

        <div class="titleBox">服务配置</div>
        <el-form-item label="上架设置" prop="listedStatus" required>
          <el-radio-group v-model="form.listedStatus">
            <el-radio :label="0">上架</el-radio>
            <el-radio :label="1">暂不上架</el-radio>
            <el-radio :label="2">下架</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div style="margin-top: 50px;margin-left:150px;">
        <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </div>

    <!-- 新增标签规则 -->
     <addTags :visible.sync="showAddTags" :defaultData="defaultData" @addSuccess="addSuccess" />

    <!-- 助教选择 -->
    <el-dialog
      title="添加助教"
      :visible="addTeachingAssistant"
      width="60%"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div class="flex myBox">
        <div
          class="flex-1"
          style="border: 1px solid #ccc; margin-right: 10px; padding: 20px"
        >
          <div style="border-bottom: 1px solid #ccc; margin-bottom: 10px">
            <el-input
              v-model="form.categoryName"
              prefix-icon="el-icon-search"
              placeholder="请搜索"
              style="width: 100%; border: none !important"
            />
          </div>
          <el-tree
            ref="userTree"
            :data="userTreeData"
            show-checkbox
            node-key="id"
            filter
            :default-checked-keys="defaultChecked"
            :props="defaultProps"
            :check-strictly="true"
            @check="handleCheckChange"
          />
        </div>
        <div class="flex-1" style="border: 1px solid #ccc; padding: 20px">
          <div
            style="
              border-bottom: 1px solid #ccc;
              line-height: 36px;
              margin-bottom: 10px;
            "
          >
            已选择的助教({{checkedNodes.length}})
          </div>
          <div>
            <p v-for="(item,index) in checkedNodes" :key="index">{{ item.label }}</p>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 选择视频 -->
    <chooeseVideo :visible.sync="showCreate" :is-create-template="false" @chooeseVideo="chooeseVideoFunc" />

    <!-- 选择图片 -->
    <chooeseImage :visible.sync="chooeseImageShow" :is-create-template="false" @chooeseImageSuccess="chooeseSuccess" />
  </div>
</template>

<script>
import { addRecordCourseData, editeRecordCourseData, getUserTreeList, searchTemplateList, courseSelectList, msgListData, getDetailInfo, getCourseMsgList, getImageInfo } from '@/api/liveBroadcastManagement/messageTemplate'
import chooeseImage from '@/views/liveBroadcastManagement/components/chooeseImage.vue'
import chooeseTags from '@/views/liveBroadcastManagement/components/chooeseTags.vue'
import addTags from '@/views/liveBroadcastManagement/components/addTags.vue'
import chooeseVideo from "../../components/chooeseVideo.vue";
import posterImg from "@/assets/images/courseImg.png";
import videoImg from "@/assets/images/courseVideo.png";
import { getVideoSrc } from "@/api/sourceMaterial/index";
import { throttle } from '@/utils';
export default {
  name: "createLiveRecord",
  dicts: ["live_user_behavior"],
  components:{ chooeseVideo, chooeseImage, chooeseTags, addTags },
  data() {
    return {
      courseVideoSrc: '', // 课程视频地址
      courseVideoPoster: '', // 课程视频地址
      videoSrc: '', // 暖场视频地址
      videoPoster: '', // 暖场视频封面
      chooeseImageShow: false, // 选择图片弹窗
      showCreate: false, // 选择课程弹窗
      chooeseImg: '', // 选择的图片
      posterImg,
      videoImg,
      total: 0,
      dataList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        systemed: null,
        categoryName: null,
        categoryType: null,
        status: null
      },
      defaultChecked: [],
      // 表单参数
      form: {
        title: "",//直播标题
        notice: "",//直播公告
        courseGroupId: "",//课程分组
        liveIntroduction: "",//直播简介
        startTime: "",//直播开始时间
        stopTime: "",//直播结束时间
        liveImageId: "",//直播封面id
        warmUpVideoId: "",//暖场视频ID
        anchorId: null,//主播Id
        anchorNick: null,//主播nick
        assistantIds: [],//助教信息 --- 助教用户ID列表
        contentSource: 0,//内容来源 0-自定义,1-直播模板
        courseVideoId: null,//课程视频ID(内容来源是自定义的时候设置)
        liveTemplateId: null,//直播模板ID(内容来源是直播模板的时候设置)
        userBehaviorTagDtos:[],//标签
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: '直播标题不能为空', trigger: ['blur', 'change'] }
        ],
        notice: [
          { required: true, message: '直播公告不能为空', trigger: ['blur', 'change'] }
        ],
        courseGroupId: [
          { required: true, message: '课程分组不能为空', trigger: 'change' }
        ],
        liveIntroduction: [
          { required: true, message: '直播简介不能为空', trigger: ['blur', 'change'] }
        ],
        startTime: [
          { required: true, message: '请选择直播开始时间', trigger: ['blur', 'change'] }
        ],
        stopTime: [
          { required: true, message: '请选择直播结束时间', trigger: ['blur', 'change'] }
        ],
        liveImageId: [
          { required: true, message: '请选择直播封面', trigger: 'change' }
        ],
        warmUpVideoId: [
          { required: true, message: '请选择暖场视频', trigger: 'change' }
        ],
        anchorId: [
          { required: true, message: '请选择讲师', trigger: 'change' }
        ],
        assistantIds: [
          { required: true, message: '请选择助教', trigger: 'change' }
        ],
        contentSource: [
          { required: true, message: '请选择内容来源', trigger: 'change' }
        ],
        listedStatus: [
          { required: true, message: "请设置上下架状态", trigger: "change" },
        ],
        mediaPlatformType: [
          { required: true, message: "请设置上下架状态", trigger: "change" },
        ],
      },
      addTeachingAssistant: false, // 助教弹窗
      userTreeData: [], // 树形数据
      dropdownList: [], // 下拉
      defaultProps: {
        children: 'children',
        label: 'label',
        disabled: (data, node) => {
          return data.type !== 1 // 非 type === 1 的节点禁用勾选
        }
      },
      checkedNodes: [], // 已勾选助教数据
      checkedKeys: [], // 已勾选助教ID
      templateListData: [], // 直播模版
      courseSelectData: [],
      chooeseVideoType: '', // 判断当前是选择暖场视频还是课程视频
      chooeseVideoId: null,
      messageList: [],
      pickerOptions: {
        disabledDate(time) {
          const todayStart = new Date()
          todayStart.setHours(0, 0, 0, 0) // 今天零点
          // 禁用今天零点之前的日期（昨天及更早）
          return time.getTime() < todayStart.getTime()
        }
      },
      submitLoading: false,//提交按钮加载

      showAddTags: false,//新增标签弹窗
      defaultData: {},

    };
  },
  created() {
    if (this.$route.query?.id) {
      this.loadDetailInfo(this.$route.query?.id)
    } else {
      this.getUserTree1()// 讲师
      this.getUserTree2()// 助教
    }
    this.getTemplate()// 可用直播模版
    this.getCourseSelectList()// 课程下拉列表

    // 浏览器控制按钮前进后退触发函数
    if (window.history && window.history.pushState) {
      history.pushState(null, null, document.URL)
      window.addEventListener('popstate', this.popstate, false)
    }
  },
  destroyed(){
    window.removeEventListener('popstate', this.popstate, false);
  },
  methods: {
    popstate(){
      this.$tab.closeOpenPage({ path: '/courseManagement/createLiveRecord' })
      this.$router.push('/courseManagement/liveCourse')
    },
    // 修改标签
    handleUpdate(data,index){
      this.defaultData = JSON.parse(JSON.stringify(data))
      this.defaultData.index = index+1
      this.showAddTags = true
    },
    // 删除标签
    deleteTas(data){
      // 提示
      this.$modal.confirm("是否确认删除？").then(() => {
        let index = this.form.userBehaviorTagDtos.findIndex(item => item.liveUserBehaviorValue == data.liveUserBehaviorValue)
        if(index > -1){
          this.form.userBehaviorTagDtos.splice(index,1)
        }
      })
    },
    // 表单label
    getLabelName(arr,value){
      let obj = arr.filter(item => item.value === value)
      let str = ''
      if(obj.length){
        str = obj[0].label
      }
      return str
    },
    // 标签新增成功
    addSuccess(data){
      this.showAddTags = false
      if(data.index){
        this.form.userBehaviorTagDtos.splice(data.index-1,1)
      }
      this.form.userBehaviorTagDtos.push(data)
    },
    // 新增标签规则
    addTagsInfo(){
      this.defaultData = {}
      this.showAddTags = true
    },
    disabledDate(time) {
      // 禁用小于当前时间的日期
      return time.getTime() < Date.now()
    },
    // 加载编辑直播间详情
    loadDetailInfo(id) {
      getDetailInfo(id).then(res => {
        if (res.code == 200) {
          this.form = JSON.parse(JSON.stringify(res.data))
          // 标签回显
          let tagArr = []
          let infoTag = JSON.parse(JSON.stringify(res.data.userBehaviorTagsVos))
          infoTag.forEach(item => {
            let obj = {
              liveUserBehaviorValue: item.liveUserBehaviorValue+'',
              tagIds: item.userTagVos
            }
            tagArr.push(obj)
          })
          this.form.userBehaviorTagDtos = tagArr
          console.log(this.form.userBehaviorTagDtos,"=============")
          // 加载暖场视频回显
          const params1 = { id: res.data.warmUpVideoId }
          this.loadVideoInfoData(params1, 1)
          // 助教回显
          if (res.data.assistantList.length) {
            this.form.assistantIds = res.data.assistantList.map(item => item.assistantId)
            const checkedNodes = []
            res.data.assistantList.forEach(item => {
              const obj = {
                label: item.assistantName
              }
              checkedNodes.push(obj)
            })
            this.checkedNodes = checkedNodes
            // 内容来源--- 自定义 ----课程回显
            if (res.data.contentSource == 0) {
              this.loadCourseMessageList()
              const params2 = { id: res.data.courseVideoId }
              this.loadVideoInfoData(params2, 2)
            }
            // 封面图片回显
            this.loadImageInfo()
            // 加载助教数据
            this.getUserTree2()
          }
        }
      })
    },
    // 加载图片回显
    loadImageInfo() {
      getImageInfo(this.form.liveImageId).then(res => {
        this.chooeseImg = res.data.url
      })
    },
    // 加载课程消息
    loadCourseMessageList() {
      getCourseMsgList(this.form.courseVideoId).then(res => {
        this.messageList = res.data
      })
    },
    // 切换内容来源
    changeContentSource() {
      if (this.form.contentSource == 1) {
        this.form.courseVideoId = null
      } else if (this.form.contentSource == 0) {
        this.form.courseVideoId = null
        this.form.liveTemplateId = null
      }
    },
    // 选择课程分组
    getCourseSelectList() {
      courseSelectList().then(res => {
        this.courseSelectData = res.data
      })
    },
    // 选择图片
    handleChooeseImg() {
      this.chooeseImageShow = true
    },
    // 选择图片回调
    chooeseSuccess(data) {
      this.form.liveImageId = data.id
      this.chooeseImg = data.url
    },
    // 选择视频
    handleChooeseVideo(str) {
      this.chooeseVideoType = str
      this.showCreate = true
    },
    // 选择视频回调
    chooeseVideoFunc(data) {
      if (data.videoId) {
        if (this.chooeseVideoType === 'warmUpVideoId') {
          this.form.warmUpVideoId = data.id
          const parmar = { videoId: data.videoId }
          this.loadVideoInfoData(parmar, 1)
        } else if (this.chooeseVideoType === 'courseVideoId') {
          this.chooeseVideoId = data.videoId
          this.loadMessageList()
          this.form.courseVideoId = data.id
          const parmar = { videoId: data.videoId }
          this.loadVideoInfoData(parmar, 2)
        }
      }
    },
    // 获取视频信息
    loadVideoInfoData(data, type) {
      getVideoSrc(data).then(res => {
        if (res.code == 200) {
          const videoObj = res.data?.playInfoList.filter(item => item.specification === 'Original')
          if (type == 1) {
            this.videoSrc = videoObj[0].playURL
            this.videoPoster = videoObj[0].coverURL
          }
          if (type == 2) {
            this.courseVideoSrc = videoObj[0].playURL
            this.courseVideoPoster = videoObj[0].coverURL
          }
        }
      })
    },
    // 消息内容下拉列表
    loadMessageList() {
      msgListData(this.chooeseVideoId).then(res => {
        this.messageList = res.rows
      })
    },
    // 直播模版下拉
    getTemplate() {
      searchTemplateList().then(res => {
        this.templateListData = res.data
      })
    },
    // 讲师下拉选择
    changeAnchorId(e) {
      const obj = this.dropdownList.filter(item => item.value === e)
      if (obj.length) {
        this.form.anchorNick = obj[0].label
      }
    },
    // 助教选择确认
    handleConfirm() {
      // console.log(this.checkedNodes, this.checkedKeys, "===checkedKeys")
      this.form.assistantIds = this.checkedKeys
      this.addTeachingAssistant = false
    },
    // 助教选择取消
    handleCancel() {
      this.addTeachingAssistant = false
    },
    handleClose() {
      this.addTeachingAssistant = false
    },
    // 勾选 选择
    handleCheckChange(checkedNodes, checkedKeys) {
      this.checkedNodes = checkedKeys['checkedNodes']
      this.checkedKeys = checkedKeys['checkedKeys']
    },
    /** 讲师 */
    getUserTree1() {
      let parmas
      if (this.form.assistantIds) {
        parmas = {
          userIds: this.form.assistantIds
        }
      } else {
        parmas = {}
      }
      getUserTreeList(parmas).then((response) => {
        this.dropdownList = []
        this.collectType1Nodes(response.data[0])
      })
    },
    /** 助教 */
    getUserTree2() {
      let parmas
      if (this.form.anchorId) {
        parmas = {
          userIds: [this.form.anchorId]
        }
      } else {
        parmas = {}
      }
      getUserTreeList(parmas).then((response) => {
        this.userTreeData = response.data
      })
    },
    // 取消按钮
    cancel() {
      this.$tab.closeOpenPage({ path: '/courseManagement/createLiveRecord' })
      this.$router.push('/courseManagement/liveCourse')
    },
    /** 提交按钮 */
    submitForm: throttle(function() {
      const that = this

      if (that.form.contentSource == 0 && !that.form.courseVideoId) {
        return that.$modal.msgWarning('请选择课程视频！')
      }

      if (that.form.contentSource == 1 && !that.form.liveTemplateId) {
        return that.$modal.msgWarning('请选择直播模版！')
      }

      if(that.form.userBehaviorTagDtos && that.form.userBehaviorTagDtos.length > 0){
        let tagArr = that.form.userBehaviorTagDtos.map(item => item.liveUserBehaviorValue)
        let uniqueTagArr = Array.from(new Set(tagArr))
        if(tagArr.length != uniqueTagArr.length){
          return that.$modal.msgWarning("用户行为不能重复！");
        }
      }

      that.$refs["form"].validate((valid) => {
        if (valid) {
          // 修改、新增、复制都必须要结束时间>开始时间
          const bool = that.isStartBeforeEnd(that.form.startTime, that.form.stopTime)
          if (!bool) {
            that.$modal.msgWarning('开始时间不能大于结束时间！')
            return
          }
          that.submitLoading = true
          if (that.form.id && that.$route.query?.type != 'copy') {
            // 处理标签数据 tagIds 只传ID数组
            let parmas = JSON.parse(JSON.stringify(that.form))
            if(parmas.userBehaviorTagDtos && parmas.userBehaviorTagDtos.length){
              parmas.userBehaviorTagDtos.forEach(item => {
                if(item.tagIds && item.tagIds.length){
                  let arr = item.tagIds.map(i => i.tagId)
                  item.tagIds = arr
                }
              })
            }
            console.log(JSON.parse(JSON.stringify(parmas)), "编辑🔥🔥🔥🔥🔥🔥🔥")
            editeRecordCourseData(parmas).then((res) => {
              if(res.code == 200){
                that.$modal.msgSuccess("修改成功");
                that.$tab.closeOpenPage({ path: '/courseManagement/createLiveRecord' })
                that.$router.push('/courseManagement/liveCourse')
              }
              that.submitLoading = false
            }).catch(() => {
              that.submitLoading = false
            })
          } else {
            // 新增、复制时，开始时间和结束时间都不能小于当前时间前5分钟
            const timeBool1 = that.isValidTime(that.form.startTime)
            const timeBool2 = that.isValidTime(that.form.stopTime)
            if (!timeBool1 || !timeBool2) {
              that.$modal.msgWarning('开始时间和结束时间不能小于当前时间前5分钟！')
              that.submitLoading = false
              return
            }
            // 处理标签数据 tagIds 只传ID数组
            let parmas = JSON.parse(JSON.stringify(that.form))
            if(parmas.userBehaviorTagDtos && parmas.userBehaviorTagDtos.length){
              parmas.userBehaviorTagDtos.forEach(item => {
                if(item.tagIds && item.tagIds.length){
                  let arr = item.tagIds.map(i => i.tagId)
                  item.tagIds = arr
                }
              })
            }
            console.log(JSON.parse(JSON.stringify(parmas)), "新增parmas🔥🔥🔥🔥🔥🔥🔥")
            addRecordCourseData(parmas).then((res) => {
              if(res.code == 200){
                that.$modal.msgSuccess("新增成功");
              }
              that.submitLoading = false
              that.$tab.closeOpenPage({ path: '/courseManagement/createLiveRecord' })
              that.$router.push('/courseManagement/liveCourse')
            }).catch(() => {
              that.submitLoading = false
            })
          }
        }
      })
    }, 1500),
    isStartBeforeEnd(startTime, endTime) {
      // 将字符串转为 Date 对象
      const start = new Date(startTime.replace(/-/g, '/')) // 兼容 Safari
      const end = new Date(endTime.replace(/-/g, '/'))
      // 判断大小
      return start <= end // true 表示合法，false 表示 startTime > endTime
    },
    isValidTime(targetTime) {
      // 转换成 Date 对象，兼容 Safari
      const time = new Date(targetTime.replace(/-/g, '/'))
      const now = new Date()
      // 当前时间减去 5 分钟
      const minAllowedTime = new Date(now.getTime() - 5 * 60 * 1000)
      // 返回 true 表示合法，false 表示不合法
      return time >= minAllowedTime
    },
    // 组装讲师下拉数据
    collectType1Nodes(node) {
      if (node.type === 1) {
        this.dropdownList.push({
          label: node.label,
          value: node.id
        })
      }
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => this.collectType1Nodes(child))
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.tips {
  font-size: 12px;
  color: gray;
  line-height: 20px;
  margin-top: 10px;
}
.titleBox {
  border-left: 3px solid #007aff;
  text-indent: 10px;
  font-weight: bold;
  margin-bottom: 20px;
}
.myBox{
  min-height: 300px;
}
::v-deep .myBox .el-input__inner {
  border: none !important;
  outline: none !important;
}
::v-deep .el-radio-group .el-radio{
  margin-bottom: 0px !important;
}
.tags{
  margin-right: 5px;
  margin-bottom: 5px;
}
</style>

