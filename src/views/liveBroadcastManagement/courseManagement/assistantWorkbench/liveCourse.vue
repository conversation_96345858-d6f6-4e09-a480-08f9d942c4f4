<template>
  <div class="app-container">
    <el-radio-group v-model="queryParams.status" style="margin-bottom: 30px;" @change="handleQuery">
      <el-radio-button :label="1">进行中</el-radio-button>
      <el-radio-button :label="0">待开课</el-radio-button>
      <el-radio-button :label="2">已结束</el-radio-button>
    </el-radio-group>
    <div class="boxSearch">
      <el-form ref="queryForm" :model="queryParams" :inline="true">
        <el-form-item prop="roomName">
          <el-input
            v-model="queryParams.roomName"
            placeholder="请输入"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button plain icon="el-icon-search" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column
        label="直播名称"
        align="center"
        prop="title"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="类型"
        align="center"
        prop="courseTypeName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="讲师"
        align="center"
        prop="anchorNick"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="直播类型"
        align="center"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">web端直播</template>
      </el-table-column>
      <el-table-column label="直播状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag type="primary" :options="dict.type.live_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="text" @click="toLive(scope.row)">进入直播</el-button>
          <el-button type="text" @click="shareFunc(scope.row)">分享给学员</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 分享链接 -->
    <share-link :data-share="dataShare" :visible.sync="showShareLink" />
  </div>
</template>

<script>
import { listConfig } from '@/api/system/config'
import userSelector from '@/views/system/notice/components/UserSelector.vue'
import { getLiveRooms } from '@/api/liveBroadcastManagement/messageTemplate'
import { throttle } from '@/utils';
import ShareLink from '../../components/shareLink.vue';
import { getConfigKey } from "@/api/system/config";
import axios from 'axios'
import { getToken } from '@/utils/auth'
export default {
  name: 'Notice',
  components: { ShareLink, userSelector },
  dicts: ['live_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 公告表格数据
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: 0,
        roomName: null
      },

      dataShare: {},
      showShareLink: false
    }
  },
  created() {
    this.getList()
    this.getLiveUrl()
  },
  methods: {
    // 场控token 请求地址
    getLiveUrl(){
      getConfigKey("live_aui_token_api").then(res => {
        if (res.code === 200) {
          this.baseUrl = res.msg;
        }
      });
      // 获取场控地址
      getConfigKey("live_aui_control_address").then(res => {
        if (res.code === 200) {
          this.liveUrl = res.msg;
        }
      });
    },
    // 进入直播
    toLive(data){
      let token = 'Bearer ' + getToken()
      let path = `${this.liveUrl.split('?')[0]}?roomId=${data.id}&token=${token}`
      window.open(path)
      // axios.post(this.baseUrl,{roomId: data.id,authorization: token},{
      //   headers: {
      //     'Content-Type': 'application/json'
      //   }
      // }).then(res => {
      //   let path = `${this.liveUrl.split('?')[0]}?roomId=${data.id}&token=${res.data.data}`
      //   window.open(path)
      // })
    },
    // 分享给学员
    shareFunc(data) {
      this.dataShare = data
      this.showShareLink = true
    },
    // 待开发
    waiteDevelop() {
      this.$modal.msgWarning('待开发!')
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true
      getLiveRooms(this.queryParams).then(response => {
        this.dataList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 表单重置
    reset() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        status: 0,
        roomName: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    }
  }
}
</script>
<style lang="scss" scoped>
.boxSearch{
  display: flex;
  justify-content: flex-end;
}
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
.pic {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
