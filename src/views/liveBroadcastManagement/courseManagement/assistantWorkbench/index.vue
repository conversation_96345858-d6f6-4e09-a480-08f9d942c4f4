<template>
  <div class="app-container">
    <el-tabs v-model="activeName" style="margin-top: 5px;" @tab-click="handleClick">
      <el-tab-pane label="直播课程" name="liveCourse" />
      <el-tab-pane label="训练营" name="bootcamp" />
    </el-tabs>
    <div class="pageRight">
      <component :is="activeComponent" />
    </div>
  </div>
</template>

<script>
import liveCourse from './liveCourse.vue'
import bootcamp from './bootcamp.vue'
export default {
  name: 'Index',
  components: {
    liveCourse,
    bootcamp
  },
  data() {
    return {
      activeName: 'liveCourse'
    }
  },
  computed: {
    activeComponent() {
      return this.activeName === 'liveCourse' ? 'liveCourse' : 'bootcamp'
    }
  },
  methods: {
    handleClick(tab) {
      this.activeName = tab.name
    }
  }
}
</script>
<style lang="scss" scoped>
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
</style>

