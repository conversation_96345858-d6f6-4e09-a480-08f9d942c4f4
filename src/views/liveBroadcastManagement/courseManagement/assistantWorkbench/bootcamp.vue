<template>
  <div class="app-container">
    <el-radio-group v-model="queryParams.status" style="margin-bottom: 30px;" @change="handleQuery">
      <el-radio-button :label="1">进行中</el-radio-button>
      <el-radio-button :label="0">待开课</el-radio-button>
      <el-radio-button :label="2">已结束</el-radio-button>
    </el-radio-group>
    <div class="boxSearch">
      <el-form :inline="true">
        <el-form-item>
          <el-input
            v-model="queryParams.roomName"
            style="width: 240px;"
            placeholder="请输入"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button plain icon="el-icon-search" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table v-loading="loading" :data="dataList">
      <el-table-column
        label="序号"
        fixed="left"
        align="center"
        type="index"
        width="60"
      />
      <el-table-column
        label="课节"
        fixed="left"
        align="center"
        prop="className"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="营期"
        align="center"
        prop="periodName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="训练营"
        align="center"
        prop="campName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="主讲老师"
        align="center"
        prop="anchor"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="课节类型"
        align="center"
        prop="classTypeName"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="直播类型" align="center" prop="liveTypeName" width="100" />
      <el-table-column
        label="创建人"
        align="center"
        prop="createBy"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="修改人"
        align="center"
        prop="updateBy"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="修改时间"
        align="center"
        prop="updateTime"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" v-if="queryParams.status == 1 || queryParams.status == 2">
        <template slot-scope="scope">
          <el-button type="text" @click="toShare(scope.row)">分享链接</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 分享链接 -->
    <share-link :data-share="dataShare" :visible.sync="showShareLink" />
  </div>
</template>

<script>
import { getAssistantBootcampList } from '@/api/liveBroadcastManagement/messageTemplate'
import ShareLink from '../../components/shareLink.vue'
export default {
  name: 'Bootcamp',
  components: { ShareLink },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 公告表格数据
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roomName: '',
        status: 0
      },
      dataShare: {},
      showShareLink: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 分享
    toShare(data) {
      this.dataShare = data
      this.showShareLink = true
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true
      getAssistantBootcampList(this.queryParams).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    }
  }
}
</script>
<style lang="scss" scoped>
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
.boxSearch{
  display: flex;
  justify-content: flex-end;
}
</style>
