<template>
  <div class="app-container">
    <!-- <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="用户身份" name="first"></el-tab-pane>
      <el-tab-pane label="助教身份" name="second"></el-tab-pane>
    </el-tabs> -->
    <div v-if="activeName == 'first'" class="pageBox">
      <userPage />
    </div>
    <div v-if="activeName == 'second'" class="pageBox">
      <teachAssistant />
    </div>
  </div>
</template>

<script>
import teachAssistant from './teachAssistant.vue'
import userPage from './user.vue'
export default {
  name: 'CourseGroup',
  dicts: ['category_type'],
  components: { teachAssistant, userPage },
  data() {
    return {
      activeName: 'first'
    }
  },
  created() {},
  methods: {}
}
</script>
<style lang="scss" scoped></style>

