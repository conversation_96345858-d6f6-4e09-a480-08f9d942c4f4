<template>
  <div>
    <div style="margin-bottom: 10px;">
      <el-alert title="用于创建规则时选择助教身份，结合消息库，在直播间执行发送，活跃气氛" :closable="false" type="warning" />
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          @click="handleAdd"
        >新增助教身份</el-button>
      </el-col>
    </el-row>

    <div class="userBox">
      <div v-for="(item,index) in 20" :key="index" class="userItem">
        <div class="userNameAndImg">
          <img src="@/assets/logo/logo.png" class="userPhoto" alt="">
          <p class="userName">用户名称1用户名称1用户名称1用户名称1用户名称1</p>
        </div>
        <div class="operationBtn">
          <i class="el-icon-edit editeIcon" @click="handleEdite(item)" />
          <i class="el-icon-delete deleteIcon" @click="handleDelete(item)" />
        </div>
      </div>
    </div>

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="分类名称" prop="categoryName" required>
          <el-input v-model="form.categoryName" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分组类型" prop="categoryType" required>
          <el-select v-model="form.categoryType" placeholder="请选择分组类型">
            <el-option
              v-for="dict in dict.type.category_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否系统类型" required>
          <el-radio-group v-model="form.systemed">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="启用禁用" required>
          <el-radio-group v-model="form.status">
            <el-radio :label="0">启用</el-radio>
            <el-radio :label="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addCourseCategory, deleteCourseCategory, editeCourseCategory, detailCourseCategory, queryCourseCategory } from '@/api/liveBroadcastManagement'
import { debounce } from '@/utils'
export default {
  name: 'CourseGroup',
  dicts: ['category_type'],
  data() {
    return {
      activeName: 'first',

      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      dataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        systemed: null,
        categoryName: null,
        categoryType: null,
        status: null
      },
      // 表单参数
      form: {
        status: 0,
        systemed: true,
        categoryName: null,
        categoryType: null,
        deleted: 0
      },
      // 表单校验
      rules: {
        categoryName: [
          { required: true, message: '分类名称不能为空', trigger: 'blur' }
        ],
        categoryType: [
          { required: true, message: '分组类型不能为空', trigger: 'change' }
        ]
      },
      // 用户选择器
      userSelectorVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleClick() {

    },
    handleEdite() {

    },
    /** 查询公告列表 */
    getList() {
      this.loading = true
      queryCourseCategory(this.queryParams).then(response => {
        this.dataList = response.data.rows
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        status: 0,
        systemed: true,
        categoryName: null,
        categoryType: null,
        deleted: 0
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        systemed: null,
        categoryName: null,
        categoryType: null,
        status: null
      }
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.noticeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加分类'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      detailCourseCategory(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改分类'
      })
    },
    /** 提交按钮 */
    submitForm: debounce(function() {
      const that = this
      that.$refs['form'].validate(valid => {
        if (valid) {
          if (that.form.id !== undefined) {
            editeCourseCategory(that.form).then(response => {
              that.$modal.msgSuccess('修改成功')
              that.open = false
              that.getList()
            })
          } else {
            addCourseCategory(that.form).then(response => {
              that.$modal.msgSuccess('新增成功')
              that.open = false
              that.getList()
            })
          }
        }
      })
    }, 1500),
    /** 删除按钮操作 */
    handleDelete(row) {
      const noticeIds = row.id
      this.$modal.confirm('是否确认删除分类"' + row.categoryName + '"数据项？').then(function() {
        return deleteCourseCategory([noticeIds])
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
.userBox{
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  .userItem{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: calc(25% - 20px);
    height: 60px;
    margin: 0 20px 20px 0;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    border-radius: 30px;
    cursor: pointer;
    overflow: hidden;
    .userNameAndImg{
      display: flex;
      align-items: center;
      .userName{
        width: 160px;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
        align-items: center;
      }
    }
    .operationBtn{
      display: none;
      margin-right: 15px;
      .editeIcon{
        margin-right: 10px;
      }
    }
    .userPhoto{
      width: 36px;
      height: 36px;
      margin-right: 10px;
      margin-left: 15px;
      border-radius: 36px;
    }
  }
  .userItem:hover .operationBtn{
    display: block;
  }
}
</style>

