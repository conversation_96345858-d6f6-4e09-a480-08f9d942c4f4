<template>
  <div class="app-container">
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          :maxlength="32"
          style="width: 240px;"
          placeholder="请输入模板名称"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button plain icon="el-icon-search" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          @click="handleAdd"
        >创建模板</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          text
          @click="toDeletePath"
        >已删除模板日志记录</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column
        label="序号"
        align="center"
        type="index"
        width="60"
      />
      <el-table-column
        label="模版名称"
        align="center"
        prop="templateName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="视频来源"
        align="center"
        prop="appVideoName"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <div>{{ formatName(scope.row.appVideoName) }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="视频时长"
        align="center"
        prop="appVideoDuration"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.appVideoDuration" type="success">无</el-tag>
          <el-tag v-else type="success">{{ formatSeconds(scope.row.appVideoDuration) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="模板状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == 0" type="success">关闭</el-tag>
          <el-tag v-if="scope.row.status == 1" type="danger">开启</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="创建人"
        align="center"
        prop="createBy"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="修改人"
        align="center"
        prop="updateBy"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="修改时间"
        align="center"
        prop="updateTime"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="操作" align="center" class-name="small-padding">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleEdite(scope.row)"
          >编辑</el-button>
          <el-button
            type="text"
            @click="handleCopy(scope.row)"
          >复制</el-button>
          <el-button
            v-if="scope.row.status == 0"
            type="text"
            @click="handleOpenOrClose(scope.row,1)"
          >开启</el-button>
          <el-button
            v-if="scope.row.status == 1"
            type="text"
            @click="handleOpenOrClose(scope.row,0)"
          >关闭</el-button>
          <el-button
            v-if="scope.row.status != 1"
            type="text"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增模版 -->
    <chooeseVideo :visible.sync="showCreate" :is-create-template="true" />

    <!-- 复制模版 -->
    <el-dialog :title="editeOrCopyTitle" :visible.sync="openEdite" width="600px" append-to-body>
      <el-form ref="form" :model="copyForm" :rules="rules" label-width="80px">
        <el-row>
          <el-col>
            <el-form-item label="模版名称" prop="templateName">
              <el-input v-model="copyForm.templateName" placeholder="请输入模版名称" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitLoading" @click="submitEditeForm">确 定</el-button>
        <el-button @click="openEdite = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 选择用户 -->
    <el-drawer
      title="选择用户"
      size="100%"
      :visible.sync="userSelectorVisible"
    >
      <userSelector v-if="open" :default-user-ids="form.userIds" @confirm="handleSelectUser" />
    </el-drawer>
  </div>
</template>

<script>
import { listNotice, getNotice, delNotice, addNotice, updateNotice } from '@/api/system/notice'
import userSelector from '@/views/system/notice/components/UserSelector.vue'
import { queryMessageTemplateList, templateDelete, templateEdite, templateOpenOrClose, templateCopy } from '@/api/liveBroadcastManagement/messageTemplate'
import chooeseVideo from '../../components/chooeseVideo.vue'
import { formatSeconds } from '@/utils/render'
export default {
  name: 'LiveTemplate',
  components: { userSelector, chooeseVideo },
  data() {
    return {
      submitLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      dataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateName: null
      },
      // 表单校验
      rules: {
        templateName: [
          { required: true, message: '请输入模版名称', trigger: 'change' }
        ]
      },
      // 用户选择器
      userSelectorVisible: false,

      // 创建消息
      showCreate: false,
      editeOrCopyTitle: '',
      openEdite: false,
      copyForm: {
        templateName: '',
        idStr: ''
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    formatName(name) {
      let str = ''
      if(name && name.indexOf('.') !== -1){
        str = name.split('.')[0]
      }
      return str
    },
    // 时间格式化
    formatSeconds(data) {
      return formatSeconds(data)
    },
    // 删除记录
    toDeletePath() {
      this.$router.push('/courseManagement/deleteRecord')
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true
      queryMessageTemplateList(this.queryParams).then(response => {
        this.dataList = response.data.rows
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        templateName: null
      }
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.noticeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.showCreate = true
    },
    // 修改
    handleEdite(row) {
      const parmas = {
        'idStr': row.id,
        'templateName': row.templateName,
        'appVideoIdStr': row.appVideoId,
        'pcVideoIdStr': row.pcVideoId,
        'status': row.status
      }
      templateEdite(parmas).then(res => {
        this.$router.push({
          path: '/courseManagement/createTemplate'
        })
        localStorage.setItem('createTemplateId', res.data.id)
      })
    },
    // 复制模版提交
    submitEditeForm() {
      this.submitLoading = true
      templateCopy(this.copyForm).then(res => {
        if (res.code == 200) {
          this.submitLoading = false
          this.$router.push({
            path: '/courseManagement/createTemplate'
          })
          localStorage.setItem('createTemplateId', res.data)
        }
      }).catch(() => {
        this.submitLoading = false
      })
    },
    // 复制
    handleCopy(row) {
      this.editeOrCopyTitle = '复制编辑模版名称'
      this.copyForm.templateName = row.templateName
      this.copyForm.idStr = row.id
      this.openEdite = true
    },
    // 开启或者关闭
    handleOpenOrClose(row, status) {
      const that = this
      if (status == 1) {
        that.$modal.confirm('是否确认开启？').then(function() {
          const parmas = {
            idStr: row.id,
            openStatus: status
          }
          templateOpenOrClose(parmas).then(res => {
            if (res.code == 200) {
              that.$modal.msgSuccess('开启操作成功')
              that.getList()
            }
          })
        })
      } else {
        that.$modal.confirm('是否确认关闭？').then(function() {
          const parmas = {
            idStr: row.id,
            openStatus: status
          }
          templateOpenOrClose(parmas).then(res => {
            if (res.code == 200) {
              that.$modal.msgSuccess('关闭操作成功')
              that.getList()
            }
          })
        })
      }
    },
    /** 删除操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除名为"' + row.templateName + '"的直播模版？').then(function() {
        return templateDelete([row.id])
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    // 选择用户
    handleSelectUser(res) {
      this.userSelectorVisible = false
      this.form.userIds = res.userIds
      this.form.deptIds = res.deptIds
    }
  }
}
</script>
<style lang="scss" scoped>
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
</style>
