<template>
  <div class="app-container">
    <el-tag type="success" closable>在直播消息管理板块操作时，请仔细核查添加内容，确保其源自历史直播中已存在的真实消息，杜绝网络水军等非法营销行为。关于规范私域直播秩序的公告</el-tag>
    <el-tabs v-model="activeName" style="margin-top: 5px;" @tab-click="handleClick">
      <el-tab-pane label="直播模版" name="liveTemplate" />
      <!-- <el-tab-pane label="视频直播消息内容" name="liveMessage"></el-tab-pane> -->
    </el-tabs>
    <div class="pageRight">
      <component :is="activeComponent" />
    </div>
  </div>
</template>

<script>
import LiveTemplate from './liveTemplate.vue'
import LiveMessage from './liveMessage.vue'
export default {
  name: 'Index',
  components: {
    LiveTemplate,
    LiveMessage
  },
  data() {
    return {
      activeName: 'liveTemplate'
    }
  },
  computed: {
    activeComponent() {
      return this.activeName === 'liveTemplate' ? 'LiveTemplate' : 'LiveMessage'
    }
  },
  methods: {
    handleClick(tab) {
      this.activeName = tab.name
    }
  }
}
</script>
<style lang="scss" scoped>
.selected-tip {
  margin-left: 10px;
  color: #909399;
}
</style>

