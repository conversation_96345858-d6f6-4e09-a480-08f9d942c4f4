<template>
  <div>
    <el-dialog title="从其他营期选择" :visible.sync="visible" width="70%" :close-on-click-modal="false" @close="handleClose">
      <div>
        <el-form ref="queryForm" :model="queryParams" :inline="true">
          <el-form-item label="训练营">
            <el-select
              v-model="queryCampList.campId"
              placeholder="请选择训练营"
              style="width: 300px"
              @change="loadBootList"
            >
              <el-option
                v-for="(item, index) in bootcampDataList"
                :key="index"
                :label="item.campName"
                :value="item.campId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="营期">
            <el-select
              v-model="queryParams.periodId"
              placeholder="请选择营期"
              clearable
            >
              <el-option
                v-for="(item, index) in campDataList"
                :key="index"
                :label="item.periodName"
                :value="item.periodId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="课程名称">
            <el-input
              v-model="queryParams.className"
              placeholder="请输入课程名称"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
        <el-table ref="singleSelectTable" v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" align="center" :selectable="handleSelectable" width="55" />
          <el-table-column
            label="课程名称"
            align="center"
            prop="className"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="课程类型"
            align="center"
            prop="classTypeName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="上架状态"
            align="center"
            prop="duration"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <el-tag v-if="!scope.row.duration" type="success">无</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="更新时间"
            align="center"
            prop="updateTime"
            :show-overflow-tooltip="true"
          />
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="loadCourseListData"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
import { bootcampSelectData, campSelectData, searchCourse, copyCourse } from '@/api/liveBroadcastManagement/bootcamp'
import { throttle } from '@/utils'
export default {
  name: 'ChooeseCourse',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    chapterId: {
      type: String,
      required: ''
    }
  },
  data() {
    return {
      selectedRows: [],
      dataList: [],
      loading: false,
      total: 0,
      queryParams: {
        className: null,
        periodId: null,
        pageSize: 10,
        pageNum: 1
      },

      videoSrc: '',
      posterImg: '',
      videoVisible: false,

      campDataList: [],
      bootcampDataList: [],
      queryCampList: {
        campId: '',
        currentPeriodId: ''
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadSelectData()
      }
    }
  },
  methods: {
    // 加载训练营下拉数据
    loadSelectData() {
      bootcampSelectData().then(res => {
        if (res.code == 200) {
          this.bootcampDataList = res.data
        }
      })
    },
    // 加载营期下拉数据
    loadBootList() {
      this.queryCampList.currentPeriodId = this.$route.query.id
      campSelectData(this.queryCampList).then(res => {
        if (res.code == 200) {
          this.campDataList = res.data
        }
      })
    },
    // 时间格式化
    formatSeconds(data) {
      return formatSeconds(data)
    },
    // 加载数据
    loadCourseListData() {
      this.loading = true
      searchCourse(this.queryParams).then((response) => {
        this.dataList = response.rows || []
        this.total = response.total
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.loadCourseListData()
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    handleCancel() {
      this.$emit('update:visible', false)
    },
    handleConfirm: throttle(function() {
      const that = this
      if (that.selectedRows.length) {
        const parmas = {
          targetPeriodId: that.$route.query.id,
          targetChapterId: that.chapterId,
          sourceClassId: that.selectedRows[0].classId
        }
        copyCourse(parmas).then(res => {
          if (res.code === 200) {
            that.$message.success('复制课程成功')
            that.$emit('refresh',true)
            that.$emit('update:visible', false)
          }
        })
      } else {
        that.$message.warning('请先选择并勾选课程')
      }
    }, 1500),
    // 选择表格行
    handleSelectionChange(selection) {
      if (selection.length > 1) {
        // 只保留最后一个选中的
        const lastSelected = selection[selection.length - 1]
        // 清空所有勾选
        this.$refs.singleSelectTable.clearSelection()
        // 勾选最后一个
        this.$refs.singleSelectTable.toggleRowSelection(lastSelected, true)
        // 手动更新 selectedRows，只保留一个
        this.selectedRows = [lastSelected]
      } else {
        this.selectedRows = selection
      }
    },
    // 勾选或者不勾选
    handleSelectable(row) {
      return row.convertFlag !== 1
    }
  }
}
</script>

<style lang="scss" scoped>
.tips{
  margin-top: 10px;
  margin-bottom: 20px;
  p{
    margin: 0;
    font-size: 12px;
    line-height: 20px;
  }
}
::v-deep .el-table__header-wrapper .el-checkbox {
   display: none;
}
</style>
