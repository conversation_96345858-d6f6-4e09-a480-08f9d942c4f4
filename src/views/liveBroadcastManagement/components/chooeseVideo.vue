<template>
  <div>
    <el-dialog title="视频库" :visible.sync="visible" width="70%" :close-on-click-modal="false" @close="handleClose">
      <div>
        <el-form ref="queryForm" :model="queryParams" :inline="true">
          <el-form-item label="视频分组">
            <el-select
              v-model="queryParams.fileCategory"
              placeholder="请选择视频分组"
              clearable
            >
              <el-option
                v-for="(item, index) in categoryList"
                :key="index"
                :label="item.cateName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="视频名称">
            <el-input
              v-model="queryParams.fileName"
              placeholder="请输入视频名称"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="tips">
          <p>1、直播模版的课程视频，只能使用推流专用视频。若所需视频没有生成推流专用视频，请先在 视频库 中转码生成。</p>
          <p>2、文件最大不超过20G，支持mp4、avi、wmv、mov、flv、rmvb、3gp、m4v、mkv格式</p>
          <p>3、模板引用的推流专用视频生成后，方可开启使用。转码生成时长受视频大小影响，请尽可能早的提前生成，否则可能会影响模板使用与开播</p>
          <p>4、视频上传进度可在右下角上传任务浮框中查询，上传完成后，请点击刷新下方列表</p>
        </div>
        <el-table ref="singleSelectTable" v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" align="center" :selectable="handleSelectable" width="55" />
          <el-table-column
            label="视频名称"
            align="center"
            prop="title"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="推流专用视频"
            align="center"
            prop="appVideoName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="视频时长"
            align="center"
            prop="duration"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <el-tag v-if="!scope.row.duration" type="success">无</el-tag>
              <el-tag v-else type="success">{{ formatSeconds(scope.row.duration) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="视频分组" align="center" prop="cateName">
            <template #default="scope">
              <span>{{ scope.row.cateName || '默认分组' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="上传时间"
            align="center"
            prop="createTime"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="操作" align="center" class-name="small-padding">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-view" @click="handleView(scope.row)">预览</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="loadVideoListData"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 视频预览 -->
    <el-dialog
      :visible.sync="videoVisible"
      title="视频预览"
      width="800px"
      top="5vh"
      append-to-body
      @close="closeVideo"
    >
      <video
        ref="videoRef"
        :poster="posterImg"
        class="preview-video"
        style="width: 100%;max-height: 700px"
        :src="videoSrc"
        controls
      />
    </el-dialog>
  </div>

</template>

<script>
import { queryVideoList, queryGroup, createTemplate, getVideoSrc } from '@/api/sourceMaterial/index'
import { formatSeconds } from '@/utils/render'
import { throttle } from '@/utils'
export default {
  name: 'CreateVideo',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    // 是否是新增模版
    isCreateTemplate: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      selectedRows: [],
      categoryList: [],
      dataList: [],
      loading: true,
      total: 0,
      queryParams: {
        fileName: null,
        fileCategory: null,
        pageSize: 10,
        pageNum: 1
      },
      videoData: {},
      showVideo: false,

      videoSrc: '',
      posterImg: '',
      videoVisible: false
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadVideoListData()
        this.loadVideoCategory()
      }
    }
  },
  methods: {
    // 时间格式化
    formatSeconds(data) {
      return formatSeconds(data)
    },
    // 关闭视频预览
    closeVideo() {
      if (this.$refs.videoRef && this.$refs.videoRef.pause) {
        this.$refs.videoRef.pause()
      }
    },
    // 预览视频
    handleView(row) {
      getVideoSrc({ videoId: row.videoId }).then(res => {
        if (res.code == 200) {
          const videoObj = res.data?.playInfoList.filter(item => item.specification === 'Original')
          this.videoSrc = videoObj[0].playURL
          this.posterImg = res.data?.videoBase?.coverURL
          this.videoVisible = true
        }
      })
    },
    // 加载数据
    loadVideoListData() {
      this.loading = true
      queryVideoList(this.queryParams).then((response) => {
        this.dataList = response.rows || []
        this.total = response.total
        this.loading = false
      })
    },
    // 加载视频分组数据
    loadVideoCategory() {
      const parmas = {
        cateName: null
      }
      queryGroup(parmas).then((response) => {
        this.categoryList = response.data || []
      })
    },
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.loadVideoListData()
    },
    // 重置
    resetQuery() {
      this.queryParams.fileCategory = null
      this.queryParams.fileName = null
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.loadVideoListData()
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    handleCancel() {
      this.$emit('update:visible', false)
    },
    handleConfirm: throttle(function() {
      const that = this
      if (that.selectedRows.length) {
        if (that.isCreateTemplate) {
          // 创建模版
          let name = ''
          if (that.selectedRows[0].title && that.selectedRows[0].title.indexOf('.') != -1) {
            name = that.selectedRows[0].title.split('.')[0]
          } else {
            name = that.selectedRows[0].title
          }
          const parmas = {
            'templateName': name,
            'appVideoIdStr': that.selectedRows[0].id,
            'status': 1
          }
          createTemplate(parmas).then(res => {
            if (res.code === 200) {
              that.$message.success('创建模板成功')
              that.$router.push({
                path: '/courseManagement/createTemplate'
              })
              localStorage.setItem('createTemplateId', res.data)
              that.$emit('update:visible', false)
            }
          })
        } else {
          // 新增录播直播选择课程
          that.$emit('chooeseVideo', that.selectedRows[0])
          that.$emit('update:visible', false)
        }
      } else {
        that.$message.warning('请先勾选视频')
      }
    }, 1500),
    // 选择表格行
    handleSelectionChange(selection) {
      if (selection.length > 1) {
        // 只保留最后一个选中的
        const lastSelected = selection[selection.length - 1]
        // 清空所有勾选
        this.$refs.singleSelectTable.clearSelection()
        // 勾选最后一个
        this.$refs.singleSelectTable.toggleRowSelection(lastSelected, true)
        // 手动更新 selectedRows，只保留一个
        this.selectedRows = [lastSelected]
      } else {
        this.selectedRows = selection
      }
    },
    // 勾选或者不勾选
    handleSelectable(row) {
      return row.convertFlag !== 1
    }
  }
}
</script>

<style lang="scss" scoped>
.tips{
  margin-top: 10px;
  margin-bottom: 20px;
  p{
    margin: 0;
    font-size: 12px;
    line-height: 20px;
  }
}
::v-deep .el-table__header-wrapper .el-checkbox {
   display: none;
}
</style>
