<template>
  <div class="time-adjuster">
    <el-button size="small" :disabled="localTime <= 0" @click="decreaseTime">-</el-button>
    <div class="time-display">{{ formattedTime }}</div>
    <el-button size="small" :disabled="localTime >= maxcurrentTime" @click="increaseTime">+</el-button>
  </div>
</template>

<script>
export default {
  name: 'TimeAdjuster',
  props: {
    currentTime: {
      type: Number,
      default: 0
    },
    maxcurrentTime: {
      type: Number
    }
  },
  data() {
    return {
      localTime: 0,
      formattedTime: '00:00:00'
    }
  },
  watch: {
    currentTime: {
      immediate: true,
      handler(newVal) {
        this.localTime = newVal
        this.updateFormattedTime()
      }
    },
    localTime() {
      this.updateFormattedTime()
      this.$emit('timeChange', this.localTime)
    }
  },
  methods: {
    increaseTime() {
      if (this.localTime + 10 <= this.maxcurrentTime) {
        this.localTime += 10
      }
    },
    decreaseTime() {
      if (this.localTime - 10 >= 0) {
        this.localTime -= 10
      }
    },
    updateFormattedTime() {
      const hours = String(Math.floor(this.localTime / 3600)).padStart(2, '0')
      const minutes = String(Math.floor((this.localTime % 3600) / 60)).padStart(2, '0')
      const seconds = String(this.localTime % 60).padStart(2, '0')
      this.formattedTime = `${hours}:${minutes}:${seconds}`
    }
  }
}
</script>
<style scoped>
.time-adjuster {
  display: flex;
  align-items: center;
  border-radius: 4px;
}
.time-display {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
}
</style>
