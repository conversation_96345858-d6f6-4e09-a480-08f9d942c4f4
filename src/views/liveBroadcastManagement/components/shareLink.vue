<template>
  <div>
    <el-dialog title="分享给学员" :visible.sync="showShare" width="650px" append-to-body :close-on-click-modal="false" @close="handleClose">
      <el-form ref="shareForm" :model="shareForm" :rules="rules" label-width="100px">
        <el-form-item label="企业微信" prop="cropId" required>
          <el-select v-model="shareForm.cropId" placeholder="请选择企业微信" style="width: 100%">
            <el-option
              v-for="(item,index) in weComListData"
              :key="index"
              :label="item.corpName"
              :value="item.corpId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="链接域名" prop="domain" required>
          <div style="display:flex;">
            <div style="display:flex;">
              <el-input disabled value="https://" style="width: 30%" />
              <el-select v-model="shareForm.domain" placeholder="请选择链接域名" style="width: 100%">
                <el-option
                  v-for="(item,index) in domainListData"
                  :key="index"
                  :label="item.domainUrl"
                  :value="item.domainUrl"
                />
              </el-select>
            </div>
            <el-button type="text" style="margin-left: 20px" @click="createLine">生成链接</el-button>
          </div>
        </el-form-item>
        <el-form-item v-if="shareForm.longUrl" label="链接地址">
          <div style="display:flex;">
            <el-input disabled :value="shareForm.longUrl" style="width: 100%" />
            <el-button type="primary" style="margin-left: 20px" @click="copyUrl">一键复制</el-button>
          </div>
        </el-form-item>
        <el-form-item v-if="shareForm.qrCode" label="二维码">
          <div style="display:flex;align-items: flex-start">
            <div>
              <el-image
                class="pic"
                :src="shareForm.qrCode"
              />
            </div>
            <el-button type="primary" style="margin-left: 20px" @click="download">下载</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { throttle } from '@/utils'
import { createShareLink, getDomainList, getWeComData } from '@/api/liveBroadcastManagement/messageTemplate'
export default {
  name: 'ShareLink',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    dataShare: {
      type: Object,
      required: {}
    }
  },
  data() {
    return {
      // 表单校验
      rules: {
        cropId: [
          { required: true, message: '企业微信不能为空', trigger: ['blur', 'change'] }
        ],
        domain: [
          { required: true, message: '链接域名不能为空', trigger: ['blur', 'change'] }
        ]
      },
      domainListData: [],
      weComListData: [],
      showShare: false,
      shareForm: {
        roomId: null,
        domain: null,
        cropId: null,
        longUrl: null,
        qrCode: null
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadDomainList()
        this.loadWeComList()
        this.shareForm.roomId = this.dataShare.id
        this.shareForm.longUrl = null
        this.shareForm.qrCode = null
        this.shareForm.domain = null
        this.shareForm.cropId = null
        if (this.dataShare['type'] == 'CAMP_COURSE_LIVE') {
          this.shareForm['type'] = this.dataShare.type
          this.shareForm['classId'] = this.dataShare.classId
          this.shareForm['periodId'] = this.dataShare.periodId
        }
        if (this.dataShare.roomId) {
          this.shareForm['roomId'] = this.dataShare.roomId
        }
        this.$nextTick(() => {
          if (this.$refs.shareForm) {
            this.$refs.shareForm.resetFields()
          }
        })
        this.showShare = val
      }
    }
  },
  methods: {
    // 关闭分享弹窗
    handleClose() {
      this.localVisible = false
      this.$emit('update:visible', false)
    },
    // 域名下拉数据
    loadDomainList() {
      getDomainList().then(res => {
        if (res.code == 200) {
          this.domainListData = res.data
        }
      })
    },
    // 企业微信下拉数据
    loadWeComList() {
      getWeComData().then(res => {
        if (res.code == 200) {
          this.weComListData = res.data
        }
      })
    },
    // 复制链接
    copyUrl() {
      this.copyText(this.shareForm.longUrl)
    },
    // 下载图片
    download() {
      this.downloadBase64Image(this.shareForm.qrCode, '分享图片.png')
    },
    // 生成链接
    createLine: throttle(function() {
      const that = this
      that.$refs['shareForm'].validate(valid => {
        if (valid) {
          const params = {
            ...that.shareForm
          }
          params.domain = `https://${params.domain}live/room`
          console.log(params, '=================🔥🔥🔥')
          createShareLink(params).then(res => {
            if (res.code == 200) {
              that.shareForm['longUrl'] = res.data.longUrl
              that.shareForm['qrCode'] = res.data.qrCode
            }
          })
        }
      })
    }, 1000),
    copyText(text) {
      const textarea = document.createElement('textarea')
      textarea.value = text
      document.body.appendChild(textarea)
      textarea.select() // 选中文本
      document.execCommand('copy') // 执行复制
      document.body.removeChild(textarea)
      this.$modal.msgSuccess('复制成功!')
    },
    base64ToBlob(base64Data, contentType = '') {
      // 去掉 data URL 前缀（如果有的话）
      const parts = base64Data.split(',')
      const rawData = parts.length > 1 ? parts[1] : parts[0]
      // atob 解码
      const byteCharacters = atob(rawData.replace(/\s/g, ''))
      // 转换为字节数组
      const byteNumbers = new Array(byteCharacters.length)
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i)
      }
      const byteArray = new Uint8Array(byteNumbers)
      return new Blob([byteArray], { type: contentType })
    },
    downloadBase64Image(base64Data, filename) {
      const blob = this.base64ToBlob(base64Data)
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = filename || 'download.png'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href) // 释放内存
    }
  }
}
</script>

<style lang="scss" scoped>
.pic {
  width: 140px;
  height: 140px;
  display: block;
}
</style>
