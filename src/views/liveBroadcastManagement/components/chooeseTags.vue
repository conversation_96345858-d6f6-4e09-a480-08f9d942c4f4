<template>
  <div>
    <el-dialog
      title="选择标签"
      :visible.sync="visible"
      width="70%"
      :close-on-click-modal="false"
      @close="handleCancel"
    >
      <div class="tagsContainer">
        <div v-if="chooeseItems.length" class="tagsName">所选标签</div>
        <div v-if="chooeseItems.length" class="chooeseItem">
          <el-tag v-for="(item,index) in chooeseItems" :key="index" type="success" :closable="false" class="tags">{{ item.tagName }}</el-tag>
        </div>
        <div v-for="(item,index) in dataList" :key="index">
          <div class="tagsName">{{ item.tagName }}</div>
          <div>
            <el-tag v-for="(items,indexs) in item.list" :key="indexs" :type="items.chooeseBool ? 'success':'info'" :closable="false" class="tags" @click="tagClick(items)">{{ items.tagName }}</el-tag>
          </div>
        </div>
        <div class="emptyBox">
          <el-empty v-show="!dataList.length" description="暂无数据..." />
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { loadTagsData } from '@/api/liveBroadcastManagement/bootcamp'
export default {
  name: 'ChooeseImage',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    defaultChooese: {
      type: Array
    }
  },
  data() {
    return {
      selectedRows: [],
      categoryList: [],
      dataList: [],
      loading: true,
      total: 0,
      queryParams: {
        fileName: null,
        fileCategory: null,
        pageSize: 10,
        pageNum: 1
      },
      videoData: {},
      showVideo: false,
      selectedRadio: null
    }
  },
  computed: {
    chooeseItems() {
      const arr = []
      this.dataList.forEach(item => {
        item.list.forEach(itemk => {
          if (itemk.chooeseBool) {
            arr.push(itemk)
          }
        })
      })
      return arr
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadTagsListData()
      }
    }
  },
  methods: {
    // 点击标签
    tagClick(data) {
      const tags = JSON.parse(JSON.stringify(this.dataList))
      tags.forEach(item => {
        item.list.forEach(itemk => {
          if (itemk.tagId === data.tagId) {
            itemk['chooeseBool'] = !itemk['chooeseBool']
          }
        })
      })
      this.dataList = tags
    },
    // 加载数据
    loadTagsListData() {
      this.loading = true
      loadTagsData(this.queryParams).then((res) => {
        this.dataList = Object.entries(res.data).map(([key, value]) => ({
          tagName: key,
          list: value
        }))
        this.dataList.forEach(item => {
          item.list.forEach(itemk => {
            itemk['chooeseBool'] = false
          })
        })
        // 默认选择 回显
        if (this.defaultChooese.length) {
          this.dataList.forEach(item => {
            item.list.forEach(itemk => {
              // 默认先置为 false
              itemk.chooeseBool = false
              // 判断是否在 defaultChooese 里
              const matched = this.defaultChooese.some(itemc => itemc.tagId === itemk.tagId)
              if (matched) {
                itemk.chooeseBool = true
              }
            })
          })
        }

        this.loading = false
      })
    },
    handleCancel() {
      this.$emit('update:visible', false)
    },
    handleConfirm() {
      if (this.chooeseItems.length) {
        this.$emit('chooeseTagsSuccess', this.chooeseItems)
        this.$emit('update:visible', false)
      } else {
        this.$message.warning('请先选择标签')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tagsContainer{
  max-height: 500px;
  padding: 0 20px;
  overflow-y: scroll;
}
.chooeseItem{
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9e9eb;
}
.tagsName{
  font-size: 16px;
  font-weight: bold;
  color: #000;
  margin-bottom: 20px;
}
.tags{
  margin-right: 10px;
  margin-bottom: 10px;
  cursor: pointer;
}
.emptyBox{
  width: 100%;
  display: flex;
  justify-content: center;
}
</style>
