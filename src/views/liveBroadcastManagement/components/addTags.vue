<template>
  <div>
    <el-dialog
      title="新增标签规则"
      :visible="visible"
      width="30%"
      @close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form ref="tagsForm" :model="tagsForm" :rules="rules" label-width="120px">
        <el-form-item label="用户行为" prop="liveUserBehaviorValue" required>
          <el-select v-model="tagsForm.liveUserBehaviorValue" placeholder="请选择用户行为" clearable style="width: 100%">
            <el-option
              v-for="dict in dict.type.live_user_behavior"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
              <span> {{ dict.label }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="tagsForm.liveUserBehaviorValue" :label="getLabelName(dict.type.live_user_behavior,tagsForm.liveUserBehaviorValue)" prop="templateName" required>
          {{getLabelContent(dict.type.live_user_behavior,tagsForm.liveUserBehaviorValue)}}
        </el-form-item>

        <el-form-item label="标签">
          <div>
            <div class="tagsBox">
              <el-tag size="large" v-for="(item,index) in tagsForm.listTag" :key="index" type="success" :closable="false" class="tags">{{ item.tagName }}</el-tag>
              <el-button icon="el-icon-plus" size="small" type="primary" @click="handleChooeseTags">选择标签</el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 选择标签 -->
    <chooeseTags :visible.sync="chooeseTagsShow" :defaultChooese="tagsForm.tagIds" @chooeseTagsSuccess="chooeseTagsSuccess" />
  </div>

</template>

<script>
import chooeseTags from '@/views/liveBroadcastManagement/components/chooeseTags.vue'
import { throttle } from '@/utils'
export default {
  name: "createVideo",
  dicts: ["live_user_behavior"],
  components:{ chooeseTags },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    defaultData: {
      type: Object
    },
  },
  data() {
    return {
      tagsForm: {
        liveUserBehaviorValue: '',//用户行为
        tagIds: [],//标签  
      },
      rules: {
        liveUserBehaviorValue: [
          { required: true, message: "请选择用户行为", trigger: "change" },
        ]
      },
      chooeseTagsShow: false,//选择标签弹窗
      listLoading: false,
      list: [],
    };
  },
  watch: {
    visible(val) {
      if (val) {
        if(this.defaultData){
          this.tagsForm = {
            liveUserBehaviorValue: this.defaultData.liveUserBehaviorValue || '',//用户行为
            tagIds: this.defaultData.tagIds || [],//标签  
            listTag: this.defaultData.tagIds || [],
          }
        }else{
          this.tagsForm = {
            liveUserBehaviorValue: '',//用户行为
            tagIds: [],//标签  
            listTag: [],
          }
        }
        this.$nextTick(() => {
          if (this.$refs.tagsForm) {
            this.$refs.tagsForm.clearValidate();
          }
        });
      }
    },
  },
  methods: {
    // 表单label
    getLabelName(arr,value){
      let obj = arr.filter(item => item.value === value)
      let str = ''
      if(obj.length){
        str = obj[0].label
      }
      return str
    },
    // 表单content
    getLabelContent(arr,value){
      let obj = arr.filter(item => item.value === value)
      let str = ''
      if(obj.length){
        str = '已'+obj[0].label.split('状态')[0]
      }
      return str
    },
    handleClose() {
      this.$emit("update:visible", false);
    },
    handleCancel() {
      this.$emit("update:visible", false);
    },
    // 选择标签 点击成功
    chooeseTagsSuccess(data){
      let arr = []
      data.forEach(item => {
        let obj = {
          tagId: item.tagId,
          tagName: item.tagName,
        }
        arr.push(obj)
      })
      this.tagsForm.listTag = arr
    },
    // 选择标签
    handleChooeseTags() {
      this.chooeseTagsShow = true
    },
    // 确认
    handleConfirm: throttle( function() {
      if(!this.tagsForm.liveUserBehaviorValue){
        return this.$modal.msgWarning("请选择用户行为！");
      }
      if(!this.tagsForm.listTag.length){
        return this.$modal.msgWarning("请选择标签！");
      }
      let obj = {
        liveUserBehaviorValue: this.tagsForm.liveUserBehaviorValue,
        tagIds: this.tagsForm.listTag,
      }
      if(this.defaultData.index){
        obj['index'] = this.defaultData.index
      }
      this.$emit("addSuccess", obj)
      this.$emit("update:visible", false);
    },1500),
  },
};
</script>

<style lang="scss" scoped>
.tagsBox{
  display: flex;
  flex-wrap: wrap;
}
.tags{
  margin-right: 10px;
  cursor: pointer;
}
</style>
