<template>
  <div>
    <el-dialog title="添加学员" :visible.sync="visible" width="40%" @close="handleClose" :close-on-click-modal="false">
      <div>
        <el-table v-loading="loading" :data="dataList" ref="singleSelectTable" @selection-change="handleSelectionChange">
          <el-table-column type="selection" align="center" :selectable="handleSelectable" width="55" />
          <el-table-column
            label="头像"
            align="center"
            prop="avatar"
          >
            <template slot-scope="scope">
              <el-avatar :src="scope.row.avatar" :size="40" />
            </template>
          </el-table-column>
          <el-table-column
            label="名称"
            align="center"
            prop="userName"
            :show-overflow-tooltip="true"
          />
        </el-table>
  
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="loadListData"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
import { customerList, addStudents } from '@/api/liveBroadcastManagement/bootcamp'
import { throttle } from '@/utils'
export default {
  name: "createVideo",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    periodId: {
      type: String,
    },
  },
  data() {
    return {
      selectedRows:[],
      dataList: [],
      loading: true,
      total: 0,
      queryParams:{
        pageSize: 10,
        pageNum: 1,
      },
      videoSrc: '',
      posterImg: '',
      videoVisible: false,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadListData();
      }
    },
  },
  methods: {
    // 加载数据
    loadListData(){
      this.loading = true;
      customerList(this.queryParams).then((res) => {
        this.dataList = res.rows || [];
        this.total = res.total;
        this.loading = false;
      });
    },
    // 搜索
    handleQuery(){
      this.queryParams.pageNum = 1
      this.loadListData()
    },
    // 重置
    resetQuery(){
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.loadListData()
    },
    handleClose() {
      this.$emit("update:visible", false);
    },
    handleCancel() {
      this.$emit("update:visible", false);
    },
    handleConfirm: throttle( function() {
      let that = this
      if(that.selectedRows.length){
        let ids = []
        that.selectedRows.forEach(item => {
          ids.push(item.id)
        })
        let parmas = {
          periodId: that.$route.query?.id,
          customerIds: ids
        }
        addStudents(parmas).then(res => {
          if(res.code === 200){
            that.$message.success("添加学员成功");
            that.$emit("addSuccess",true)
            that.$emit("update:visible", false);
          }
        })
      }else{
        that.$message.warning("请先勾选学员");
      }
    },1500),
    // 选择表格行
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    // 勾选或者不勾选
    handleSelectable(row) {
      return row.convertFlag !== 1
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
