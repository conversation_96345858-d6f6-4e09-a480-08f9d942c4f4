<template>
  <div>
    <!-- 添加评论消息 -->
    <el-dialog
      :title="title"
      :visible.sync="localVisible"
      width="45%"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div v-if="operationType == 3" class="replayBox">
        <div>{{ replyData.senderType == 1 ? '学员消息':'助教消息' }}({{ replyData.senderName }}): {{ replyData.messageContent }}</div>
      </div>
      <el-form :key="dialogKey" ref="msgForm" :model="msgForm" :rules="rules" label-width="100px">
        <el-form-item label="发送时间" prop="sendTime" required>
          <el-time-picker
            v-model="msgForm.sendTime"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            placeholder="请选择消息发送时间"
          />
        </el-form-item>
        <!-- 如果是回复消息 则只需要选择助教身份就行-->
        <el-form-item v-if="operationType != 3" label="发送者类型" prop="senderType" required>
          <el-radio-group v-model="msgForm.senderType">
            <el-radio :label="1">学员</el-radio>
            <el-radio :label="2">助教</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 回复消息只能是文本 且 学员不能发送图片-->
        <el-form-item label="消息类型" prop="messageContentType" required>
          <el-radio-group v-model="msgForm.messageContentType">
            <el-radio :label="1">文本</el-radio>
            <!-- <el-radio :label="2" v-if="msgForm.senderType != 1 && !addReplyData.id">图片</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="msgForm.messageContentType == 1"
          label="文本"
          prop="messageContent"
          required
        >
          <el-input
            v-model="msgForm.messageContent"
            type="textarea"
            :rows="6"
            :maxlength="512"
            show-word-limit
            placeholder="请输入内容"
          />
        </el-form-item>
        <!-- <el-form-item
          v-if="msgForm.messageContentType === 2"
          label="图片"
          prop="imageUrl"
          required
        >
          <div class="boxImage">
            <el-image
              v-if="msgForm.imageUrl"
              class="pic"
              :src="msgForm.imageUrl"
              fit="contain"
            />
            <el-button
              type="text"
              icon="el-icon-plus"
              @click="chooeseImageShow = true"
            >选择图片</el-button>
          </div>
        </el-form-item> -->
        <el-form-item :label="msgForm.senderType === 1 ? '学员身份' : '助教身份'">
          <el-button type="primary" @click="chooeseUser">选择身份</el-button>
          <div v-if="msgForm.senderId">所选{{ msgForm.senderType === 1 ? '学员' : '助教' }}名称:
            <el-tag closable @close="deleteChooese">{{ msgForm.senderName }}</el-tag>
          </div>
          <div class="tips">
            *
            可以选择用户身份库的身份，选择后昵称可修改，若不选择，系统将自动分配一个用户身份。
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 选择身份 -->
    <chooeseIdentity :visible.sync="showChooeseIdentity" :type="userType" @chooeseUserInfo="chooeseUserInfo" @chooeseAssistant="chooeseAssistant" />
    <!-- 选择图片 -->
    <chooeseImage :visible.sync="chooeseImageShow" :is-create-template="true" @chooeseImageSuccess="chooeseSuccess" />
  </div>
</template>

<script>
import { createMessage, editeMessage } from '@/api/liveBroadcastManagement/messageTemplate'
import chooeseIdentity from '@/views/liveBroadcastManagement/components/chooeseIdentity.vue'
import chooeseImage from '@/views/liveBroadcastManagement/components/chooeseImage.vue'
import { throttle } from '@/utils'
export default {
  name: 'AddCommentMsg',
  components: { chooeseIdentity, chooeseImage },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    chooeseVideoId: {
      type: [Number, String]
    },
    chooeseTime: {
      type: [Number, String]
    },
    operationType: {
      type: Number
    }
  },
  data() {
    return {
      submitLoading: false,
      localVisible: this.visible,
      title: '',
      chooeseImageShow: false,
      dataList: [],
      loading: true,
      total: 0,
      queryParams: {
        categoryName: null,
        status: null,
        pageSize: 10,
        pageNum: 1
      },
      msgForm: {
        templateIdStr: this.chooeseVideoId,
        messageType: 1, // 评论消息
        messageContentType: 1,
        messageContent: '',
        senderType: 1,
        sendTime: '',
        imageUrl: ''
      },
      showChooeseIdentity: false,
      rules: {
        sendTime: [
          { required: true, message: '请选择消息发送时间', trigger: 'change' }
        ],
        senderType: [
          { required: true, message: '请选择发送者类型', trigger: 'change' }
        ],
        messageContentType: [
          { required: true, message: '请选择消息类型', trigger: 'change' }
        ],
        messageContent: [
          { required: true, message: '请输入消息内容', trigger: ['blur', 'change'] }
        ],
        imageUrl: [
          { required: true, message: '请选择图片上传', trigger: 'change' }
        ]
      },
      replyData: {},
      dialogKey: 0
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.dialogKey++
        this.$nextTick(() => {
          if (this.$refs.msgForm) {
            this.$refs.msgForm.resetFields()
          }
        })
        if (this.operationType == 1) {
          this.reset()
          this.msgForm.sendTime = '00:00:00'
          this.title = '添加评论消息'
        }
        if (this.operationType == 2) {
          this.title = '编辑评论消息'
          const info = localStorage.getItem('msgData')
          const msgInfo = info ? JSON.parse(info) : {}
          this.msgForm.id = msgInfo.id
          this.msgForm.messageType = msgInfo.messageType
          this.msgForm.messageContentType = Number(msgInfo.messageContentType)
          this.msgForm.messageContent = msgInfo.messageContent
          this.msgForm.senderType = Number(msgInfo.senderType)
          this.msgForm.sendTime = msgInfo.sendTime
          this.msgForm.imageUrl = msgInfo.imageUrl
          this.msgForm.senderName = msgInfo.senderName
          this.msgForm.senderId = msgInfo.senderId
          this.msgForm['replayMessageId'] = null
        }
        if (this.operationType == 3) {
          this.reset()
          this.title = '添加回复评论消息'
          const info = localStorage.getItem('msgData')
          const msgInfo = info ? JSON.parse(info) : {}
          this.replyData = msgInfo
          this.msgForm['replayMessageId'] = msgInfo.id
          this.msgForm.senderType = 2
          this.msgForm.sendTime = msgInfo.sendTime
        }
        if (this.operationType == 4) {
          this.title = '复制评论消息'
          const info = localStorage.getItem('msgData')
          const msgInfo = info ? JSON.parse(info) : {}
          this.msgForm.id = msgInfo.id
          this.msgForm.messageType = msgInfo.messageType
          this.msgForm.messageContentType = Number(msgInfo.messageContentType)
          this.msgForm.messageContent = msgInfo.messageContent
          this.msgForm.senderType = Number(msgInfo.senderType)
          this.msgForm.sendTime = msgInfo.sendTime
          this.msgForm.imageUrl = msgInfo.imageUrl
          this.msgForm.senderName = msgInfo.senderName
          this.msgForm.senderId = msgInfo.senderId
          this.msgForm['replayMessageId'] = null
        }
        this.localVisible = val
      }
    },
    localVisible(val) {
      this.$emit('update:visible', val)
      if (!val) {
        localStorage.removeItem('msgData')
      }
    },
    chooeseTime: {
      handler(newVal) {
        setTimeout(() => {
          this.msgForm.sendTime = newVal
        }, 1000)
      },
      deep: true,
      immediate: true
    },
    'msgForm.senderType': {
      handler(newVal) {
        this.userType = newVal === 1 ? 'user' : 'assistant'
        if (newVal === 1) {
          this.msgForm.messageContentType = 1 // 学员只能发送文本消息
        }
      },
      immediate: true
    }
  },
  methods: {
    // 删除已选择身份
    deleteChooese(){
      this.msgForm.senderId = null
      this.msgForm.senderName = null
      this.$forceUpdate();
      console.log('删除已选择身份',this.msgForm.senderId)
      console.log('删除已选择身份',this.msgForm.senderName)
    },
    // 选择助教
    chooeseAssistant(data) {
      this.msgForm.senderId = data.id
      this.msgForm.senderName = data.label
    },
    // 选择学员
    chooeseUserInfo(data) {
      this.msgForm.senderId = data.id
      this.msgForm.senderName = data.nickname
    },
    // 选择图片
    chooeseSuccess(url) {
      this.msgForm.imageUrl = url
    },
    chooeseUser() {
      this.showChooeseIdentity = true
    },
    handleClose() {
      this.reset()
      this.localVisible = false
      this.$emit('update:visible', false)
    },
    handleCancel() {
      this.reset()
      this.localVisible = false
      this.$emit('update:visible', false)
    },
    handleConfirm: throttle(function() {
      const that = this
      that.$refs['msgForm'].validate((valid) => {
        if (valid) {
          that.submitLoading = true
          // 新增--回复--复制
          if (that.operationType == 1 || that.operationType == 3 || that.operationType == 4) {
            delete that.msgForm['id']
            createMessage(that.msgForm).then((response) => {
              that.reset()
              that.localVisible = false
              if (that.operationType == 1) {
                that.$modal.msgSuccess('新增评论成功')
              }
              if (that.operationType == 3) {
                that.$modal.msgSuccess('添加回复评论成功')
              }
              if (that.operationType == 4) {
                that.$modal.msgSuccess('复制评论成功')
              }
              that.submitLoading = false
              that.$emit('update:visible', false)
              that.$emit('loadList', true)
            }).catch(err => {
              that.submitLoading = false
            })
          }
          // 编辑
          if (that.operationType == 2) {
            editeMessage(that.msgForm).then((response) => {
              that.reset()
              that.localVisible = false
              that.submitLoading = false
              that.$emit('update:visible', false)
              that.$modal.msgSuccess('修改评论成功')
              that.$emit('loadList', true)
            }).catch(err => {
              that.submitLoading = false
            })
          }
        }
      })
    }, 1500),
    reset() {
      this.msgForm.templateIdStr = this.chooeseVideoId
      this.msgForm.messageType = 1
      this.msgForm.messageContentType = 1
      this.msgForm.messageContent = null
      this.msgForm.senderType = 1
      this.msgForm.sendTime = null
      this.msgForm.imageUrl = null
      this.msgForm.senderName = null
      this.msgForm.senderId = null
      this.msgForm['replayMessageId'] = null
    }
  }
}
</script>

<style lang="scss" scoped>
.tips {
  font-size: 12px;
  margin-top: 10px;
  color: #909399;
}
.boxImage {
  display: flex;
  align-items: center;
}
.pic {
  width: 60px;
  height: 60px;
}
.replayBox{
  padding: 10px 0 20px 20px;
}
</style>
