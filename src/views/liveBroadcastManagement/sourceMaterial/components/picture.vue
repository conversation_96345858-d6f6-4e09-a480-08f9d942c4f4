<template>
  <div>
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="上传时间">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <!-- <el-form-item label="图片名称" prop="fileName">
        <el-input
          v-model.trim="queryParams.fileName"
          placeholder="请输入文件名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上传时间" prop="fileName">
        <el-input
          v-model.trim="queryParams.fileName"
          placeholder="请输入文件名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上传人" prop="fileName">
        <el-input
          v-model.trim="queryParams.fileName"
          placeholder="请输入文件名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div style="margin-bottom: 10px">
      <el-button icon="el-icon-plus" type="primary" @click="handleAdd">
        上传图片
      </el-button>
      <!-- <el-button icon="el-icon-delete" plain @click="handleDelete">
        移除分组
      </el-button> -->
    </div>
    <div>
      <el-table
        v-loading="loading"
        :data="fileresList"
      >
        <!-- @selection-change="handleSelectionChange"
        <el-table-column
          v-if="!selector"
          type="selection"
          width="55"
          align="center"
        /> -->
        <el-table-column
          label="序号"
          type="index"
          width="55"
          align="center"
        />

        <el-table-column
          label="图片名称"
          prop="title"
          show-overflow-tooltip
        />
        <el-table-column label="图片" align="center" prop="url">
          <template #default="scope">
            <el-image :src="scope.row.url" class="info-img" :preview-src-list="[scope.row.url]" />
          </template>
        </el-table-column>
        <el-table-column label="图片大小" prop="fileSize" align="center">
          <template #default="scope">
            <span>{{ scope.row.fileSize | bytes }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="图片分组"
          align="center"
          prop="cateName"
        >
          <template #default="scope">
            <span>{{ scope.row.cateName || '默认分组' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上传时间" align="center" prop="creationTime">
          <template slot-scope="scope">
            {{ dayjsFunc(scope.row.creationTime) }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="150"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="updateName(scope.row)"
            >重命名</el-button>
            <el-button
              size="mini"
              type="text"
              style="color: #ff4d4f"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getImageDataList"
    />

    <!-- 添加或修改资源文件对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" v-loading="uploadLoading" :model="addForm" label-width="80px" element-loading-text="拼命上传中....">
        <el-form-item label="图片分组" required>
          <el-select v-model="addForm.cateId" placeholder="请选择分组">
            <el-option
              v-for="(item, index) in categoryList"
              :key="index"
              :label="item.cateName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="上传图片" required>
          <div class="uploader">
            <input id="fileUpload" ref="fileUpload" type="file" accept="image/*" @change="fileChange($event)">
            <label class="status">
              <span>上传状态: {{ statusText }}</span>
            </label>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="uploadLoading" @click="submitAdd">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 图片重命名 -->
    <el-dialog title="图片重命名" :visible.sync="renameOpen" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="editeNameform" :model="editeNameParmas" :rules="rules" label-width="80px" >
        <el-form-item label="图片名称" required>
          <el-input v-model.trim="editeNameParmas.title" placeholder="请输入名称" clearable />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="uploadLoading" @click="submitRename">确 定</el-button>
        <el-button @click="cancelRename">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Category from '@/views/promotion/category/index.vue'
import ClipboardButton from '@/components/ClipboardButton/index.vue'
import AliUpload from '@/components/AliUpload/index.vue'
import videoImage from '@/assets/images/video.png'
import dayjs from 'dayjs'
import {
  uploadImgAuth,
  uploadImageSuccess,
  queryGroup,
  queryImageList,
  deleteImage,
  updateImageName
} from '@/api/sourceMaterial/index'
import { bus } from '@/utils/bus.js'
import { throttle } from '@/utils'
export default {
  name: 'Fileres',
  components: { Category, ClipboardButton, AliUpload },
  filters: {
    bytes(value) {
      if (value === undefined || value === null || value === 0) return '-'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      const i = Math.floor(Math.log(value) / Math.log(k))
      return (value / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i]
    }
  },
  data() {
    return {
      dateRange: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 资源文件表格数据
      fileresList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: '图片名称不能为空', trigger: ['blur', 'change'] }
        ]
      },
      currentCategory: null,
      videoImage,
      videoVisible: false,
      videoSrc: '',

      // 对接
      categoryList: [],
      addForm: {
        cateId: null
      },
      file: {},

      timeout: '',
      partSize: '',
      parallel: '',
      retryCount: '',
      retryDuration: '',
      region: '',
      userId: '1266850844244965',
      file: null,
      authProgress: 0,
      uploadDisabled: true,
      resumeDisabled: true,
      pauseDisabled: true,
      uploader: null,
      statusText: '',

      uploadAuth: null,
      requestId: null,
      uploadAddress: null,
      videoId: null,
      uploadLoading: false,

      renameOpen: false,
      editeNameParmas:{
        idStr: '',
        title: '',
      }
    }
  },
  mounted() {
    // 初始化sdk
    this.initUploader()
    bus.$on('selectPicture', (data) => {
      this.currentCategory = data
      this.queryParams.fileCategory = data?.id
      this.getImageDataList()
      this.getCategoryList()
    })
    bus.$on('loadPicture', () => {
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.getImageDataList()
      this.getCategoryList()
    })
  },
  destroyed() {
    bus.$off('selectPicture')
    bus.$off('loadPicture')
  },
  methods: {
    // 重命名
    updateName(data){
      this.editeNameParmas.title = data.title
      this.editeNameParmas.idStr = data.id
      this.renameOpen = true
    },
    // 提交重命名
    submitRename() {
      let that = this
      that.$refs['editeNameform'].validate((valid) => {
        if (valid) {
          updateImageName(that.editeNameParmas).then(res => {
            that.$modal.msgSuccess('重命名成功')
            that.getImageDataList()
            that.cancelRename()
          })
        }
      })
    },
    // 取消重命名
    cancelRename(){
      this.editeNameParmas.title = ''
      this.editeNameParmas.idStr = ''
      this.renameOpen = false
    },
    dayjsFunc(data) {
      return dayjs(data).format('YYYY-MM-DD HH:mm:ss')
    },
    loadScript(src) {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = src
        script.onload = resolve
        script.onerror = reject
        document.head.appendChild(script)
      })
    },
    async initUploader() {
      try {
        await this.loadScript('/aliyun-upload-sdk/lib/es6-promise.min.js')
        await this.loadScript('/aliyun-upload-sdk/lib/aliyun-oss-sdk-6.17.1.min.js')
        await this.loadScript('/aliyun-upload-sdk/aliyun-upload-sdk-1.5.7.min.js')

        if (window.AliyunUpload && window.AliyunUpload.Vod) {
          this.uploader = new window.AliyunUpload.Vod({
            // 配置
          })
          console.log('AliyunUpload 初始化成功')
        } else {
          console.error('AliyunUpload SDK 加载失败')
        }
      } catch (e) {
        console.error('加载 SDK 脚本异常', e)
      }
    },
    // 提交
    submitAdd: throttle(function() {
      if (!this.file) {
        this.$modal.msgError('请先选择需要上传的文件!')
        return
      }
      if (!this.addForm.cateId) {
        this.$modal.msgError('请选择分组!')
        return
      }
      this.uploadLoading = true
      // 上传
      this.authUpload()
    }, 1500),
    fileChange(e) {
      this.file = e?.target?.files[0] || null
      if (!this.file) {
        alert('请先选择需要上传的文件!')
        return
      }
      var userData = '{"Vod":{}}'
      if (this.uploader) {
        this.uploader.stopUpload()
        this.authProgress = 0
        this.statusText = ''
      }
      this.uploader = this.createUploader()
      this.uploader.addFile(this.file, null, null, null, userData)
      this.uploadDisabled = false
      this.pauseDisabled = true
      this.resumeDisabled = true
    },
    authUpload() {
      // 然后调用 startUpload 方法, 开始上传
      if (this.uploader !== null) {
        this.uploader.startUpload()
        this.uploadDisabled = true
        this.pauseDisabled = false
      }
    },
    /** 查询资源文件列表 */
    getImageDataList() {
      this.loading = true
      queryImageList(this.queryParams).then((response) => {
        this.fileresList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 加载分组列表
    getCategoryList() {
      const parmas = {
        cateName: null
      }
      queryGroup(parmas).then((response) => {
        response.data.forEach(item => {
          if (item.cateName === '全部') {
            item['id'] = '全部'
          }
        })
        this.categoryList = response.data || []
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        fileCategory: null,
        fileName: null,
        fileSuffix: null,
        newFileName: null,
        originalFileName: null,
        bucketName: null,
        filePath: null,
        fileSize: null,
        fileType: null,
        url: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        deleted: null
      }
      this.resetForm('form')
    },
    createUploader(type) {
      const self = this
      const uploader = new AliyunUpload.Vod({
        timeout: self.timeout || 60000,
        partSize: Math.round(self.partSize || 1048576),
        parallel: self.parallel || 5,
        retryCount: self.retryCount || 3,
        retryDuration: self.retryDuration || 2,
        region: self.region,
        userId: self.userId,
        localCheckpoint: true, // 此参数是禁用服务端缓存，不影响断点续传
        // 添加文件成功
        addFileSuccess: function(uploadInfo) {
          self.uploadDisabled = false
          self.resumeDisabled = false
          self.statusText = '添加文件成功, 等待上传...'
          console.log('addFileSuccess: ' + uploadInfo.file.name)
        },

        // 开始上传
        onUploadstarted: function(uploadInfo) {
          const parmas = {
            title: self.file.name,
            categoryId: self.addForm.cateId === '全部' ? '' : self.addForm.cateId
          }
          uploadImgAuth(parmas).then((res) => {
            if (res.code == 200) {
              self.requestId = res.data.requestId
              self.uploadAuth = res.data.uploadAuth
              self.uploadAddress = res.data.uploadAddress
              self.videoId = res.data.imageId
              uploader.setUploadAuthAndAddress(
                uploadInfo,
                self.uploadAuth,
                self.uploadAddress,
                self.videoId
              )
            }
          })
        },
        // 文件上传成功
        onUploadSucceed: function(uploadInfo) {
          self.statusText = '文件上传成功!'
        },
        // 文件上传失败
        onUploadFailed: function(uploadInfo, code, message) {
          self.statusText = '文件上传失败!'
          this.uploadLoading = false
        },
        onUploadProgress: function(uploadInfo, totalSize, progress) {
          const progressPercent = Math.ceil(progress * 100)
          self.authProgress = progressPercent
          self.statusText = '文件上传中...'
        },
        // 全部文件上传结束
        onUploadEnd: function(uploadInfo) {
          const parmas = {
            imageId: self.videoId,
            categoryId: self.addForm.cateId === '全部' ? '' : self.addForm.cateId
          }
          // 调取保存接口(处理阿里上传延迟拿数据)
          setTimeout(() => {
            uploadImageSuccess(parmas).then(res => {
              self.open = false
              self.addForm.cateId = null
              self.statusText = '未选择文件'
              self.uploadLoading = false
              self.file = {}
              self.$refs.fileUpload.value = ''
              self.getImageDataList()
            })
          }, 5000)
          self.statusText = '文件上传完毕'
        }
      })
      return uploader
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.startTime = this.dateRange[0]
        this.queryParams.endTime = this.dateRange[1]
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }
      this.queryParams.pageNum = 1
      this.getImageDataList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.dateRange = []
      this.queryParams.startTime = null
      this.queryParams.endTime = null
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.getCategoryList()
      this.title = '新增图片上传'
      this.addForm.cateId = localStorage.getItem('cateIdChooese')
      this.open = true
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除"' + row.title + '"图片？')
        .then(function() {
          return deleteImage(row.id)
        })
        .then(() => {
          this.getImageDataList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    canPlay() {
      this.$nextTick(() => {
        this.$refs.videoRef.play().catch((err) => console.log('err:', err))
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.file-container {
  padding: 10px;
  border: 1px solid #eaeaea;
}
.query-header {
  display: flex;
  justify-content: space-between;
}
.popover-category-name {
  text-align: center;
  font-weight: bold;
  padding-bottom: 5px;
  width: 100%;
}
.info-img {
  width: 50px;
  height: 50px;
}
#fileUpload{
  display: block;
}
.preview-video {
  width: 350px;
  height: 100%;
  max-height: 80vh;
}
</style>
