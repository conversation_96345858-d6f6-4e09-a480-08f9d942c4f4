<template>
  <div>
    <el-form
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="上传时间">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div style="margin-bottom: 10px">
      <el-button icon="el-icon-plus" type="primary" @click="handleAdd">
        上传视频
      </el-button>
      <el-button icon="el-icon-plus" type="primary" @click="uploadDedicatedVideo">
        上传视频并生成专用视频
      </el-button>
    </div>
    <div>
      <el-table
        v-loading="loading"
        :data="fileresList"
      >
        <el-table-column
          label="序号"
          type="index"
          width="55"
          align="center"
        />
        <el-table-column
          label="视频名称"
          prop="title"
          show-overflow-tooltip
        />
        <el-table-column
          label="转码状态"
          align="center"
          prop="transcodeStatus"
        >
          <template #default="scope">
            <el-tag v-if="scope.row.transcodeStatus == 1" type="success">已转码</el-tag>
            <el-tag v-else type="warning">未转码</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="视频分类" align="center" prop="cateName">
          <template #default="scope">
            <span>{{ scope.row.cateName || '默认分组' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="视频时长"
          align="center"
          prop="duration"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <el-tag v-if="!scope.row.duration" type="success">无</el-tag>
            <el-tag v-else type="success">{{ formatSeconds(scope.row.duration) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="视频大小" align="center" prop="size">
          <template #default="scope">
            <span>{{ scope.row.size | bytes }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上传时间" align="center" prop="createTime" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="150"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >预览</el-button>
            <el-button
              size="mini"
              type="text"
              style="color: #ff4d4f"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getVideoDataList"
    />

    <!-- 添加或修改资源文件对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" v-loading="uploadLoading" :model="addForm" label-width="80px" element-loading-text="拼命上传中....">
        <el-form-item label="视频分组" required>
          <el-select v-model="addForm.cateId" placeholder="请选择分组">
            <el-option
              v-for="(item, index) in categoryList"
              :key="index"
              :label="item.cateName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="上传视频" required>
          <div class="uploader">
            <input id="fileUpload" ref="fileUpload" type="file" accept="video/*" @change="fileChange($event)">
            <label class="status">
              <span>上传状态: {{ statusText }}</span>
            </label>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="uploadLoading" @click="submitAdd">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="videoVisible"
      title="视频预览"
      width="800px"
      top="5vh"
      append-to-body
      @close="closeVideo"
    >
      <video
        ref="videoRef"
        :poster="posterImg"
        class="preview-video"
        style="width: 100%;max-height: 700px"
        :src="videoSrc"
        controls
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  listFileres,
  delFileres,
  addFileres,
  updateFileres
} from '@/api/promotion/fileres'
import axios from 'axios'
import Category from '@/views/promotion/category/index.vue'
import ClipboardButton from '@/components/ClipboardButton/index.vue'
import AliUpload from '@/components/AliUpload/index.vue'
import videoImage from '@/assets/images/video.png'
import { downloadFile } from '@/utils'
import { Loading } from 'element-ui'
import {
  uploadVideoAuth,
  uploadVideoSuccess,
  uploadConfigRefesh,
  queryGroup,
  queryVideoList,
  getVideoSrc,
  deleteVideo
} from '@/api/sourceMaterial/index'
import { bus } from '@/utils/bus.js'
import { formatSeconds } from '@/utils/render'
import { throttle } from '@/utils'
export default {
  name: 'Fileres',
  // dicts: ['transcoding_status', 'push_video_streaming', 'video_source'],
  components: { Category, ClipboardButton, AliUpload },
  filters: {
    bytes(value) {
      if (value === undefined || value === null || value === 0) return '-'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      const i = Math.floor(Math.log(value) / Math.log(k))
      return (value / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i]
    }
  },
  data() {
    return {
      dateRange: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 资源文件表格数据
      fileresList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        cateId: null
        // fileCategory: null,
        // fileName: null,
        // fileSuffix: null,
        // originalFileName: null,
        // bucketName: null,
        // fileType: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      currentCategory: null,
      videoImage,
      videoVisible: false,
      picked: 'one',

      timeout: '',
      partSize: '',
      parallel: '',
      retryCount: '',
      retryDuration: '',
      region: '',
      userId: '1266850844244965',
      file: null,
      authProgress: 0,
      uploadDisabled: true,
      resumeDisabled: true,
      pauseDisabled: true,
      uploader: null,
      statusText: '未选择文件',

      uploadAuth: null,
      requestId: null,
      uploadAddress: null,
      videoId: null,

      categoryList: [],
      addForm: {
        cateId: null
      },

      videoSrc: '',
      posterImg: '',

      uploadLoading: false,
      type: 0
    }
  },
  mounted() {
    bus.$on('selectVideo', (data) => {
      this.currentCategory = data
      this.queryParams.fileCategory = data?.id
      this.getVideoDataList()
      this.getCategoryList()
    })
    bus.$on('loadVideo', () => {
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.queryParams.startTime = null
      this.queryParams.endTime = null
      this.getVideoDataList()
      this.getCategoryList()
    })
  },
  destroyed() {
    bus.$off('selectVideo')
    bus.$off('loadVideo')
  },
  methods: {
    // 时间格式化
    formatSeconds(data) {
      return formatSeconds(data)
    },
    // 关闭视频预览
    closeVideo() {
      if (this.$refs.videoRef && this.$refs.videoRef.pause) {
        this.$refs.videoRef.pause()
      }
    },
    // 预览视频
    handleView(row) {
      getVideoSrc({ videoId: row.videoId }).then(res => {
        if (res.code == 200) {
          const videoObj = res.data?.playInfoList.filter(item => item.specification === 'Original')
          this.videoSrc = videoObj[0].playURL
          this.posterImg = res.data?.videoBase?.coverURL
          this.videoVisible = true
        }
      })
    },
    // 提交
    submitAdd: throttle(function() {
      if (!this.file) {
        this.$modal.msgError('请先选择需要上传的文件!')
        return
      }
      if (!this.addForm.cateId) {
        this.$modal.msgError('请选择分组!')
        return
      }
      this.uploadLoading = true
      // 上传
      this.authUpload()
    }, 1500),

    // 加载分组列表
    getCategoryList() {
      const parmas = {
        cateName: null
      }
      queryGroup(parmas).then((response) => {
        response.data.forEach(item => {
          if (item.cateName === '全部') {
            item['id'] = '全部'
          }
        })
        this.categoryList = response.data || []
      })
    },
    /** 查询资源文件列表 */
    getVideoDataList() {
      this.loading = true
      queryVideoList(this.queryParams).then((response) => {
        this.fileresList = response.rows || []
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.type = 0
      this.form = {
        id: null,
        fileCategory: null,
        fileName: null,
        fileSuffix: null,
        newFileName: null,
        originalFileName: null,
        bucketName: null,
        filePath: null,
        fileSize: null,
        fileType: null,
        url: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        deleted: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.startTime = this.dateRange[0]
        this.queryParams.endTime = this.dateRange[1]
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }
      this.queryParams.pageNum = 1
      this.getVideoDataList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.dateRange = []
      this.queryParams.startTime = null
      this.queryParams.endTime = null
      this.queryParams.pageNum = 1
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.getCategoryList()
      this.title = '新增视频上传'
      this.addForm.cateId = localStorage.getItem('cateIdChooese')
      this.open = true
    },
    // 上传专用视频
    uploadDedicatedVideo() {
      this.reset()
      this.getCategoryList()
      this.title = '新增视频并生成专用视频'
      this.type = 1
      this.addForm.cateId = localStorage.getItem('cateIdChooese')
      this.open = true
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateFileres(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getVideoDataList()
            })
          } else {
            addFileres(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getVideoDataList()
            })
          }
        }
      })
    },
    // 下载
    handleDownload(row) {
      if (
        (~row.fileType.indexOf('image') || row.fileType === 'jpeg') &&
        !~row.url.indexOf('aliyun')
      ) {
        downloadFile(row.url, row.fileName)
      } else {
        const form = document.createElement('form')
        form.setAttribute('action', row.url)
        form.setAttribute('method', 'get')
        form.setAttribute('target', '_blank')
        form.setAttribute('style', 'display:none')
        document.body.appendChild(form)
        form.submit()
        document.body.removeChild(form)
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除 "' + row.title + '" 视频？')
        .then(function() {
          return deleteVideo(row.id)
        })
        .then(() => {
          this.getVideoDataList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    handleCategoryChange(c) {
      this.currentCategory = c
      this.queryParams.fileCategory = c?.id
      this.getVideoDataList()
    },
    handleUploadSuccess() {
      this.getVideoDataList()
    },
    handleChoose(row) {
      this.$emit('select', row.filePath, row)
    },
    close() {
      this.$emit('close')
    },
    editFileName(row) {
      this.$modal
        .prompt('请输入新的文件名称', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
        .then(({ value }) => {
          updateFileres({ id: row.id, fileName: value }).then(() => {
            this.$modal.msgSuccess('修改成功')
            this.getVideoDataList()
          })
        })
    },
    fileChange(e) {
      this.file = e.target.files[0]
      if (!this.file) {
        alert('请先选择需要上传的文件!')
        return
      }
      var userData = '{"Vod":{}}'
      if (this.uploader) {
        this.uploader.stopUpload()
        this.authProgress = 0
        this.statusText = ''
      }
      this.uploader = this.createUploader()
      this.uploader.addFile(this.file, null, null, null, userData)
      this.uploadDisabled = false
      this.pauseDisabled = true
      this.resumeDisabled = true
    },
    authUpload() {
      // 然后调用 startUpload 方法, 开始上传
      if (this.uploader !== null) {
        this.uploader.startUpload()
        this.uploadDisabled = true
        this.pauseDisabled = false
      }
    },
    createUploader(type) {
      const self = this
      const uploader = new AliyunUpload.Vod({
        timeout: self.timeout || 60000,
        partSize: Math.round(self.partSize || 1048576),
        parallel: self.parallel || 5,
        retryCount: self.retryCount || 3,
        retryDuration: self.retryDuration || 2,
        region: self.region,
        userId: self.userId,
        localCheckpoint: true, // 此参数是禁用服务端缓存，不影响断点续传
        // 添加文件成功
        addFileSuccess: function(uploadInfo) {
          self.uploadDisabled = false
          self.resumeDisabled = false
          self.statusText = '添加文件成功, 等待上传...'
          console.log('addFileSuccess: ' + uploadInfo.file.name)
        },
        // 开始上传
        onUploadstarted: function(uploadInfo) {
          const parmas = {
            title: self.file.name,
            fileName: self.file.name,
            categoryId: self.addForm.cateId === '全部' ? '' : self.addForm.cateId
          }

          uploadVideoAuth(parmas).then((res) => {
            if (res.code == 200) {
              self.requestId = res.data.requestId
              self.uploadAuth = res.data.uploadAuth
              self.uploadAddress = res.data.uploadAddress
              self.videoId = res.data.videoId
              uploader.setUploadAuthAndAddress(
                uploadInfo,
                self.uploadAuth,
                self.uploadAddress,
                self.videoId
              )
            }
          })
        },
        // 文件上传成功
        onUploadSucceed: function(uploadInfo) {
          self.statusText = '文件上传成功!'
        },
        // 文件上传失败
        onUploadFailed: function(uploadInfo, code, message) {
          self.statusText = '文件上传失败!'
          this.uploadLoading = false
        },
        onUploadProgress: function(uploadInfo, totalSize, progress) {
          const progressPercent = Math.ceil(progress * 100)
          self.authProgress = progressPercent
          self.statusText = '文件上传中...'
        },
        // 上传凭证超时刷新
        onUploadTokenExpired: function(uploadInfo) {
          const parmas = {
            videoId: self.videoId
          }
          uploadConfigRefesh(parmas).then((resk) => {
            if (resk.code == 200) {
              self.requestId = resk.data.RequestId
              self.uploadAuth = resk.data.UploadAuth
              self.uploadAddress = resk.data.UploadAddress
              self.videoId = resk.data.VideoId
              uploader.resumeUploadWithAuth(uploadAuth)
            }
          })
          self.statusText = '文件超时...'
        },
        // 全部文件上传结束
        onUploadEnd: function(uploadInfo) {
          const parmas = {
            videoId: self.videoId,
            categoryId: self.addForm.cateId === '全部' ? '' : self.addForm.cateId,
            isTranscode: 0
          }
          if (self.type === 1) {
            parmas.isTranscode = 1
          }
          setTimeout(() => {
            uploadVideoSuccess(parmas).then(() => {
              self.open = false
              self.addForm.cateId = null
              self.statusText = '未选择文件'
              self.$refs.fileUpload.value = ''
              self.uploadLoading = false
              self.file = {}
              self.getVideoDataList()
            })
          }, 5000)
          self.statusText = '文件上传完毕'
        }
      })
      return uploader
    }
  }
}
</script>

<style lang="scss" scoped>
.file-container {
  padding: 10px;
  border: 1px solid #eaeaea;
}
.query-header {
  display: flex;
  justify-content: space-between;
}
.popover-category-name {
  text-align: center;
  font-weight: bold;
  padding-bottom: 5px;
  width: 100%;
}
.files-table-wrap {
  height: calc(100vh - 250px);
}
.export-files-table-wrap {
  height: calc(100vh - 400px);
}
.info-img {
  width: 50px;
  height: 50px;
}
#fileUpload{
  display: block;
}
.preview-video {
  width: 350px;
  height: 100%;
  max-height: 80vh;
}
::v-deep .el-table{
  min-height: 300px;
}
</style>
