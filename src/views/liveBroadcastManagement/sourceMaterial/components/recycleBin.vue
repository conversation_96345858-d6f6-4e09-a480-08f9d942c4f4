<template>
  <div>
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="文件名称" prop="fileName">
        <el-input
          v-model.trim="queryParams.fileName"
          placeholder="请输入文件名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文件类型" prop="fileName">
        <el-input
          v-model.trim="queryParams.fileName"
          placeholder="请输入文件名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button v-if="fileCategory" @click="close">关闭</el-button>
      </el-form-item>
    </el-form>
    <div style="margin-bottom: 10px">
      <el-button icon="el-icon-plus" type="primary" @click="handleDelete">
        批量恢复
      </el-button>
      <el-button icon="el-icon-delete" plain @click="handleDelete">
        批量删除
      </el-button>
    </div>
    <div
      :class="[fileCategory ? 'export-files-table-wrap' : 'files-table-wrap']"
    >
      <el-table
        v-loading="loading"
        height="100%"
        :data="fileresList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          v-if="!selector"
          type="selection"
          width="55"
          align="center"
        />
        <el-table-column
          label="文件名称"
          prop="fileName"
          show-overflow-tooltip
        />
        <el-table-column label="文件类型" align="center" prop="fileType" />
        <el-table-column label="文件大小" align="center" prop="fileSize">
          <template #default="scope">
            <span>{{ scope.row.fileSize | bytes }}</span>
          </template>
        </el-table-column>
        <el-table-column label="删除人" align="center" prop="createTime" />
        <el-table-column label="删除时间" align="center" prop="createTime" />
        <el-table-column
          label="预计彻底删除时间"
          align="center"
          prop="createTime"
        />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="150"
        >
          <template slot-scope="scope">
            <el-button
              v-if="!selector"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="editFileName(scope.row)"
            >改名</el-button>
            <el-button
              v-if="!selector"
              size="mini"
              type="text"
              icon="el-icon-download"
              @click="handleDownload(scope.row)"
            >下载</el-button>
            <el-button
              v-if="!selector"
              v-hasPermi="['promotion:fileres:remove']"
              size="mini"
              type="text"
              style="color: #ff4d4f"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
            <el-button
              v-if="selector"
              type="primary"
              size="small"
              @click="handleChoose(scope.row)"
            >选择</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改资源文件对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="文件类别" prop="fileCategory">
          <el-input
            v-model.trim="form.fileCategory"
            placeholder="请输入文件类别"
          />
        </el-form-item>
        <!--        <el-form-item label="商品图片素材" prop="goodsImageUrl">-->
        <!--          <image-upload v-model="form.goodsImageUrl" :limit="1" :file-size="1" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="视频素材" prop="videoUrl">-->
        <!--          <FileUpload v-model="form.videoUrl" :limit="1" :file-type="['mp4']" :file-size="50" />-->
        <!--        </el-form-item>-->
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="videoVisible"
      title="预览"
      width="800"
      top="5vh"
      append-to-body
      destroy-on-close
    >
      <video
        v-if="videoVisible"
        ref="videoRef"
        class="preview-video"
        style="width: 100%"
        :src="videoSrc"
        controls
        @canplay="canPlay"
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  listFileres,
  delFileres,
  addFileres,
  updateFileres
} from '@/api/promotion/fileres'
import Category from '@/views/promotion/category/index.vue'
import ClipboardButton from '@/components/ClipboardButton/index.vue'
import AliUpload from '@/components/AliUpload/index.vue'
import videoImage from '@/assets/images/video.png'
import { downloadFile } from '@/utils'

export default {
  name: 'Fileres',
  components: { Category, ClipboardButton, AliUpload },
  filters: {
    bytes(value) {
      if (value === undefined || value === null || value === 0) return '-'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      const i = Math.floor(Math.log(value) / Math.log(k))
      return (value / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i]
    }
  },
  props: {
    selector: {
      type: Boolean,
      default: false
    },
    fileCategory: {
      type: String,
      default: ''
    },
    defaultSelected: {
      type: String,
      default: ''
    },
    imgFileSize: {
      type: Number,
      default: 1
    },
    uploadAll: {
      type: Boolean,
      default: false
    }
  },
  emits: ['select'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 资源文件表格数据
      fileresList: [],
      // 资源类别表格数据
      categoryList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileCategory: null,
        fileName: null,
        fileSuffix: null,
        originalFileName: null,
        bucketName: null,
        fileType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      currentCategory: null,
      videoImage,
      videoVisible: false,
      videoSrc: ''
    }
  },
  created() {
    if (this.fileCategory) this.queryParams.fileCategory = this.fileCategory
    if (!this.defaultSelected) {
      this.getList()
    }
  },
  methods: {
    /** 查询资源文件列表 */
    getList() {
      this.loading = true
      listFileres(this.queryParams).then((response) => {
        this.fileresList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        fileCategory: null,
        fileName: null,
        fileSuffix: null,
        newFileName: null,
        originalFileName: null,
        bucketName: null,
        filePath: null,
        fileSize: null,
        fileType: null,
        url: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        deleted: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加资源文件'
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateFileres(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addFileres(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },

    // 下载
    handleDownload(row) {
      if (
        (~row.fileType.indexOf('image') || row.fileType === 'jpeg') &&
        !~row.url.indexOf('aliyun')
      ) {
        downloadFile(row.url, row.fileName)
      } else {
        const form = document.createElement('form')
        form.setAttribute('action', row.url)
        form.setAttribute('method', 'get')
        form.setAttribute('target', '_blank')
        form.setAttribute('style', 'display:none')
        document.body.appendChild(form)
        form.submit()
        document.body.removeChild(form)
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认删除资源文件编号为"' + ids + '"的数据项？')
        .then(function() {
          return delFileres(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    handleCategoryChange(c) {
      this.currentCategory = c
      this.queryParams.fileCategory = c?.id
      this.getList()
    },
    handleUploadSuccess() {
      this.getList()
    },
    handleChoose(row) {
      this.$emit('select', row.filePath, row)
    },
    playVideo(src) {
      this.videoSrc = src
      this.videoVisible = true
    },
    canPlay() {
      this.$nextTick(() => {
        this.$refs.videoRef.play().catch((err) => console.log('err:', err))
      })
    },
    close() {
      this.$emit('close')
    },
    editFileName(row) {
      this.$modal
        .prompt('请输入新的文件名称', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
        .then(({ value }) => {
          updateFileres({ id: row.id, fileName: value }).then(() => {
            this.$modal.msgSuccess('修改成功')
            this.getList()
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.file-container {
  padding: 10px;
  border: 1px solid #eaeaea;
}
.query-header {
  display: flex;
  justify-content: space-between;
}
.popover-category-name {
  text-align: center;
  font-weight: bold;
  padding-bottom: 5px;
  width: 100%;
}
.files-table-wrap {
  height: calc(100vh - 250px);
}
.export-files-table-wrap {
  height: calc(100vh - 400px);
}
.info-img {
  //display: flex;
  //justify-content: center;
  //align-items: center;
  width: 50px;
  height: 50px;
}

.preview-video {
  width: 350px;
  height: 100%;
  max-height: 80vh;
}
</style>
