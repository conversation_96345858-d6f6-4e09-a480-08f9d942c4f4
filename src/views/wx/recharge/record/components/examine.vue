<template>
  <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <div class="presentation_info">
        <div class="list">
          <div>
            <span>公司名称：</span>
            <span>{{ form.companyName }}</span>
          </div>
          <div>
            <span>开户行：</span>
            <span>{{ form.transferType }}</span>
          </div>
        </div>
        <div class="list">
          <div>
            <span>交易凭证：</span>

            <el-image
              v-if="form.transferVoucher"
              style="width: 80px; height: 30px;border-radius: 6px;"
              :src="form.transferVoucher"
              fit="cover"
              :preview-src-list="[form.transferVoucher]"
            />
          </div>
          <div>
            <span>交易流水号：</span>
            <span>{{ form.transferSerialNo }}</span>
          </div>
        </div>
        <div class="list">
          <div>
            <span>付款账户名称：</span>
            <span>{{ form.paymentAccountName }}</span>
          </div>
          <div>
            <span>付款账户ID：</span>
            <span>{{ form.paymentAccountNo }}</span>
          </div>
        </div>
        <div class="list">
          <div>
            <span>收款账户名称：</span>
            <span>{{ form.receivingAccountName }}</span>
          </div>
          <div>
            <span>收款账户ID：</span>
            <span>{{ form.receivingAccountNo }}</span>
          </div>
        </div>
        <div class="list">
          <div>
            <span>充值金额：</span>
            <span>{{ form.transferAmount }}元</span>
          </div>
          <div>
            <span>充值时间：</span>
            <span>{{ form.transferTime }}</span>
          </div>
        </div>
        <div class="list">
          <div>
            <span>备注：</span>
            <span>{{ form.remark }}</span>
          </div>
          <div />
        </div>
      </div>

      <el-form-item label="审核状态：" prop="auditState">
        <el-radio v-model="form.auditState" :label="1">通过</el-radio>
        <el-radio v-model="form.auditState" :label="2">拒绝</el-radio>
      </el-form-item>
      <el-form-item label="财务审核意见：" prop="auditComment">
        <el-input
          v-model="form.auditComment"
          placeholder="请输入财务审核意见"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        v-loading="submitLoding"
        type="primary"
        @click="submitForm"
      >确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { auditRecharge } from '@/api/recharge/record'
export default {
  data() {
    return {
      open: false,
      title: '',
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        auditState: [
          { required: true, message: '请选择审核状态', trigger: 'change' }
        ],
        auditComment: [
          { required: true, message: '请输入审核意见', trigger: 'blur' }
        ]
      },
      submitLoding: false
    }
  },

  methods: {
    init(row) {
      this.reset()
      if (row) {
        const list = JSON.parse(JSON.stringify(row))
        const obj = {
          auditState: 1,
          auditComment: ''
        }
        this.form = { ...list, ...obj }
        if (row.transferAmount) {
          this.form.transferAmount = row.transferAmount / 100
        }

        this.open = true
        this.title = '审核'

        // this.getDetail(row.id)
      }
    },

    cancel() {
      this.open = false
    },
    reset() {
      this.form = {
        id: null,
        companyId: null,

        transferSerialNo: null,

        transferState: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null,
        // 公司名称
        companyName: null,
        // 开户行
        transferType: null,
        // 识别单据
        transferVoucher: null,
        // 付款人姓名
        paymentAccountName: null,
        // 付款账户ID
        paymentAccountNo: null,
        // 收款账户名称
        receivingAccountName: null,
        // 收款账户ID
        receivingAccountNo: null,
        // 充值金额
        transferAmount: null,
        // 充值时间
        transferTime: null,
        // 审核状态
        auditState: 1,
        // 财务审核意见
        auditComment: '',
        // 备注
        remark: null

      }
      // 表单提交状态
      this.submitLoding = false
      this.resetForm('form')
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const data = {
            businessId: this.form.id,
            nodeId: this.form.nodeId,
            auditState: this.form.auditState,
            auditComment: this.form.auditComment
          }

          this.submitLoding = true
          auditRecharge(data)
            .then((response) => {
              this.submitLoding = false
              this.$modal.msgSuccess('操作成功')
              this.open = false
              this.$emit('Refresh', true)
            })
            .catch(() => {
              this.submitLoding = false
            })
        }
      })
    }
  }
}
</script>
<style lang="scss">
.presentation_info {
  padding: 16px 16px 12px 16px;
  margin-bottom: 12px;
  background: #FAFAFA;
  .list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    >div{
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: 14px;
      width: 50%;
      >:first-child{
        color: rgba(0,0,0,0.55);

      }
      >:last-child{
        color: rgba(0,0,0,0.75);
      }
    }
  }
}
</style>
