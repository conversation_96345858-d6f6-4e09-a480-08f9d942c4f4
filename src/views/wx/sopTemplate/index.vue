<template>
  <div class="sop-template-page adaption-container">
    <div class="layout">
      <div class="group-panel">
        <div class="group-header">
          <el-input
            v-model="groupKeyword"
            placeholder="请输入分组名称"
            size="small"
            clearable
            prefix-icon="el-icon-search"
            @input="handleGroupSearch"
          />
          <el-button type="primary" size="small" icon="el-icon-plus" @click="openGroupDialog('create')" />
        </div>
        <div class="group-list">

          <el-scrollbar class="h-full" :noresize="false">
            <div
              v-for="group in displayGroups"
              :key="group.id"
              :class="['group-item', { active: activeGroupId === group.id }]"
              @click="selectGroup(group)"
            >
              <div class="group-info">
                <span class="name" :title="group.groupName">{{ group.groupName }}</span>
                <el-dropdown v-if="group.manageable" trigger="click" @command="handleGroupCommand(group, $event)">
                  <span class="group-actions" @click.stop>
                    <i class="el-icon-more" />
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="rename">重命名</el-dropdown-item>
                    <el-dropdown-item command="delete">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <span class="count">{{ group.templateCount || 0 }}</span>
              </div>

            </div>
            <el-empty v-if="!groupLoading && displayGroups.length === 0" description="暂无分组" />
          </el-scrollbar>
        </div>
      </div>

      <section class="content-panel">
        <el-form
          v-show="showSearch"
          ref="queryForm"
          :model="queryParams"
          class="search-form"
          size="small"
          :inline="true"
          label-width="96px"
        >
          <el-form-item prop="taskName">
            <el-input v-model="queryParams.taskName" placeholder="请输入模板名称" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item prop="creatorId">
            <el-select
              v-model="queryParams.creatorId"
              placeholder="请选择创建者"
              clearable
              filterable
              remote
              :remote-method="remoteSearchCreator"
              :loading="creatorLoading"
            >
              <el-option
                v-for="creator in creatorOptions"
                :key="creator.userId || creator.value"
                :label="creator.userName || creator.label"
                :value="creator.userId || creator.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="templateType">
            <el-select v-model="queryParams.templateType" placeholder="请选择模板类型" clearable>
              <el-option
                v-for="option in templateTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item>
            <SavedSearches v-model="queryParams" storage-key="sopTemplateManagement" @search="handleQuery" />
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter="10" class="toolbar">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="openCreateDrawer('add')">
              新建模板
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button plain icon="el-icon-document-copy" size="mini" :disabled="multiple" @click="handleBatchCopy">
              批量复制
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button plain icon="el-icon-folder" size="mini" :disabled="multiple" @click="openMoveDialog">
              批量移动分组
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleBatchDelete">
              批量删除
            </el-button>
          </el-col>
          <right-toolbar
            :show-search.sync="showSearch"
            :columns="operatedColumns"
            :custom-list="customList"
            :columns-instance="columnsInstance"
            @queryTable="getList"
          />
        </el-row>

        <el-table
          ref="templateTableRef"
          v-loading="loading"
          border
          :data="templateList"
          @selection-change="handleSelectionChange"
          @header-dragend="handleHeaderDragend"
        >
          <el-table-column type="selection" width="55" align="center" />

          <template v-for="(c, i) in columns">
            <template v-if="c.visible !== false">
              <!-- 发送内容列自定义渲染 -->
              <template v-if="c.prop === 'messageTypes'">
                <el-table-column :key="i" :label="c.label" :min-width="c.minWidth" :width="c.width" align="center">
                  <template #default="scope">
                    <div class="message-type-tags">
                      <el-tag
                        v-for="(tag, index) in getMessageTypeTags(scope.row)"
                        :key="index"
                        size="mini"
                        :type="tag.type"
                      >
                        {{ tag.label }}
                      </el-tag>
                      <span v-if="!getMessageTypeTags(scope.row).length">-</span>
                    </div>
                  </template>
                </el-table-column>
              </template>

              <!-- 模板类型列渲染 -->
              <template v-else-if="c.prop === 'templateType'">
                <el-table-column :key="i" :label="c.label" :width="c.width" align="center">
                  <template #default="scope">
                    <el-tag size="mini" :type="getTemplateTypeTag(scope.row.templateType)">
                      {{ getTemplateTypeLabel(scope.row.templateType) }}
                    </el-tag>
                  </template>
                </el-table-column>
              </template>

              <!-- 创建时间显示 -->
              <template v-else-if="c.prop === 'createTime'">
                <el-table-column :key="i" :label="c.label" :width="c.width" align="center">
                  <template #default="scope">
                    {{ formatDateTime(scope.row.createTime) }}
                  </template>
                </el-table-column>
              </template>

              <!-- 操作列 -->
              <template v-else-if="c.prop === 'actions'">
                <el-table-column :key="i" :label="c.label" :width="c.width" fixed="right" align="center">
                  <template #default="scope">
                    <el-button size="mini" type="text" icon="el-icon-view" @click="openPreview(scope.row)">
                      预览
                    </el-button>
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="openCreateDrawer('edit', scope.row)">
                      编辑
                    </el-button>
                    <el-dropdown @command="command => handleRowMoreCommand(command, scope.row)">
                      <span class="more-actions">
                        更多<i class="el-icon-arrow-down el-icon--right" />
                      </span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="copy">复制</el-dropdown-item>
                        <el-dropdown-item command="move">移动分组</el-dropdown-item>
                        <el-dropdown-item command="delete">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>
                </el-table-column>
              </template>

              <!-- 普通列 -->
              <CustomTableColumn v-else :key="i" :data="c" :render-map="renderMap" :sortable="!!c.sortable" />
            </template>
          </template>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </section>
    </div>
    <create-sop-template-drawer
      :visible.sync="createDrawerVisible"
      :mode="drawerMode"
      :template-id="editingTemplateId"
      :group-options="groupOptions"
      @close="handleCreateDrawerClose"
      @success="handleCreateSuccess"
    />

    <sop-template-preview-drawer
      :visible.sync="previewVisible"
      :template-id="previewTemplateId"
    />

    <el-dialog
      :title="groupDialogMode === 'create' ? '新增分组' : '重命名分组'"
      :visible.sync="groupDialogVisible"
      width="400px"
      append-to-body
    >
      <el-form ref="groupFormRef" :model="groupForm" :rules="groupRules" label-width="80px">
        <el-form-item label="分组名称" prop="groupName">
          <el-input v-model="groupForm.groupName" maxlength="20" show-word-limit placeholder="请输入分组名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="groupDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="groupDialogLoading" @click="submitGroupForm">确定</el-button>
      </template>
    </el-dialog>

    <el-dialog title="移动至分组" :visible.sync="moveDialogVisible" width="420px" append-to-body>
      <el-form label-width="90px">
        <el-form-item label="目标分组">
          <el-select v-model="moveTargetGroup" placeholder="请选择目标分组">
            <el-option
              v-for="group in groupOptions"
              :key="group.id"
              :label="group.groupName"
              :value="group.id"
              :disabled="group.id === activeGroupId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="moveDialogVisible = false">取消</el-button>
        <el-button type="primary" :disabled="!moveTargetGroup" :loading="moveLoading" @click="submitMoveGroup">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import {
  getSopTemplateList,
  deleteSopTemplate,
  getSopTemplateGroupList,
  addSopTemplateGroup,
  updateSopTemplateGroup,
  deleteSopTemplateGroup,
  getSopTemplateCreators,
  updateSopTemplate
} from '@/api/wx/sopTemplate'
import SavedSearches from '@/components/SavedSearches/index.vue'
import CustomTableColumn from '@/components/CustomTable/CustomTableColumn.vue'
import CreateSopTemplateDrawer from './components/CreateSopTemplateDrawer.vue'
import SopTemplatePreviewDrawer from './components/SopTemplatePreviewDrawer.vue'

const ALL_GROUP_ID = '__ALL__'

export default {
  name: 'SopTemplate',
  components: {
    SavedSearches,
    CustomTableColumn,
    CreateSopTemplateDrawer,
    SopTemplatePreviewDrawer
  },
  data() {
    return {
      showSearch: true,
      loading: false,
      groupLoading: false,
      creatorLoading: false,
      groupKeyword: '',
      groupList: [],
      activeGroupId: ALL_GROUP_ID,
      templateList: [],
      total: 0,
      dateRange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: '',
        templateType: '',
        creatorId: ''
      },
      creatorOptions: [],
      ids: [],
      single: true,
      multiple: true,
      createDrawerVisible: false,
      drawerMode: 'add',
      editingTemplateId: null,
      previewVisible: false,
      previewTemplateId: null,
      templateTypeOptions: [
        { label: '群聊SOP', value: 'GROUP' },
        { label: '私聊SOP', value: 'PRIVATE' },
        { label: '标签SOP', value: 'TAG' }
      ],
      groupDialogVisible: false,
      groupDialogMode: 'create',
      groupDialogLoading: false,
      groupForm: {
        id: null,
        groupName: '',
        systemed: false,
        status: 1
      },
      groupRules: {
        groupName: [
          { required: true, message: '请输入分组名称', trigger: 'blur' }
        ]
      },
      moveDialogVisible: false,
      moveTargetGroup: null,
      moveLoading: false
    }
  },
  computed: {
    ...mapGetters(['device']),
    displayGroups() {
      const keyword = this.groupKeyword.trim().toLowerCase()
      const groups = (this.groupList || []).map(group => ({
        id: group.id ?? group.groupId,
        groupName: group.categoryName || group.groupName || group.name || '未命名分组',
        templateCount: group.templateCount ?? group.templateTotal ?? group.total ?? 0,
        manageable: group.systemed !== true && group.manageable !== false,
        systemed: group.systemed === true,
        status: group.status ?? 1
      })).filter(group => {
        if (!keyword) return true
        return group.groupName.toLowerCase().includes(keyword)
      })

      const totalCount = groups.reduce((sum, group) => sum + (group.templateCount || 0), 0)
      const allGroup = {
        id: ALL_GROUP_ID,
        groupName: '全部模板',
        templateCount: totalCount,
        manageable: false,
        systemed: true
      }

      return [allGroup, ...groups]
    },
    groupOptions() {
      return this.displayGroups.filter(group => group.id !== ALL_GROUP_ID && group.manageable !== false)
    }
  },
  created() {
    this.initPage()
  },
  methods: {
    async initPage() {
      this.groupLoading = true
      try {
        await this.fetchGroupList()
      } catch (error) {
        console.error('加载分组失败:', error)
        this.$message.error('加载模板分组失败')
      } finally {
        this.groupLoading = false
      }
      this.getList()
    },

    async fetchGroupList() {
      try {
        const response = await getSopTemplateGroupList({ pageNum: 1, pageSize: 9999 })
        const list = response?.rows ||
          response?.data?.rows ||
          response?.data?.data?.rows ||
          []
        this.groupList = Array.isArray(list) ? list : []
      } catch (error) {
        console.error('获取模板分组失败:', error)
        this.groupList = []
      }
      if (!this.groupList.some(group => (group.id ?? group.groupId) === this.activeGroupId)) {
        this.activeGroupId = ALL_GROUP_ID
      }
    },

    async getList() {
      this.loading = true
      const params = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        taskName: this.queryParams.taskName?.trim() || undefined,
        templateType: this.queryParams.templateType || undefined,
        creatorId: this.queryParams.creatorId || undefined
      }

      if (this.dateRange && this.dateRange.length === 2) {
        params.beginTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }

      if (this.activeGroupId !== ALL_GROUP_ID) {
        params.groupId = this.activeGroupId
      }

      try {
        const response = await getSopTemplateList(params)
        const rows = response?.rows || response?.data?.records || response?.data?.list || []
        this.templateList = Array.isArray(rows) ? rows : []
        this.total = response?.total ?? response?.data?.total ?? this.templateList.length
      } catch (error) {
        console.error('获取模板列表失败:', error)
        this.$modal?.msgError ? this.$modal.msgError('获取模板列表失败') : this.$message.error('获取模板列表失败')
        this.templateList = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    resetQuery() {
      this.dateRange = []
      this.queryParams.taskName = ''
      this.queryParams.templateType = ''
      this.queryParams.creatorId = ''
      if (this.$refs.queryForm) {
        this.$refs.queryForm.resetFields()
      }
      this.handleQuery()
    },

    handleGroupSearch() {
      // 输入事件已通过双向绑定更新 groupKeyword，此处主要用于触发筛选
    },

    selectGroup(group) {
      if (!group || group.id === this.activeGroupId) return
      this.activeGroupId = group.id
      this.handleQuery()
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = selection.length === 0
    },

    openCreateDrawer(mode, row) {
      this.drawerMode = mode
      this.editingTemplateId = row?.id || null
      this.createDrawerVisible = true
    },

    handleCreateDrawerClose() {
      this.createDrawerVisible = false
      this.drawerMode = 'add'
      this.editingTemplateId = null
    },

    handleCreateSuccess() {
      this.$message.success(this.drawerMode === 'edit' ? '模板更新成功' : '模板创建成功')
      this.handleCreateDrawerClose()
      this.fetchGroupList()
      this.getList()
    },

    openPreview(row) {
      this.previewTemplateId = row?.id
      this.previewVisible = true
    },

    handleBatchDelete() {
      if (!this.ids.length) {
        this.$message.warning('请先选择需要删除的模板')
        return
      }
      const names = this.templateList
        .filter(item => this.ids.includes(item.id))
        .map(item => item.taskName)
        .join('、')
      const confirmMessage = names ? `是否确认删除模板“${names}”？` : '是否确认删除所选模板？'
      this.$modal.confirm(confirmMessage).then(() => {
        return deleteSopTemplate(this.ids.join(','))
      }).then(() => {
        this.$modal.msgSuccess('删除成功')
        this.ids = []
        this.single = true
        this.multiple = true
        this.fetchGroupList()
        this.getList()
      }).catch(() => {})
    },

    handleDelete(row) {
      if (!row?.id) return
      this.$modal.confirm(`是否确认删除模板“${row.taskName || ''}”？`).then(() => {
        return deleteSopTemplate(row.id)
      }).then(() => {
        this.$modal.msgSuccess('删除成功')
        this.ids = []
        this.single = true
        this.multiple = true
        this.fetchGroupList()
        this.getList()
      }).catch(() => {})
    },

    handleBatchCopy() {
      if (this.ids.length !== 1) {
        this.$message.warning('请选择一条模板进行复制')
        return
      }
      const template = this.templateList.find(item => item.id === this.ids[0])
      if (!template) {
        this.$message.error('未找到需要复制的模板')
        return
      }
      this.openCreateDrawer('copy', template)
    },

    openMoveDialog(row) {
      if (row?.id) {
        this.ids = [row.id]
      }
      if (!this.ids.length) {
        this.$message.warning('请先选择需要移动的模板')
        return
      }
      this.moveTargetGroup = null
      this.moveDialogVisible = true
    },
    async submitMoveGroup() {
      if (!this.ids.length || !this.moveTargetGroup) return
      this.moveLoading = true
      try {
        const requests = this.ids.map(id => updateSopTemplate({ id, groupId: this.moveTargetGroup }))
        await Promise.all(requests)
        this.$message.success('移动分组成功')
        this.moveDialogVisible = false
        this.moveTargetGroup = null
        this.ids = []
        this.single = true
        this.multiple = true
        this.fetchGroupList()
        this.getList()
      } catch (error) {
        console.error('移动分组失败:', error)
        this.$message.error('移动分组失败，请稍后重试')
      } finally {
        this.moveLoading = false
      }
    },

    handleRowMoreCommand(command, row) {
      switch (command) {
        case 'copy':
          this.openCreateDrawer('copy', row)
          break
        case 'move':
          this.openMoveDialog(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
        default:
          break
      }
    },

    openGroupDialog(mode, group) {
      if (mode === 'create') {
        this.groupForm = { id: null, groupName: '', systemed: false, status: 1 }
      } else if (group) {
        this.groupForm = {
          id: group.id ?? group.groupId,
          groupName: group.groupName || group.name,
          systemed: group.systemed === true,
          status: group.status ?? 1
        }
      }
      this.groupDialogMode = mode
      this.groupDialogVisible = true
      this.$nextTick(() => {
        this.$refs.groupFormRef && this.$refs.groupFormRef.clearValidate()
      })
    },

    submitGroupForm() {
      this.$refs.groupFormRef.validate(async valid => {
        if (!valid) return
        this.groupDialogLoading = true
        try {
          if (this.groupDialogMode === 'create') {
            await addSopTemplateGroup({
              groupName: this.groupForm.groupName,
              status: this.groupForm.status,
              systemed: this.groupForm.systemed
            })
            this.$message.success('新增分组成功')
          } else {
            await updateSopTemplateGroup({
              id: this.groupForm.id,
              groupName: this.groupForm.groupName,
              status: this.groupForm.status,
              systemed: this.groupForm.systemed
            })
            this.$message.success('分组重命名成功')
          }
          this.groupDialogVisible = false
          await this.fetchGroupList()
          this.getList()
        } catch (error) {
          console.error('保存分组失败:', error)
          this.$message.error('保存分组失败，请稍后重试')
        } finally {
          this.groupDialogLoading = false
        }
      })
    },

    handleGroupCommand(group, command) {
      if (group.id === ALL_GROUP_ID) return
      if (command === 'rename') {
        this.openGroupDialog('rename', group)
      } else if (command === 'delete') {
        this.handleDeleteGroup(group)
      }
    },

    handleDeleteGroup(group) {
      const groupId = group.id ?? group.groupId
      this.$modal.confirm(`是否确认删除分组“${group.groupName}”？`).then(() => {
        return deleteSopTemplateGroup(groupId)
      }).then(() => {
        this.$modal.msgSuccess('删除成功')
        if (this.activeGroupId === groupId) {
          this.activeGroupId = ALL_GROUP_ID
        }
        this.fetchGroupList()
        this.getList()
      }).catch(() => {})
    },
    async remoteSearchCreator(query) {
      if (!query) {
        this.creatorOptions = []
        return
      }
      this.creatorLoading = true
      try {
        const response = await getSopTemplateCreators({ keyword: query })
        const list = response?.rows || response?.data || []
        this.creatorOptions = Array.isArray(list) ? list : []
      } catch (error) {
        console.error('加载创建者失败:', error)
        this.creatorOptions = []
      } finally {
        this.creatorLoading = false
      }
    },

    getTemplateTypeLabel(value) {
      const option = this.templateTypeOptions.find(item => item.value === value)
      return option ? option.label : value || '-'
    },

    getTemplateTypeTag(value) {
      const tagMap = {
        'GROUP': 'primary',
        'PRIVATE': 'success',
        'TAG': 'info'
      }
      return tagMap[value] || 'info'
    },

    resolveMessageType(type) {
      const typeMap = {
        text: { label: '文本', type: 'info' },
        image: { label: '图片', type: 'success' },
        video: { label: '视频', type: 'warning' },
        file: { label: '文件', type: 'default' },
        link: { label: '链接', type: 'primary' },
        10001: { label: '文本', type: 'info' },
        10002: { label: '图片', type: 'success' },
        10003: { label: '语音', type: 'warning' },
        10004: { label: '视频', type: 'warning' },
        10006: { label: '文件', type: 'default' },
        10012: { label: '链接', type: 'primary' }
      }
      return typeMap[type] || { label: '其他', type: 'default' }
    },

    getMessageTypeTags(row) {
      const types = []
      if (Array.isArray(row?.messageTypes)) {
        row.messageTypes.forEach(type => types.push(this.resolveMessageType(type)))
      } else if (Array.isArray(row?.contentTypes)) {
        row.contentTypes.forEach(type => types.push(this.resolveMessageType(type)))
      } else if (row?.messageStatistics && typeof row.messageStatistics === 'object') {
        Object.keys(row.messageStatistics).forEach(key => {
          types.push(this.resolveMessageType(key))
        })
      } else if (Array.isArray(row?.nodes)) {
        row.nodes.forEach(node => {
          if (Array.isArray(node.tasks)) {
            node.tasks.forEach(task => {
              if (Array.isArray(task.contents)) {
                task.contents.forEach(content => {
                  types.push(this.resolveMessageType(content.contentType))
                })
              }
            })
          }
        })
      }

      if (!types.length && Array.isArray(row?.contents)) {
        row.contents.forEach(content => {
          types.push(this.resolveMessageType(content.contentType))
        })
      }

      const uniqueKeys = new Set()
      const uniqueTypes = []
      types.forEach(item => {
        const key = `${item.label}-${item.type}`
        if (!uniqueKeys.has(key)) {
          uniqueKeys.add(key)
          uniqueTypes.push(item)
        }
      })
      return uniqueTypes
    },

    formatDateTime(value) {
      if (!value) return '-'
      const date = new Date(value)
      if (Number.isNaN(date.getTime())) {
        return typeof value === 'string' ? value : '-'
      }
      const yyyy = date.getFullYear()
      const MM = String(date.getMonth() + 1).padStart(2, '0')
      const dd = String(date.getDate()).padStart(2, '0')
      const hh = String(date.getHours()).padStart(2, '0')
      const mm = String(date.getMinutes()).padStart(2, '0')
      return `${yyyy}-${MM}-${dd} ${hh}:${mm}`
    }
  }
}
</script>
<script setup>
import { ref } from 'vue'
import useColumns from '@/hooks/useColumns'

const templateTableRef = ref(null)

const renderMap = {
  templateType: (value) => {
    const map = {
      GROUP: '群聊SOP',
      PRIVATE: '私聊SOP',
      TAG: '标签SOP'
    }
    return map[value] || value || '-'
  },
  groupName: (value) => value || '-'
}

const defaultColumns = [
  { prop: 'taskName', label: '模板名称', minWidth: 200, fixed: 'left' },
  { prop: 'messageTypes', label: '发送内容', minWidth: 220 },
  { prop: 'taskCount', label: '模板内任务数', width: 140 },
  { prop: 'templateType', label: '模板类型', width: 120 },
  { prop: 'groupName', label: '所属分组', width: 140 },
  { prop: 'createBy', label: '创建人', width: 120 },
  { prop: 'createTime', label: '创建时间', width: 160 },
  { prop: 'actions', label: '操作', width: 200, fixed: 'right' }
]

const { columnsInstance, columns, operatedColumns, customList, handleHeaderDragend } = useColumns({
  defaultColumns,
  tableRef: templateTableRef,
  name: 'sopTemplateManagement'
})
</script>
<style lang="scss" scoped>
.sop-template-page {
  .layout {
    display: flex;
    height: 100%;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    overflow: hidden;
    background: #fff;
  }

  .group-panel {
    width: 260px;
    border-right: 1px solid #e8eaed;
    display: flex;
    flex-direction: column;
    background: linear-gradient(180deg, #fafbfc 0%, #f5f7fa 100%);
    margin-bottom: 0;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.04);

    .group-header {
      padding: 20px 16px;
      display: flex;
      gap: 14px;
      border-bottom: 1px solid #e8eaed;
      background: #fff;

    }

    .group-list {
      padding: 5px;
      flex: 1;

      .group-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 14px;
        margin-bottom: 6px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid transparent;
        position: relative;
        background: #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 0;
          background: linear-gradient(180deg, #409eff 0%, #36a3f7 100%);
          border-radius: 0 2px 2px 0;
          transition: height 0.3s ease;
        }

        &:hover {
          background: #fff;
          border-color: #e1f0ff;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
          transform: translateY(-1px);

          &::before {
            height: 24px;
          }
        }

        &.active {
          background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
          color: #fff;
          border-color: transparent;
          box-shadow: 0 6px 20px rgba(64, 158, 255, 0.3);
          transform: translateY(-2px);

          &::before {
            height: 100%;
            width: 4px;
            background: rgba(255, 255, 255, 0.3);
          }

          .group-actions {
            color: rgba(255, 255, 255, 0.9);

            &:hover {
              color: #fff;
            }
          }

          .count {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
          }
        }

        .group-info {
          display: flex;
          align-items: center;
          gap: 10px;
          flex: 1;
          overflow: hidden;

          .name {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 500;
            font-size: 14px;
            line-height: 1.4;
          }

          .count {
            font-size: 11px;
            background: #f0f4f8;
            color: #6b7280;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
            min-width: 18px;
            text-align: center;
            transition: all 0.3s ease;
          }
        }

        .group-actions {
          color: #9ca3af;
          font-size: 16px;
          padding: 4px;
          border-radius: 4px;
          transition: all 0.3s ease;
          opacity: 0;

          &:hover {
            background: rgba(0, 0, 0, 0.05);
            color: #6b7280;
          }
        }

        &:hover .group-actions {
          opacity: 1;
        }

        &.active .group-actions {
          opacity: 1;
        }
      }

      .el-empty {
        padding: 40px 20px;

        :deep(.el-empty__description) {
          color: #9ca3af;
          font-size: 13px;
        }

        :deep(.el-empty__image) {
          width: 60px;
          height: 60px;
        }
      }
    }
  }

  .content-panel {
    flex: 1;
    width:0;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .toolbar {
      margin-bottom: 12px;
    }

    .message-type-tags {
      display: flex;
      justify-content: center;
      gap: 6px;
      flex-wrap: wrap;
      min-height: 24px;
    }

    :deep(.el-table) {
      .el-table__row {
        td {
          padding: 12px 0;
        }
      }
    }
  }

  .more-actions {
    color: #409eff;
    cursor: pointer;
    margin-left: 8px;
  }
}
</style>
