<template>
  <el-tabs v-model="activeName" style="margin-top: 0px">
    <el-tab-pane name="advertiser" lazy>
      <template slot="label">
        <div style="padding: 0 60px">
          <svg-icon icon-class="table" />账户报表
        </div>
      </template>
      <TencentStatisticsComponent group-type="advertiser" />
    </el-tab-pane>
    <el-tab-pane name="adgroup" lazy>
      <template slot="label">
        <div style="padding: 0 60px">
          <svg-icon icon-class="form" />广告组报表
        </div>
      </template>
      <TencentStatisticsComponent group-type="adgroup" />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import TencentStatisticsComponent from './components/TencentStatisticsComponent.vue'

export default {
  name: 'TencentStatistics',
  components: {
    TencentStatisticsComponent
  },
  data() {
    return {
      activeName: 'advertiser',
      loading: false,
      pageList: [{
        key: 'advertiser',
        label: '账户报表',
        icon: 'table'
      }, {
        key: 'adgroup',
        label: '广告组报表',
        icon: 'form'
      }]
    }
  },
  created() {
    this.setTabs()
  },
  activated() {
  },
  methods: {
    setTabs() {
      const tab = this.$route.query.tab
      if (tab && ['advertiser', 'adgroup'].includes(tab)) {
        this.activeName = tab
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

::v-deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

::v-deep(.el-tabs__item) {
  height: 50px;
  line-height: 50px;
  font-size: 14px;

  &.is-active {
    color: #409eff;
    font-weight: 600;
  }
}

::v-deep(.el-tabs__content) {
  padding: 0;
}

.svg-icon {
  margin-right: 8px;
  font-size: 16px;
}
</style>
