<template>
  <div class="app-container  adaption-container" :class="{'single-select-container': selector && !selectorConfig.multiple}">
    <el-form v-if="selector && !selectorConfig.multiple" label-width="auto">
      <el-form-item label="绑定的审核页">
        {{ currentPage.pageAuditName || '无' }}
        <el-button v-if="currentPage.pageAuditName" :loading="formLoading" type="danger" icon="el-icon-delete" size="mini" @click="removeAuditPage">解绑</el-button>
      </el-form-item>
    </el-form>
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" class="search-form" size="small" :inline="true" label-width="68px">
      <el-form-item prop="shopId">
        <el-input
          v-model="queryParams.shopId"
          placeholder="店铺ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="goodsId">
        <el-input
          v-model="queryParams.goodsId"
          placeholder="商品ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="pageId">
        <el-input
          v-model="queryParams.pageId"
          placeholder="云魔方页面id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="goodsName">
        <el-input
          v-model="queryParams.goodsName"
          placeholder="商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="pageTitle">
        <el-input
          v-model="queryParams.pageTitle"
          placeholder="云魔方标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="shopName">
        <el-input
          v-model="queryParams.shopName"
          placeholder="店铺名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          clearable
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="创建时间"
        />
      </el-form-item>
      <el-form-item>
        <SavedSearches
          v-model="queryParams"
          @search="handleQuery"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['health:page:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col v-if="!selector" :span="1.5">
        <el-button
          v-hasPermi="['health:page:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['health:page:transfer']"
          plain
          size="mini"
          icon="el-icon-sort"
          :disabled="multiple"
          @click="handleTransfer"
        >一键移交</el-button>
      </el-col>
      <right-toolbar
        :show-search.sync="showSearch"
        :columns="operatedColumns"
        :custom-list="customList"
        :columns-instance="columnsInstance"
        @queryTable="getList"
      />
    </el-row>

    <div ref="tableWrap" class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="pageList"
        v-bind="tableHeight"
        stripe
        border
        row-key="id"
        :class="{'single-select': selector && !selectorConfig.multiple}"
        @header-dragend="handleHeaderDragend"
        @selection-change="handleSelectionChange"
        @select="handleSelectChange"
      >
        <el-table-column type="selection" width="55" align="center" reserve-selection />
        <el-table-column
          type="index"
          label="序号"
          align="center"
          :fixed="device !== 'mobile'"
          width="50"
        />
        <!--        <el-table-column label="媒体" align="center" prop="mediaPlatformType" width="55" :fixed="device !== 'mobile'">-->
        <!--          <template slot-scope="scope">-->
        <!--            <svg-icon v-if="mediaTypeMap[scope.row.mediaPlatformType]" style="font-size: 30px" :icon-class="mediaTypeMap[scope.row.mediaPlatformType]" />-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <template v-for="(c,i) in columns">
          <el-table-column v-if="c.visible !==false && (c.prop !== 'auth' || configStore.landing_page_audit === '0')" :key="i" :label="c.label" :align="c.align || 'center'" :prop="c.prop" :min-width="c.width" :fixed="c.fixed && device !== 'mobile'" :show-overflow-tooltip="c.overflow">
            <template #header="scope">
              <span>{{ scope.column.label }}</span>
              <el-tooltip v-if="c.tooltip" effect="dark" :content="c.tooltip" placement="top">
                <i class="el-icon-question color-primary" />
              </el-tooltip>
              <el-popover
                v-if="c.prop === 'status'"
                placement="bottom"
                trigger="hover"
                width="420"
              >
                新建中: 等待用户搭建完成, 搭建完成后请 `关闭搭建页面并提审` <br>
                提审中/编辑提审中: 等待客服审核<br>
                审核中/编辑审核中: 等待阿里小二复审<br>
                页面审核通过后内容有任何编辑调整, 都需要点击 `提审`, 触发审核<br>
                页面状态为: 编辑中、编辑提审中、编辑审核中、编辑拒审、编辑已驳回时不影响之前已过审页面的访问
                <i slot="reference" class="el-icon-question" />
              </el-popover>
              <el-popover
                v-if="c.prop === 'fullPath'"
                placement="bottom"
                trigger="hover"
                width="400"
              >
                查看及预览仅审核通过的页面可用<br>
                查看: 在系统内预览, 由于阿里端代码限制, 会出现滚动条, 实际投放时不会显示<br>
                预览: 用手淘扫码预览实际效果, 所见即所得<br>
                <!-- 提取: 用于在主页面中使用子页面, 提取链接后在 `魔切` 内的 `链接` 使用 -->
                <i slot="reference" class="el-icon-question" />
              </el-popover>
              <el-popover
                v-if="c.prop === 'pageType'"
                placement="bottom"
                trigger="hover"
                width="400"
              >
                主页面: 用于直达链接跳转后展示的页面内容<br>
                子页面: 在主页面内点击展开的页面内容, 一般为评价列表、买家秀、问大家等<br>
                落地页: 用于避免广告卡审的页面, 在配置CID链接时可选
                <i slot="reference" class="el-icon-question" />
              </el-popover>
            </template>
            <template v-if="c.prop === 'auth' " #default="scope">
              <el-button type="text" :disabled="isAuth(scope.row.status)" @click="handleAuth(scope.row)">提审</el-button>
              <el-button v-if="scope.row.status === 7" v-has-permi="['health:page:audit']" type="text" @click="handleAudit(scope.row)">审核</el-button>
            </template>
            <template v-else-if="c.prop === 'action'" #default="scope">
              <el-button type="text" :disabled="isAuth(scope.row.status)" @click="handleUpdate(scope.row)">编辑</el-button>
              <el-button v-has-permi="['health:page:offline']" type="text" class="color-warning" @click="handleOffline(scope.row)">下线</el-button>
              <el-button type="text" class="color-danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
            <template v-else-if="c.prop === 'pageAuditName'" #default="scope">
              <template v-if="scope.row.pageType === '1'">
                {{ scope.row.pageAuditName || '-' }}
                <el-button type="text" icon="el-icon-edit" @click="editAuditPage(scope.row)" />
              </template>
            </template>
            <template v-else-if="c.prop === 'pageType'" #default="scope">
              <el-tag :type=" PageType[scope.row.pageType].tag">{{ PageType[scope.row.pageType].label }}</el-tag>
            </template>
            <template v-else-if="c.prop === 'status'" #default="scope">
              <el-tag :type="StatusMap[scope.row.status].tag">{{ StatusMap[scope.row.status].label }}</el-tag>
            </template>
            <template v-else-if="c.prop === 'newStatus'" #default="scope">
              <el-tag :type="StatusMap[scope.row.newStatus ?? scope.row.pageStatus].tag">{{ StatusMap[scope.row.newStatus ?? scope.row.pageStatus].label }}</el-tag>
            </template>
            <template v-else-if="c.prop === 'pageStatus'" #default="scope">
              <el-tag :type="StatusMap[scope.row.pageStatus].tag">{{ StatusMap[scope.row.pageStatus].label }}</el-tag>
            </template>
            <template v-else-if="c.prop === 'fullPath'" #default="scope">
              <el-button type="text" :disabled="!isPass(scope.row.pageStatus)" @click="showPage(scope.row)">查看</el-button>
              <el-button type="text" :disabled="!isPass(scope.row.pageStatus)" @click="showQRCode(scope.row)">预览</el-button>
              <!-- <el-button v-clipboard="scope.row.fullPath" v-clipboard:success="clipboardSuccess" type="text" :disabled="scope.row.pageType==='1'">提取</el-button> -->
            </template>
            <template v-else #default="scope">
              <RenderComponent v-if="c.render" :render="renderMap[c.prop](scope.row)" />
              <BaseInfoCell v-else-if="c.info" :id="scope.row[c.info.id]" :name="scope.row[c.info.name]" />
              <TableColumnSet v-else-if="c.set" :set="c.set" :row="scope.row" :render-map="renderMap" :label-width="c.labelWidth" />
              <template v-else>{{ scope.row[c.prop] }}</template>
              <ClipboardButton v-if="c.copy" :value="c.render ? renderMap[c.prop](scope.row) : scope.row[c.prop]" />
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <div v-if="selector">
      已选: <el-tag v-for="item in selections" :key="item.id" class="mr5">{{ item.goodsName }}</el-tag>
    </div>

    <div v-if="selector" style="display: flex; justify-content: end">
      <el-button :loading="formLoading" :disabled="multiple" type="primary" @click="handlePageSelect">选择</el-button>
      <el-button @click="cancelSelector">取消</el-button>
    </div>
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="页面类型" prop="pageType">
          <el-radio-group v-model="form.pageType">
            <el-radio-button :label="1">
              主页面
              <el-tooltip content="用于直达链接跳转后展示的页面内容" placement="top">
                <i class="el-icon-question" />
              </el-tooltip>
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="页面标题" prop="pageTitle">
          <el-input v-model="form.pageTitle" :disabled="formType ==='edit'" placeholder="请输入页面标题" />
        </el-form-item>
        <el-form-item label="商品ID" prop="goodsId">
          <el-input v-model="form.goodsId" :disabled="formType ==='edit'" placeholder="请输入商品ID" />
        </el-form-item>
        <el-form-item label="商品名称" prop="goodsName">
          <el-input v-model="form.goodsName" :disabled="formType ==='edit'" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="店铺" prop="shopId">
          <el-select
            v-model="form.shopId"
            placeholder="请选择店铺"
            :disabled="formType ==='edit'"
            filterable
          >
            <el-option
              v-for="c in companyList"
              :key="c.id"
              :label="c.shopName"
              :value="c.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-popover
          v-model="popoverVisible"
          placement="top"
          width="200"
        >
          <div>搭建前请确认已登录淘宝账号
            <a target="_blank" class="color-primary" href="https://login.taobao.com/member/login.jhtml?redirectURL=https%3A%2F%2Fwww.taobao.com%2F">去登录</a>
            搭建完成后, 请返回提交审核
          </div>
          <div style="text-align: right; margin: 0">
            <el-button size="mini" type="text" @click="popoverVisible = false">取消</el-button>
            <el-button type="primary" size="mini" @click="submitForm">已登录，去搭建</el-button>
          </div>
          <el-button slot="reference" :loading="formLoading" type="primary" plain>{{ formType === 'edit' ?'继续修改页面配置': '确 定' }}</el-button>
        </el-popover>
        <el-button v-if="formType === 'edit'" :loading="formLoading" style="margin-left: 10px" type="primary" @click="submitForm">确 定</el-button>
        <el-button style="margin-left: 10px" @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="审核" :visible.sync="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核状态" prop="auditState">
          <el-select
            v-model="auditForm.auditState"
            placeholder="请选择审核状态"
          >
            <el-option
              label="通过"
              :value="1"
            />
            <el-option
              label="驳回"
              :value="2"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审核意见" prop="auditComment">
          <el-input v-model="auditForm.auditComment" type="textarea" placeholder="请输入审核意见" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="yuntaiVisible = true">查看页面</el-button>
        <el-button :loading="formLoading" style="margin-left: 10px" type="primary" @click="submitAudit">确 定</el-button>
        <el-button @click="cancelAudit">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="previewTitle" :visible.sync="previewVisible" width="400px" top="5vh">
      <iframe
        class="preview-frame"
        :src="previewUrl"
        frameborder="no"
        style="width: 100%;"
        scrolling="auto"
      />
    </el-dialog>
    <el-dialog :title="previewTitle" :visible.sync="qrcodeVisible" width="400px" top="5vh">
      <UrlQrcode :src="previewUrl" style="width: 100%;" />
      <div class="color-primary text-center">使用手淘App扫码预览</div>
    </el-dialog>

    <el-dialog title="提示" :visible.sync="yuntaiVisible" append-to-body top="10vh">
      <div class="mb10" style="font-size: 16px">搭建前请确认已登录淘宝账号
        <a target="_blank" class="color-primary" href="https://login.taobao.com/member/login.jhtml?redirectURL=https%3A%2F%2Fwww.taobao.com%2F">
          <i class="el-icon-link" />去登录
        </a>
        搭建完成后, 请返回提交审核
      </div>
      <el-card>
        <el-alert
          title="搭建完成时，请点击提交"
          type="warning"
          show-icon
          :closable="false"
        />
      </el-card>

      <span slot="footer" class="dialog-footer">
        <el-button @click="yuntaiVisible = false">取 消</el-button>
        <el-button type="primary" @click="toSetUp">已登录，打开页面</el-button>
      </span>
    </el-dialog>
    <!--  一键移交  -->
    <el-dialog
      title="选择一键移交目标用户"
      :visible.sync="transferVisible"
      width="50%"
      top="5vh"
      append-to-body
    >
      <el-form size="small" label-width="auto">
        <TransferUser @select="handleTransferSelect" />
      </el-form></el-dialog>
  </div>
</template>

<script>
import { listPage, delPage, addPage, updatePage, offlinePage, auditPage, toPage, transferApi } from '@/api/health/page'
import ClipboardButton from '@/components/ClipboardButton/index.vue'
import TableColumnSet from '@/components/TableColumnSet/index.vue'
import BaseInfoCell from '@/components/BaseInfoCell/index.vue'
import RenderComponent from '@/components/RenderComponent/index.vue'
import { parseTime } from '@/utils/ruoyi'
import { mapGetters } from 'vuex'
import { getCompanyList } from '@/api/promotion/company'
import TransferUser from '@/components/TransferUser/index.vue'
export default {
  name: 'Page',
  components: { RenderComponent, BaseInfoCell, TableColumnSet, ClipboardButton, TransferUser },
  props: {
    selector: {
      default: false,
      type: Boolean
    },
    selectorConfig: {
      default: () => ({
        multiple: true,
        pageType: 1,
        pageStatus: 4
      }),
      type: Object
    },
    currentPage: {
      default: () => ({}),
      type: Object
    }
  },
  emits: ['select', 'cancel'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      selections: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 阿里大健康表格数据
      pageList: [],
      companyList: [],
      // 弹出层标题
      title: '',
      formType: 'add',
      // 是否显示弹出层
      open: false,
      auditOpen: false,
      formLoading: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        shopId: null,
        goodsId: null,
        pageId: null,
        goodsName: null,
        pageTitle: null,
        shopName: null,
        pageType: null,
        newStatus: null,
        pageStatus: null,
        status: null,
        offlineTime: null,
        fullPath: null
      },
      // 表单参数
      form: {
        pageType: 1
      },
      // 表单校验
      rules: {
        goodsId: [
          { required: true, message: '商品ID不能为空', trigger: 'blur' }
        ],
        goodsName: [
          { required: true, message: '商品名称不能为空', trigger: 'blur' }
        ],
        pageTitle: [
          { required: true, message: '页面标题不能为空', trigger: 'blur' }
        ],
        shopId: [
          { required: true, message: '请选择店铺', trigger: 'blur' }
        ]
      },
      auditForm: {
        auditState: null
      },
      auditRules: {
        auditState: [
          { required: true, message: '请选择审核状态不能为空', trigger: 'blur' }
        ],
        auditComment: [
          { required: true, message: '审核意见不能为空', trigger: 'blur' }
        ]
      },
      popoverVisible: false,
      auditVisible: false,
      yuntaiVisible: false,
      transferVisible: false
    }
  },
  computed: {
    ...mapGetters([
      'device',
      'tableHeight'
    ])
  },
  created() {
    this.getList()
    this.fetchCompany()
  },
  methods: {
    parseTime,
    /** 查询阿里大健康列表 */
    getList() {
      this.loading = true
      if (this.selector) {
        this.queryParams.pageStatus = this.selectorConfig.pageStatus
        this.queryParams.pageType = this.selectorConfig.pageType
      }
      listPage(this.queryParams).then(response => {
        this.pageList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        shopId: null,
        goodsId: null,
        pageId: null,
        goodsName: null,
        pageTitle: null,
        shopName: null,
        pageType: 1,
        newStatus: null,
        pageStatus: null,
        status: null,
        offlineTime: null,
        fullPath: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      if (this.selector && !this.selectorConfig.multiple) {
        return
      }
      this.ids = selection.map(item => item.id)
      this.selections = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleSelectChange(selection, row) {
      if (this.selector && !this.selectorConfig.multiple) {
        // 清除 所有勾选项
        this.$refs.tableRef.clearSelection()
        // 当表格数据都没有被勾选的时候 就返回
        // 主要用于将当前勾选的表格状态清除
        if (selection.length === 0) {
          this.selections = []
          this.multiple = true
          return
        }
        this.$refs.tableRef.toggleRowSelection(row, true)
        this.selections = [row]
        this.multiple = false
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.formType = 'add'
      this.title = '添加云台落地页'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.form = row
      this.open = true
      this.formType = 'edit'
      this.title = '修改云台落地页'
    },
    /** 提交按钮 */
    submitForm() {
      this.form.model = this.formType === 'add' ? 1 : 2
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.formLoading = true
          if (this.form.id != null) {
            updatePage(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              if (this.popoverVisible) {
                this.popoverVisible = false
                window.open(response.msg)
              }
              this.open = false
              this.getList()
            }).finally(() => {
              this.formLoading = false
            })
          } else {
            addPage(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.popoverVisible = false
              window.open(response.msg)
              this.getList()
            }).finally(() => {
              this.formLoading = false
            })
          }
        }
      })
    },
    editAuditPage(row) {
      this.auditVisible = true
      this.form = row
    },
    removeAuditPage() {
      const postData = {
        model: 4,
        id: this.currentPage.id
      }
      this.formLoading = true
      updatePage(postData).then(response => {
        this.$modal.msgSuccess('解除成功')
        this.$emit('cancel')
      }).finally(() => {
        this.formLoading = false
      }
      )
    },
    handleAuditPageSelect(selections) {
      this.form.model = 2
      this.form.pageAuditId = selections[0].pageId
      this.form.pageAuditName = selections[0].pageTitle
      this.formLoading = true
      updatePage(this.form).then(response => {
        this.$modal.msgSuccess('绑定成功')
        this.auditVisible = false
        this.getList()
      }).finally(() => {
        this.formLoading = false
      }
      )
    },
    cancelAuditPage() {
      this.auditVisible = false
      this.getList()
    },
    handleAuth(row) {
      this.$modal.confirm('新增或编辑页面内容并提审后, 审核员才会介入审核; 已过审的页面如无修改, 请不要提审!').then(() => {
        row.model = 3
        updatePage(row).then(response => {
          this.$modal.msgSuccess('操作成功')
          this.getList()
        })
      }).catch(() => {})
    },
    handleAudit(row) {
      this.auditForm = row
      this.auditForm.businessId = row.id
      this.auditForm.nodeId = row.nodeId
      this.auditOpen = true
    },
    submitAudit() {
      auditPage(this.auditForm).then(() => {
        this.getList()
        this.auditOpen = false
        this.$modal.msgSuccess('操作成功')
      }).catch(() => {})
    },
    cancelAudit() {
      this.resetForm('auditForm')
      this.auditOpen = false
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除所选数据项？').then(function() {
        return delPage(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 删除按钮操作 */
    handleOffline(row) {
      this.$modal.confirm('是否确认下线').then(function() {
        return offlinePage(row.id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('下线成功')
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('health/page/export', {
        ...this.queryParams
      }, `page_${new Date().getTime()}.xlsx`)
    },
    fetchCompany() {
      getCompanyList({ platform: 8 }).then((data) => {
        if (data.code === 200) {
          this.companyList = data.data
        }
      })
    },
    handlePageSelect() {
      this.$emit('select', this.selections)
    },
    cancelSelector() {
      this.$emit('cancel')
    },
    toSetUp() {
      toPage(this.auditForm.id).then(res => {
        window.open(res.msg)
      })
    },
    clipboardSuccess() {
      this.$message.success('拷贝成功！')
    },
    handleTransfer() {
      this.transferVisible = true
    },
    handleTransferSelect(userId) {
      this.transferVisible = false
      transferApi({
        ids: this.selections.map((item) => item.id),
        userId
      }).then((response) => {
        this.$message({
          message: '移交成功',
          type: 'success'
        })
        this.getList()
      })
    }
  }
}
</script>

<script setup>
import UrlQrcode from '@/components/UrlQrcode/index.vue'
import useColumns from '@/hooks/useColumns'
import useStoreTableScroller from '@/hooks/useStoreTableScroller'
import { getCurrentInstance, ref } from 'vue'
import useConfigs from '@/hooks/useConfigs'
import SavedSearches from '@/components/SavedSearches/index.vue'

const configStore = useConfigs(['landing_page_audit'])

const self = getCurrentInstance().proxy

const PageType = {
  1: { label: '主页面', tag: 'primary' },
  2: { label: '子页面', tag: 'warning' },
  3: { label: '审核页', tag: 'info' }
}

const StatusMap = {
  1: { label: '待发布' },
  2: { label: '淘宝审核中', tag: 'warning' },
  3: { label: '淘宝被驳回', tag: 'danger' },
  4: { label: '已发布', tag: 'success' },
  5: { label: '已下线', tag: 'info' },
  6: { label: '已过期', tag: 'warning' },
  7: { label: '熊猫审核中', tag: 'warning' },
  8: { label: '熊猫被驳回', tag: 'danger' },
  9: { label: '熊猫已通过', tag: 'success' }
}

const isPass = (s) => (s === 4)
const isAuth = (s) => (s === 2 || s === 7)

const tableRef = ref()
const defaultColumns = [
  { prop: 'GoodsInfo', label: '商品信息', width: 220, align: 'left',
    info: { id: 'goodsId', name: 'goodsName' }
  },
  { prop: 'PageInfo', label: '云魔方页面', width: 220, align: 'left',
    info: { id: 'pageId', name: 'pageTitle' }
  },
  { label: `备注`, prop: 'remark', width: '100' },
  { label: `审核`, prop: 'auth', width: '100' },
  { label: `页面地址`, prop: 'fullPath', width: '150' },
  { label: `操作`, prop: 'action', width: '150' },
  // { label: `云魔方审核页`, prop: 'pageAuditName', width: '150' },
  { label: `页面类型`, prop: 'pageType', width: '100' },
  { label: `状态`, prop: 'status', width: '120' },
  { label: `页面最新状态`, prop: 'newStatus', width: '120' },
  { label: `云魔方页面当前状态`, prop: 'pageStatus', width: '100' },
  { prop: 'ShopInfo', label: '店铺信息', width: 240, align: 'left',
    info: { id: 'shopId', name: 'shopName' }
  },
  // { prop: 'convertFlag', label: '回传状态', width: 100 },
  // { prop: 'conversionTimeStamp', label: '回传时间', width: 100, render: true }
  { label: `创建用户`, prop: 'createBy', width: '100' },
  { label: `创建时间`, prop: 'createTime', width: '100' },
  { label: `编辑用户`, prop: 'updateBy', width: '100' },
  { label: `编辑时间`, prop: 'updateTime', width: '100' },
  { label: `下线时间`, prop: 'offlineTime', width: '100' }
]

const columnsConfig = { defaultColumns, tableRef }
if (self.selector) {
  columnsConfig.name = 'djkPageSelector'
  columnsConfig.defaultColumns = columnsConfig.defaultColumns.filter(item =>
    item.prop !== 'auth' && item.prop !== 'action' && item.prop !== 'pageAuditName'
  )
}

const { columnsInstance, columns, operatedColumns, customList, handleHeaderDragend } = useColumns(columnsConfig)
useStoreTableScroller(tableRef)

const previewTitle = ref('')
const previewVisible = ref(false)
const previewUrl = ref('')
const showPage = (row) => {
  previewVisible.value = true
  previewTitle.value = row.pageTitle
  previewUrl.value = row.fullPath
}
const qrcodeVisible = ref(false)
const showQRCode = (row) => {
  qrcodeVisible.value = true
  previewTitle.value = row.pageTitle
  previewUrl.value = row.fullPath
}
</script>

<style scoped lang="scss">
.preview-frame {
  height: calc(80vh);
}
.single-select-container {
  height: calc(100vh - 120px);
}
::v-deep .el-table__header-wrapper .el-checkbox {
  display: none !important;
}
::v-deep .el-dialog__body {
  padding-top: 0;
}
</style>
