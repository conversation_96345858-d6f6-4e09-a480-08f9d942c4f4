<template>
  <div class="resource-toolbar">
    <div class="toolbar-left">
      <!-- 主要操作按钮 -->
      <div class="action-buttons">
        <el-button
          type="primary"
          icon="el-icon-upload"
          size="medium"
          :disabled="!currentCategory"
          @click="handleUpload"
        >
          上传文件
        </el-button>
        <el-button
          v-if="hasSelectedFiles && !selectorMode"
          type="danger"
          icon="el-icon-delete"
          size="medium"
          @click="handleBatchDelete"
        >
          删除 ({{ selectedFiles.length }})
        </el-button>

        <el-dropdown
          v-if="hasSelectedFiles && !selectorMode"
          trigger="click"
          @command="handleBatchAction"
        >
          <el-button size="medium">
            批量操作
            <i class="el-icon-arrow-down el-icon--right" />
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="download">批量下载</el-dropdown-item>
            <el-dropdown-item command="move">移动到...</el-dropdown-item>
            <el-dropdown-item command="copy">复制到...</el-dropdown-item>
            <el-dropdown-item divided command="delete">删除</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>

    <div class="toolbar-center">
      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        {{ currentCategory?.categoryName || '全部文件' }}
      </div>
    </div>

    <div class="toolbar-right">
      <!-- 搜索框 -->
      <div class="search-container">
        <el-input
          v-model="localSearchKeyword"
          placeholder="搜索文件..."
          size="medium"
          clearable
          class="search-input"
          @clear="handleSearchClear"
          @keyup.enter.native="handleSearchEnter"
        >
          <el-button
            slot="append"
            icon="el-icon-search"
            @click="handleSearch"
          />
        </el-input>

        <!-- 高级搜索按钮 -->
        <el-button
          icon="el-icon-more"
          size="medium"
          title="高级搜索"
          class="advanced-search-btn"
          @click="showAdvancedSearch = true"
        />
      </div>

      <!-- 视图切换 -->
      <div class="view-toggle">
        <el-radio-group
          :value="viewMode"
          size="medium"
          @input="handleViewModeChange"
        >
          <el-radio-button label="grid">
            <i class="el-icon-menu" />
          </el-radio-button>
          <el-radio-button label="list">
            <i class="el-icon-s-grid" />
          </el-radio-button>
        </el-radio-group>
      </div>

      <!-- 排序选择 -->
      <!-- <div class="sort-container">
        <el-dropdown
          trigger="click"
          @command="handleSortChange"
        >
          <el-button size="medium">
            <i class="el-icon-sort" />
            排序
            <i class="el-icon-arrow-down el-icon--right" />
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="name_asc">名称 ↑</el-dropdown-item>
            <el-dropdown-item command="name_desc">名称 ↓</el-dropdown-item>
            <el-dropdown-item command="size_asc">大小 ↑</el-dropdown-item>
            <el-dropdown-item command="size_desc">大小 ↓</el-dropdown-item>
            <el-dropdown-item command="time_asc">时间 ↑</el-dropdown-item>
            <el-dropdown-item command="time_desc">时间 ↓</el-dropdown-item>
            <el-dropdown-item command="type_asc">类型 ↑</el-dropdown-item>
            <el-dropdown-item command="type_desc">类型 ↓</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div> -->

      <!-- 刷新按钮 -->
      <el-button
        :icon="refreshing ? 'el-icon-loading' : 'el-icon-refresh'"
        size="medium"
        :title="refreshing ? '刷新中...' : '刷新'"
        :loading="refreshing"
        :disabled="refreshing"
        @click="handleRefresh"
      />
    </div>

    <!-- 高级搜索弹窗 -->
    <el-dialog
      title="高级搜索"
      :visible.sync="showAdvancedSearch"
      width="600px"
      append-to-body
    >
      <AdvancedSearch
        @search="handleAdvancedSearch"
        @close="showAdvancedSearch = false"
      />
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ToolbarComponent'
}
</script>
<script setup>
import { ref, computed, watch } from 'vue'
import AdvancedSearch from './AdvancedSearch.vue'
import { debounce } from '../utils/fileUtils'

const props = defineProps({
  searchKeyword: {
    type: String,
    default: ''
  },
  selectedFiles: {
    type: Array,
    default: () => []
  },
  currentCategory: {
    type: Object,
    default: null
  },
  viewMode: {
    type: String,
    default: 'grid'
  },
  refreshing: {
    type: Boolean,
    default: false
  },
  selectorMode: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'upload',
  'search',
  'view-mode-change',
  'batch-delete',
  'sort-change',
  'refresh',
  'batch-action',
  'advanced-search'
])

// 响应式数据
const showAdvancedSearch = ref(false)
const localSearchKeyword = ref(props.searchKeyword)

// 计算属性
const hasSelectedFiles = computed(() => props.selectedFiles.length > 0)

// 监听搜索关键词变化
watch(() => props.searchKeyword, (newValue) => {
  localSearchKeyword.value = newValue
})

// 事件处理
const handleUpload = () => {
  emit('upload')
}

const handleBatchDelete = () => {
  emit('batch-delete')
}

const handleBatchAction = (command) => {
  emit('batch-action', command, props.selectedFiles)
}

const handleSearchClear = () => {
  localSearchKeyword.value = ''
  emit('search', '')
}

const handleSearchEnter = () => {
  emit('search', localSearchKeyword.value)
}

const handleSearch = () => {
  emit('search', localSearchKeyword.value)
}

const handleViewModeChange = (mode) => {
  emit('view-mode-change', mode)
}

const handleSortChange = (sortType) => {
  emit('sort-change', sortType)
}

const handleRefresh = () => {
  emit('refresh')
}

const handleAdvancedSearch = (searchParams) => {
  showAdvancedSearch.value = false
  emit('advanced-search', searchParams)
}

</script>

<style lang="scss" scoped>
.resource-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 16px 20px;
  // background: var(--ios-background-secondary);
  // border-radius: var(--ios-border-radius);
  // box-shadow: 0 2px 8px var(--ios-shadow-light);
  margin-bottom: 16px;
  gap: 16px;
  flex-wrap: wrap;

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;

    .action-buttons {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
    }
  }

  .toolbar-center {
    display: flex;
    align-items: center;
    justify-content: center;

    .breadcrumb {
      .el-breadcrumb {
        font-size: 14px;

        ::v-deep .el-breadcrumb__item {
          .el-breadcrumb__inner {
            color: var(--ios-text-secondary);
            font-weight: 500;

            &.is-link {
              color: var(--ios-blue);

              &:hover {
                color: var(--ios-blue-dark);
              }
            }
          }

          &:last-child .el-breadcrumb__inner {
            color: var(--ios-text-primary);
            font-weight: 600;
          }
        }
      }
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    justify-content: flex-end;
    min-width: 0;

    .search-container {
      display: flex;
      align-items: center;
      gap: 8px;

      .search-input {
        width: 280px;
      }

      .advanced-search-btn {
        border-radius: var(--ios-border-radius);
      }
    }

    .sort-container {
      .el-button {
        border-radius: var(--ios-border-radius);
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .resource-toolbar {
    .toolbar-center {
      display: none;
    }
  }
}

@media (max-width: 768px) {
  .resource-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;

    .toolbar-left,
    .toolbar-right {
      flex: none;
      justify-content: center;
    }

    .toolbar-right {
      .search-container {
        .search-input {
          width: 200px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .resource-toolbar {
    padding: 12px 16px;

    .toolbar-left {
      .action-buttons {
        width: 100%;
        justify-content: center;
      }
    }

    .toolbar-right {
      flex-direction: column;
      gap: 8px;

      .search-container {
        width: 100%;

        .search-input {
          flex: 1;
          width: auto;
        }
      }
    }
  }
}
</style>
