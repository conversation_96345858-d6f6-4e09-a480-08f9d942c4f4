<template>
  <div v-loading="loading" class="file-display">
    <!-- 网格视图 -->
    <div v-if="viewMode === 'grid'" class="grid-view">
      <div
        v-for="file in files"
        :key="file.id"
        :class="[
          'file-item',
          { 'selected': isFileSelected(file) }
        ]"
        @click="handleFileClick(file, $event)"
        @dblclick="handleFileDoubleClick(file)"
        @contextmenu.prevent="handleContextMenu($event, file)"
      >
        <div class="file-thumbnail">
          <!-- 图片缩略图 -->
          <img
            v-if="isImageFile(file.fileName)"
            :src="generateThumbnailUrl(file.url)"
            :alt="file.fileName"
            class="thumbnail-image"
            @error="handleImageError"
          >
          <!-- 视频缩略图 -->
          <div v-else-if="isVideoFile(file.fileName)" class="video-thumbnail">
            <img :src="videoPlaceholder" class="thumbnail-image">
            <i class="el-icon-video-play play-icon" />
          </div>
          <!-- 其他文件图标 -->
          <div v-else class="file-icon">
            <i :class="getFileIcon(file.fileName)" :style="{ color: getFileColor(file.fileName) }" />
          </div>

          <!-- 选择框 -->
          <div v-if="isFileSelected(file)" class="selection-indicator">
            <i class="el-icon-check" />
          </div>

          <!-- 悬停操作按钮 -->
          <div v-if="!selectorMode" class="grid-action-buttons">
            <el-button
              type="text"
              size="mini"
              icon="el-icon-view"
              title="预览"
              class="action-btn preview-btn"
              @click.stop="handlePreview(file)"
            />
            <el-button
              type="text"
              size="mini"
              icon="el-icon-download"
              title="下载"
              class="action-btn download-btn"
              @click.stop="handleDownload(file)"
            />
            <el-button
              type="text"
              size="mini"
              icon="el-icon-edit"
              title="重命名"
              class="action-btn rename-btn"
              @click.stop="handleRename(file)"
            />
            <el-button
              type="text"
              size="mini"
              icon="el-icon-delete"
              title="删除"
              class="action-btn delete-btn"
              @click.stop="handleDelete(file)"
            />
          </div>
        </div>

        <div class="file-info">
          <div class="file-name" :title="file.fileName">
            <!-- 重命名输入框 -->
            <el-input
              v-if="renamingFileId === file.id"
              ref="renameInput"
              v-model="newFileName"
              size="mini"
              class="rename-input"
              @blur="handleRenameConfirm(file)"
              @keyup.enter="handleRenameConfirm(file)"
              @keyup.esc="handleRenameCancel"
            />
            <!-- 文件名显示 -->
            <span v-else>{{ file.fileName }}</span>
          </div>
          <div class="file-meta">
            <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
            <span class="file-time">{{ formatDate(file.createTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && files.length === 0" class="empty-state">
        <i class="el-icon-document-copy empty-icon" />
        <p class="empty-text">暂无文件</p>
      </div>
    </div>

    <!-- 列表视图 -->
    <div v-else class="list-view">
      <el-table
        :data="files"
        height="100%"
        stripe
        class="file-table"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        @row-dblclick="handleRowDoubleClick"
        @row-contextmenu="handleRowContextMenu"
      >
        <el-table-column type="selection" width="55" align="center" />

        <el-table-column label="文件名" prop="fileName" min-width="200">
          <template #default="{ row }">
            <div class="file-name-cell">
              <div class="file-icon-small">
                <img
                  v-if="isImageFile(row.fileName)"
                  :src="generateThumbnailUrl(row.url, 32)"
                  :alt="row.fileName"
                  class="mini-thumbnail"
                  @error="handleImageError"
                >
                <i v-else :class="getFileIcon(row.fileName)" :style="{ color: getFileColor(row.fileName) }" />
              </div>
              <!-- 重命名输入框 -->
              <el-input
                v-if="renamingFileId === row.id"
                ref="renameInput"
                v-model="newFileName"
                size="mini"
                class="rename-input"
                @blur="handleRenameConfirm(row)"
                @keyup.enter="handleRenameConfirm(row)"
                @keyup.esc="handleRenameCancel"
              />
              <!-- 文件名显示 -->
              <span v-else class="file-name-text" :title="row.fileName">
                {{ row.fileName }}
              </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="大小" prop="fileSize" width="100" align="right">
          <template #default="{ row }">
            {{ formatFileSize(row.fileSize) }}
          </template>
        </el-table-column>

        <el-table-column label="类型" prop="fileType" width="100" align="center">
          <template #default="{ row }">
            <el-tag size="mini" :type="getFileTypeTagType(row.fileName)">
              {{ getFileTypeLabel(row.fileName) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="上传时间" prop="createTime" width="160" align="center">
          <template #default="{ row }">
            {{ formatDate(row.createTime, 'YYYY-MM-DD HH:mm') }}
          </template>
        </el-table-column>

        <el-table-column v-if="!selectorMode" label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="text" size="mini" icon="el-icon-view" title="预览" @click.stop="handlePreview(row)" />
              <el-button type="text" size="mini" icon="el-icon-download" title="下载" @click.stop="handleDownload(row)" />
              <el-button type="text" size="mini" icon="el-icon-edit" title="重命名" @click.stop="handleRename(row)" />
              <el-button
                type="text"
                size="mini"
                icon="el-icon-delete"
                title="删除"
                style="color: var(--ios-red);"
                @click.stop="handleDelete(row)"
              />
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { ref, computed, nextTick } from 'vue'
import {
  isImageFile,
  isVideoFile,
  formatFileSize,
  formatDate,
  generateThumbnailUrl,
  getFileType
} from '../utils/fileUtils'
import { FILE_ICONS, FILE_COLORS, FILE_TYPES } from '../utils/constants'
import videoPlaceholder from '@/assets/images/video.png'

export default {
  name: 'FileDisplay',
  props: {
    files: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    viewMode: {
      type: String,
      default: 'grid'
    },
    selectedFiles: {
      type: Array,
      default: () => []
    },
    selectorMode: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'file-select',
    'file-preview',
    'file-download',
    'file-delete',
    'file-rename',
    'selection-change'
  ],
  setup(props, { emit }) {
    // 重命名相关状态
    const renamingFileId = ref(null)
    const newFileName = ref('')
    const renameInput = ref(null)

    // 计算属性
    const selectedFileIds = computed(() =>
      props.selectedFiles.map(file => file.id)
    )

    // 工具方法
    const isFileSelected = (file) => {
      return selectedFileIds.value.includes(file.id)
    }

    const getFileIcon = (fileName) => {
      const fileType = getFileType(fileName)
      return FILE_ICONS[fileType] || FILE_ICONS[FILE_TYPES.OTHER]
    }

    const getFileColor = (fileName) => {
      const fileType = getFileType(fileName)
      return FILE_COLORS[fileType] || FILE_COLORS[FILE_TYPES.OTHER]
    }

    const getFileTypeLabel = (fileName) => {
      const fileType = getFileType(fileName)
      const labels = {
        [FILE_TYPES.IMAGE]: '图片',
        [FILE_TYPES.VIDEO]: '视频',
        [FILE_TYPES.DOCUMENT]: '文档',
        [FILE_TYPES.AUDIO]: '音频',
        [FILE_TYPES.ARCHIVE]: '压缩包',
        [FILE_TYPES.OTHER]: '其他'
      }
      return labels[fileType] || '其他'
    }

    const getFileTypeTagType = (fileName) => {
      const fileType = getFileType(fileName)
      const tagTypes = {
        [FILE_TYPES.IMAGE]: 'success',
        [FILE_TYPES.VIDEO]: 'warning',
        [FILE_TYPES.DOCUMENT]: 'primary',
        [FILE_TYPES.AUDIO]: 'danger',
        [FILE_TYPES.ARCHIVE]: 'info',
        [FILE_TYPES.OTHER]: ''
      }
      return tagTypes[fileType] || ''
    }

    // 事件处理
    const handleFileClick = (file, event) => {
      const isCtrlPressed = event.ctrlKey || event.metaKey
      const isShiftPressed = event.shiftKey

      emit('file-select', file, {
        isCtrlPressed,
        isShiftPressed,
        allFiles: props.files
      })
    }

    const handleFileDoubleClick = (file) => {
      emit('file-preview', file)
    }

    const handleContextMenu = (event, file) => {
      // 右键菜单逻辑
      console.log('右键菜单', file)
    }

    const handleSelectionChange = (selection) => {
      emit('selection-change', selection)
    }

    const handleRowClick = (row, column, event) => {
      if (column.property !== 'actions') {
        handleFileClick(row, event)
      }
    }

    const handleRowDoubleClick = (row) => {
      emit('file-preview', row)
    }

    const handleRowContextMenu = (row, column, event) => {
      handleContextMenu(event, row)
    }

    const handlePreview = (file) => {
      emit('file-preview', file)
    }

    const handleDownload = (file) => {
      emit('file-download', file)
    }

    const handleRename = (file) => {
      // 设置重命名状态
      renamingFileId.value = file.id
      newFileName.value = file.fileName

      // 等待DOM更新后聚焦输入框
      nextTick(() => {
        if (renameInput.value) {
          renameInput.value.focus()
          // 选中文件名（不含扩展名）
          const dotIndex = file.fileName.lastIndexOf('.')
          if (dotIndex > 0) {
            renameInput.value.select()
            renameInput.value.setSelectionRange(0, dotIndex)
          } else {
            renameInput.value.select()
          }
        }
      })
    }

    // 确认重命名
    const handleRenameConfirm = (file) => {
      if (!newFileName.value.trim()) {
        // 文件名不能为空
        newFileName.value = file.fileName
        renamingFileId.value = null
        return
      }

      // 如果文件名没有变化，直接取消
      if (newFileName.value === file.fileName) {
        renamingFileId.value = null
        return
      }

      // 保留原始扩展名
      let newName = newFileName.value
      const oldExt = file.fileName.split('.').pop()
      const newExt = newName.split('.').pop()

      if (oldExt !== newExt) {
        newName = `${newName}.${oldExt}`
      }

      // 提交重命名
      emit('file-rename', file, newName)
      renamingFileId.value = null
    }

    // 取消重命名
    const handleRenameCancel = () => {
      renamingFileId.value = null
    }

    const handleDelete = (file) => {
      emit('file-delete', file)
    }

    const handleImageError = (event) => {
      // 图片加载失败时的处理
      event.target.style.display = 'none'
    }

    return {
      // 数据
      videoPlaceholder,
      renamingFileId,
      newFileName,
      renameInput,

      // 计算属性
      selectedFileIds,

      // 方法
      isFileSelected,
      isImageFile,
      isVideoFile,
      formatFileSize,
      formatDate,
      generateThumbnailUrl,
      getFileIcon,
      getFileColor,
      getFileTypeLabel,
      getFileTypeTagType,
      handleFileClick,
      handleFileDoubleClick,
      handleContextMenu,
      handleSelectionChange,
      handleRowClick,
      handleRowDoubleClick,
      handleRowContextMenu,
      handlePreview,
      handleDownload,
      handleRename,
      handleRenameConfirm,
      handleRenameCancel,
      handleDelete,
      handleImageError
    }
  }
}
</script>

<style lang="scss" scoped>
.file-display {
  flex: 1;
  overflow: hidden;
  background: var(--ios-background-secondary);
  border-radius: var(--ios-border-radius);
  box-shadow: 0 2px 8px var(--ios-shadow-light);
}

.grid-view {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  grid-auto-rows: max-content;
  gap: 16px;
  height: 100%;
  overflow-y: auto;
  align-content: start;

  .file-item {
    background: var(--ios-background-secondary);
    border: 2px solid transparent;
    border-radius: var(--ios-border-radius);
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    position: relative;
    height: fit-content;

    &:hover {
      border-color: var(--ios-blue);
      box-shadow: 0 4px 12px var(--ios-shadow-medium);
      transform: translateY(-2px);
    }

    &.selected {
      border-color: var(--ios-blue);
      background: var(--ios-selection-background);
    }

    .file-thumbnail {
      position: relative;
      width: 100%;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--ios-fill-quaternary);
      border-radius: var(--ios-border-radius-small);
      margin-bottom: 8px;
      overflow: hidden;

      .thumbnail-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: cover;
        border-radius: var(--ios-border-radius-small);
      }

      .video-thumbnail {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .play-icon {
          position: absolute;
          font-size: 24px;
          color: white;
          background: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          padding: 8px;
        }
      }

      .file-icon {
        i {
          font-size: 48px;
        }
      }

      .selection-indicator {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        background: var(--ios-blue);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
      }
    }

    .file-info {
      .file-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--ios-text-primary);
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-meta {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: var(--ios-text-secondary);

        .file-size,
        .file-time {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .empty-state {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;

    .empty-icon {
      font-size: 64px;
      color: var(--ios-text-quaternary);
      margin-bottom: 16px;
    }

    .empty-text {
      font-size: 16px;
      color: var(--ios-text-secondary);
      margin: 0;
    }
  }
}

.list-view {
  height: 100%;

  .file-table {
    ::v-deep .el-table__header {
      background: var(--ios-light-gray);
    }

    .file-name-cell {
      display: flex;
      align-items: center;
      gap: 8px;

      .file-icon-small {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        .mini-thumbnail {
          width: 24px;
          height: 24px;
          object-fit: cover;
          border-radius: 4px;
        }

        i {
          font-size: 18px;
        }
      }

      .file-name-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
      }
    }

    .action-buttons {
      display: flex;
      gap: 4px;
      justify-content: center;

      .el-button {
        padding: 4px 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .grid-view {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    grid-auto-rows: max-content;
    gap: 12px;
    padding: 12px;
    align-content: start;

    .file-item {
      height: fit-content;

      .file-thumbnail {
        height: 80px;
      }

      .file-info {
        .file-name {
          font-size: 13px;
        }

        .file-meta {
          font-size: 11px;
        }
      }
    }
  }
}

// 重命名输入框样式
.rename-input {
  width: 100%;

  :deep(.el-input__inner) {
    padding: 2px 8px;
    font-size: 12px;
    border: 1px solid var(--ios-blue);
    border-radius: 4px;
    background: var(--ios-background);

    &:focus {
      border-color: var(--ios-blue);
      box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
    }
  }
}

// 网格视图操作按钮样式
.grid-action-buttons {
  position: absolute;
  bottom: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  z-index: 10;

  .action-btn {
    width: 26px;
    height: 26px;
    padding: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    i {
      font-size: 13px;
      color: var(--ios-text-primary);
    }

    &.preview-btn:hover i {
      color: var(--ios-blue);
    }

    &.download-btn:hover i {
      color: var(--ios-green);
    }

    &.rename-btn:hover i {
      color: var(--ios-orange);
    }

    &.delete-btn:hover i {
      color: var(--ios-red);
    }
  }
}

.grid-view {
  .file-item {
    position: relative;

    .rename-input {
      margin-top: 4px;
    }

    .file-thumbnail {
      position: relative;
      overflow: hidden;
    }

    // 悬停时显示操作按钮
    &:hover .grid-action-buttons {
      opacity: 1;
      transform: translateY(0);
    }

    // 悬停时添加遮罩效果
    &:hover .file-thumbnail::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.1);
      z-index: 5;
      transition: all 0.3s ease;
    }
  }
}

.list-view {
  .file-name-cell {
    .rename-input {
      flex: 1;
      margin-left: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .grid-view {
    .file-item {
      // 移动端始终显示操作按钮
      .grid-action-buttons {
        opacity: 1;
        transform: translateY(0);
        gap: 4px;

        .action-btn {
          width: 22px;
          height: 22px;

          i {
            font-size: 11px;
          }
        }
      }
    }
  }
}

// 触摸设备优化
@media (hover: none) and (pointer: coarse) {
  .grid-view {
    .file-item {
      .grid-action-buttons {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
}
</style>
