import { isWb, isZh } from '@/utils/judge/media'
import { isTbPro, isDjk } from '@/utils/judge/platform'
import { useTemplateRenderer } from '@/hooks/useTemplateRenderer.js'

const schemeReg = /^(?:([A-Za-z.]+):\/\/)?/
export const defaultImg = 'https://oss.pangdasc.com/2024/05/21/promotion-card_20240521163812A001.png'
const flashCss = `.flash {animation-name: flash;animation-duration: 2s;animation-fill-mode: both;animation-iteration-count: infinite;}
@keyframes flash{0%,50%,to {opacity: 1;transform: scale(1)}
25%,75% {opacity: 1;transform: scale(.9)}}`

const normalImgCss = `.normal-image {width: 100%;display: block;border: 0;object-fit: cover;}`
// 生成html
export const data2Html = (config) => {
  const { form } = config
  const codeMap = form.pageType === 'template' ? generateTemplateHtml(config) : generateHtml(config)
  const callApp = getCallApp()
  const mainJs = generateJs(config)

  // <script src="https://unpkg.com/callapp-lib@3.5.3/dist/index.umd.js"></script>
  return `
  <!DOCTYPE html>
  <html>
    <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
      <meta http-equiv="X-UA-Compatible" content="ie=edge" />
      <title>${form.title}</title>
      <style>
        body {
          margin: 0;
        }
        #app {
          position: relative;
        }
        ${codeMap.css}
      </style>
      <script src="https://oss.pangdasc.com/assets/clipboard.min.js"></script>
      <script type="text/javascript">
        ${callApp}
      </script>

<style>
  .c-btn{
    cursor:pointer;
    width: 1px;
    height: 1px;
    opacity: 0;
    display: inline-block;
  }
</style>
    </head>
    <body>
      <div id="app">
        <div id="tooltip"></div>
        ${codeMap.html}
      </div>
      <span id="copy-text" class="c-btn" ></span>
      <script type="text/javascript">
        ${codeMap.js}
      </script>
        ${mainJs}
        <script type="text/javascript">

  function copy() {
    var clipboard = new ClipboardJS('#copy-text');
    clipboard.on('success',  e => {clipboard.destroy()})
    clipboard.on('error', e => {clipboard.destroy()})
  }
  var copyText = document.querySelector('#copy-text');
  copyText.addEventListener('click', () => {
    copy()
  })

  function copyClipboardFun(str) {
      document.getElementById('copy-text').setAttribute("data-clipboard-text", str);
      setTimeout(()=>{
        var event = new MouseEvent('click', {
          'view': window,
          'bubbles': true,
          'cancelable': true
        });
        copyText.dispatchEvent(event);
      },10)
  }
</script>
    </body>
    </html>
  `
}

function generateJs(config) {
  const { form, goods, monitorUrl, mediaType, advertiserId, landingPageShowUrl, backupGoods } = config
  const { url, platform } = goods
  const isKS = mediaType === 2
  const isJD = platform === '3'
  const invokeMonitor = mediaType === 2 || mediaType === 3 || !url.includes('%22ckId%22%3A')
  const isUrlSwap = goods.linkType === '4'
  let backupInvokeMonitor = false
  if (backupGoods) {
    backupInvokeMonitor = mediaType === 2 || mediaType === 3 || !backupGoods.url.includes('%22ckId%22%3A')
  }

  let requestUrl = ''
  let rIdStr = `
        function getParam(n) {
          return new URLSearchParams(window.location.search).get(n);
        }

        function uuid() {
          var i
          var random
          var uuid = ''
          for (i = 0; i < 20; i++) {
            random = (Math.random() * 16) | 0
            uuid += (i === 12 ? 4 : i === 16 ? (random & 3) | 8 : random).toString(16)
          }
          return uuid
        }
        function formatTime() {
          var d = new Date()
          return '' + d.getFullYear()  + add0(d.getMonth() + 1)  + add0(d.getDate())  + add0(d.getHours())  + add0(d.getMinutes())
        }
        function add0(m) {
          return m < 10 ? '0' + m : m
        }
    `
  let getLink = ''
  if (platform === '5') {
    requestUrl = `
         var reqId = formatTime() + uuid()
         ${isKS ? `if(getParam('requestId')) reqId = getParam('requestId')` : ''}
        var dplink = '__DPURL__'
        var _url = '__URL__'
    `
    getLink = `function getLink(goodsId) {
          return new Promise((resolve) => {
            var b = '${monitorUrl}' + window.location.search.replace('goodsId=', '_goodsId_=') + '&goodsId='+goodsId+'&mediaPlatformType=${mediaType}';
            ${(mediaType === 3 && advertiserId) ? `if(!getParam('advertiserId')) b += "&advertiserId=${advertiserId}"` : ''}
            ${isKS ? `if(!getParam('requestId')) ` : ``} b += '&requestId=' + reqId
            var a = new XMLHttpRequest();
            a.open("GET", b, true);
            a.send();
            showLoading();
            a.onreadystatechange = (data) => {
              if (a.readyState ==  XMLHttpRequest.DONE) {
                if (a.status == 200) {
                  var res = JSON.parse(a.responseText);
                  if(res.code === 200 && res.data) {
                    var reg = /^(?:([A-Za-z]+):\\/\\/)?/
                    var protocol = reg.exec(res.data.dpUrl)[1]
                    dplink = res.data.dpUrl.replace(reg, '')
                    lib = new CallApp({
                      scheme: {
                        protocol: protocol,
                      },
                      isSupportWeibo: true,
                      appstore: res.data.url,
                      fallback: res.data.url,
                      timeout: 2000,
                    });
                    _url = res.data.url
                  } else {
                    console.log(res.code);
                  }
                } else{
                  console.log("status is " + a.code);
                }
                resolve()
              }
            }
          })
        }`
  } else {
    requestUrl =
      ` ${isZh(mediaType) ? `var zhCallback = new URLSearchParams(window.location.search).get('cb')` : ''}
         var reqId = formatTime() + uuid()
         ${isKS ? `if(getParam('requestId')) reqId = getParam('requestId')` : ''}
         var dplink = rId('__DPURL__')
         var _url = rId('__URL__')

         var schemeReg = /^(?:([A-Za-z.]+):\\/\\/)?/
         var scheme = schemeReg.exec(dplink)[1]
         dplink = dplink.replace(schemeReg, '')

         lib = new CallApp({
          scheme: {
            protocol: scheme,
          },
          isSupportWeibo: true,
          ${(isJD && !isUrlSwap) ? '' : 'appstore: _url, fallback: _url,'}
          timeout: 2000,
        });
    `
    getLink = `function getLink(goodsId) {
        return new Promise((resolve) => {
      resolve()
    })
          return new Promise((resolve) => {
            var b = '${monitorUrl}' + ${isZh(mediaType) ? `'?mediaPlatformType=${mediaType}&cb='+ encodeURIComponent(zhCallback)` : `window.location.search.replace('goodsId=', '_goodsId_=')`} + '&goodsId=' + goodsId;
            ${(isWb(mediaType) && advertiserId) ? `if(!getParam('advertiserId')) b += "&advertiserId=${advertiserId}"` : ''}
            ${(isZh(mediaType)) ? `b += "&advertiserId=" + getParam('advertiserId');` : ''}
            ${isKS ? `if(!getParam('requestId')) ` : ``} b += '&requestId=' + reqId
            var a = new XMLHttpRequest();
            a.open("GET", b, true);
            a.send();
            showLoading();
            a.onreadystatechange = () => {
              if (a.readyState ==  XMLHttpRequest.DONE) {
                if (a.status == 200) {
                  var res = JSON.parse(a.responseText);
                  if(res.code === 200 && res.data) {
                    resolve(res.data)
                  } else {
                    console.log(res.code);
                  }
                } else{
                  console.log("status is " + a.code);
                }
                resolve()
              }
            }
          })
        }`

    if (mediaType === 9) {
      rIdStr += `
      function rId(url) {
          return url
          .replace('__ADVERTISER_ID__', getParam('advertiserId'))
          .replace('{source_id}', getParam('source_id'))
          .replace('{planid}', getParam('planid'))
          .replaceAll('{', '')
          .replaceAll('}', '')
        }`
    } else if (isTbPro(platform) || isDjk(platform)) {
      rIdStr += `
      function rId(url) {
        const urlObj = new URL(url)
        if(urlObj.searchParams.has('h5Url')) {
          const h5Url = urlObj.searchParams.get('h5Url')
          const h5UrlObj = new URL(decodeURIComponent(h5Url))
          h5UrlObj.search = window.location.search
          urlObj.searchParams.set('h5Url', h5UrlObj.toString())
        } else {
          urlObj.search = window.location.search
        }
        const urlStr = urlObj.toString();
        ${isDjk(platform) ? `return urlStr.replace('__CID__', getParam('cid') || '__CID__')
          .replace('__AID__', getParam('adid') || '__AID__')
          .replace('__ADVERTISER_ID__', getParam('advId') || '__ADVERTISER_ID__')
          `
    : 'return urlStr'}

      }
      `
    } else {
      rIdStr += `
      function rId(url) {
      ${isZh(mediaType) ? `var _cid = isValidUrl(zhCallback) ?  new URLSearchParams(new URL(zhCallback).search).get('cid'):''` : ''}
          return url.replace("__WB_MARK__WB__ADVERTISER_ID__", reqId+'WB')
          .replace(${mediaType === 2 ? "'__ADREQUESTID__'" : "'__REQUEST_ID__'"}, reqId)
          .replace('__TRACK_ID__', reqId)
          .replace('__ADVERTISER_ID__', getParam('advertiserId'))
          .replace('__ACCOUNTID__', getParam('accountId'))
          .replace('__ACCOUNT_ID__', getParam('accountId') || '__ACCOUNT_ID__')
          .replace('__CLICKID__', getParam('clickid') || '__CLICKID__')
          .replace('__CLICK_ID__', getParam('ckId') || '__CLICK_ID__')
          .replace('__AID__', getParam(${mediaType === 1 ? "'adId'" : "'aid'"}) || '__AID__')
          .replace('__PROMOTION_ID__', getParam('pmid') || '__PROMOTION_ID__')
          .replace('__CALLBACK__', getParam(${mediaType === 4 ? "'cb'" : "'callback'"}) || '__CALLBACK__')
          .replace('__ADGROUP_ID__',getParam('plId') || '__ADGROUP_ID__')
          .replace('__CALLBACK_PARAM__',getParam('callbackParam') || '__CALLBACK_PARAM__')
          .replace('__CSITE__',getParam('csite') || '__CSITE__')
          .replace('__SITE_SET_NAME__',getParam('csite') || '__SITE_SET_NAME__')
          .replace('__TRACKID__',getParam('trackId') === '__TRACKID__' ? uuid() : getParam('trackId'))
          .replace(/__MD5CMFROMTRACKID__/g,getParam('md5TrackId') || '__MD5CMFROMTRACKID__')
          .replace('__IP__', getParam('ip') || '__IP__')
          ${isZh(mediaType) ? `.replace('__CB__', encodeURIComponent(encodeURIComponent(zhCallback)) || '__CB__')
          .replace('__CID__', _cid || '__CID__')` : ''}
        }
        ${isZh(mediaType) ? `function isValidUrl(string) {
  try {
    new URL(string);
    return true;
  } catch (err) {
    return false;
  }
}` : ''}`
    }
  }

  const enterPage = `
    function enterPage() {var req = new XMLHttpRequest();
        req.open("GET","${landingPageShowUrl}" +  window.location.search  , true);
        req.send();}
  `
  // 副商品
  let backupJS = ''
  if (backupGoods) {
    const isBackupSwap = backupGoods.linkType === '4'
    if (form.isCallback) {
      requestUrl += `
        var dplink_backup = rId('__DPURL_BACKUP__')
        var _url_backup = rId('__URL_BACKUP__')
      `
    } else {
      requestUrl += `
        var dplink_backup = '__DPURL_BACKUP__'
        var _url_backup = '__URL_BACKUP__'
      `
    }

    if (platform !== '5') {
      requestUrl += `
         var schemeReg = /^(?:([A-Za-z.]+):\\/\\/)?/
         var scheme_backup = schemeReg.exec(dplink_backup)[1]
    dplink_backup = dplink_backup.replace(schemeReg, '')`
    }
    backupJS = `
        var lib_backup = new CallApp({
          scheme: {
            protocol: scheme_backup,
          },
          isSupportWeibo: true,
          ${(isJD && !isBackupSwap) ? '' : 'appstore: _url_backup, fallback: _url_backup,'}
          timeout: 2000,
        });

        function openBackup(event) {
          event.stopPropagation();
          ${(backupInvokeMonitor && form.isCallback && !isTbPro(platform)) ? `getLink('__GOODSID_BACKUP__').finally(nextBackup)` : 'showLoading();nextBackup()'}
        }
        function nextBackup() {
           if(isWechat()) {
              window.top.location.href = _url_backup;
            } else {
              lib_backup.open({path: dplink_backup${(isJD && !isBackupSwap) ? `,callback() {
            var b = '${config.jdConvertWx}?url=' + encodeURIComponent(_url_backup);
            var a = new XMLHttpRequest();
            a.open("GET", b, true);
            a.send();
            showLoading();
            a.onreadystatechange = function() {
             if (a.readyState == 4 && a.status == 200) {
              var res = JSON.parse(a.responseText)
               window.top.location.href = res.msg
              }
            }
          }` : ''}});
              setTimeout(hideLoading, 2000)
            }
        }
        var isLoad = false;
        var backupTooltip = document.getElementById('backup')
        function listenHashChange(event) {
           if (isLoad) {
            event.preventDefault();
            showBackupTooltip()
          }
        }
        window.addEventListener('hashchange', listenHashChange);

        function pushHistory() {
          var state = {title: "title",url: "#"}
          window.history.pushState(state, "title", "#");
          isLoad = true
        }
        function ListenVisibilitychange() {
          if(document.visibilityState == "hidden") {
${form.showAfterSuccess ? `showBackupTooltip()` : ` window.removeEventListener('hashchange', listenHashChange);`}
          }
        }
        document.addEventListener('visibilitychange' , ListenVisibilitychange)

        function showBackupTooltip() {
          backupTooltip.style.display = 'block';
          window.removeEventListener('hashchange', listenHashChange);
${form.showAfterSuccess ? `document.removeEventListener('visibilitychange', ListenVisibilitychange)` : ''}
        }

        function hideBackupTooltip(event) {
          event.stopPropagation();
          backupTooltip.style.display = 'none';
        }
        setTimeout(()=> {
          pushHistory()
        },300)
        `
  }
  return `
  <script type="text/javascript">
        window.onload = function () {
          ${isKS ? 'enterPage();' : ''}
          ${form.redirectType !== 1 ? `setTimeout(openApp, ${form.autoRedirectTime}*1000);` : ''}
        };

        ${requestUrl}
        ${isKS ? enterPage : ''}

        function openApp() {
          ${isTbPro(platform) ? `next()` : `getLink('__GOODSID__').then((str) => {if(str) copyClipboardFun(str)}).finally(next)`}
        }
        var lib;
        function next() {
           if(isWechat()) {
              window.top.location.href = _url;
            } else {
              lib.open({path: dplink${(isJD && !isUrlSwap) ? `,callback() {
            var b = '${config.jdConvertWx}?url=' + encodeURIComponent(_url);
            var a = new XMLHttpRequest();
            a.open("GET", b, true);
            a.send();
            showLoading();
            a.onreadystatechange = function() {
             if (a.readyState == 4 && a.status == 200) {
              var res = JSON.parse(a.responseText)
               window.top.location.href = res.msg
              }
            }
          }` : ''}});
              setTimeout(hideLoading, 2000)
            }
        }
        ${getLink}

        function isWechat() {
            return /MicroMessenger/i.test(window.navigator.userAgent);
        }
        ${rIdStr}

        ${backupJS}
      </script>
  `
}

// 生成模板页面HTML
function generateTemplateHtml(config) {
  const { form } = config

  if (form.pageType !== 'template') {
    return generateHtml(config) // 使用原有逻辑
  }

  // 使用统一的模板渲染引擎
  try {
    const { renderHtmlString } = useTemplateRenderer()

    // 构建模板数据
    const templateData = {
      templateImage: form.templateImage || (config.goods && config.goods.goodsImageUrl) || '',
      templateGoodsName: form.templateGoodsName || (config.goods && config.goods.goodsName) || '',
      templatePrice: form.templatePrice || '',
      templateButtonText: form.templateButtonText || '立即购买',
      companyName: form.companyName || ''
    }

    const templateStyle = form.templateStyle || 'template1'
    const result = renderHtmlString(templateStyle, templateData)

    const loading = generateLoadingHtml()
    result.html += loading.html
    result.css += loading.css
    result.js += loading.js

    return {
      html: result.html,
      css: result.css,
      js: result.js
    }
  } catch (error) {
    console.error('模板渲染失败，回退到原有逻辑:', error)
  }
}

function generateHtml(config) {
  const { form, area, button, img, backup } = config
  form.slides = form.slides.filter((item) => item)
  form.normalImages = form.normalImages.filter((item) => item)
  const hasArea = area && area.length > 0
  const hasButton = button && button.length > 0
  const hasImg = img && img.length > 0
  const hasBackup = backup && backup.length > 0

  let css = `
  .scroll-phone-main {
    box-sizing: border-box;
    position: relative;
    flex:1;
    width: 100%;
    height: 100%;
    min-height:100vh;
    overflow-y: auto;
  }`
  let html = `<div ${hasArea ? '' : 'onclick="openApp()"'} class="phone-body"><div class="scroll-phone-main">`
  let js = ''

  if (hasArea) {
    const click = generateClickAreas(area)
    html += click.html
    css += click.css
  }

  if (hasButton) {
    const click = generateClickBtns(button)
    html += click.html
    css += click.css
  }

  if (hasImg) {
    const click = generateClickImgs(img)
    html += click.html
    css += click.css
  }

  if (hasButton || hasImg || hasBackup) {
    css += flashCss
  }

  if (form.slides.length > 0) {
    html += `<div class="slideshow" onmouseenter="stopPlay()" onmouseleave="startPlay()" ><div ref="sRef" class="slides">`
    form.slides.forEach((item) => {
      html += `<img src="${item}" class="slides-img active" onload="sLoad(event)" alt="轮播图">`
    })
    html += `</div><a href="#" class="prev" onclick="pSlide()">«</a><a href="#" class="next" onclick="nSlide()">»</a></div>`
    js += `var sRef = document.querySelector('.slides');
  var sImg = document.querySelectorAll('.slides-img');
  var pBtn = document.querySelector('.prev');
  var nBtn = document.querySelector('.next');
  var ci = 0;
  var sWidth = 0;
  var sTimer = null;
  var playInterval = null;
  function sLoad(event) {
    clearTimeout(sTimer);
    sTimer = setTimeout(() => {
      if (!event.target) return;
      sWidth = event.target.offsetWidth;
      showSlide(ci);
    }, 100);
  }
  function showSlide(index) {
    if (index < 0) {
      index = sImg.length - 1;
    } else if (index >= sImg.length) {
      index = 0;
    }
    sRef.style.transform = \`translateX(-\${index}00%)\`;
    ci = index;
  }
  function nSlide() {
    showSlide(ci + 1);
  }
  function pSlide() {
    showSlide(ci - 1);
  }
  function startPlay() {
    if (playInterval !== null) return;
    playInterval = setInterval(() => {
      nSlide();
    }, 3000);
  }
  function stopPlay() {
    clearInterval(playInterval);
    playInterval = null;
  }
  window.addEventListener('resize', () => {
    showSlide(ci);
  });
  sImg.forEach(slideImg => {
    slideImg.addEventListener('load', sLoad);
  });
  pBtn.addEventListener('click', pSlide);
  nBtn.addEventListener('click', nSlide);
  sRef.addEventListener('mouseenter', stopPlay);
  sRef.addEventListener('mouseleave', startPlay);
  sWidth = sRef.offsetWidth;
  startPlay();
  showSlide(ci);
  `
    css += `
    .slideshow {
      position: relative;
      cursor: grab;
      overflow: hidden;
    }
    .slideshow:hover {
      cursor: pointer;
    }
    .slideshow:hover .prev,
    .slideshow:hover .next {
      display: block;
    }
    .slides {
      display: flex;
      transition: transform 0.3s ease-in-out;
    }
    .slides::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
    .slides-img {
      scroll-snap-align: start;
      flex-shrink: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .prev,
    .next {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 1;
      display: block;
      width: 40px;
      height: 40px;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      text-align: center;
      line-height: 40px;
      font-size: 24px;
      color: #333;
      text-decoration: none;
      transition: background-color 0.3s ease;
    }
    .prev:hover,
    .next:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
    .prev {
      left: 10px;
    }
    .next {
      right: 10px;
    }
    .slideshow:active {
      cursor: grabbing;
    }`
  }

  if (form.normalImages.length > 0) {
    const normal = generateNormalHtml(form)
    html += normal.html
    css += normal.css
  }

  if (form.companyName) {
    html += `<div class="company-name">${form.companyName}</div>`
    css += `
    .company-name {
      text-align: center;
      font-size: 14px;
      color: #949494;
      line-height: 18px;
    }`
  }
  html += `</div>`
  html += `</div>`

  if (hasBackup) {
    const click = generateBackup(backup[0])
    html += click.html
    css += click.css
  }

  const loading = generateLoadingHtml()
  html += loading.html
  css += loading.css
  js += loading.js

  css += normalImgCss

  return {
    css,
    html,
    js
  }
}

function generateNormalHtml(form) {
  const { normalImages } = form
  let html = ''
  normalImages.forEach((item) => {
    html += `<img  src="${item}" class="normal-image" alt="商品图片">`
  })

  return {
    css: '',
    html
  }
}

function generateBtnHtml(config) {
  const { form, btnImgPosition } = config
  return {
    css: `
      .button-fixed {
        position: fixed;
        bottom: ${btnImgPosition.bottom ?? 0};
        width: 100%;
      }
  `,
    html: `<div onclick="openApp()" class="button-fixed"><img class="normal-image" src="${form.buttonImage}" alt="跳转按钮"></div>`
  }
}

function generateClickAreas(areas) {
  let html = ''
  areas.forEach((item) => {
    const { left, top, width, height, zIndex } = item
    html += `<div class="click-part" onclick="openApp()" style="top:${top};left:${left};width:${width};height:${height};z-index: ${zIndex}"></div>`
  })
  return {
    css: `.click-part {position: absolute}`,
    html
  }
}
function generateClickBtns(btns) {
  let html = ''
  btns.forEach((item) => {
    const { left, top, width, height, text, color, background, size, flash, zIndex, position, bottom } = item
    const vertical = bottom ? `bottom:${bottom}` : `top:${top}`
    html += `<div class="click-btn-wrap"  style="position: ${position};${vertical};left:${left};width:${width};height: ${height};z-index: ${zIndex} ">
 <div class="click-btn ${flash ? 'flash' : ''}" style="background-color: ${background};color: ${color};font-size: ${size}px;">${text}</div></div>`
  })
  return {
    css: `.click-btn-wrap{box-sizing:border-box;position:absolute;padding: 10px;display: flex;flex-direction: row ;align-items: center;}
.click-btn {flex: 1;background: #00afff;padding: 10px;font-weight: 500;text-align: center;border-radius: 10px;color: white;}
`,
    html
  }
}
function generateClickImgs(imgs) {
  let html = ''
  imgs.forEach((item) => {
    const { left, top, width, url, flash, zIndex, position, bottom } = item
    const vertical = bottom ? `bottom:${bottom}` : `top:${top}`
    html += `<img class="normal-image  ${flash ? 'flash' : ''}" style="position: ${position};${vertical};left:${left};width:${width};z-index: ${zIndex} " src="${url}" alt="跳转按钮">`
  })
  return {
    css: '',
    html
  }
}

function generateBackup(backup) {
  const { left, top, width, url, descSize, descText, btnSize, btnText, flash } = backup
  const html = `
<div id="backup" onclick="hideBackupTooltip(event)"   style="display: none;background:rgba(0,0,0,0.2);position: absolute;top:0;left:0;right:0;bottom:0;z-index: 999">
 <div class="promotion-card ${flash ? 'flash' : ''}" onclick="openBackup(event)" style="position: absolute;top:${top};left:${left};width:${width};">
  <img
    class="normal-image"
    src="${url || defaultImg}"
  >
  ${url ? `` : `
      <div class="promotion-card-desc" style="font-size: ${descSize}px">${descText}</div>
      <div class="promotion-card-btn" style="font-size: ${btnSize}px">${btnText}</div>
  `}
</div>

</div>`

  return {
    css: `.promotion-card {position: relative;text-align: center}
.promotion-card-desc {position: absolute;top: 18%;right: 20%;left: 20%;color: #96252b;}
.promotion-card-btn {position: absolute;top: 72%;right: 0;left: 0;bottom: 0;font-size: 22px;color: #D53640;}
`,
    html
  }
}

function getCallApp() {
  return `(function(a,b){typeof exports==='object'&&typeof module!=='undefined'?module.exports=b():typeof define==='function'&&define.amd?define(b):(a=a||self,a.CallApp=b())}(this,(function(){'use strict';function _classCallCheck(a,b){if(!(a instanceof b)){throw new TypeError("Cannot call a class as a function");}}function _defineProperties(a,b){for(var i=0;i<b.length;i++){var c=b[i];c.enumerable=c.enumerable||false;c.configurable=true;if("value"in c)c.writable=true;Object.defineProperty(a,c.key,c)}}function _createClass(a,b,c){if(b)_defineProperties(a.prototype,b);if(c)_defineProperties(a,c);return a}function _defineProperty(a,b,c){if(b in a){Object.defineProperty(a,b,{value:c,enumerable:true,configurable:true,writable:true})}else{a[b]=c}return a}function _extends(){_extends=Object.assign||function(a){for(var i=1;i<arguments.length;i++){var b=arguments[i];for(var c in b){if(Object.prototype.hasOwnProperty.call(b,c)){a[c]=b[c]}}}return a};return _extends.apply(this,arguments)}var k=navigator.userAgent||'';var l=function l(a,b){var c=window,isNaN=c.isNaN;var d=a.split('.');var e=b.split('.');for(var i=0;i<3;i++){var f=Number(d[i]);var g=Number(e[i]);if(f>g)return 1;if(g>f)return-1;if(!isNaN(f)&&isNaN(g))return 1;if(isNaN(f)&&!isNaN(g))return-1}return 0};var m=function m(){var a=navigator.appVersion.match(/OS (\\d+)_(\\d+)_?(\\d+)?/);return Number.parseInt(a[1],10)};var n=function n(){var a=navigator.appVersion.match(/micromessenger\\/(\\d+\\.\\d+\\.\\d+)/i);return a[1]};var o=/android/i.test(k);var p=/iphone|ipad|ipod/i.test(k);var q=/micromessenger\\/([\\d.]+)/i.test(k);var r=/(weibo).*weibo__([\\d.]+)/i.test(k);var s=/(baiduboxapp)\\/([\\d.]+)/i.test(k);var t=/qq\\/([\\d.]+)/i.test(k);var u=/(qqbrowser)\\/([\\d.]+)/i.test(k);var v=/qzone\\/.*_qz_([\\d.]+)/i.test(k);var w=/chrome\\/[\\d.]+ mobile safari\\/[\\d.]+/i.test(k)&&o&&k.indexOf('Version')<0;function generateQS(b){var c=typeof b!=='undefined'?Object.keys(b).map(function(a){return"".concat(a,"=").concat(b[a])}).join('&'):'';return c?"?".concat(c):''}function buildScheme(a,b){var c=a.path,param=a.param;var d=b.scheme,customBuildScheme=b.buildScheme;if(typeof customBuildScheme!=='undefined'){return customBuildScheme(a,b)}var e=d.host,port=d.port,protocol=d.protocol;var f=port?":".concat(port):'';var g=e?"".concat(e).concat(f,"/"):'';var h=generateQS(param);return"".concat(protocol,"://").concat(g).concat(c).concat(h)}function generateScheme(a,b){var c=b.outChain;var d=buildScheme(a,b);if(typeof c!=='undefined'&&c){var e=c.protocol,path=c.path,key=c.key;d="".concat(e,"://").concat(path,"?").concat(key,"=").concat(encodeURIComponent(d))}return d}function generateIntent(b,c){var d=c.outChain;var e=c.intent,fallback=c.fallback;if(typeof e==='undefined')return'';var f=Object.keys(e);var g=f.map(function(a){return"".concat(a,"=").concat(e[a],";")}).join('');var h="#Intent;".concat(g,"S.browser_fallback_url=").concat(encodeURIComponent(fallback),";end;");var i=buildScheme(b,c);if(typeof d!=='undefined'&&d){var j=d.path,key=d.key;return"intent://".concat(j,"?").concat(key,"=").concat(encodeURIComponent(i)).concat(h)}i=i.slice(i.indexOf('//')+2);return"intent://".concat(i).concat(h)}function generateUniversalLink(a,b){var c=b.universal;if(typeof c==='undefined')return'';var d=c.host,pathKey=c.pathKey;var e=a.path,param=a.param;var f=generateQS(param);var g="https://".concat(d,"/").concat(e).concat(f);var h="https://".concat(d,"?").concat(pathKey,"=").concat(e).concat(f.replace('?','&'));return pathKey?h:g}function generateYingYongBao(a,b){var c=generateScheme(a,b);return"".concat(b.yingyongbao,"&android_schema=").concat(encodeURIComponent(c))}var x;var y;var z;function getSupportedProperty(){if(typeof document==='undefined')return;if(typeof document.hidden!=='undefined'){x='hidden';y='visibilitychange'}else if(typeof document.msHidden!=='undefined'){x='msHidden';y='msvisibilitychange'}else if(typeof document.webkitHidden!=='undefined'){x='webkitHidden';y='webkitvisibilitychange'}}getSupportedProperty();function isPageHidden(){if(typeof x==='undefined')return false;return document[x]}function evokeByLocation(a){window.top.location.href=a}function evokeByTagA(a){var b=document.createElement('a');b.setAttribute('href',a);b.style.display='none';document.body.appendChild(b);b.click()}function evokeByIFrame(a){if(!z){z=document.createElement('iframe');z.style.cssText='display:none;border:0;width:0;height:0;';document.body.appendChild(z)}z.src=a}function checkOpen(b,c){var d=setTimeout(function(){var a=isPageHidden();if(!a){b()}},c);if(typeof y!=='undefined'){document.addEventListener(y,function(){clearTimeout(d)})}else{window.addEventListener('pagehide',function(){clearTimeout(d)})}}var A=function(){function A(a){_classCallCheck(this,A);_defineProperty(this,"options",void 0);var b={timeout:2000};this.options=_extends(b,a)}_createClass(A,[{key:"generateScheme",value:function generateScheme$1(a){return generateScheme(a,this.options)}},{key:"generateIntent",value:function generateIntent$1(a){return generateIntent(a,this.options)}},{key:"generateUniversalLink",value:function generateUniversalLink$1(a){return generateUniversalLink(a,this.options)}},{key:"generateYingYongBao",value:function generateYingYongBao$1(a){return generateYingYongBao(a,this.options)}},{key:"checkOpen",value:function checkOpen$1(a){var b=this.options,logFunc=b.logFunc,timeout=b.timeout;return checkOpen(function(){if(typeof logFunc!=='undefined'){logFunc('failure')}a()},timeout)}},{key:"fallToAppStore",value:function fallToAppStore(){var a=this;this.checkOpen(function(){evokeByLocation(a.options.appstore)})}},{key:"fallToFbUrl",value:function fallToFbUrl(){var a=this;this.checkOpen(function(){evokeByLocation(a.options.fallback)})}},{key:"fallToCustomCb",value:function fallToCustomCb(a){this.checkOpen(function(){a()})}},{key:"open",value:function open(a){var b=this.options,universal=b.universal,appstore=b.appstore,logFunc=b.logFunc,intent=b.intent;var c=a.callback;var d=typeof universal!=='undefined';var e=this.generateScheme(a);var f;if(typeof logFunc!=='undefined'){logFunc('pending')}var g=!!this.options.isSupportWeibo;if(p){if(q&&l(n(),'7.0.5')===-1||r&&!g){evokeByLocation(appstore)}else if(m()<9){evokeByIFrame(e);f=this.fallToAppStore}else if(!d||t||u||v){evokeByTagA(e);f=this.fallToAppStore}else{evokeByLocation(this.generateUniversalLink(a))}}else if(q&&typeof this.options.yingyongbao!=='undefined'){evokeByLocation(this.generateYingYongBao(a))}else if(w){if(typeof intent!=='undefined'){evokeByLocation(this.generateIntent(a))}else{evokeByLocation(e);f=this.fallToFbUrl}}else if(q||s||r&&!g||v){evokeByLocation(this.options.fallback)}else{evokeByIFrame(e);f=this.fallToFbUrl}if(typeof c!=='undefined'){this.fallToCustomCb(c);return}if(!f)return;f.call(this)}}]);return A}();return A})));`
}

function generateLoadingHtml() {
  const html = `<div id="loading" class="loading"
                  style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0,  0.5); z-index: 9999; display: none; justify-content: center; align-items: center;"
  >
    <div class="loading__content"
         style="width: 100px; height: 100px; background: #fff; border-radius: 10px; display: flex; justify-content: center; align-items: center;"
    >
      <div class="loading__icon"
           style="width: 50px; height: 50px; border: 5px solid #ccc; border-top-color: #000; border-radius: 50%; animation: loading 1s linear infinite;"
      >
      </div>
    </div>
  </div>`
  const js = `
  function showLoading() {
    document.getElementById('loading').style.display = 'flex'
  }
  function hideLoading() {
    document.getElementById('loading').style.display = 'none'
  }
  `
  const css = `
    @keyframes loading {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  `
  return { html, js, css }
}
