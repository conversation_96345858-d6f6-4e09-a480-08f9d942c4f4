/*
 Navicat Premium Dump SQL

 Source Server         : 花粉直播@local@************@mysql@root
 Source Server Type    : MySQL
 Source Server Version : 80033 (8.0.33)
 Source Host           : ************:36008
 Source Schema         : live_db

 Target Server Type    : MySQL
 Target Server Version : 80033 (8.0.33)
 File Encoding         : 65001

 Date: 20/08/2025 13:22:12
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for live_aui_room_info_ext
-- ----------------------------
DROP TABLE IF EXISTS `live_aui_room_info_ext`;
CREATE TABLE `live_aui_room_info_ext`
(
    `id`                        bigint                                                        NOT NULL COMMENT '主键',
    `room_info_id`              bigint                                                        NOT NULL COMMENT '直播间ID',
    `message_group_id`          bigint                                                        NULL DEFAULT NULL COMMENT '消息群组ID(live_aui_message_group表的主键id)',
    `live_mode`                 tinyint                                                       NULL DEFAULT 0 COMMENT '直播模式(0-全屏,1-分屏)',
    `room_name`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '直播间名称',
    `course_type`               tinyint                                                       NULL DEFAULT 1 COMMENT '课程类型(0-直播课程,1-录播直播)',
    `course_group_id`           varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '课程分组',
    `introduction`              text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '直播简介',
    `image_id`                  bigint                                                        NULL DEFAULT NULL COMMENT '直播封面id',
    `image_url`                 varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '直播封面地址',
    `warm_up_video_id`          bigint                                                        NULL DEFAULT NULL COMMENT '暖场视频ID',
    `warm_up_video_url`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '暖场视频地址',
    `content_source`            tinyint                                                       NULL DEFAULT NULL COMMENT '内容来源(0-自定义,1-直播模板)',
    `course_video_id`           bigint                                                        NULL DEFAULT NULL COMMENT '课程视频ID(内容来源是自定义的时候设置)',
    `source_video_url`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '源视频地址',
    `source_message_content_id` bigint                                                        NULL DEFAULT NULL COMMENT '引用消息内容ID--关联历史直播间id,用户获取历史直播间的直播消息(内容来源是自定义的时候设置)',
    `live_template_id`          bigint                                                        NULL DEFAULT NULL COMMENT '直播模板ID(内容来源是直播模板的时候设置)',
    `enable_chat`               tinyint                                                       NULL DEFAULT 1 COMMENT '启用聊天：0-禁用，1-启用',
    `type`                      tinyint                                                       NULL DEFAULT 0 COMMENT '直播间类型(0-直播课程，1 营期课程)',
    `deleted`                   tinyint                                                       NULL DEFAULT 0 COMMENT '删除状态;0-未删除，1-删除',
    `create_by`                 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '创建人',
    `create_time`               datetime                                                      NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`                 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '更新人',
    `update_time`               datetime                                                      NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `listed_status`             int                                                           NULL DEFAULT NULL COMMENT '上架状态(0-上架,1-暂不上架,2-下架)',
    `listed_time`               datetime                                                      NULL DEFAULT NULL COMMENT '上/下架时间'
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '直播间live_aui_room_info信息扩展表'
  ROW_FORMAT = Dynamic;


alter table live_aui_room_info_ext add column message_downloaded bit(1) default 0 comment '消息是否已下载' after type;

SET FOREIGN_KEY_CHECKS = 1h
