<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.aui.mapper.LiveAuiRoomInfoBizMapper">

    <resultMap id="voMap" type="com.panda.pollen.aui.model.vo.LiveRoomInfoVo">
        <id column="id" property="id"/>
        <result column="title" property="title" />
        <result column="status" property="status" />
        <result column="mode" property="mode" />
        <result column="chat_id" property="chatId" />
        <result column="notice" property="notice" />
        <result column="anchor_id" property="anchorId" />
        <result column="started_at" property="startedAt" />
        <result column="stopped_at" property="stoppedAt" />

    </resultMap>

    <select id="listUnDownloadRoom" resultMap="voMap">
        SELECT
            r.*
        FROM
            live_aui_room_info r
                LEFT JOIN live_aui_room_info_ext ext ON ext.room_info_id = r.id
        WHERE
            r.deleted = 0
          AND r.`status` = 2
          AND ext.deleted = 0
          AND ext.listed_status = 0
          AND ext.message_downloaded = 0
    </select>


</mapper>
