<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.panda.pollen.aui.mapper.LiveAuiCampPeriodStudentBizMapper">
    <select id="page" parameterType="com.panda.pollen.aui.model.dto.LiveAuiCampPeriodStudentPageDto" resultType="com.panda.pollen.aui.model.vo.LiveAuiCampPeriodStudentPageVo">
        SELECT lacps.id,
               lacps.period_id,
               lacps.customer_id,
               lacps.attending_classes_times,
               lacps.finish_class_times,
               lacps.attending_classes_duration,
               wacuc.company_id,
               wacuc.wechat_open_id,
               wacuc.wechat_union_id,
               wacuc.user_name,
               wacuc.mobile,
               wacuc.email,
               wacuc.gender,
               wacuc.avatar,
               wacuc.country,
               wacuc.country_code,
               wacuc.province,
               wacuc.province_code,
               wacuc.city,
               wacuc.city_code,
               wacuc.district,
               wacuc.district_code,
               wacuc.login_ip,
               wacuc.login_address,
               wacuc.login_date,
               wacuc.external_user_id,
               wacuc.we_com_user_id,
               wacuc.remark,
               wacuc.follow_id,
               wacuc.follow_status
        FROM live_aui_camp_period_student lacps
                 LEFT JOIN live_customer_user wacuc ON lacps.customer_id = wacuc.id
            AND wacuc.deleted = 0
        <where>
            lacps.deleted = 0
            <if test="dto.followId != null and dto.followId != ''">
                and wacuc.follow_id = #{dto.followId}
            </if>
            <if test="dto.campPeriodId != null and dto.campPeriodId != ''">
                and lacps.period_id = #{dto.campPeriodId}
            </if>
        </where>
    </select>
    <select id="myPage" resultType="com.panda.pollen.aui.model.vo.LiveAuiCampPeriodStudentPageVo">
        SELECT
        lacps.id,
        lacps.period_id,
        per.period_name,
        lacps.customer_id,
        lacps.attending_classes_times,
        lacps.finish_class_times,
        lacps.attending_classes_duration,
        wacuc.company_id,
        wacuc.wechat_open_id,
        wacuc.wechat_union_id,
        wacuc.user_name,
        wacuc.mobile,
        wacuc.email,
        wacuc.gender,
        wacuc.avatar,
        wacuc.country,
        wacuc.country_code,
        wacuc.province,
        wacuc.province_code,
        wacuc.city,
        wacuc.city_code,
        wacuc.district,
        wacuc.district_code,
        wacuc.login_ip,
        wacuc.login_address,
        wacuc.login_date,
        wacuc.external_user_id,
        wacuc.we_com_user_id,
        wacuc.remark,
        wacuc.follow_id,
        su.user_name follow_name,
        wacuc.follow_status
        FROM
        live_aui_camp_period_student lacps
        LEFT JOIN live_customer_user wacuc ON lacps.customer_id = wacuc.id AND wacuc.deleted = 0
        left join live_aui_camp_period per on per.id = lacps.period_id
        left join sys_user su on su.user_id = wacuc.follow_id
        <where>
            lacps.deleted = 0
            <if test="followId != null and followId != ''">
                and wacuc.follow_id = #{followId}
            </if>
            <if test="campPeriodId != null and campPeriodId != ''">
                and lacps.period_id = #{campPeriodId}
            </if>
            <if test="null != createStartTime">
                and lacps.create_time &gt;= #{createStartTime}
            </if>
            <if test="null != createEndTime">
                and lacps.create_time &lt;= #{createEndTime}
            </if>
        </where>
    </select>
</mapper>