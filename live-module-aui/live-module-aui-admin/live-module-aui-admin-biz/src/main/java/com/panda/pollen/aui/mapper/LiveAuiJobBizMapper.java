package com.panda.pollen.aui.mapper;

import com.panda.pollen.aui.model.dto.LiveAuiRoomStartDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface LiveAuiJobBizMapper {
    /**
     * 获取待启动的直播间
     *
     * @param delay 延迟时间
     * @return 直播间列表
     */
    List<LiveAuiRoomStartDto> getRooms(@Param("delay") Integer delay);

}
