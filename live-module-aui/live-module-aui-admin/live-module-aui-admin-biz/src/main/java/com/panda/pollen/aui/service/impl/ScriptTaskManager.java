package com.panda.pollen.aui.service.impl;

import com.panda.pollen.aui.model.req.AliSendLiveMessageUserRequest;
import com.panda.pollen.aui.service.IAliCloudBizService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;
import com.panda.pollen.aui.util.MessageBuilder;
import com.panda.pollen.framework.lock.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * 脚本任务管理器
 * 负责管理脚本任务的调度和执行，支持分布式环境
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ScriptTaskManager {

    @Resource
    private ScheduledExecutorService scheduledExecutorService;

    @Resource
    private ScheduledExecutorService scriptScheduledExecutorService;

    @Resource
    private IAliCloudBizService iAliCloudBizService;

    @Resource
    private RedisLock redisLock;

    /**
     * 存储正在执行的任务，key为roomId，value为任务Future列表
     */
    private final ConcurrentHashMap<Long, List<ScheduledFuture<?>>> runningTasks = new ConcurrentHashMap<>();

    /**
     * 分布式锁前缀
     */
    private static final String LOCK_PREFIX = "script_task_";

    /**
     * 锁等待时间（毫秒）
     */
    private static final long LOCK_WAIT_TIME = 100;

    /**
     * 锁持有时间（毫秒）
     */
    private static final long LOCK_LEASE_TIME = 30000;

    /**
     * 启动脚本任务
     *
     * @param roomId 直播间ID
     * @param messageGroupId 消息组ID
     * @param scripts 脚本列表
     */
    public void startScriptTasks(Long roomId, Long messageGroupId, List<LiveAuiMessageTemplateScript> scripts) {
        if (scripts == null || scripts.isEmpty()) {
            log.warn("脚本列表为空，roomId: {}", roomId);
            return;
        }

        // 停止已存在的任务
        stopScriptTasks(roomId);

        String lockKey = LOCK_PREFIX + roomId;

        redisLock.tryLock(lockKey, LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.MILLISECONDS,
                () -> {
                    log.info("开始为直播间 {} 调度脚本任务，共 {} 个脚本", roomId, scripts.size());

                    LocalDateTime startTime = LocalDateTime.now();
                    List<ScheduledFuture<?>> futures = scripts.stream()
                            .filter(MessageBuilder::isValidScript)
                            .map(script -> scheduleScriptTask(roomId, messageGroupId, script, startTime))
                            .toList();

                    runningTasks.put(roomId, futures);
                    log.info("成功调度 {} 个脚本任务，roomId: {}", futures.size(), roomId);
                },
                () -> log.warn("获取分布式锁失败，可能其他实例正在处理，roomId: {}", roomId)
        );
    }

    /**
     * 调度单个脚本任务
     */
    private ScheduledFuture<?> scheduleScriptTask(Long roomId, Long messageGroupId, LiveAuiMessageTemplateScript script, LocalDateTime startTime) {

        LocalTime sendTime = script.getSendTime();
        LocalDateTime targetTime = startTime.with(sendTime);

        // 如果目标时间已过，则立即执行
        long delay = ChronoUnit.MILLIS.between(LocalDateTime.now(), targetTime);
        if (delay < 0) {
            delay = 0;
        }

        log.debug("调度脚本任务，roomId: {}, scriptId: {}, 延迟: {}ms", roomId, script.getId(), delay);

        ScheduledExecutorService executor = getScheduledExecutorService();
        return executor.schedule(() -> executeScriptTask(roomId, messageGroupId, script), delay, TimeUnit.MILLISECONDS);
    }

    /**
     * 执行脚本任务
     */
    private void executeScriptTask(Long roomId, Long messageGroupId, LiveAuiMessageTemplateScript script) {
        String lockKey = LOCK_PREFIX + roomId + "_" + script.getId();

        redisLock.tryLock(lockKey, LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.MILLISECONDS,
                () -> {
                    try {
                        log.info("执行脚本任务，roomId: {}, scriptId: {}, 内容: {}",
                                roomId, script.getId(), script.getMessageContent());

                        AliSendLiveMessageUserRequest request = buildMessageRequest(messageGroupId, script);
                        iAliCloudBizService.sendMessage2All(request);

                        log.info("脚本消息发送成功，roomId: {}, scriptId: {}", roomId, script.getId());
                    } catch (Exception e) {
                        log.error("脚本任务执行失败，roomId: {}, scriptId: {}", roomId, script.getId(), e);
                    }
                },
                () -> log.debug("脚本任务已被其他实例执行，roomId: {}, scriptId: {}", roomId, script.getId())
        );
    }

    /**
     * 获取定时任务执行器
     */
    private ScheduledExecutorService getScheduledExecutorService() {
        if (scriptScheduledExecutorService != null) {
            return scriptScheduledExecutorService;
        }
        if (scheduledExecutorService != null) {
            return scheduledExecutorService;
        }
        throw new IllegalStateException("未找到可用的ScheduledExecutorService");
    }

    /**
     * 构建消息请求对象
     */
    private AliSendLiveMessageUserRequest buildMessageRequest(Long messageGroupId, LiveAuiMessageTemplateScript script) {
        return MessageBuilder.buildMessageRequest(messageGroupId, script);
    }

    /**
     * 停止脚本任务
     *
     * @param roomId 直播间ID
     */
    public void stopScriptTasks(Long roomId) {
        List<ScheduledFuture<?>> futures = runningTasks.remove(roomId);
        if (futures != null && !futures.isEmpty()) {
            int cancelledCount = 0;
            for (ScheduledFuture<?> future : futures) {
                if (future.cancel(false)) {
                    cancelledCount++;
                }
            }
            log.info("停止脚本任务，roomId: {}, 取消任务数: {}/{}", roomId, cancelledCount, futures.size());
        }
    }

    /**
     * 获取任务状态
     *
     * @param roomId 直播间ID
     * @return 任务状态信息
     */
    public TaskStatus getTaskStatus(Long roomId) {
        List<ScheduledFuture<?>> futures = runningTasks.get(roomId);
        if (futures == null || futures.isEmpty()) {
            return new TaskStatus(roomId, 0, 0, 0);
        }

        int total = futures.size();
        int completed = 0;
        int cancelled = 0;

        for (ScheduledFuture<?> future : futures) {
            if (future.isDone()) {
                completed++;
            }
            if (future.isCancelled()) {
                cancelled++;
            }
        }

        return new TaskStatus(roomId, total, completed, cancelled);
    }

    /**
     * 任务状态信息
     */
    public record TaskStatus(Long roomId, int totalTasks, int completedTasks, int cancelledTasks) {

        public int getRunningTasks() {
            return totalTasks - completedTasks - cancelledTasks;
        }

        @Override
        public String toString() {
            return String.format("TaskStatus{roomId=%d, total=%d, completed=%d, cancelled=%d, running=%d}",
                    roomId, totalTasks, completedTasks, cancelledTasks, getRunningTasks());
        }
    }
}
