package com.panda.pollen.aui.model.vo;

import com.panda.pollen.common.annotation.Excel;
import com.panda.pollen.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: yh
 * @Date: 2025/9/1 20:44
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LiveAuiCampVo extends BaseEntity {

    /** 主键 */
    private Long id;

    /** 训练营名称 */
    @Excel(name = "训练营名称")
    private String campName;

    /** 录播使用类型[0 录播固定时长,1 录播时长比例] */
    @Excel(name = "录播使用类型[0 录播固定时长,1 录播时长比例]")
    private Long lightVideoType;

    /** 录播固定时长 */
    @Excel(name = "录播固定时长")
    private BigDecimal lightVideoFixedDuration;

    /** 录播时长比例 */
    @Excel(name = "录播时长比例")
    private BigDecimal lightVideoDurationRatio;

    /**
     * 删除标识
     */
    @Excel(name = "删除标识")
    private Integer deleted;

    /** 营期数量 */
    private Integer periodCount;
}
