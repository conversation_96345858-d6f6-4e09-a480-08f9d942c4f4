package com.panda.pollen.aui.script;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 脚本任务配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableAsync
@EnableScheduling
public class ScriptTaskConfig {

    /**
     * 脚本任务专用的定时任务执行器
     * 如果系统中已经有ScheduledExecutorService Bean，则不会创建这个Bean
     */
    @Bean("scriptScheduledExecutorService")
    public ScheduledExecutorService scriptScheduledExecutorService() {
        // 根据CPU核心数创建线程池
        int corePoolSize = Runtime.getRuntime().availableProcessors();
        ThreadFactory threadFactory = new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, "script-task-剧本执行-" + threadNumber.getAndIncrement());
                thread.setDaemon(true);
                return thread;
            }
        };
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(corePoolSize, threadFactory);
        executor.setRemoveOnCancelPolicy(true);
        log.info("创建脚本任务定时执行器，核心线程数: {}", corePoolSize);
        return executor;
    }
}
