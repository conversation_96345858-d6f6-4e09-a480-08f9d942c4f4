package com.panda.pollen.aui.script;

import com.panda.pollen.aui.model.req.AliSendLiveMessageUserRequest;
import com.panda.pollen.aui.service.IAliCloudBizService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;
import com.panda.pollen.framework.lock.RedisLock;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 简化版脚本任务管理器
 * 集成任务调度、消费和执行功能，减少代码复杂度
 */
@Slf4j
@Component
public class SimpleScriptTaskManager implements ApplicationRunner {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private IAliCloudBizService iAliCloudBizService;
    @Resource
    private RedisLock redisLock;

    private RDelayedQueue<ScriptTask> delayedQueue;
    private RQueue<ScriptTask> queue;
    private final ConcurrentHashMap<Long, TaskStatus> taskStatusMap = new ConcurrentHashMap<>();
    private volatile boolean running = false;

    @PostConstruct
    public void init() {
        queue = redissonClient.getQueue("script_task_queue");
        delayedQueue = redissonClient.getDelayedQueue(queue);
    }

    @Override
    public void run(ApplicationArguments args) {
        startConsumer();
    }

    /**
     * 启动脚本任务
     */
    public void startScriptTasks(Long roomId, Long messageGroupId, List<LiveAuiMessageTemplateScript> scripts) {
        if (scripts == null || scripts.isEmpty()) return;

        redisLock.tryLock("script_task_" + roomId, 100, 30000, TimeUnit.MILLISECONDS, () -> {
            stopScriptTasks(roomId);
            
            LocalDateTime startTime = LocalDateTime.now();
            int validCount = 0;
            
            for (LiveAuiMessageTemplateScript script : scripts) {
                if (MessageBuilder.isValidScript(script)) {
                    LocalDateTime scheduledTime = calculateScheduledTime(startTime, script.getSendTime());
                    long delayMillis = ChronoUnit.MILLIS.between(LocalDateTime.now(), scheduledTime);
                    
                    if (delayMillis > 0) {
                        ScriptTask task = new ScriptTask(roomId, messageGroupId, script, scheduledTime);
                        delayedQueue.offer(task, delayMillis, TimeUnit.MILLISECONDS);
                        validCount++;
                    }
                }
            }
            
            taskStatusMap.put(roomId, new TaskStatus(roomId, validCount, 0, 0));
            log.info("脚本任务调度完成，roomId: {}, 任务数: {}", roomId, validCount);
        }, () -> log.warn("获取锁失败，roomId: {}", roomId));
    }

    /**
     * 停止脚本任务
     */
    public void stopScriptTasks(Long roomId) {
        taskStatusMap.remove(roomId);
        log.info("脚本任务已停止，roomId: {}", roomId);
    }

    /**
     * 获取任务状态
     */
    public ScriptTaskStatus getTaskStatus(Long roomId) {
        TaskStatus status = taskStatusMap.getOrDefault(roomId, new TaskStatus(roomId, 0, 0, 0));
        return new ScriptTaskStatus(status.getRoomId(), status.getTotalTasks(), status.getCompletedTasks(), status.getCancelledTasks());
    }

    /**
     * 启动消费者
     */
    private void startConsumer() {
        if (running) return;
        running = true;
        
        // 启动消费者线程
        CompletableFuture.runAsync(() -> {
            while (running) {
                try {
                    ScriptTask task = queue.poll(5, TimeUnit.SECONDS);
                    if (task != null && isTaskValid(task)) {
                        executeTask(task);
                    }
                } catch (Exception e) {
                    log.error("消费任务异常", e);
                    try { Thread.sleep(5000); } catch (InterruptedException ie) { break; }
                }
            }
        });
    }

    /**
     * 执行任务
     */
    private void executeTask(ScriptTask task) {
        String lockKey = "execute_" + task.getTaskId();
        
        redisLock.tryLock(lockKey, 100, 30000, TimeUnit.MILLISECONDS, () -> {
            try {
                AliSendLiveMessageUserRequest request = MessageBuilder.buildMessageRequest(task.getMessageGroupId(), task.getScript());
                iAliCloudBizService.sendLiveMessageUser(request);
                updateTaskStatus(task.getRoomId(), true, false);
                log.info("脚本任务执行成功，roomId: {}, scriptId: {}", task.getRoomId(), task.getScript().getId());
            } catch (Exception e) {
                log.error("脚本任务执行失败，roomId: {}, scriptId: {}", task.getRoomId(), task.getScript().getId(), e);
                
                if (task.getRetryCount() < 3) {
                    task.setRetryCount(task.getRetryCount() + 1);
                    long retryDelay = 1000L * (1L << task.getRetryCount());
                    delayedQueue.offer(task, retryDelay, TimeUnit.MILLISECONDS);
                    log.info("任务将重试，roomId: {}, 重试次数: {}", task.getRoomId(), task.getRetryCount());
                } else {
                    updateTaskStatus(task.getRoomId(), false, true);
                    log.error("任务重试次数已达上限，roomId: {}", task.getRoomId());
                }
            }
        }, () -> log.debug("获取执行锁失败，taskId: {}", task.getTaskId()));
    }

    /**
     * 计算调度时间
     */
    private LocalDateTime calculateScheduledTime(LocalDateTime startTime, LocalTime sendTime) {
        if (sendTime == null) return startTime;
        LocalDateTime scheduledTime = startTime.toLocalDate().atTime(sendTime);
        return scheduledTime.isBefore(startTime) ? scheduledTime.plusDays(1) : scheduledTime;
    }

    /**
     * 检查任务是否有效
     */
    private boolean isTaskValid(ScriptTask task) {
        TaskStatus status = taskStatusMap.get(task.getRoomId());
        return status != null && status.getTotalTasks() > 0;
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Long roomId, boolean completed, boolean failed) {
        TaskStatus status = taskStatusMap.get(roomId);
        if (status != null) {
            if (completed) status.setCompletedTasks(status.getCompletedTasks() + 1);
            if (failed) status.setCancelledTasks(status.getCancelledTasks() + 1);
        }
    }

    @Data
    public static class ScriptTask {
        private Long roomId;
        private Long messageGroupId;
        private LiveAuiMessageTemplateScript script;
        private LocalDateTime scheduledTime;
        private int retryCount = 0;
        private String taskId;

        public ScriptTask() {}

        public ScriptTask(Long roomId, Long messageGroupId, LiveAuiMessageTemplateScript script, LocalDateTime scheduledTime) {
            this.roomId = roomId;
            this.messageGroupId = messageGroupId;
            this.script = script;
            this.scheduledTime = scheduledTime;
            this.taskId = roomId + "_" + script.getId() + "_" + scheduledTime.toString();
        }
    }

    @Data
    public static class TaskStatus {
        private final Long roomId;
        private final int totalTasks;
        private int completedTasks;
        private int cancelledTasks;

        public TaskStatus(Long roomId, int totalTasks, int completedTasks, int cancelledTasks) {
            this.roomId = roomId;
            this.totalTasks = totalTasks;
            this.completedTasks = completedTasks;
            this.cancelledTasks = cancelledTasks;
        }

        public int getRunningTasks() {
            return totalTasks - completedTasks - cancelledTasks;
        }
    }
}
