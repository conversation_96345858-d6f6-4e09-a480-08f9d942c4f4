package com.panda.pollen.aui.model.dto;

import com.panda.pollen.common.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@ApiModel("图片信息分页查询参数")
@EqualsAndHashCode(callSuper = true)
@Data
public class ImagePageDto extends BasePageQuery {
    @ApiModelProperty("文件分组")
    private String fileCategory;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("上传时间-开始时间")
    private LocalDate startTime;

    @ApiModelProperty("上传时间-结束时间")
    private LocalDate endTime;

}
