package com.panda.pollen.aui.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel("直播间启动信息")
@Data
public class LiveAuiRoomStartDto {
    @ApiModelProperty("直播间ID")
    private String id;
    @ApiModelProperty("聊天室ID")
    private String chatId;
    @ApiModelProperty("开始时间")
    private String startedAt;
    @ApiModelProperty("结束时间")
    private String stoppedAt;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("消息组ID")
    private String messageGroupId;
}
