package com.panda.pollen.aui.script;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 脚本任务消费者
 * 负责从Redis延迟队列中消费任务并执行
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ScriptTaskConsumer implements ApplicationRunner {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ScriptTaskManager scriptTaskManager;

    /**
     * 消费者线程池
     */
    
    private final ExecutorService executorService = Executors.newFixedThreadPool(
            Runtime.getRuntime().availableProcessors(), 
            r -> {
                Thread thread = new Thread(r, "script-task-consumer-");
                thread.setDaemon(true);
                return thread;
            }
    );

    /**
     * 是否正在运行
     */
    private volatile boolean running = false;

    @Override
    public void run(ApplicationArguments args) {
        startConsumer();
    }

    /**
     * 启动消费者
     */
    public void startConsumer() {
        if (running) {
            log.warn("脚本任务消费者已经在运行中");
            return;
        }

        running = true;
        log.info("启动脚本任务消费者");

        // 启动多个消费者线程
        int consumerCount = Runtime.getRuntime().availableProcessors();
        for (int i = 0; i < consumerCount; i++) {
            final int consumerId = i;
            CompletableFuture.runAsync(() -> consumeTask(consumerId), executorService);
        }
    }

    /**
     * 消费任务
     */
    private void consumeTask(int consumerId) {
        RQueue<ScriptTask> queue = redissonClient.getQueue(ScriptTaskManager.QUEUE_NAME);
        
        log.info("脚本任务消费者 {} 开始工作", consumerId);
        
        while (running) {
            try {
                // 阻塞获取任务，超时时间5秒
                ScriptTask task = queue.poll();
                
                if (task != null) {
                    log.debug("消费者 {} 获取到任务: {}", consumerId, task.getTaskId());
                    
                    // 检查任务是否仍然有效（房间是否还在运行）
                    if (isTaskValid(task)) {
                        // 执行任务
                        scriptTaskManager.executeScriptTask(task);
                    } else {
                        log.debug("任务已无效，跳过执行: {}", task.getTaskId());
                    }
                } else {
                    // 没有任务时短暂休眠
                    Thread.sleep(1000);
                }
                
            } catch (InterruptedException e) {
                log.info("消费者 {} 被中断", consumerId);
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("消费者 {} 处理任务时发生错误", consumerId, e);
                // 发生错误时短暂休眠，避免快速重试
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        log.info("脚本任务消费者 {} 停止工作", consumerId);
    }

    /**
     * 检查任务是否仍然有效
     */
    private boolean isTaskValid(ScriptTask task) {
        // 检查任务状态是否存在，如果不存在说明任务已被停止
        ScriptTaskStatus status = scriptTaskManager.getTaskStatus(task.getRoomId());
        return status != null && status.getTotalTasks() > 0;
    }
}
