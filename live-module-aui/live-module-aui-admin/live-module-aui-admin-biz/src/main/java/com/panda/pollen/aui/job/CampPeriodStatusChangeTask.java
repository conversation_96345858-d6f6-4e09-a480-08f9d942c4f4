package com.panda.pollen.aui.job;

import com.alibaba.fastjson2.JSON;
import com.panda.pollen.aui.service.ILiveAuiCampPeriodBizService;
import com.panda.pollen.aui.model.dto.TaskParamDTO;
import com.panda.pollen.common.utils.Dater;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: yh
 * @Date: 2025/9/2 20:40
 * @Description: 训练营营期状态改变任务
 */
@SuppressWarnings("unused")
@Slf4j
@Component
public class CampPeriodStatusChangeTask {
    @Resource
    private ILiveAuiCampPeriodBizService liveAuiCampPeriodBizService;

    @XxlJob("campPeriodTask.executeStatusChangeTask")
    public void executeStatusChangeTask() {
        String jobParams = XxlJobHelper.getJobParam();
        TaskParamDTO param = JSON.parseObject(jobParams, TaskParamDTO.class);
        long start = System.currentTimeMillis();
        log.info("【Robot Split】{}---------->> Start Task ", Dater.ymdhms());
        liveAuiCampPeriodBizService.executeStatusChangeTask();
        log.info("【Robot Split】---------->>耗时：{} End",  System.currentTimeMillis() - start);
    }
}
