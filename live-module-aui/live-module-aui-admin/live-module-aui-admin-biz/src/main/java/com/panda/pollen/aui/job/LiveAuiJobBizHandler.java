package com.panda.pollen.aui.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.panda.pollen.aui.model.dto.TaskParamPlayDto;
import com.panda.pollen.aui.service.ILiveAuiJobBizService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Slf4j
@Component
public class LiveAuiJobBizHandler {
    @Resource
    private ILiveAuiJobBizService iLiveAuiJobBizService;


    @XxlJob(value = "liveAuiJobBizHandler.play")
    public void play() {
        log.info(CharSequenceUtil.format("任务ID:【{}】----开始执行：{}", XxlJobHelper.getJobId(), DateUtil.now()));
        // 传入的参数(都是一个字符串)
        String param = XxlJobHelper.getJobParam().trim();
        try {
            log.info("{}：开始进行：{}", LocalDateTimeUtil.now(), "播放直播脚本");
            // 参数解析
            TaskParamPlayDto dto = JSONUtil.toBean(param, TaskParamPlayDto.class);
            // 任务执行
            iLiveAuiJobBizService.play(dto.getDelay());
            XxlJobHelper.handleSuccess(CharSequenceUtil.format("自动任务成功执行完成:{},传入的参数为：{}", DateUtil.now(), param));
        } catch (Exception e) {
            log.error("自动任务执行发生错误! ", e);
            XxlJobHelper.handleFail(e.getMessage());
        }
    }
}