package com.panda.pollen.aui.model.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.panda.pollen.common.annotation.Excel;
import com.panda.pollen.common.encrypt.annotation.EncryptField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class LiveAuiCampPeriodStudentPageVo {
    /** 主键 */
    private Long id;

    /** 营期ID */
    private Long periodId;

    /** 营期ID */
    @Excel(name = "营期名称", sort = 1)
    private String periodName;

    /** 客户id */
    private String customerId;

    /** 到课次数 */
    @Excel(name = "到课次数", sort = 7)
    private Long attendingClassesTimes;

    /** 完课次数 */
    @Excel(name = "完课次数", sort = 8)
    private Long finishClassTimes;

    /** 上课时长;单位 分钟 */
    @Excel(name = "上课时长", sort = 9)
    private Long attendingClassesDuration;

    /**
     * 公司ID（first_dept_id）
     */
    private Long companyId;

    /**
     * 微信open_id
     */
    private String wechatOpenId;

    /**
     * 微信union_id
     */
    @Excel(name = "微信union_id", sort = 4)
    private String wechatUnionId;

    /**
     * 用户昵称
     */
    @Excel(name = "微信昵称", sort = 3)
    private String userName;

    /**
     * 手机号码
     */
    @EncryptField
    @Excel(name = "手机号", sort = 2)
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户性别（0未知 1男 2女）
     */
    private Integer gender;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 省份
     */
    private String province;

    /**
     * 省份代码
     */
    private String provinceCode;

    /**
     * 城市
     */
    private String city;

    /**
     * 城市代码
     */
    private String cityCode;

    /**
     * 区县
     */
    private String district;

    /**
     * 区县代码
     */
    private String districtCode;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录地址
     */
    private String loginAddress;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 企微客户id
     */
    private String externalUserId;

    /**
     * 企微员工id
     */
    private String weComUserId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 跟进人id
     */
    private Long followId;

    /**
     * 跟进人名称
     */
    @Excel(name = "跟进人名称", sort = 5)
    private String followName;

    /**
     * 跟进状态[0 待跟进，1 跟进中，2 待签单，3 已签单，4 复购，5 无效]
     */
    @Excel(name = "跟进状态", sort = 6,readConverterExp = "0=待跟进,1=跟进中,2=待签单,3=已签单,4=复购,5=无效")
    private Integer followStatus;
}
