package com.panda.pollen.aui.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyuncs.live.model.v20161101.CreateLiveMessageGroupResponse;
import com.github.pagehelper.PageHelper;
import com.panda.pollen.aui.mapper.LiveAuiCampPeriodClassBizMapper;
import com.panda.pollen.aui.mapper.LiveAuiLiveBizMapper;
import com.panda.pollen.aui.mapstruct.MsLiveAuiCampPeriodClassBiz;
import com.panda.pollen.aui.mapstruct.MsLiveAuiLiveBiz;
import com.panda.pollen.aui.model.dto.*;
import com.panda.pollen.aui.model.enums.EnumListedStatus;
import com.panda.pollen.aui.model.enums.EnumLiveStatus;
import com.panda.pollen.aui.model.req.AliCreateLiveMessageGroupRequest;
import com.panda.pollen.aui.model.vo.*;
import com.panda.pollen.aui.service.ILiveAuiCampPeriodClassBizService;
import com.panda.pollen.aui.service.ILiveAuiLiveBizService;
import com.panda.pollen.aui.service.ILiveAuiMessageTemplateBizService;
import com.panda.pollen.aui.system.domain.*;
import com.panda.pollen.aui.system.service.*;
import com.panda.pollen.common.core.domain.entity.SysUser;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.exception.base.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LiveAuiCampPeriodClassBizServiceImpl implements ILiveAuiCampPeriodClassBizService {

    @Resource
    private ILiveAuiCampPeriodChapterService iLiveAuiCampPeriodChapterService;
    @Resource
    private ILiveAuiCampPeriodClassService iLiveAuiCampPeriodClassService;
    @Resource
    private ILiveAuiCampPeriodService iLiveAuiCampPeriodService;
    @Resource
    private ILiveAuiCampService iLiveAuiCampService;
    @Resource
    private LiveAuiLiveBizMapper liveAuiLiveBizMapper;
    @Resource
    private ILiveAuiImageFileService iLiveAuiImageFileService;
    @Resource
    private ILiveAuiVideoFileService iLiveAuiVideoFileService;
    @Resource
    private ILiveAuiLiveBizService iLiveAuiLiveBizService;
    @Resource
    private MsLiveAuiCampPeriodClassBiz msLiveAuiCampPeriodClassBiz;
    @Resource
    private ILiveAuiMessageTemplateService liveAuiMessageTemplateService;
    @Resource
    private ILiveAuiRoomInfoService iLiveAuiRoomInfoService;
    @Resource
    private ILiveAuiRoomInfoExtService iLiveAuiRoomInfoExtService;
    @Resource
    private MsLiveAuiLiveBiz msLiveAuiLiveBiz;
    @Resource
    private ILiveAuiMessageGroupService iLiveAuiMessageGroupService;
    @Resource
    private ILiveAuiRoomStreamUrlInfoService iLiveAuiRoomStreamUrlInfoService;
    @Resource
    private ILiveAuiRoomAssistantService iLiveAuiRoomAssistantService;
    @Resource
    private ILiveAuiRoomRobotService iLiveAuiRoomRobotService;
    @Resource
    private LiveAuiCampPeriodClassBizMapper liveAuiCampPeriodClassBizMapper;
    @Resource
    private ILiveAuiRoomActiveMemberService iLiveAuiRoomActiveMemberService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveClass(LiveAuiCampPeriodClassSaveDto dto) {
        // 参数校验
        this.validateParams(dto, null);
        //创建直播间数据
        Long roomId = this.createClassLiveRoom(dto);
        // 创建课节数据
        LiveAuiCampPeriodClass campPeriodClass = this.msLiveAuiCampPeriodClassBiz.dto2entity(dto, roomId);
        // 获取最大课节排序
        int maxSort = this.getMaxSort(dto.getPeriodId());
        campPeriodClass.setClassSort(maxSort + 1);
        return this.iLiveAuiCampPeriodClassService.save(campPeriodClass);
    }

    /**
     * 获取最大课节排序
     *
     * @param periodId 课时ID
     * @return 最大课节排序
     */
    private int getMaxSort(String periodId) {
        //  //查询所有章节
        List<Long> chapterIds = this.getChapterIds(periodId);
        // 获取最大课节排序
        return this.getMaxClassSort(chapterIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateClass(LiveAuiCampPeriodClassUpdateDto dto) {
        LiveAuiCampPeriodClassSaveDto saveDto = this.msLiveAuiCampPeriodClassBiz.copyProperties(dto);
        // 课节id
        String classId = dto.getClassId();
        // 查询课节
        LiveAuiCampPeriodClass liveAuiCampPeriodClass = this.iLiveAuiCampPeriodClassService.getById(classId);
        // 直播间id
        Long roomId = liveAuiCampPeriodClass.getRoomId();
        // 参数验证
        this.validateParams(saveDto, roomId);
        //查询课节
        if (ObjectUtil.isEmpty(liveAuiCampPeriodClass)) {
            throw new ServiceException("未找到ID为 " + classId + " 的课节");
        }
        //更新直播间数据
        Boolean updateClassLiveRoom = this.updateClassLiveRoom(dto, String.valueOf(liveAuiCampPeriodClass.getRoomId()));
        if (Boolean.FALSE.equals(updateClassLiveRoom)) {
            throw new ServiceException("更新课程直播间失败");
        }
        //更新课节
        liveAuiCampPeriodClass.setLiveTime(dto.getLiveTime());
        // 课节时长
        liveAuiCampPeriodClass.setClassDuration(dto.getClassDuration());
        // 更新课节
        return this.iLiveAuiCampPeriodClassService.updateById(liveAuiCampPeriodClass);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateClassTime(LiveAuiCampPeriodClassUpdateTimeDto dto) {
        // 参数验证 开始时间不能早于/等于当前时间
        LocalDateTime liveTime = dto.getLiveTime();
        if (liveTime == null || liveTime.isBefore(LocalDateTime.now().plusMinutes(5))) {
            throw new BaseException("直播开始时间不能低于当前时间5分钟");
        }
        //查询课程
        LiveAuiCampPeriodClass periodClass = this.iLiveAuiCampPeriodClassService.getById(dto.getClassId());
        if (ObjectUtils.isEmpty(periodClass)) {
            throw new BaseException("课程不存在");
        }
        //查询直播间拓展表
        LiveAuiRoomInfoExt roomInfoExt = this.iLiveAuiRoomInfoExtService.lambdaQuery()
                .eq(LiveAuiRoomInfoExt::getRoomInfoId, periodClass.getRoomId()).list().stream().findFirst().orElse(null);
        if (ObjectUtils.isEmpty(roomInfoExt)) {
            throw new BaseException("课程扩展信息不存在");
        }
        //计算结束时间
        LocalDateTime stopTime = this.getStopTime(roomInfoExt.getContentSource().intValue(), String.valueOf(roomInfoExt.getCourseVideoId()), String.valueOf(roomInfoExt.getLiveTemplateId()), dto.getLiveTime());
        //修改直播间时间
        boolean updateLiveRoom = this.iLiveAuiRoomInfoService.lambdaUpdate()
                .set(LiveAuiRoomInfo::getStartedAt, liveTime)
                .set(LiveAuiRoomInfo::getStoppedAt, stopTime)
                .eq(LiveAuiRoomInfo::getId, periodClass.getRoomId())
                .update();
        if (!updateLiveRoom) {
            throw new BaseException("修改直播间时间失败");
        }
        //创建推流任务
        String sourceUrl = iLiveAuiLiveBizService.getSourceUrl(String.valueOf(roomInfoExt.getCourseVideoId()), String.valueOf(roomInfoExt.getLiveTemplateId()));
        boolean taskSaved = iLiveAuiLiveBizService.createCreateLivePullToPush(sourceUrl, periodClass.getRoomId(), liveTime, stopTime);
        if (!taskSaved) {
            throw new BaseException("创建拉流转推任务失败");
        }
        periodClass.setLiveTime(liveTime);
        periodClass.setClassDuration(1);
        return this.iLiveAuiCampPeriodClassService.updateById(periodClass);
    }

    @Override
    public boolean sortClass(LiveAuiCampPeriodClassSortDto dto) {
//查询课节
        LiveAuiCampPeriodClass targetClass = this.iLiveAuiCampPeriodClassService.getById(dto.getClassId());
        if (ObjectUtil.isEmpty(targetClass)) {
            throw new RuntimeException("未找到ID为 " + dto.getClassId() + " 的课节");
        }
        Integer oldSortOrder = targetClass.getClassSort();
        Integer newSortOrder = dto.getSort();
        // 如果排序值没有变化，直接返回
        if (oldSortOrder.equals(newSortOrder)) {
            return true;
        }
        // 根据新旧排序值的关系调整其他项目的排序
        List<LiveAuiCampPeriodClass> periodClasses;
        if (newSortOrder > oldSortOrder) {
            // 新排序值更大，需要将中间的项目排序值减1
            periodClasses = this.iLiveAuiCampPeriodClassService.lambdaQuery().eq(LiveAuiCampPeriodClass::getId, dto.getClassId())
                    .gt(LiveAuiCampPeriodClass::getClassSort, oldSortOrder)
                    .le(LiveAuiCampPeriodClass::getClassSort, newSortOrder)
                    .list();
            periodClasses.forEach(item -> item.setClassSort(item.getClassSort() - 1));
        } else {
            // 新排序值更小，需要将中间的项目排序值加1
            periodClasses = this.iLiveAuiCampPeriodClassService.lambdaQuery().eq(LiveAuiCampPeriodClass::getId, dto.getClassId())
                    .gt(LiveAuiCampPeriodClass::getClassSort, newSortOrder)
                    .le(LiveAuiCampPeriodClass::getClassSort, oldSortOrder)
                    .list();
            periodClasses.forEach(item -> item.setClassSort(item.getClassSort() + 1));
        }
        this.iLiveAuiCampPeriodClassService.updateBatchById(periodClasses);
        // 更新目标项目的排序值
        targetClass.setClassSort(newSortOrder);
        return this.iLiveAuiCampPeriodClassService.updateById(targetClass);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteClass(String classId) {
        //查询课节
        LiveAuiCampPeriodClass targetClass = this.iLiveAuiCampPeriodClassService.getById(classId);
        if (ObjectUtil.isEmpty(targetClass)) {
            throw new ServiceException("未找到ID为 " + classId + " 的课节");
        }
        //查询课节下的直播状态
        Long roomId = targetClass.getRoomId();
        LiveAuiRoomInfo roomInfo = this.iLiveAuiRoomInfoService.getById(roomId);
        if (ObjectUtil.isEmpty(roomInfo)) {
            throw new ServiceException("未找到ID为 " + roomId + " 的直播间");
        }
        //直播状态
        Long status = roomInfo.getStatus();
        if (EnumLiveStatus.LIVE_STATUS_ON.getVal() == status) {
            throw new ServiceException("直播间正在直播中，请先结束直播");
        }
        //删除课节下的直播任务
        Boolean delete = this.iLiveAuiLiveBizService.delete(String.valueOf(roomId));
        if (!delete) {
            throw new ServiceException("删除课程直播任务失败");
        }
        //删除课节
        return this.iLiveAuiCampPeriodClassService.removeById(targetClass);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copySaveClass(LiveAuiCampPeriodClassCopyDto dto) {
        // 查询源课节
        LiveAuiCampPeriodClass sourceClass = this.iLiveAuiCampPeriodClassService.getById(dto.getSourceClassId());
        if (ObjectUtil.isEmpty(sourceClass)) {
            throw new ServiceException("未找到ID为 " + dto.getSourceClassId() + " 的课节");
        }
        //查询目标营期 目标章节是否存在
        List<LiveAuiCampPeriodChapter> periodChapters = this.iLiveAuiCampPeriodChapterService.lambdaQuery()
                .eq(LiveAuiCampPeriodChapter::getId, dto.getTargetChapterId())
                .eq(LiveAuiCampPeriodChapter::getPeriodId, dto.getTargetPeriodId())
                .list();
        if (CollUtil.isEmpty(periodChapters)) {
            throw new ServiceException("未找到ID为 " + dto.getTargetChapterId() + " 的章节");
        }
        //查询直播间
        LiveAuiRoomInfo roomInfo = this.iLiveAuiRoomInfoService.getById(sourceClass.getRoomId());
        if (ObjectUtil.isEmpty(roomInfo)) {
            throw new ServiceException("未找到课节ID为 " + dto.getSourceClassId() + " 的直播间");
        }
        //查询营期下的所有章节
        List<Long> chapterIds = getChapterIds(dto.getTargetPeriodId());
        //查询章节下的所有课节
        List<LiveAuiCampPeriodClass> periodClasses = this.iLiveAuiCampPeriodClassService.lambdaQuery()
                .in(LiveAuiCampPeriodClass::getChapterId, chapterIds).list();
        if (CollUtil.isNotEmpty(periodClasses)) {
            List<Long> roomIds = periodClasses.stream().map(LiveAuiCampPeriodClass::getRoomId).distinct().toList();
            List<LiveAuiRoomInfo> roomInfos = this.iLiveAuiRoomInfoService.listByIds(roomIds);
            if (CollUtil.isNotEmpty(roomInfos)) {
                List<String> titles = roomInfos.stream().map(LiveAuiRoomInfo::getTitle).toList();
                boolean contains = titles.contains(roomInfo.getTitle());
                if (contains) {
                    throw new BaseException("课节名称不可重复");
                }
            }
        }
        //复制直播间
        Long roomId = this.copyLiveRoom(roomInfo);
        //复制课节
        LiveAuiCampPeriodClass copyClass = new LiveAuiCampPeriodClass();
        copyClass.setRoomId(roomId);
        copyClass.setChapterId(Long.valueOf(dto.getTargetChapterId()));
        int maxClassSort = this.getMaxSort(dto.getTargetPeriodId());
        copyClass.setClassSort(maxClassSort + 1);
        return this.iLiveAuiCampPeriodClassService.save(copyClass);
    }

    @Override
    public boolean moveSaveClass(LiveAuiCampPeriodClassMoveDto dto) {
        //查询目标章节是否存在
        LiveAuiCampPeriodChapter targetChapter = this.iLiveAuiCampPeriodChapterService.getById(dto.getTargetChapterId());
        if (ObjectUtil.isEmpty(targetChapter)) {
            throw new ServiceException("未找到ID为 " + dto.getTargetChapterId() + " 的章节");
        }
        //查询需要移动的课节
        List<LiveAuiCampPeriodClass> periodClasses = this.iLiveAuiCampPeriodClassService.lambdaQuery()
                .in(LiveAuiCampPeriodClass::getId, dto.getClassIds())
                .list();
        if (ObjectUtil.isEmpty(periodClasses)) {
            throw new ServiceException("未找到ID为 " + dto.getClassIds() + " 的课节");
        }
        periodClasses.forEach(item -> item.setChapterId(targetChapter.getId()));
        return this.iLiveAuiCampPeriodClassService.updateBatchById(periodClasses);
    }

    /**
     * 根据章节ID分页查询课节
     *
     * @param chapterIds 章节ID
     * @param pageNum    页码
     * @param pageSize   每页大小
     * @return 分页课节列表
     */
    @Override
    public List<LiveAuiCampPeriodClassPageVo> queryClassByChapterIdPage(List<Long> chapterIds, Integer pageNum, Integer pageSize) {
        if (CollUtil.isEmpty(chapterIds)) {
            return new ArrayList<>();
        }
        PageHelper.startPage(pageNum, pageSize);
        List<LiveAuiCampPeriodClassPageVo> classPageVos = liveAuiCampPeriodClassBizMapper.queryClassByChapterIdPage(chapterIds);
        if (CollUtil.isNotEmpty(classPageVos)) {
            classPageVos.forEach(item -> {
                item.setClassType(1);
                item.setClassTypeName("录播直播");
                item.setLiveType(1);
                item.setLiveTypeName("web端直播");
                item.setLiveMode(1);
                item.setLiveModeName("新分页直播");
                item.setLiveStatusName(EnumLiveStatus.of(item.getLiveStatus()).getDesc());
                // 查询助教
                item.setAssistantList(this.iLiveAuiLiveBizService.generateAssistant(String.valueOf(item.getRoomId())));
            });
        }
        return classPageVos;
    }


    @Override
    public List<LiveAuiCampPeriodClassPageVo> pageQueryClass(String periodId, Integer pageNum, Integer pageSize) {
        //查询营期下的所有课程
        List<Long> chapterIds = this.getChapterIds(periodId);
        return queryClassByChapterIdPage(chapterIds, pageNum, pageSize);
    }

    @Override
    public LiveAuiCampPeriodClassDetailVo queryClassDetail(String classId) {
        // 1. 查询课节基础信息
        LiveAuiCampPeriodClassPageVo pageVo = this.liveAuiCampPeriodClassBizMapper.queryClassDetail(classId);
        if (ObjectUtil.isEmpty(pageVo)) {
            return new LiveAuiCampPeriodClassDetailVo();
        }
        LiveAuiCampPeriodClassDetailVo classDetailVo = BeanUtil.copyProperties(pageVo, LiveAuiCampPeriodClassDetailVo.class);
        if (StrUtil.isNotBlank(classDetailVo.getLiveTemplateId())) {
            classDetailVo.setLiveTemplateName(iLiveAuiLiveBizService.generateLiveTemplateId(classDetailVo.getLiveTemplateId()));
        }
        // 查询助教
        classDetailVo.setAssistantList(this.iLiveAuiLiveBizService.generateAssistant(String.valueOf(classDetailVo.getRoomId())));
        return classDetailVo;
    }

    @Override
    public List<LiveAuiCampSelectVo> queryCampSelect() {
        List<LiveAuiCamp> camps = this.iLiveAuiCampService.list();
        if (ObjectUtil.isEmpty(camps)) {
            return List.of();
        }
        return camps.stream().map(item -> {
            LiveAuiCampSelectVo vo = new LiveAuiCampSelectVo();
            vo.setCampId(String.valueOf(item.getId()));
            vo.setCampName(item.getCampName());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<LiveAuiCampPeriodSelectVo> queryPeriodSelect(String campId, String currentPeriodId) {
        List<LiveAuiCampPeriod> periods = this.iLiveAuiCampPeriodService.lambdaQuery()
                .eq(LiveAuiCampPeriod::getCampId, campId)
                //排除当前营期
                .ne(LiveAuiCampPeriod::getId, currentPeriodId)
                .list();
        if (ObjectUtil.isEmpty(periods)) {
            return List.of();
        }
        return periods.stream().map(item -> {
            LiveAuiCampPeriodSelectVo vo = new LiveAuiCampPeriodSelectVo();
            vo.setCampId(String.valueOf(item.getCampId()));
            vo.setPeriodId(String.valueOf(item.getId()));
            vo.setPeriodName(item.getPeriodName());
            return vo;
        }).collect(Collectors.toList());
    }


    @Override
    public List<LiveAuiCampPeriodClassCopyPageVo> pageQueryOtherPeriodClass(LiveAuiCampPeriodClassCopyQueryDto dto) {
        //根据营期查询章节
        List<Long> chapterIds = this.getChapterIds(dto.getPeriodId());
        if (CollUtil.isEmpty(chapterIds)) {
            return List.of();
        }
        List<LiveAuiCampPeriodClassPageVo> classPageVos = this.queryClassByChapterIdPage(chapterIds, dto.getPageNum(), dto.getPageSize());
        if (CollUtil.isEmpty(classPageVos)) {
            return List.of();
        }
        if (StrUtil.isNotBlank(dto.getClassName())) {
            classPageVos = classPageVos.stream()
                    .filter(item -> StrUtil.contains(item.getClassName(), dto.getClassName()))
                    .collect(Collectors.toList());
        }
        List<LiveAuiCampPeriodClassCopyPageVo> vos = new ArrayList<>();
        classPageVos.forEach(item -> {
            LiveAuiCampPeriodClassCopyPageVo vo = new LiveAuiCampPeriodClassCopyPageVo();
            BeanUtil.copyProperties(item, vo);
            vo.setPeriodId(dto.getPeriodId());
            vos.add(vo);
        });
        return vos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveBatch(LiveAuiCampPeriodClassSaveBatchDto dto) {
        // 排课日期列表
        Stream
                // 从指定日期开的日期流
                .iterate(dto.getStartDate(), date -> date.plusDays(1))
                // 过滤出指定星期的日期
                .filter(date -> dto.getWeekDays().contains(date.getDayOfWeek().getValue()))
                // 截取指定数量的日期
                .limit(dto.getClassNos())
                // 指定每个日期的开始时间
                .map(date -> LocalDateTime.of(date, LocalTime.parse(dto.getStartTime())))
                // 映射为课节保存参数
                .map(startDateTime -> {
                    LiveAuiCampPeriodClassSaveDto saveDto = new LiveAuiCampPeriodClassSaveDto();
                    this.msLiveAuiCampPeriodClassBiz.copyProperties(dto, saveDto);
                    // 设置开始时间
                    saveDto.setLiveTime(startDateTime);
                    return saveDto;
                })
                // 课节保存
                .forEach(this::saveClass);
        return true;
    }

    //批量下架课程
    @Override
    public void putOnOrOffCours(List<LiveAuiCampPeriodClass> classList) {
        List<Long> roomIdList = classList.stream().map(LiveAuiCampPeriodClass::getRoomId).collect(Collectors.toList());
        boolean result = this.iLiveAuiLiveBizService.batchOnOrOffShelf(roomIdList);
        if (!result) {
            throw new ServiceException("批量上/下架营期下课程失败！请联系管理员！");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyBatch(List<LiveAuiCampPeriodClassCopyDto> listClass) {
        boolean result = true;
        if (null != listClass && !listClass.isEmpty()) {
            for (LiveAuiCampPeriodClassCopyDto aClass : listClass) {
                result = result && this.copySaveClass(aClass);
            }
        }
        return result;
    }

    /**
     * 获取最大课节排序
     *
     * @param chapterIds 章节ID
     * @return 最大课节排序
     */
    private int getMaxClassSort(List<Long> chapterIds) {
        LiveAuiCampPeriodClass maxClassSort = this.iLiveAuiCampPeriodClassService.lambdaQuery()
                .in(LiveAuiCampPeriodClass::getChapterId, chapterIds)
                .select(LiveAuiCampPeriodClass::getClassSort)
                .orderByDesc(LiveAuiCampPeriodClass::getClassSort)
                .last("LIMIT 1")
                .one();
        return Optional.ofNullable(maxClassSort).map(LiveAuiCampPeriodClass::getClassSort).orElse(0);
    }

    /**
     * 获取章节ID
     *
     * @param periodId 课时ID
     * @return 章节ID
     */
    private List<Long> getChapterIds(String periodId) {
        return this.iLiveAuiCampPeriodChapterService.lambdaQuery()
                .eq(LiveAuiCampPeriodChapter::getPeriodId, Long.valueOf(periodId)).list()
                .stream().map(LiveAuiCampPeriodChapter::getId)
                .toList();
    }

    /**
     * 创建直播间
     *
     * @param dto 创建直播间参数
     * @return 直播间ID
     */
    private Long createClassLiveRoom(LiveAuiCampPeriodClassSaveDto dto) {
        LiveRoomCreateDto liveRoomCreateDto = this.msLiveAuiCampPeriodClassBiz.dto2dto(dto);
        // 设置直播间状态(在课节是默认上架)
        liveRoomCreateDto.setListedStatus(EnumListedStatus.LISTED_STATUS_ONLINE.getVal());
        // 设置直播结束时间
        LocalDateTime stopTime = this.getStopTime(dto.getContentSource(), dto.getCourseVideoId(), dto.getLiveTemplateId(), dto.getLiveTime());
        liveRoomCreateDto.setStopTime(stopTime);
        return this.iLiveAuiLiveBizService.createLiveRoom(liveRoomCreateDto);
    }

    /**
     * 更新直播间
     *
     * @param dto 更新直播间参数
     * @return 更新结果
     */
    private Boolean updateClassLiveRoom(LiveAuiCampPeriodClassUpdateDto dto, String liveRoomId) {
        LiveRoomCreateDto liveRoomCreateDto = this.msLiveAuiCampPeriodClassBiz.dto2dto(dto);
        liveRoomCreateDto.setListedStatus(EnumListedStatus.LISTED_STATUS_ONLINE.getVal());
        LocalDateTime stopTime = this.getStopTime(dto.getContentSource(), dto.getCourseVideoId(), dto.getLiveTemplateId(), dto.getLiveTime());
        liveRoomCreateDto.setStopTime(stopTime);
        LiveRoomUpdateDto liveRoomUpdateDto = BeanUtil.copyProperties(liveRoomCreateDto, LiveRoomUpdateDto.class);
        liveRoomUpdateDto.setId(liveRoomId);
        return this.iLiveAuiLiveBizService.update(liveRoomUpdateDto);
    }

    /**
     * 获取直播结束时间
     *
     * @param contentSource  内容源
     * @param courseVideoId  课程视频ID
     * @param liveTemplateId 直播模板ID
     * @param liveTime       直播开始时间
     * @return 直播结束时间
     */
    private LocalDateTime getStopTime(Integer contentSource, String courseVideoId, String liveTemplateId, LocalDateTime liveTime) {
        if (1 == contentSource && CharSequenceUtil.isNotBlank(liveTemplateId)) {
            LiveAuiMessageTemplate templateServiceById = liveAuiMessageTemplateService.getById(Long.valueOf(liveTemplateId));
            if (ObjectUtils.isEmpty(templateServiceById)) {
                throw new ServiceException("直播模板不存在");
            }
            courseVideoId = !ObjectUtils.isEmpty(templateServiceById.getAppVideoId()) ? String.valueOf(templateServiceById.getAppVideoId()) : String.valueOf(templateServiceById.getPcVideoId());
        }
        AtomicReference<LocalDateTime> stopTime = new AtomicReference<>(liveTime);
        // 课程视频
        Optional.ofNullable(courseVideoId)
                .ifPresent(id -> {
                    //课程视频
                    LiveAuiVideoFile courseVideo = this.iLiveAuiVideoFileService.getById(id);
                    Optional.ofNullable(courseVideo).orElseThrow(() -> new BaseException("请选择课程视频"));
                    Optional.of(courseVideo)
                            .ifPresent(v -> {
                                Float duration = v.getDuration();
                                //直播间时长 直播时间+时长=结束时间
                                stopTime.set(liveTime.plusSeconds(duration.longValue()));
                            });
                });
        log.info("设置直播结束时间: {}", stopTime);
        return stopTime.get();
    }

    /**
     * 参数验证
     *
     * @param dto 参数
     */
    private void validateParams(LiveAuiCampPeriodClassSaveDto dto, Long roomId) {
        //章节ID
        String chapterId = dto.getChapterId();
        if (ObjectUtil.isEmpty(chapterId)) {
            throw new BaseException("请选择章节");
        }
        LiveAuiCampPeriodChapter chapter = this.iLiveAuiCampPeriodChapterService.getById(chapterId);
        if (ObjectUtil.isEmpty(chapter)) {
            throw new BaseException("请正确选择章节");
        }
        // 直播间名称标题校验
        //获取所有章节
        List<Long> chapterIds = this.getChapterIds(dto.getPeriodId());
        if (CollUtil.isNotEmpty(chapterIds)) {
            //查询所有课节
            List<LiveAuiCampPeriodClass> periodClasses = this.iLiveAuiCampPeriodClassService.lambdaQuery().in(LiveAuiCampPeriodClass::getChapterId, chapterIds).list();
            if (CollUtil.isNotEmpty(periodClasses)) {
                List<Long> roomIds = periodClasses.stream().map(LiveAuiCampPeriodClass::getRoomId).toList();
                if (roomIds != null) {
                    String title = dto.getTitle();
                    List<String> selectByTitle = this.liveAuiLiveBizMapper.selectByTitle(title, 1, roomIds);
                    if (!ObjectUtils.isEmpty(selectByTitle)) {
                        throw new BaseException("课程名称已存在");
                    }
                }
            }
        }

        // 参数验证 开始时间不能早于/等于当前时间
        LocalDateTime liveTime = dto.getLiveTime();
        if (liveTime == null || liveTime.isBefore(LocalDateTime.now().plusMinutes(5))) {
            throw new BaseException("直播开始时间不能低于当前时间5分钟");
        }
        // 主播id
        String anchorId = dto.getAnchorId();
        List<Long> anchorIds = Collections.singletonList(Long.valueOf(anchorId));

        List<SysUser> anchorSysUsers = this.liveAuiLiveBizMapper.getUserById(anchorIds);
        if (anchorSysUsers == null || anchorSysUsers.isEmpty()) {
            throw new BaseException("主播不存在");
        }
        // 主播昵称
        String nickName = anchorSysUsers.stream().findFirst().get().getNickName();
        dto.setAnchorNick(nickName);
        anchorSysUsers.stream()
                .findFirst()
                .ifPresent(sysUser -> dto.setAnchor(sysUser.getUserName()));

        if (1 == dto.getWarmType()) {
            // 暖场视频
            String warmUpVideoId = dto.getWarmUpVideoId();
            if (!CharSequenceUtil.isEmpty(warmUpVideoId)) {
                LiveAuiVideoFile warmVideo = this.iLiveAuiVideoFileService.getById(warmUpVideoId);
                if (warmVideo == null) {
                    throw new BaseException("暖场视频不存在");
                }
            }
        }
        if (2 == dto.getWarmType()) {
            // 暖场图片
            String warmUpImageId = dto.getWarmUpImageId();
            if (warmUpImageId != null && !warmUpImageId.isEmpty()) {
                LiveAuiImageFile warmImage = this.iLiveAuiImageFileService.getById(warmUpImageId);
                if (warmImage == null) {
                    throw new BaseException("暖场图片不存在");
                }
            }
        }
        // 助教核对
        List<String> assistantIds = dto.getAssistantIds();
        List<Long> assistantLongIds = Optional.ofNullable(assistantIds)
                .orElse(Collections.emptyList())
                .stream()
                .map(Long::parseLong)
                .toList();
        if (CollUtil.isEmpty(assistantIds)) {
            throw new BaseException("助教不能为空");
        }
        List<SysUser> assistantSysUsers = this.liveAuiLiveBizMapper.getUserById(assistantLongIds);
        if (assistantSysUsers == null || assistantSysUsers.isEmpty()) {
            throw new BaseException("助教不存在");
        }
        //处理id为空的情况
        if (CharSequenceUtil.isEmpty(dto.getWarmUpVideoId())) {
            dto.setWarmUpVideoId(null);
        }
        if (CharSequenceUtil.isEmpty(dto.getWarmUpImageId())) {
            dto.setWarmUpImageId(null);
        }
        if (CharSequenceUtil.isEmpty(dto.getCourseVideoId())) {
            dto.setCourseVideoId(null);
        }
        if (CharSequenceUtil.isEmpty(dto.getLiveTemplateId())) {
            dto.setLiveTemplateId(null);
        }
    }

    private Long copyLiveRoom(LiveAuiRoomInfo roomInfo) {
        Long roomInfoId = roomInfo.getId();
        LiveAuiRoomInfoExt infoExt = this.iLiveAuiRoomInfoExtService.lambdaQuery().eq(LiveAuiRoomInfoExt::getRoomInfoId, roomInfoId).one();
        //查询助教
        List<LiveAuiRoomAssistant> liveAuiRoomAssistantListCopy = this.iLiveAuiRoomAssistantService.lambdaQuery().eq(LiveAuiRoomAssistant::getRoomInfoId, roomInfoId).list();
        // 获取消息组
        AliCreateLiveMessageGroupRequest request = this.msLiveAuiLiveBiz.po2req(roomInfo);
        CreateLiveMessageGroupResponse response = this.iLiveAuiLiveBizService.createLiveMessageGroup(request);
        // 1. 创建基础直播间
        roomInfo.setChatId(response.getGroupId());
        roomInfo.setId(null);
        roomInfo.setStatus((long) EnumLiveStatus.LIVE_STATUS_PREPARE.getVal());
        boolean riSaved = this.iLiveAuiRoomInfoService.save(roomInfo);
        if (!riSaved) {
            throw new BaseException("保存直播间数据失败");
        }
        // 2. 消息组数据
        LiveAuiMessageGroup liveAuiMessageGroup = this.msLiveAuiLiveBiz.resAndDto2Po(response, request);
        liveAuiMessageGroup.setRoomInfoId(roomInfo.getId());
        boolean mgSaved = this.iLiveAuiMessageGroupService.save(liveAuiMessageGroup);
        if (!mgSaved) {
            throw new BaseException("保存消息组数据失败");
        }
        // 3. 创建直播间扩展信息
        infoExt.setRoomInfoId(roomInfo.getId());
        infoExt.setId(null);
        infoExt.setMessageGroupId(liveAuiMessageGroup.getId());
        boolean rieSaved = this.iLiveAuiRoomInfoExtService.save(infoExt);
        if (!rieSaved) {
            throw new BaseException("保存直播间扩展信息失败");
        }
        // 4. 保存流URL信息
        List<LiveAuiRoomStreamUrlInfo> liveAuiRoomStreamUrlInfos = this.iLiveAuiLiveBizService.parseStreamUrlInfo(roomInfo.getId(), liveAuiMessageGroup.getGroupId());
        boolean rsuiSaved = this.iLiveAuiRoomStreamUrlInfoService.saveBatch(liveAuiRoomStreamUrlInfos);
        if (!rsuiSaved) {
            throw new BaseException("保存流URL信息失败");
        }
        // 5. 创建助教关联信息
        liveAuiRoomAssistantListCopy.forEach(item -> {
            item.setRoomInfoId(roomInfo.getId());
            item.setId(null);
        });
        boolean assistantSaved = this.iLiveAuiRoomAssistantService.saveBatch(liveAuiRoomAssistantListCopy);
        if (!assistantSaved) {
            throw new BaseException("保存助教关联信息失败");
        }
        // 7、自动选择10个机器人关联到当前直播间
        List<Long> assistantIds = liveAuiRoomAssistantListCopy.stream().map(LiveAuiRoomAssistant::getId).toList();
        List<LiveAuiRoomRobot> liveAuiRoomRobotMsgRecords = this.iLiveAuiLiveBizService.getLiveAuiRoomRobots(roomInfo);
        liveAuiRoomRobotMsgRecords.forEach(liveAuiRoomRobotMsgRecord -> {
            // 随机选择一个助教
            Long userId = RandomUtil.randomEle(assistantIds);
            liveAuiRoomRobotMsgRecord.setUserId(userId);
        });
        boolean robotSaved = this.iLiveAuiRoomRobotService.saveBatch(liveAuiRoomRobotMsgRecords);
        if (!robotSaved) {
            throw new BaseException("保存机器人关联信息失败");
        }
        // 8、设置直播间的人员信息
        List<LiveAuiRoomActiveMember> liveAuiRoomActiveMembers = this.iLiveAuiLiveBizService.getLiveAuiRoomActiveMembers(roomInfo);
        boolean memberSaved = this.iLiveAuiRoomActiveMemberService.saveBatch(liveAuiRoomActiveMembers);
        if (!memberSaved) {
            throw new BaseException("保存直播间人员信息失败");
        }
        return roomInfo.getId();
    }

}