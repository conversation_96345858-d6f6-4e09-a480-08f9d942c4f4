package com.panda.pollen.aui.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@ApiModel("分类更新参数")
@Data
public class CategoryUpdateDto {
    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty(value = """
            分类名称。
                最多支持 64 个字节。
                UTF-8 编码。
            """, required = true)
    @NotBlank(message = "分类名称不能为空")
    private String cateName;
}
