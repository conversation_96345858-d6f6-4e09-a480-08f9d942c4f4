package com.panda.pollen.aui.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.panda.pollen.aui.system.domain.LiveAuiCampPeriod;
import com.panda.pollen.aui.system.domain.LiveAuiCampPeriodTags;
import com.panda.pollen.common.annotation.Excel;
import com.panda.pollen.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: yh
 * @Date: 2025/8/29 21:48
 * @Description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LiveAuiCampPeriodDto extends BaseEntity {

    /** 主键 */
    private Long id;

    /** 主键字符串 */
    private String idStr;

    /** 训练营ID */
    private Long campId;

    /** 训练营ID 字符串*/
    private String campIdStr;

    /** 营期名称 */
    private String periodName;

    /** 营期简介 */
    private String description;

    /** 封面(背景图id) */
    private Long imageId;

    /** 封面(背景图id字符串) */
    private String imageIdStr;

    /** 营期详情（详情介绍） */
    private String detailDescription;

    /** 招生开始时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recruitStartTime;

    /** 招生结束时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recruitEndTime;

    /** 开课时间类型 */
    private String curriculumTimeType;

    /** 开课开始时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime curriculumStartTime;

    /** 开课结束时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime curriculumEndTime;

    /** 课程目录模式 */
    private Long courseMode;

    /** 上架状态[0 下架，1 稍后上架，2 上架 ] */
    private Long publishStatus;

    /** 上架时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    /** 下架时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime soldOutTime;

    /** 营期状态[0 未开始，1 招生中，2 等待开营，3 开营中，4 已结束] */
    private Long campPeriodStatus;

    /** 期数 */
    private Long sort;

    /** 营期章节 */
    private List<LiveAuiCampPeriodTags> listTag;
}
