package com.panda.pollen.aui.task;

import com.alibaba.fastjson2.JSON;
import com.panda.pollen.aui.service.ILiveAuiCampPeriodBizService;
import com.panda.pollen.aui.service.ILiveAuiMessageRecordBizService;
import com.panda.pollen.aui.task.dto.TaskParamDTO;
import com.panda.pollen.common.utils.Dater;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: yh
 * @Date: 2025/9/16 20:38
 * @Description:
 */
@Slf4j
@Component
public class DownloadLiveRoomMessageTask {

    @Resource
    private ILiveAuiMessageRecordBizService liveAuiMessageRecordBizService;

    @XxlJob("liveMessageTask.executeDownloadLiveRoomMessageTask")
    public void executeDownloadLiveRoomMessageTask() {
        String jobParams = XxlJobHelper.getJobParam();
        TaskParamDTO param = JSON.parseObject(jobParams, TaskParamDTO.class);
        long start = System.currentTimeMillis();
        log.info("【直播间消息下载】{}---------->> Start Task ", Dater.ymdhms());
        liveAuiMessageRecordBizService.executeDownloadLiveRoomMessageTask();
        log.info("【直播间消息下载】---------->>耗时：{} End",  System.currentTimeMillis() - start);
    }
}
