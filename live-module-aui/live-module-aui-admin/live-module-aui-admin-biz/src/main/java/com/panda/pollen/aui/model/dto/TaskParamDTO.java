package com.panda.pollen.aui.model.dto;

import groovy.transform.builder.Builder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p> 任务运行参数DTO <p/>
 * <AUTHOR>
 * @date 2025年7月7日 13:07:46 
 * @version v1.0   
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskParamDTO {

	private Long start;
	
	private Long end;
	
	private Integer mode;
	
	public Integer getMode() {
		if(mode == null) {
			return 100;
		}
		return mode;
	}
	
}
