package com.panda.pollen.aui.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.aliyuncs.vod.model.v20170321.*;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.github.pagehelper.PageHelper;
import com.panda.pollen.aui.mapper.LiveAuiMaterialImageBizMapper;
import com.panda.pollen.aui.mapstruct.MsLiveAuiMaterialImageBiz;
import com.panda.pollen.aui.model.dto.ImagePageDto;
import com.panda.pollen.aui.model.dto.ImageSaveDto;
import com.panda.pollen.aui.model.dto.ImageUpdateDto;
import com.panda.pollen.aui.model.dto.UploadImageUrlAndAuthGetDto;
import com.panda.pollen.aui.model.enums.EnumDeleteImageType;
import com.panda.pollen.aui.model.req.*;
import com.panda.pollen.aui.model.vo.LiveAuiImageFileVo;
import com.panda.pollen.aui.service.IAliCloudBizService;
import com.panda.pollen.aui.service.ILiveAuiMaterialImageBizService;
import com.panda.pollen.aui.system.domain.LiveAuiCategory;
import com.panda.pollen.aui.system.domain.LiveAuiImageAuth;
import com.panda.pollen.aui.system.domain.LiveAuiImageFile;
import com.panda.pollen.aui.system.domain.LiveAuiImageMezzanine;
import com.panda.pollen.aui.system.service.ILiveAuiCategoryService;
import com.panda.pollen.aui.system.service.ILiveAuiImageAuthService;
import com.panda.pollen.aui.system.service.ILiveAuiImageFileService;
import com.panda.pollen.aui.system.service.ILiveAuiImageMezzanineService;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.exception.base.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LiveAuiMaterialImageBizServiceImpl implements ILiveAuiMaterialImageBizService {
    @Resource
    private IAliCloudBizService iAliCloudBizService;
    @Resource
    private MsLiveAuiMaterialImageBiz msLiveAuiMaterialImageBiz;
    @Resource
    private ILiveAuiCategoryService iLiveAuiCategoryService;
    @Resource
    private ILiveAuiImageAuthService iLiveAuiImageAuthService;
    @Resource
    private ILiveAuiImageFileService iLiveAuiImageFileService;
    @Resource
    private ILiveAuiImageMezzanineService iLiveAuiImageMezzanineService;
    @Resource
    private LiveAuiMaterialImageBizMapper liveAuiMaterialImageBizMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public LiveAuiImageAuth getUploadImageUrlAndAuth(UploadImageUrlAndAuthGetDto dto) {
        // 参数部署
        String originalFileName = dto.getOriginalFileName();
        String title = dto.getTitle();
        if (CharSequenceUtil.isBlank(title)) {
            title = new File(originalFileName).getName();
        }
        dto.setTitle(title);
        // 分类id
        String categoryId = dto.getCategoryId();
        LiveAuiCategory liveAuiCategory = null;
        if (!ObjectUtils.isEmpty(categoryId)) {
            // 本地分类
            liveAuiCategory = iLiveAuiCategoryService.getById(categoryId);
            if (liveAuiCategory == null) {
                throw new BaseException("分类不存在！");
            }
            GetCategoriesResponse response = iAliCloudBizService.getCategories(liveAuiCategory.getCateId());
            if (ObjectUtil.isNull(response)) {
                throw new BaseException("阿里分类不存在！");
            }
        }
        // 远程获取图片上传地址和凭证
        AliCreateUploadImageRequest req = msLiveAuiMaterialImageBiz.dto2req(dto);
        // 分组逻辑
        if (liveAuiCategory != null) {
            req.setCateId(liveAuiCategory.getCateId());
        }
        CreateUploadImageResponse response = iAliCloudBizService.createUploadImage(req);
        if (response == null) {
            throw new BaseException("获取图片上传地址和凭证失败！");
        }
        // 保存图片上传凭证记录
        LiveAuiImageAuth liveAuiImageAuth = msLiveAuiMaterialImageBiz.res2UploadImagePo(req, response);
        // 实际响应结果
        liveAuiImageAuth.setJsonRes((JSONObject) JSON.toJSON(response));
        iLiveAuiImageAuthService.save(liveAuiImageAuth);
        return liveAuiImageAuth;
    }

    @Override
    public LiveAuiImageFile get(String id) {
        LiveAuiImageFile liveAuiImageFile = iLiveAuiImageFileService.getById(id);
        if (liveAuiImageFile == null) {
            throw new BaseException("图片不存在");
        }
        return liveAuiImageFile;
    }

    @Override
    public boolean delete(Long id) {
        LiveAuiImageFile liveAuiImageFile = iLiveAuiImageFileService.getById(id);
        if (liveAuiImageFile == null) {
            throw new BaseException("图片不存在");
        }
        // 先判断图片是否存在
        AliGetImageInfoRequest req = new AliGetImageInfoRequest();
        req.setImageId(liveAuiImageFile.getImageId());
        GetImageInfoResponse res = iAliCloudBizService.getImageInfo(req);
        // 图片存在，才去删除图片
        if (res != null) {
            AliDeleteImageRequest request = new AliDeleteImageRequest();
            request.setDeleteImageType(EnumDeleteImageType.IMAGE_ID.getCode());
            request.setImageIds(liveAuiImageFile.getImageId());
            // 远程删除图片
            DeleteImageResponse response = iAliCloudBizService.deleteImage(request);
            if (response == null || CharSequenceUtil.isBlank(response.getRequestId())) {
                throw new BaseException("阿里云删除图片失败");
            }
        }
        return iLiveAuiImageFileService.removeById(id);
    }

    @Override
    public List<LiveAuiImageFileVo> page(ImagePageDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        LocalDate endTime = dto.getEndTime();
        if (!ObjectUtils.isEmpty(endTime)) {
            // 处理查询时间的结束时间
            dto.setEndTime(endTime.plusDays(BigDecimal.ONE.longValue()));
        }
        // 分页获取本地数据库的图片信息
        return liveAuiMaterialImageBizMapper.page(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean save(ImageSaveDto dto) {
        String imageId = dto.getImageId();
        // 获取认证信息
        LiveAuiImageAuth liveAuiImageAuth = iLiveAuiImageAuthService.lambdaQuery()
                .eq(LiveAuiImageAuth::getImageId, imageId)
                .one();
        if (liveAuiImageAuth == null) {
            throw new BaseException("图片上传凭证不存在");
        }
        // 如果传入了分类id，就查询分类信息
        String categoryId = dto.getCategoryId();
        LiveAuiCategory liveAuiCategory = null;
        if (CharSequenceUtil.isNotBlank(categoryId)) {
            liveAuiCategory = iLiveAuiCategoryService.lambdaQuery()
                    .eq(LiveAuiCategory::getId, categoryId)
                    .one();
            if (liveAuiCategory == null) {
                throw new BaseException("分类不存在");
            }
        }

        AliGetImageInfoRequest request = new AliGetImageInfoRequest();
        request.setImageId(imageId);
        GetImageInfoResponse response = iAliCloudBizService.getImageInfo(request);
        if (response == null) {
            throw new BaseException("从阿里云获取图片失败");
        }
        LiveAuiImageFile liveAuiImageFile = msLiveAuiMaterialImageBiz.res2ImagePo(response.getImageInfo());
        liveAuiImageFile.setImageAuthId(liveAuiImageAuth.getId());
        liveAuiImageFile.setRequestId(response.getRequestId());
        // 有分类信息的情况下才设置分类相关的数据
        if (liveAuiCategory != null) {
            // 图片分类id-本地业务数据
            liveAuiImageFile.setCategoryId(Long.valueOf(categoryId));
            // 图片分类id
            liveAuiImageFile.setCateId(liveAuiCategory.getCateId());
            // 图片分类名称
            liveAuiImageFile.setCateName(liveAuiCategory.getCateName());
        }
        // 请求响应的原始值
        liveAuiImageFile.setJsonRes((JSONObject) JSON.toJSON(response));
        // 查询已经存在的图片记录
        LiveAuiImageFile liveAuiImageFileExist = iLiveAuiImageFileService.lambdaQuery().eq(LiveAuiImageFile::getImageId, imageId).one();
        if (liveAuiImageFileExist != null) {
            liveAuiImageFile.setId(liveAuiImageFileExist.getId());
        }
        boolean fileSaved = iLiveAuiImageFileService.saveOrUpdate(liveAuiImageFile);
        if (!fileSaved) {
            throw new BaseException("本地保存图片记录失败");
        }
        // 获取图片 mezzanine 信息
        GetImageInfoResponse.ImageInfo.Mezzanine mezzanine = response.getImageInfo().getMezzanine();
        LiveAuiImageMezzanine liveAuiImageMezzanine = msLiveAuiMaterialImageBiz.res2MezzaninePo(mezzanine);
        liveAuiImageMezzanine.setImageFileId(liveAuiImageFile.getId());
        LiveAuiImageMezzanine liveAuiImageMezzanineExist = iLiveAuiImageMezzanineService.lambdaQuery().eq(LiveAuiImageMezzanine::getImageFileId, liveAuiImageFile.getId()).one();
        if (liveAuiImageMezzanineExist != null) {
            liveAuiImageMezzanine.setId(liveAuiImageMezzanineExist.getId());
        }
        boolean mezzanineSaved = iLiveAuiImageMezzanineService.saveOrUpdate(liveAuiImageMezzanine);
        if (!mezzanineSaved) {
            throw new BaseException("本地保存图片记录失败");
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(ImageUpdateDto dto) {
        if(null == dto.getIdStr() || dto.getIdStr().isEmpty()){
            throw new ServiceException("参数不正确！");
        }
        dto.setId(Long.valueOf(dto.getIdStr()));
        // 获取图片信息
        LiveAuiImageFile liveAuiImageFile = iLiveAuiImageFileService.getById(dto.getId());
        if (liveAuiImageFile == null) {
            throw new BaseException("图片不存在");
        }
        dto.setImageId(liveAuiImageFile.getImageId());
        dto.setCateId(liveAuiImageFile.getCateId());
        dto.setDescription(liveAuiImageFile.getDescription());
        dto.setTags(liveAuiImageFile.getTags());

        // 远程更新图片信息
        AliUpdateImageInfosRequest.UpdateContent updateContent = msLiveAuiMaterialImageBiz.dto2req(dto);
        AliUpdateImageInfosRequest req = new AliUpdateImageInfosRequest();
        req.setUpdateContent(Collections.singletonList(updateContent));
        UpdateImageInfosResponse res = iAliCloudBizService.updateImageInfos(req);
        if (res == null) {
            throw new BaseException("更新图片信息失败！");
        }
        Long cateId = updateContent.getCateId();
        LiveAuiCategory liveAuiCategory;
        LambdaUpdateChainWrapper<LiveAuiImageFile> wrapper = iLiveAuiImageFileService.lambdaUpdate()
                .set(LiveAuiImageFile::getTitle, updateContent.getTitle())
                .set(LiveAuiImageFile::getDescription, updateContent.getDescription())
                .set(LiveAuiImageFile::getTags, updateContent.getTags())
                .set(LiveAuiImageFile::getCateId, cateId)
                .set(LiveAuiImageFile::getImageId, updateContent.getImageId())
                .eq(LiveAuiImageFile::getId, dto.getId());
        if (cateId != null) {
            liveAuiCategory = iLiveAuiCategoryService.getById(cateId);
            wrapper.set(ObjectUtil.isNotNull(liveAuiCategory), LiveAuiImageFile::getCateName, liveAuiCategory.getCateName());
        }
        // 本地数据库更新图片信息
        return wrapper.update();
    }

    @Override
    public void setNewUrl(List<LiveAuiImageFileVo> liveAuiImageFileVos) {
        if (liveAuiImageFileVos.isEmpty()) {
            return;
        }
        List<String> imageIds = liveAuiImageFileVos.stream().map(LiveAuiImageFileVo::getImageId).toList();
        AliGetImageInfosRequest req = new AliGetImageInfosRequest();
        req.setImageIds(imageIds);
        GetImageInfosResponse response = iAliCloudBizService.getImageInfos(req);
        if (ObjectUtils.isEmpty(response)) {
            throw new BaseException("阿里云获取图片信息失败");
        }
        List<GetImageInfosResponse.Image> imageInfos = response.getImageInfo();
        liveAuiImageFileVos.forEach(liveAuiImageFileVo -> imageInfos.stream()
                .filter(imageInfo -> imageInfo.getImageId().equals(liveAuiImageFileVo.getImageId()))
                .findFirst()
                .ifPresent(image -> liveAuiImageFileVo.setUrl(image.getURL())));
    }

}