package com.panda.pollen.aui.service;

import com.panda.pollen.aui.model.dto.ImagePageDto;
import com.panda.pollen.aui.model.dto.ImageSaveDto;
import com.panda.pollen.aui.model.dto.ImageUpdateDto;
import com.panda.pollen.aui.model.dto.UploadImageUrlAndAuthGetDto;
import com.panda.pollen.aui.model.vo.LiveAuiImageFileVo;
import com.panda.pollen.aui.system.domain.LiveAuiImageAuth;
import com.panda.pollen.aui.system.domain.LiveAuiImageFile;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ILiveAuiMaterialImageBizService {
    /**
     * 获取上传图片的url和凭证
     *
     * @return 上传地址和凭证
     */
    LiveAuiImageAuth getUploadImageUrlAndAuth(UploadImageUrlAndAuthGetDto dto);

    /**
     * 获取单个图片信息
     *
     * @param id@return 图片信息
     */
    LiveAuiImageFile get(String id);

    /**
     * 删除图片或视频截图
     *
     * @param id@return 删除结果
     */
    boolean delete(Long id);

    /**
     * 分页获取图片信息
     *
     * @param dto 图片信息
     * @return 图片信息列表
     */
    List<LiveAuiImageFileVo> page(ImagePageDto dto);

    /**
     * 保存图片信息
     *
     * @param dto 图片信息
     * @return 保存结果
     */
    boolean save(ImageSaveDto dto);

    /**
     * 更新图片信息
     *
     * @param dto 图片信息
     * @return 更新结果
     */
    boolean update(ImageUpdateDto dto);

    /**
     * 设置图片新的url
     *
     * @param liveAuiImageFileVos 图片信息
     */
    void setNewUrl(List<LiveAuiImageFileVo> liveAuiImageFileVos);

}
