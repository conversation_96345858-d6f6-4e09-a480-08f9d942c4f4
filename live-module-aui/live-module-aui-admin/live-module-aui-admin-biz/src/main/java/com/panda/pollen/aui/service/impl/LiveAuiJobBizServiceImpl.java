package com.panda.pollen.aui.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.panda.pollen.aui.mapper.LiveAuiJobBizMapper;
import com.panda.pollen.aui.model.dto.LiveAuiRoomStartDto;
import com.panda.pollen.aui.script.SimpleScriptTaskManager;
import com.panda.pollen.aui.script.ScriptTaskStatus;
import com.panda.pollen.aui.service.ILiveAuiJobBizService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplate;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfo;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfoExt;
import com.panda.pollen.aui.system.service.ILiveAuiMessageTemplateScriptService;
import com.panda.pollen.aui.system.service.ILiveAuiMessageTemplateService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoExtService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoService;
import com.panda.pollen.common.exception.base.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 直播任务服务实现类，负责管理直播转发任务的延迟队列和执行。
 * 1、使用redis的延迟队列
 * 2、使用redisson的分布式锁
 * 3、必须可重复执行
 * 4、任务执行失败重试
 * 5、不写测试类和方法
 * 6、不生成更新的README文档
 * 7、使用lombok注解
 * 8、简化实现、代码量少
 * 9、java类一定不能报错
 * <AUTHOR>
 */
@Slf4j
@Service
public class LiveAuiJobBizServiceImpl implements ILiveAuiJobBizService {
    @Resource
    private ILiveAuiRoomInfoService iLiveAuiRoomInfoService;
    @Resource
    private ILiveAuiRoomInfoExtService iLiveAuiRoomInfoExtService;
    @Resource
    private ILiveAuiMessageTemplateService iLiveAuiMessageTemplateService;
    @Resource
    private ILiveAuiMessageTemplateScriptService iLiveAuiMessageTemplateScriptService;
    @Resource
    private SimpleScriptTaskManager scriptTaskManager;
    @Resource
    private LiveAuiJobBizMapper liveAuiJobBizMapper;

    @Override
    public void play(Integer delay) {
        log.info("{}：开始进行：{}", LocalDateTimeUtil.now(), "播放直播脚本");
        if (ObjectUtils.isEmpty(delay)) {
            log.info("延迟时间为空，设置默认延迟时间30分钟");
            delay = 30;
        }
        // 查询当前时间之后的直播间列表(指定了查询范围)
        List<LiveAuiRoomStartDto> liveAuiRoomStartDtos = liveAuiJobBizMapper.getRooms(delay);
        // 启动脚本
        liveAuiRoomStartDtos.stream()
                // 提取直播间ID
                .map(LiveAuiRoomStartDto::getId)
                // 去重
                .distinct()
                // 启动脚本
                .forEach(roomId -> this.start(Long.valueOf(roomId)));
    }


    @Override
    public void start(Long roomId) {
        if (ObjectUtils.isEmpty(roomId)) {
            throw new BaseException("直播间ID不能为空");
        }

        log.info("开始播放直播间脚本，roomId: {}", roomId);

        try {
            // 获取直播间信息
            LiveAuiRoomInfo liveAuiRoomInfo = iLiveAuiRoomInfoService.getById(roomId);
            if (ObjectUtils.isEmpty(liveAuiRoomInfo)) {
                throw new BaseException("未找到ID为 " + roomId + " 的直播间");
            }

            // 获取直播间扩展信息
            List<LiveAuiRoomInfoExt> liveAuiRoomInfoExts = iLiveAuiRoomInfoExtService.lambdaQuery()
                    .eq(LiveAuiRoomInfoExt::getRoomInfoId, roomId)
                    .list();
            if (liveAuiRoomInfoExts.size() != 1) {
                throw new BaseException(CharSequenceUtil.format("未找到ID为{}的直播间扩展信息/直播间扩展信息不唯一", roomId));
            }

            LiveAuiRoomInfoExt liveAuiRoomInfoExt = liveAuiRoomInfoExts.get(0);
            Long liveTemplateId = liveAuiRoomInfoExt.getLiveTemplateId();
            Long messageGroupId = liveAuiRoomInfoExt.getMessageGroupId();

            if (ObjectUtils.isEmpty(messageGroupId)) {
                throw new BaseException("直播间消息组ID不能为空");
            }

            if (!ObjectUtils.isEmpty(liveTemplateId)) {
                // 验证模板存在
                LiveAuiMessageTemplate liveAuiMessageTemplate = iLiveAuiMessageTemplateService.getById(liveTemplateId);
                if (ObjectUtils.isEmpty(liveAuiMessageTemplate)) {
                    throw new BaseException("未找到ID为 " + liveTemplateId + " 的直播间模板消息");
                }

                // 获取所有要演绎的脚本并按发送时间排序
                List<LiveAuiMessageTemplateScript> scripts = iLiveAuiMessageTemplateScriptService.lambdaQuery()
                        .eq(LiveAuiMessageTemplateScript::getTemplateId, liveTemplateId)
                        .orderByAsc(LiveAuiMessageTemplateScript::getSendTime)
                        .list();

                if (!scripts.isEmpty()) {
                    log.info("找到 {} 个脚本，开始调度定时任务，roomId: {}", scripts.size(), roomId);

                    // 使用脚本任务管理器启动定时任务
                    scriptTaskManager.startScriptTasks(roomId, messageGroupId, scripts);

                    log.info("脚本任务调度完成，roomId: {}", roomId);
                } else {
                    log.warn("未找到可执行的脚本，roomId: {}, templateId: {}", roomId, liveTemplateId);
                }
            } else {
                log.warn("直播间未配置消息模板，roomId: {}", roomId);
            }

        } catch (Exception e) {
            log.error("播放直播间脚本失败，roomId: {}", roomId, e);
            throw e;
        }
    }

    @Override
    public void stop(Long roomId) {
        if (ObjectUtils.isEmpty(roomId)) {
            throw new BaseException("直播间ID不能为空");
        }
        log.info("停止播放直播间脚本，roomId: {}", roomId);
        scriptTaskManager.stopScriptTasks(roomId);
    }

    @Override
    public ScriptTaskStatus getTaskStatus(Long roomId) {
        if (ObjectUtils.isEmpty(roomId)) {
            throw new BaseException("直播间ID不能为空");
        }
        return scriptTaskManager.getTaskStatus(roomId);
    }
}