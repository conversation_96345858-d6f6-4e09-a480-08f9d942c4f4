package com.panda.pollen.aui.script;

import com.panda.pollen.aui.model.req.AliSendLiveMessageUserRequest;
import com.panda.pollen.aui.service.IAliCloudBizService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;
import com.panda.pollen.framework.lock.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 脚本任务管理器
 * 使用Redis延迟队列和Redisson分布式锁，支持任务重试和可重复执行
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ScriptTaskManager {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IAliCloudBizService iAliCloudBizService;

    @Resource
    private RedisLock redisLock;

    /**
     * 普通队列名称
     */
    public static final String QUEUE_NAME = "script_task_delay_queue";

    /**
     * 分布式锁前缀
     */
    private static final String LOCK_PREFIX = "script_task_lock_";

    /**
     * 锁等待时间（毫秒）
     */
    private static final long LOCK_WAIT_TIME = 100;

    /**
     * 锁持有时间（毫秒）
     */
    private static final long LOCK_LEASE_TIME = 30000;

    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 3;

    /**
     * 存储任务状态，key为roomId
     */
    private final ConcurrentHashMap<Long, ScriptTaskStatus> taskStatusMap = new ConcurrentHashMap<>();

    /**
     * 延迟队列
     */
    private RDelayedQueue<ScriptTask> delayedQueue;

    /**
     * 初始化Redis队列
     */
    @PostConstruct
    public void init() {
        // 普通队列
        RQueue<ScriptTask> queue = redissonClient.getQueue(QUEUE_NAME);
        // 延迟队列
        delayedQueue = redissonClient.getDelayedQueue(queue);
        log.info("ScriptTaskManager初始化完成，队列名称: {}", QUEUE_NAME);
    }

    /**
     * 启动脚本任务
     *
     * @param roomId 直播间ID
     * @param messageGroupId 消息组ID
     * @param scripts 脚本列表
     */
    public void startScriptTasks(Long roomId, Long messageGroupId, List<LiveAuiMessageTemplateScript> scripts) {
        if (scripts == null || scripts.isEmpty()) {
            log.warn("脚本列表为空，roomId: {}", roomId);
            return;
        }

        String lockKey = LOCK_PREFIX + roomId;

        redisLock.tryLock(lockKey, LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.MILLISECONDS, () -> {
            try {
                // 停止已存在的任务
                stopScriptTasks(roomId);

                log.info("开始为直播间 {} 调度脚本任务，共 {} 个脚本", roomId, scripts.size());

                LocalDateTime startTime = LocalDateTime.now();
                int validScriptCount = 0;

                for (LiveAuiMessageTemplateScript script : scripts) {
                    if (MessageBuilder.isValidScript(script)) {
                        // 计算延迟时间
                        LocalDateTime scheduledTime = calculateScheduledTime(startTime, script.getSendTime());
                        long delayMillis = ChronoUnit.MILLIS.between(LocalDateTime.now(), scheduledTime);

                        if (delayMillis > 0) {
                            // 创建任务并添加到延迟队列
                            ScriptTask task = new ScriptTask(roomId, messageGroupId, script, scheduledTime);
                            delayedQueue.offer(task, delayMillis, TimeUnit.MILLISECONDS);
                            validScriptCount++;

                            log.debug("脚本任务已添加到延迟队列，roomId: {}, scriptId: {}, 延迟: {}ms", roomId, script.getId(), delayMillis);
                        } else {
                            log.warn("脚本发送时间已过期，跳过，roomId: {}, scriptId: {}, sendTime: {}", roomId, script.getId(), script.getSendTime());
                        }
                    }
                }

                // 更新任务状态
                ScriptTaskStatus status = new ScriptTaskStatus(roomId, validScriptCount, 0, 0);
                taskStatusMap.put(roomId, status);

                log.info("脚本任务调度完成，roomId: {}, 有效任务数: {}", roomId, validScriptCount);

            } catch (Exception e) {
                log.error("启动脚本任务失败，roomId: {}", roomId, e);
                throw new RuntimeException("启动脚本任务失败", e);
            }
        }, () -> log.warn("获取分布式锁失败，可能其他实例正在处理，roomId: {}", roomId));
    }

    /**
     * 计算脚本的调度时间
     */
    private LocalDateTime calculateScheduledTime(LocalDateTime startTime, LocalTime sendTime) {
        if (sendTime == null) {
            return startTime;
        }
        //
        LocalDateTime scheduledTime = startTime.toLocalDate().atTime(sendTime);

        // 如果发送时间已经过了，则安排到明天
        if (scheduledTime.isBefore(startTime)) {
            scheduledTime = scheduledTime.plusDays(1);
        }

        return scheduledTime;
    }

    /**
     * 处理脚本任务执行
     * 支持重试机制
     */
    public void executeScriptTask(ScriptTask task) {
        String lockKey = LOCK_PREFIX + "execute_" + task.getTaskId();

        redisLock.tryLock(lockKey, LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.MILLISECONDS, () -> {
            try {
                log.info("开始执行脚本任务，roomId: {}, scriptId: {}", task.getRoomId(), task.getScript().getId());

                // 构建消息请求
                AliSendLiveMessageUserRequest request = MessageBuilder.buildMessageRequest(task.getMessageGroupId(), task.getScript());

                // 发送消息
                iAliCloudBizService.sendMessage2All(request);

                // 更新任务状态
                updateTaskStatus(task.getRoomId(), true, false);

                log.info("脚本任务执行成功，roomId: {}, scriptId: {}", task.getRoomId(), task.getScript().getId());

            } catch (Exception e) {
                log.error("脚本任务执行失败，roomId: {}, scriptId: {}, 重试次数: {}", task.getRoomId(), task.getScript().getId(), task.getRetryCount(), e);

                // 重试逻辑
                if (task.getRetryCount() < MAX_RETRY_COUNT) {
                    task.setRetryCount(task.getRetryCount() + 1);
                    // 延迟重试，每次重试延迟递增
                    long retryDelay = 1000L * (1L << task.getRetryCount()); // 指数退避
                    delayedQueue.offer(task, retryDelay, TimeUnit.MILLISECONDS);

                    log.info("脚本任务将重试，roomId: {}, scriptId: {}, 重试次数: {}, 延迟: {}ms", task.getRoomId(), task.getScript().getId(), task.getRetryCount(), retryDelay);
                } else {
                    log.error("脚本任务重试次数已达上限，放弃执行，roomId: {}, scriptId: {}", task.getRoomId(), task.getScript().getId());
                    updateTaskStatus(task.getRoomId(), false, true);
                }
            }
        }, () -> log.warn("获取执行锁失败，任务可能正在其他实例执行，taskId: {}", task.getTaskId()));
    }


    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Long roomId, boolean completed, boolean failed) {
        ScriptTaskStatus status = taskStatusMap.get(roomId);
        if (status != null) {
            if (completed) {
                status.setCompletedTasks(status.getCompletedTasks() + 1);
            }
            if (failed) {
                status.setCancelledTasks(status.getCancelledTasks() + 1);
            }
        }
    }

    /**
     * 停止脚本任务
     * 清理Redis延迟队列中的相关任务
     *
     * @param roomId 直播间ID
     */
    public void stopScriptTasks(Long roomId) {
        String lockKey = LOCK_PREFIX + roomId;

        redisLock.tryLock(lockKey, LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.MILLISECONDS, () -> {
            try {
                // 清理任务状态
                ScriptTaskStatus status = taskStatusMap.remove(roomId);
                if (status != null) {
                    log.info("停止脚本任务，roomId: {}, 清理任务状态: {}", roomId, status);
                }

                // 注意：Redis延迟队列中的任务无法直接取消，但可以在执行时检查状态
                // 这里我们通过移除taskStatusMap来标记任务已停止
                log.info("脚本任务停止完成，roomId: {}", roomId);

            } catch (Exception e) {
                log.error("停止脚本任务失败，roomId: {}", roomId, e);
            }
        }, () -> log.warn("获取停止锁失败，roomId: {}", roomId));
    }

    /**
     * 获取任务状态
     *
     * @param roomId 直播间ID
     * @return 任务状态信息
     */
    public ScriptTaskStatus getTaskStatus(Long roomId) {
        ScriptTaskStatus status = taskStatusMap.get(roomId);
        if (status == null) {
            return new ScriptTaskStatus(roomId, 0, 0, 0);
        }
        return status;
    }
}
