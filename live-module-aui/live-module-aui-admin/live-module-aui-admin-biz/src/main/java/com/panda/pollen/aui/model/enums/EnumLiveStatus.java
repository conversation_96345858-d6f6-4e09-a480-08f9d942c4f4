package com.panda.pollen.aui.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 直播状态
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum EnumLiveStatus {


    /**
     * 准备中
     */
    LIVE_STATUS_PREPARE(0, "准备中"),

    /**
     * 已开始
     */
    LIVE_STATUS_ON(1, "直播中"),

    /**
     * 已结束
     */
    LIVE_STATUS_OFF(2, "已结束"),

    /**
     * 暂停中
     */
    LIVE_STATUS_PAUSE(3, "暂停中"),
    ;

    private final int val;
    private final String desc;

    public static EnumLiveStatus of(int val) {
        return Arrays.stream(EnumLiveStatus.values()).filter(enumLiveStatus -> enumLiveStatus.val == val).findFirst().orElse(null);
    }
    public static EnumLiveStatus of(String desc) {
        return Arrays.stream(EnumLiveStatus.values()).filter(enumLiveStatus -> enumLiveStatus.desc.equals(desc)).findFirst().orElse(null);
    }


}
