package com.panda.pollen.aui.script;

import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 脚本任务数据类
 * <AUTHOR>
 */
@Data
public class ScriptTask {
    private Long roomId;
    private Long messageGroupId;
    private LiveAuiMessageTemplateScript script;
    private LocalDateTime scheduledTime;
    private int retryCount = 0;
    private String taskId;

    public ScriptTask(Long roomId, Long messageGroupId, LiveAuiMessageTemplateScript script, LocalDateTime scheduledTime) {
        this.roomId = roomId;
        this.messageGroupId = messageGroupId;
        this.script = script;
        this.scheduledTime = scheduledTime;
        this.taskId = roomId + "_" + script.getId() + "_" + scheduledTime.toString();
    }
}
