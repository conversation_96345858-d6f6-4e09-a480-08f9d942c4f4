package com.panda.pollen.aui.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.aui.mapper.LiveAuiMessageRecordBizMapper;
import com.panda.pollen.aui.model.dto.RoomInfoDto;
import com.panda.pollen.aui.model.enums.EnumListedStatus;
import com.panda.pollen.aui.model.vo.LiveAuiMessageTemplateScriptVo;
import com.panda.pollen.aui.model.vo.LiveRoomInfoVo;
import com.panda.pollen.aui.service.ILiveAuiLiveBizService;
import com.panda.pollen.aui.service.ILiveAuiMessageRecordBizService;
import com.panda.pollen.aui.service.ILiveAuiRoomInfoBizService;
import com.panda.pollen.aui.service.ILiveMessageRecordBizService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageRecord;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;
import com.panda.pollen.aui.system.domain.LiveAuiRobotPool;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfoExt;
import com.panda.pollen.aui.system.service.ILiveAuiRobotPoolService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoExtService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoService;
import com.panda.pollen.scrm.api.AuthCorpUserApi;
import com.panda.pollen.scrm.api.CustomerUserApi;
import com.tencent.ads.api.v3.OrganizationAccountRelationApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: yh
 * @Date: 2025/7/19 17:05
 * @Description:
 */
@Slf4j
@Service
public class LiveAuiMessageRecordBizServiceImpl extends ServiceImpl<LiveAuiMessageRecordBizMapper, LiveAuiMessageRecord>
        implements ILiveAuiMessageRecordBizService {

    @Resource
    private ILiveAuiRobotPoolService liveAuiRobotPoolService;
    @Resource
    private ILiveAuiRoomInfoBizService liveAuiRoomInfoBizService;
    @Resource
    private ILiveMessageRecordBizService liveMessageRecordBizService;

    @Resource
    private ILiveAuiRoomInfoExtService liveAuiRoomInfoExtService;
    @Override
    public List<LiveAuiMessageRecord> queryLiveAuiMessageRecordList(LiveAuiMessageRecord liveAuiMessageRecord) {
        QueryWrapper<LiveAuiMessageRecord> queryWrapper = Wrappers.query();
        // 完善查询条件,例如:queryWrapper.eq("student_name", sysStudent.getStudentName());
        return this.list(queryWrapper);
    }

    @Override
    public List<LiveAuiMessageTemplateScript> getHistoryMessageRecrod(String roomId) {
        return this.baseMapper.getHistoryMessageRecrod(roomId);
    }

    @Override
    public List<LiveAuiMessageTemplateScriptVo> listRoomMessage(LiveAuiMessageRecord liveAuiMessageRecord) {
        List<LiveAuiMessageTemplateScriptVo> list = this.baseMapper.listRoomMessage(liveAuiMessageRecord.getRoomId().toString());
        if(null != list && !list.isEmpty()){
            //完善机器人信息
            List<String> robotCodeList  = list.stream()
                    .map(LiveAuiMessageTemplateScriptVo::getSenderId)
                    .filter(senderId -> senderId.startsWith("R")).collect(Collectors.toList());

            LambdaQueryWrapper<LiveAuiRobotPool> robotPoolQuery = new LambdaQueryWrapper<>();
            robotPoolQuery.in(LiveAuiRobotPool::getRobotCode,robotCodeList);
            List<LiveAuiRobotPool> listRobot = liveAuiRobotPoolService.list(robotPoolQuery);

            if(null != listRobot && !listRobot.isEmpty()){
                Map<String,LiveAuiRobotPool> robotPoolMap = listRobot.stream().collect(Collectors.toMap(LiveAuiRobotPool::getRobotCode,u->u));

                list.stream().forEach(script -> {
                    if(script.getSenderId().startsWith("R")){
                        script.setSenderName(robotPoolMap.get(script.getSenderId()).getNickname());
                    }
                });
            }
        }

        return list;
    }

    @Override
    public void executeDownloadLiveRoomMessageTask() {
        List<LiveRoomInfoVo> unDownloadRoomList = liveAuiRoomInfoBizService.listUnDownloadRoom();
        if(null != unDownloadRoomList && !unDownloadRoomList.isEmpty()){
            unDownloadRoomList.forEach(obj -> {
                liveMessageRecordBizService.recordRoomMessageAfterPlay(obj.getId().toString());
            });
        }
    }
}
