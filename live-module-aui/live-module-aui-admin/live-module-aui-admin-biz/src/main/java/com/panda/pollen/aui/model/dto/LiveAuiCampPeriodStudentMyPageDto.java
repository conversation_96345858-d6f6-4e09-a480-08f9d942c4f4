package com.panda.pollen.aui.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.panda.pollen.common.base.BasePageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LiveAuiCampPeriodStudentMyPageDto extends BasePageQuery {
    @Serial
    private static final long serialVersionUID = 3427040278293309898L;

    @ApiModelProperty(value = "训练营ID")
    private String campId;

    @ApiModelProperty(value = "营期ID")
    private String campPeriodId;

    @ApiModelProperty(value = "跟进人ID")
    private String followId;

    @ApiModelProperty(value = "分配标识，0——未分配，1——已分配")
    private Integer distributeFlag;

    @ApiModelProperty(value = "创建开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createStartTime;

    @ApiModelProperty(value = "创建结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createEndTime;

}
