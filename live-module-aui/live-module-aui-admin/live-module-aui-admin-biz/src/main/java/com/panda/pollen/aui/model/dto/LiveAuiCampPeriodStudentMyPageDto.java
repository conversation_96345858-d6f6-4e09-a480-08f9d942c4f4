package com.panda.pollen.aui.model.dto;

import com.panda.pollen.common.base.BasePageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LiveAuiCampPeriodStudentMyPageDto extends BasePageQuery {
    @Serial
    private static final long serialVersionUID = 3427040278293309898L;

    /**
     * 营期ID
     */
    private String campPeriodId;

    /**
     * 跟进人ID
     */
    private String followId;

}
