package com.panda.pollen.aui.model.vo;

import com.panda.pollen.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 获取直播间消息
 */
@Data
public class LiveRoomInfoVo {

    @ApiModelProperty("直播间ID")
    private Long id;

    @ApiModelProperty("直播间标题")
    private String title;

    @ApiModelProperty("直播间状态;0-未开始，1-直播中，2-已结束")
    private Long status;

    @ApiModelProperty("直播间模式;0-普通直播，1-连麦直播")
    private Long mode;

    @ApiModelProperty("直播间会话ID IM群Id")
    private String chatId;

    @ApiModelProperty("直播间公告")
    private String notice;

    @ApiModelProperty("主播ID")
    private String anchorId;

    @ApiModelProperty("主播昵称")
    private String anchorNick;

    @ApiModelProperty("直播开始时间")
    private LocalDateTime startedAt;

    @ApiModelProperty("直播结束时间")
    private LocalDateTime stoppedAt;

    @ApiModelProperty("消息群组ID(live_aui_message_group表的主键id)")
    private Long messageGroupId;

    @ApiModelProperty("直播名称")
    private String roomName;

    @ApiModelProperty("直播模式 0-全屏,1-二分屏")
    private Integer liveMode;

    @ApiModelProperty("课程类型(0-直播课程,1-录播直播)")
    private Integer courseType;

    @ApiModelProperty("课程分组")
    private String courseGroupId;

    @ApiModelProperty("直播简介")
    private String liveIntroduction;

    @ApiModelProperty("直播封面id")
    private Long liveImageId;

    @ApiModelProperty("暖场视频ID")
    private Long warmUpVideoId;

    @ApiModelProperty("内容来源(0-自定义,1-直播模板)")
    private Long contentSource;

    @ApiModelProperty("课程视频ID(内容来源是自定义的时候设置)")
    private Long courseVideoId;

    @ApiModelProperty("直播模板ID(内容来源是直播模板的时候设置)")
    private Long liveTemplateId;

    @ApiModelProperty("当前观看人数")
    private Long viewerCount;

    @ApiModelProperty("真实观看人数（不含机器人）")
    private Long realViewerCount;

}
