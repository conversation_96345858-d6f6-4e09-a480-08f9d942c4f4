package com.panda.pollen.aui.service.impl;

import com.panda.pollen.aui.mapper.LiveAuiRoomInfoBizMapper;
import com.panda.pollen.aui.model.vo.LiveRoomInfoVo;
import com.panda.pollen.aui.service.ILiveAuiRoomInfoBizService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LiveAuiRoomInfoBizServiceImpl implements ILiveAuiRoomInfoBizService {

    @Resource
    private LiveAuiRoomInfoBizMapper liveAuiRoomInfoBizMapper;
    @Override
    public List<LiveRoomInfoVo> listUnDownloadRoom() {
        return liveAuiRoomInfoBizMapper.listUnDownloadRoom();
    }
}
