package com.panda.pollen.aui.service.impl;

import com.aliyuncs.live.model.v20161101.ListLiveMessageGroupMessagesResponse;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.panda.pollen.aui.mapper.LiveAuiMessageRecordBizMapper;
import com.panda.pollen.aui.model.enums.EnumMessageSenderType;
import com.panda.pollen.aui.model.req.AliListLiveMessageGroupMessagesRequest;
import com.panda.pollen.aui.model.vo.LiveAuiMessageRecordVo;
import com.panda.pollen.aui.model.vo.LiveAuiRoomActiveMemberVo;
import com.panda.pollen.aui.service.IAliCloudBizService;
import com.panda.pollen.aui.service.ILiveAuiRoomActiveMemberBizService;
import com.panda.pollen.aui.service.ILiveMessageRecordBizService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageRecord;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfo;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfoExt;
import com.panda.pollen.aui.system.service.ILiveAuiMessageRecordService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoExtService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoService;
import com.panda.pollen.aui.utils.LocalDateTimeUtil;
import com.panda.pollen.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;


/**
 * <AUTHOR> yh
 * @Date : 2025/6/28 15:59
 * @Description :
 */
@Slf4j
@Service
public class LiveMessageRecordBizServiceImpl implements ILiveMessageRecordBizService {
    @Resource
    private IAliCloudBizService aliCloudBizService;
    @Resource
    private LiveAuiMessageRecordBizMapper liveAuiMessageRecordBizMapper;

    @Resource
    private ILiveAuiMessageRecordService liveAuiMessageRecordService;

    @Resource
    private ILiveAuiRoomInfoService liveAuiRoomInfoService;

    @Resource
    private ILiveAuiRoomInfoExtService liveAuiRoomInfoExtService;

    @Resource
    private ILiveAuiRoomActiveMemberBizService liveAuiRoomActiveMemberBizService;

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private final String[] EXPORT_HEAD = new String[]{"消息ID", "发送时间", "身份类型", "用户昵称", "回复消息", "智能消息", "客户ID", "跟进人", "跟进人所在机构"};

    private boolean saveRoomMessaeAfterPlay(Long roomId, ListLiveMessageGroupMessagesResponse response) {
        String groupId = response.getGroupId();
        List<ListLiveMessageGroupMessagesResponse.Messages> messagesList = response.getMessageList();
        //保存至DB
        List<LiveAuiMessageRecord> list = new ArrayList<>(messagesList.size());
        if (null != messagesList && messagesList.size() > 0) {
            messagesList.stream().forEach(messages -> {
                Long senderType = EnumMessageSenderType.USER.getType();
                if (messages.getSender().getUserId().startsWith("R")) {
                    senderType = EnumMessageSenderType.ROBOT.getType();
                }
                if (messages.getSender().getUserId().startsWith("A")) {
                    senderType = EnumMessageSenderType.TOTUR.getType();
                }

                LiveAuiMessageRecord record = new LiveAuiMessageRecord();
                record.setAppId(this.aliCloudBizService.getDefaultProfile().getAppId());
                record.setRoomId(roomId);
                record.setGroupId(groupId);
                record.setSenderId(messages.getSender().getUserId());
                record.setSenderInfo(messages.getSender().getUserInfo());
                record.setSenderType(senderType);
                record.setBody(messages.getBody());
                record.setSendTime(LocalDateTimeUtil.timeStamp2LocalDateTime(messages.getTimestamp()));
                record.setMsgTid(messages.getMsgTid());
//                record.setReceiverId(messages.get);
                record.setMsgType(messages.getMsgType());
                record.setSeqNumber(messages.getSeqNumber().toString());

                list.add(record);
            });

            return this.liveAuiMessageRecordService.saveBatch(list);
        }

        return true;
    }

    @Override
    public void download(HttpServletResponse response, LiveAuiMessageRecord liveAuiMessageRecord) {
        List<LiveAuiMessageRecordVo> list = this.liveAuiMessageRecordBizMapper.selectDownload(liveAuiMessageRecord);
        if (null == list || list.isEmpty()) {
            throw new ServiceException("暂无消息可以导出");
        } else {
            //完善助教消息，跟进人，跟进人所在部门
            List<Long> memberIdList = new ArrayList<>();
            List<Long> userIdList = new ArrayList<>();

            list.stream().forEach(record -> {
                if (EnumMessageSenderType.USER.getType().equals(record.getSenderType())) {
                    memberIdList.add(Long.valueOf(record.getSenderId()));
                }
                if (EnumMessageSenderType.TOTUR.getType().equals(record.getSenderType())) {
                    userIdList.add(Long.valueOf(record.getSenderId()));
                }
            });

            if (memberIdList.size() > 0) {
                List<LiveAuiRoomActiveMemberVo> users = this.liveAuiRoomActiveMemberBizService.selectLiveUsers(liveAuiMessageRecord.getRoomId(), memberIdList);

                if (null != users && !users.isEmpty()) {
                    list.stream().forEach(record -> {
                        for (LiveAuiRoomActiveMemberVo user : users) {
                            if (record.getSenderId().equals(user.getCustomerId().toString())) {
                                record.setOwner(user.getOwner());
                                record.setOwnerDepartment(user.getOwnerDepartment());
                                break;
                            }
                        }
                    });
                }
            }

        }
        final String fileName = "直播互动消息.xlsx";
        export(response, fileName, null, Arrays.asList(this.EXPORT_HEAD), list);
    }

    @Override
    public boolean recordRoomMessageAfterPlay(String roomId) {

        LiveAuiRoomInfo liveAuiRoomInfo = this.liveAuiRoomInfoService.getById(Long.valueOf(roomId));
        //是否有下一页
        AtomicBoolean hasMore = new AtomicBoolean(true);
        //下一页起始位置
        AtomicLong nextPageIndex = new AtomicLong(1);

        try {
            while (hasMore.get()) {
                AliListLiveMessageGroupMessagesRequest request = new AliListLiveMessageGroupMessagesRequest();
                request.setGroupId(liveAuiRoomInfo.getChatId());
                request.setSortType(1);
                request.setPageSize(50);
                request.setNextPageToken(nextPageIndex.get());

                ListLiveMessageGroupMessagesResponse response = this.aliCloudBizService.recordRoomMessageAfterPlay(request);
                if (null != response) {
                    hasMore.set(response.getHasmore());
                    nextPageIndex.set(response.getNextPageToken());
                    this.saveRoomMessaeAfterPlay(Long.valueOf(roomId), response);
                } else {
                    hasMore.set(false);
                }
            }
            LambdaUpdateWrapper<LiveAuiRoomInfoExt> queryWrapper = new LambdaUpdateWrapper<>();
            queryWrapper.eq(LiveAuiRoomInfoExt::getRoomInfoId,Long.valueOf(roomId))
                    .set(LiveAuiRoomInfoExt::getMessageDownloaded,true);
            liveAuiRoomInfoExtService.update(queryWrapper);
        } catch (Exception e) {
            log.error("查询群组消息列表失败！", e);
            throw new ServiceException("查询群组消息列表失败！");
        }

        return true;

    }

    public static void export(HttpServletResponse response, String fileName, String sheetName, List<String> head, List<LiveAuiMessageRecordVo> sheetDataList) {
        SXSSFWorkbook book = null;
        FileOutputStream fos = null;
        try {
            // 整个 Excel 表格 book 对象
            book = new SXSSFWorkbook();
            //2、创建一个工作表
            Sheet sheet = book.createSheet((null == sheetName || sheetName.isEmpty()) ? "工作表1" : sheetName);

            //3、创建表头
            Row row1 = sheet.createRow(0);
            for (int i = 0; i < head.size(); i++) {
                Cell cell = row1.createCell(i);
                cell.setCellValue(head.get(i));
            }
            if (null != sheetDataList && !sheetDataList.isEmpty()) {
                for (int i = 1; i <= sheetDataList.size(); i++) {
                    Row row = sheet.createRow(i);
                    for (int j = 0; j < head.size(); j++) {
                        String cellValue = switch (head.get(j)) {
                            case "发送时间" -> (sheetDataList.get(i - 1).getSendTime()).format(TIME_FORMATTER);
                            case "身份类型" -> EnumMessageSenderType.getByType(sheetDataList.get(i - 1).getSenderType());
                            case "用户昵称" -> sheetDataList.get(i - 1).getSenderName();
                            case "回复消息" -> sheetDataList.get(i - 1).getResponseMsgBody();
                            case "智能消息" -> sheetDataList.get(i - 1).getBody();
                            case "客户ID" -> (EnumMessageSenderType.USER.getType().
                                    equals(sheetDataList.get(i - 1).getSenderType())) ? sheetDataList.get(i - 1).getSenderId() : "";
                            case "跟进人" -> sheetDataList.get(i - 1).getOwner();
                            case "跟进人所在机构" -> sheetDataList.get(i - 1).getOwnerDepartment();
                            default -> sheetDataList.get(i - 1).getId().toString();
                        };
                        Cell cell = row.createCell(j);
                        cell.setCellValue(cellValue);
                    }
                }
            }

            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            book.write(response.getOutputStream());

        } catch (Exception e) {
            log.error("导出直播互动数据错误", e);
            throw new ServiceException("导出直播互动数据错误");
        } finally {
            try {
                book.dispose();//清理临时文件
                book.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

        }
    }
}
