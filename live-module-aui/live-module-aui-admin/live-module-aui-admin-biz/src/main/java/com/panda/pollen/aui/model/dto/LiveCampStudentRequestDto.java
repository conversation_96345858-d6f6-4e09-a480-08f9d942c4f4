package com.panda.pollen.aui.model.dto;

import com.panda.pollen.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * @Author: yh
 * @Date: 2025/9/17 22:13
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LiveCampStudentRequestDto extends BaseEntity {

    @ApiModelProperty(value = "营期ID")
    private String campId;

    @ApiModelProperty(value = "跟进人ID")
    private String followId;

    @ApiModelProperty(value = "分配标识，0——未分配，1——已分配")
    private Integer distributeFlag;

    @ApiModelProperty(value = "创建开始时间")
    private LocalDateTime createStartTime;

    @ApiModelProperty(value = "创建结束时间")
    private LocalDateTime createEndTime;
}
