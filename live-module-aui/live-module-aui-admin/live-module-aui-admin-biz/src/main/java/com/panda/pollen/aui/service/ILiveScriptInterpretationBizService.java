package com.panda.pollen.aui.service;

import com.panda.pollen.aui.api.ILiveScriptInterpretationBizApi;
import com.panda.pollen.aui.service.impl.ScriptTaskManager;

/**
 * 脚本演绎业务服务接口
 * <AUTHOR>
 */
public interface ILiveScriptInterpretationBizService extends ILiveScriptInterpretationBizApi {

    /**
     * 停止播放脚本
     *
     * @param roomId 直播间ID
     */
    void stop(Long roomId);

    /**
     * 获取脚本任务状态
     *
     * @param roomId 直播间ID
     * @return 任务状态
     */
    ScriptTaskManager.TaskStatus getTaskStatus(Long roomId);
}
