package com.panda.pollen.aui.script;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 任务状态信息
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ScriptTaskStatus {
    private Long roomId;
    private int totalTasks;
    private int completedTasks;
    private int cancelledTasks;


    /**
     * 获取正在运行的任务数
     */
    public int getRunningTasks() {
        return totalTasks - completedTasks - cancelledTasks;
    }

    @Override
    public String toString() {
        return String.format("TaskStatus{roomId=%d, total=%d, completed=%d, cancelled=%d, running=%d}", roomId, totalTasks, completedTasks, cancelledTasks, getRunningTasks());
    }
}
