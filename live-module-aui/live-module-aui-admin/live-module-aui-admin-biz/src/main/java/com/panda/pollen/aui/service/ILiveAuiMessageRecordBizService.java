package com.panda.pollen.aui.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panda.pollen.aui.model.vo.LiveAuiMessageTemplateScriptVo;
import com.panda.pollen.aui.system.domain.LiveAuiMessageRecord;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;

import java.util.List;

/**
 * @Author: yh
 * @Date: 2025/7/19 17:03
 * @Description: 直播间互动消息记录
 */

public interface ILiveAuiMessageRecordBizService extends IService<LiveAuiMessageRecord> {

    /**
     * 查询直播消息互动记录列表
     *
     * @param liveAuiMessageRecord 直播消息互动记录
     * @return 直播消息互动记录集合
     */
    List<LiveAuiMessageRecord> queryLiveAuiMessageRecordList(LiveAuiMessageRecord liveAuiMessageRecord);

    /**
     * 根据消息群组ID获取当场直播所有记录
     * @param roomId 直播间ID
     * @return 直播消息记录
     */
    List<LiveAuiMessageTemplateScript> getHistoryMessageRecrod(String roomId);

    /**
     * 获取某直播间互动消息
     * @param liveAuiMessageRecord
     * @return
     */
    List<LiveAuiMessageTemplateScriptVo> listRoomMessage(LiveAuiMessageRecord liveAuiMessageRecord);

    /**
     * 下载已结束直播间消息
     */
    void executeDownloadLiveRoomMessageTask();
}
