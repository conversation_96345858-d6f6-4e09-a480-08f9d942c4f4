package com.panda.pollen.aui.script;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.panda.pollen.aui.model.dto.AliLiveMessageBody;
import com.panda.pollen.aui.model.dto.AliLiveSenderInfo;
import com.panda.pollen.aui.model.enums.LiveMsgSenderType;
import com.panda.pollen.aui.model.req.AliSendLiveMessageUserRequest;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 消息构建工具类
 * 负责根据脚本信息构建阿里云消息请求对象
 *
 * <AUTHOR>
 */
@UtilityClass
@Slf4j
public class MessageBuilder {

    /**
     * 构建消息请求对象
     *
     * @param messageGroupId 消息组ID
     * @param script 脚本信息
     * @return 消息请求对象
     */
    public static AliSendLiveMessageUserRequest buildMessageRequest(Long messageGroupId, LiveAuiMessageTemplateScript script) {
        if (messageGroupId == null || script == null) {
            throw new IllegalArgumentException("messageGroupId和script不能为空");
        }
        AliSendLiveMessageUserRequest request = new AliSendLiveMessageUserRequest();
        // 设置基本信息
        request.setGroupId(String.valueOf(messageGroupId));
        request.setSenderId(CharSequenceUtil.isNotBlank(script.getSenderId()) ? script.getSenderId() : "system");
        request.setMsgType(script.getMessageType() != null ? script.getMessageType() : 1L);
        request.setMsgTid(IdUtil.getSnowflakeNextIdStr());

        // 设置消息属性
        request.setStorage(true);
        request.setHighReliability(false);
        request.setNoCache(false);
        request.setStaticsIncrease(1L);
        request.setWeight(1L);

        // 构建发送者信息
        AliLiveSenderInfo senderInfo = buildSenderInfo(script);
        request.setSenderInfo(senderInfo);

        // 构建消息体
        AliLiveMessageBody messageBody = buildMessageBody(script);
        request.setBody(messageBody);

        return request;
    }

    /**
     * 构建发送者信息
     *
     * @param script 脚本信息
     * @return 发送者信息
     */
    private static AliLiveSenderInfo buildSenderInfo(LiveAuiMessageTemplateScript script) {
        return AliLiveSenderInfo.builder()
                .nickName(CharSequenceUtil.isNotBlank(script.getSenderName()) ? script.getSenderName() : "系统")
                .userAvatar(CharSequenceUtil.isNotBlank(script.getSenderHeadImg()) ? script.getSenderHeadImg() : "")
                .build();
    }

    /**
     * 构建消息体
     *
     * @param script 脚本信息
     * @return 消息体
     */
    private static AliLiveMessageBody buildMessageBody(LiveAuiMessageTemplateScript script) {
        LiveMsgSenderType senderType = convertSenderType(script.getSenderType());
        // 构建扩展信息
        Map<String, Object> extend = new HashMap<>();
        // 添加显示位置信息
        if (script.getDisplaySite() != null) {
            extend.put("displaySite", script.getDisplaySite());
        }
        // 添加打开方式信息
        if (script.getOpenWay() != null) {
            extend.put("openWay", script.getOpenWay());
        }
        // 添加链接地址
        if (CharSequenceUtil.isNotBlank(script.getLinkUrl())) {
            extend.put("linkUrl", script.getLinkUrl());
        }
        // 添加图片地址
        if (CharSequenceUtil.isNotBlank(script.getImageUrl())) {
            extend.put("imageUrl", script.getImageUrl());
        }
        // 添加回复消息ID
        if (CharSequenceUtil.isNotBlank(script.getReplayMessageId())) {
            extend.put("replyMessageId", script.getReplayMessageId());
        }
        // 添加关联产品或优惠券ID
        if (CharSequenceUtil.isNotBlank(script.getAssociateId())) {
            extend.put("associateId", script.getAssociateId());
        }
        // 添加消息内容类型
        if (script.getMessageContentType() != null) {
            extend.put("messageContentType", script.getMessageContentType());
        }
        return AliLiveMessageBody.builder()
                .content(CharSequenceUtil.isNotBlank(script.getMessageContent()) ? script.getMessageContent() : "")
                .sid(IdUtil.getSnowflakeNextIdStr())
                .senderType(senderType)
                .extend(extend.isEmpty() ? null : extend)
                .build();
    }

    /**
     * 转换发送者类型
     *
     * @param senderType 脚本中的发送者类型
     * @return 阿里云消息发送者类型
     */
    private static LiveMsgSenderType convertSenderType(Long senderType) {
        if (senderType == null) {
            return LiveMsgSenderType.SYSTEM;
        }

        return switch (senderType.intValue()) {
            case 1 ->
                // 学员
                    LiveMsgSenderType.CUSTOMER;
            case 2 ->
                // 助教
                    LiveMsgSenderType.ASSISTANT;
            case 3 ->
                // 机器人
                    LiveMsgSenderType.ROBOT;
            case 4 ->
                // 主播
                    LiveMsgSenderType.ANCHOR;
            case 5 ->
                // 其他
                    LiveMsgSenderType.SYSTEM;
            default -> LiveMsgSenderType.SYSTEM;
        };
    }

    /**
     * 验证脚本信息
     *
     * @param script 脚本信息
     * @return 是否有效
     */
    public static boolean isValidScript(LiveAuiMessageTemplateScript script) {
        if (script == null) {
            log.warn("脚本信息为空");
            return false;
        }
        if (CharSequenceUtil.isBlank(script.getMessageContent())) {
            log.warn("脚本消息内容为空，scriptId: {}", script.getId());
            return false;
        }
        if (script.getSendTime() == null) {
            log.warn("脚本发送时间为空，scriptId: {}", script.getId());
            return false;
        }
        return true;
    }

}
