package com.panda.pollen.aui.service.impl;

import cn.hutool.core.util.StrUtil;
import com.panda.pollen.aui.service.ILiveScriptInterpretationBizService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplate;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfo;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfoExt;
import com.panda.pollen.aui.system.service.ILiveAuiMessageTemplateScriptService;
import com.panda.pollen.aui.system.service.ILiveAuiMessageTemplateService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoExtService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoService;
import com.panda.pollen.common.exception.base.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LiveScriptInterpretationBizServiceImpl implements ILiveScriptInterpretationBizService {
    @Resource
    private ILiveAuiRoomInfoService iLiveAuiRoomInfoService;
    @Resource
    private ILiveAuiRoomInfoExtService iLiveAuiRoomInfoExtService;
    @Resource
    private ILiveAuiMessageTemplateService iLiveAuiMessageTemplateService;
    @Resource
    private ILiveAuiMessageTemplateScriptService iLiveAuiMessageTemplateScriptService;
    @Resource
    private ScriptTaskManager scriptTaskManager;

    @Override
    public void play(Long roomId) {
        if (ObjectUtils.isEmpty(roomId)) {
            throw new BaseException("直播间ID不能为空");
        }

        log.info("开始播放直播间脚本，roomId: {}", roomId);

        try {
            // 获取直播间信息
            LiveAuiRoomInfo liveAuiRoomInfo = iLiveAuiRoomInfoService.getById(roomId);
            if (ObjectUtils.isEmpty(liveAuiRoomInfo)) {
                throw new BaseException("未找到ID为 " + roomId + " 的直播间");
            }

            // 获取直播间扩展信息
            List<LiveAuiRoomInfoExt> liveAuiRoomInfoExts = iLiveAuiRoomInfoExtService.lambdaQuery()
                    .eq(LiveAuiRoomInfoExt::getRoomInfoId, roomId).list();
            if (liveAuiRoomInfoExts.size() != 1) {
                throw new BaseException(StrUtil.format("未找到ID为{}的直播间扩展信息/直播间扩展信息不唯一", roomId));
            }

            LiveAuiRoomInfoExt liveAuiRoomInfoExt = liveAuiRoomInfoExts.get(0);
            Long liveTemplateId = liveAuiRoomInfoExt.getLiveTemplateId();
            Long messageGroupId = liveAuiRoomInfoExt.getMessageGroupId();

            if (ObjectUtils.isEmpty(messageGroupId)) {
                throw new BaseException("直播间消息组ID不能为空");
            }

            if (!ObjectUtils.isEmpty(liveTemplateId)) {
                // 验证模板存在
                LiveAuiMessageTemplate liveAuiMessageTemplate = iLiveAuiMessageTemplateService.getById(liveTemplateId);
                if (ObjectUtils.isEmpty(liveAuiMessageTemplate)) {
                    throw new BaseException("未找到ID为 " + liveTemplateId + " 的直播间模板消息");
                }

                // 获取所有要演绎的脚本并按发送时间排序
                List<LiveAuiMessageTemplateScript> scripts = iLiveAuiMessageTemplateScriptService.lambdaQuery()
                        .eq(LiveAuiMessageTemplateScript::getTemplateId, liveTemplateId)
                        .orderByAsc(LiveAuiMessageTemplateScript::getSendTime)
                        .list();

                if (!scripts.isEmpty()) {
                    log.info("找到 {} 个脚本，开始调度定时任务，roomId: {}", scripts.size(), roomId);

                    // 使用脚本任务管理器启动定时任务
                    scriptTaskManager.startScriptTasks(roomId, messageGroupId, scripts);

                    log.info("脚本任务调度完成，roomId: {}", roomId);
                } else {
                    log.warn("未找到可执行的脚本，roomId: {}, templateId: {}", roomId, liveTemplateId);
                }
            } else {
                log.warn("直播间未配置消息模板，roomId: {}", roomId);
            }

        } catch (Exception e) {
            log.error("播放直播间脚本失败，roomId: {}", roomId, e);
            throw e;
        }
    }

    /**
     * 停止播放脚本
     *
     * @param roomId 直播间ID
     */
    public void stop(Long roomId) {
        if (ObjectUtils.isEmpty(roomId)) {
            throw new BaseException("直播间ID不能为空");
        }

        log.info("停止播放直播间脚本，roomId: {}", roomId);
        scriptTaskManager.stopScriptTasks(roomId);
    }

    /**
     * 获取脚本任务状态
     *
     * @param roomId 直播间ID
     * @return 任务状态
     */
    public ScriptTaskManager.TaskStatus getTaskStatus(Long roomId) {
        if (ObjectUtils.isEmpty(roomId)) {
            throw new BaseException("直播间ID不能为空");
        }

        return scriptTaskManager.getTaskStatus(roomId);
    }
}
