package com.panda.pollen.aui.service.impl;

import cn.hutool.core.util.StrUtil;
import com.panda.pollen.aui.api.ILiveScriptInterpretationBizApi;
import com.panda.pollen.aui.model.req.AliSendLiveMessageUserRequest;
import com.panda.pollen.aui.service.IAliCloudBizService;
import com.panda.pollen.aui.service.ILiveScriptInterpretationBizService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplate;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfo;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfoExt;
import com.panda.pollen.aui.system.service.ILiveAuiMessageTemplateScriptService;
import com.panda.pollen.aui.system.service.ILiveAuiMessageTemplateService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoExtService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoService;
import com.panda.pollen.common.exception.base.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LiveScriptInterpretationBizServiceImpl implements ILiveScriptInterpretationBizService, ILiveScriptInterpretationBizApi {
    @Resource
    private ILiveAuiRoomInfoService iLiveAuiRoomInfoService;
    @Resource
    private ILiveAuiRoomInfoExtService iLiveAuiRoomInfoExtService;
    @Resource
    private ILiveAuiMessageTemplateService iLiveAuiMessageTemplateService;
    @Resource
    private ILiveAuiMessageTemplateScriptService iLiveAuiMessageTemplateScriptService;
    @Resource
    private IAliCloudBizService iAliCloudBizService;

    @Override
    public void play(Long roomId) {
        if (ObjectUtils.isEmpty(roomId)) {
            throw new BaseException("直播间ID不能为空");
        }
        LiveAuiRoomInfo liveAuiRoomInfo = iLiveAuiRoomInfoService.getById(roomId);
        if (ObjectUtils.isEmpty(liveAuiRoomInfo)) {
            throw new BaseException("未找到ID为 " + roomId + " 的直播间");
        }
        List<LiveAuiRoomInfoExt> liveAuiRoomInfoExts = iLiveAuiRoomInfoExtService.lambdaQuery().eq(LiveAuiRoomInfoExt::getRoomInfoId, roomId).list();
        if (liveAuiRoomInfoExts.size() != 1) {
            throw new BaseException(StrUtil.format("未找到ID为{}的直播间扩展信息/直播间扩展信息不唯一", roomId));
        }
        // 直播间扩展信息
        LiveAuiRoomInfoExt liveAuiRoomInfoExt = liveAuiRoomInfoExts.get(0);
        Long liveTemplateId = liveAuiRoomInfoExt.getLiveTemplateId();
        if (!ObjectUtils.isEmpty(liveTemplateId)) {
            LiveAuiMessageTemplate liveAuiMessageTemplate = iLiveAuiMessageTemplateService.getById(liveTemplateId);
            if (ObjectUtils.isEmpty(liveAuiMessageTemplate)) {
                throw new BaseException("未找到ID为 " + liveTemplateId + " 的直播间模板消息");
            }
            // 获取所有要演绎的脚本
            List<LiveAuiMessageTemplateScript> liveAuiMessageTemplateScripts = iLiveAuiMessageTemplateScriptService.lambdaQuery().eq(LiveAuiMessageTemplateScript::getTemplateId, liveTemplateId).list();
            // 存在要执行的脚本
            if (!liveAuiMessageTemplateScripts.isEmpty()) {
                // 以视频的播放为起始点
                // 根据每条脚本，每条脚本的发送时间，进行消息发送
                // 消息组ID
                Long messageGroupId = liveAuiRoomInfoExt.getMessageGroupId();
                AliSendLiveMessageUserRequest request = new AliSendLiveMessageUserRequest();
                request.setGroupId(String.valueOf(messageGroupId));
                // 发送消息到群组
                iAliCloudBizService.sendMessage2All(request);
            }
        }
    }
}
