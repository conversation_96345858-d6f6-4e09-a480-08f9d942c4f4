package com.panda.pollen.aui.service;

import com.panda.pollen.aui.script.ScriptTaskStatus;

/**
 * <AUTHOR>
 */
public interface ILiveAuiJobBizService {
    /**
     * 播放直播间脚本
     *
     * @param delay 延迟时间
     */
    void play(Integer delay);

    /**
     * 播放直播间脚本
     *
     * @param roomId 直播间ID
     */
    void start(Long roomId);

    /**
     * 停止播放脚本
     *
     * @param roomId 直播间ID
     */
    void stop(Long roomId);

    /**
     * 获取脚本任务状态
     *
     * @param roomId 直播间ID
     * @return 任务状态
     */
    ScriptTaskStatus getTaskStatus(Long roomId);
}
