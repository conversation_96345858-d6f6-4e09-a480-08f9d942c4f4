package com.panda.pollen.aui.verification;

import com.panda.pollen.aui.service.impl.ScriptTaskManager;
import com.panda.pollen.aui.util.MessageBuilder;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;

import java.time.LocalTime;

/**
 * Simple compile test to verify syntax correctness
 */
public class SimpleCompileTest {

    public static void main(String[] args) {
        System.out.println("Starting simple compile test...");
        
        try {
            // Test TaskStatus creation
            ScriptTaskManager.TaskStatus status = new ScriptTaskManager.TaskStatus(1001L, 10, 5, 2);
            System.out.println("TaskStatus created: " + status.toString());
            
            // Test getter methods
            System.out.println("Room ID: " + status.getRoomId());
            System.out.println("Total tasks: " + status.getTotalTasks());
            System.out.println("Completed tasks: " + status.getCompletedTasks());
            System.out.println("Cancelled tasks: " + status.getCancelledTasks());
            System.out.println("Running tasks: " + status.getRunningTasks());
            
            // Test MessageBuilder static methods
            System.out.println("Sender type description (1): " + MessageBuilder.getSenderTypeDescription(1L));
            System.out.println("Sender type description (null): " + MessageBuilder.getSenderTypeDescription(null));
            
            System.out.println("Content type description (1): " + MessageBuilder.getContentTypeDescription(1L));
            System.out.println("Content type description (null): " + MessageBuilder.getContentTypeDescription(null));
            
            System.out.println("Display site description (1): " + MessageBuilder.getDisplaySiteDescription(1L));
            System.out.println("Display site description (null): " + MessageBuilder.getDisplaySiteDescription(null));
            
            // Test script validation
            LiveAuiMessageTemplateScript validScript = new LiveAuiMessageTemplateScript();
            validScript.setMessageContent("Test message");
            validScript.setSendTime(LocalTime.of(10, 0, 0));
            
            System.out.println("Valid script check: " + MessageBuilder.isValidScript(validScript));
            System.out.println("Null script check: " + MessageBuilder.isValidScript(null));
            
            System.out.println("Simple compile test completed successfully!");
            
        } catch (Exception e) {
            System.err.println("Compile test failed: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}
