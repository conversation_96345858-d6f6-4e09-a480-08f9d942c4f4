package com.panda.pollen.aui.service.impl;

import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplate;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfo;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfoExt;
import com.panda.pollen.aui.system.service.ILiveAuiMessageTemplateScriptService;
import com.panda.pollen.aui.system.service.ILiveAuiMessageTemplateService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoExtService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoService;
import com.panda.pollen.common.exception.base.BaseException;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LiveScriptInterpretationBizServiceImpl 测试类
 */
@ExtendWith(MockitoExtension.class)
class LiveScriptInterpretationBizServiceImplTest {

    @Mock
    private ILiveAuiRoomInfoService iLiveAuiRoomInfoService;

    @Mock
    private ILiveAuiRoomInfoExtService iLiveAuiRoomInfoExtService;

    @Mock
    private ILiveAuiMessageTemplateService iLiveAuiMessageTemplateService;

    @Mock
    private ILiveAuiMessageTemplateScriptService iLiveAuiMessageTemplateScriptService;

    @Mock
    private ScriptTaskManager scriptTaskManager;

    @Mock
    private LambdaQueryChainWrapper<LiveAuiRoomInfoExt> roomInfoExtQueryWrapper;

    @Mock
    private LambdaQueryChainWrapper<LiveAuiMessageTemplateScript> scriptQueryWrapper;

    @InjectMocks
    private LiveScriptInterpretationBizServiceImpl liveScriptInterpretationBizService;

    private Long roomId;
    private LiveAuiRoomInfo roomInfo;
    private LiveAuiRoomInfoExt roomInfoExt;
    private LiveAuiMessageTemplate messageTemplate;
    private List<LiveAuiMessageTemplateScript> scripts;

    @BeforeEach
    void setUp() {
        roomId = 1001L;

        // 创建直播间信息
        roomInfo = new LiveAuiRoomInfo();
        roomInfo.setId(roomId);
        roomInfo.setTitle("测试直播间");

        // 创建直播间扩展信息
        roomInfoExt = new LiveAuiRoomInfoExt();
        roomInfoExt.setId(2001L);
        roomInfoExt.setRoomInfoId(roomId);
        roomInfoExt.setMessageGroupId(3001L);
        roomInfoExt.setLiveTemplateId(4001L);

        // 创建消息模板
        messageTemplate = new LiveAuiMessageTemplate();
        messageTemplate.setId(4001L);
        messageTemplate.setTemplateName("测试模板");

        // 创建脚本列表
        LiveAuiMessageTemplateScript script1 = new LiveAuiMessageTemplateScript();
        script1.setId(5001L);
        script1.setTemplateId(4001L);
        script1.setMessageContent("欢迎来到直播间！");
        script1.setSendTime(LocalTime.of(10, 0, 0));
        script1.setSenderName("主播");

        LiveAuiMessageTemplateScript script2 = new LiveAuiMessageTemplateScript();
        script2.setId(5002L);
        script2.setTemplateId(4001L);
        script2.setMessageContent("记得关注我们哦！");
        script2.setSendTime(LocalTime.of(10, 5, 0));
        script2.setSenderName("助教");

        scripts = Arrays.asList(script1, script2);
    }

    @Test
    void testPlay_Success() {
        // 模拟服务调用
        when(iLiveAuiRoomInfoService.getById(roomId)).thenReturn(roomInfo);
        when(iLiveAuiRoomInfoExtService.lambdaQuery()).thenReturn(roomInfoExtQueryWrapper);
        when(roomInfoExtQueryWrapper.eq(any(), eq(roomId))).thenReturn(roomInfoExtQueryWrapper);
        when(roomInfoExtQueryWrapper.list()).thenReturn(Collections.singletonList(roomInfoExt));
        when(iLiveAuiMessageTemplateService.getById(4001L)).thenReturn(messageTemplate);
        when(iLiveAuiMessageTemplateScriptService.lambdaQuery()).thenReturn(scriptQueryWrapper);
        when(scriptQueryWrapper.eq(any(), eq(4001L))).thenReturn(scriptQueryWrapper);
        when(scriptQueryWrapper.orderByAsc(any())).thenReturn(scriptQueryWrapper);
        when(scriptQueryWrapper.list()).thenReturn(scripts);

        // 执行测试
        assertDoesNotThrow(() -> liveScriptInterpretationBizService.play(roomId));

        // 验证调用
        verify(iLiveAuiRoomInfoService).getById(roomId);
        verify(iLiveAuiRoomInfoExtService).lambdaQuery();
        verify(iLiveAuiMessageTemplateService).getById(4001L);
        verify(iLiveAuiMessageTemplateScriptService).lambdaQuery();
        verify(scriptTaskManager).startScriptTasks(roomId, 3001L, scripts);
    }

    @Test
    void testPlay_NullRoomId() {
        // 测试空的直播间ID
        BaseException exception = assertThrows(BaseException.class, () -> {
            liveScriptInterpretationBizService.play(null);
        });
        assertEquals("直播间ID不能为空", exception.getMessage());
    }

    @Test
    void testPlay_RoomNotFound() {
        // 模拟直播间不存在
        when(iLiveAuiRoomInfoService.getById(roomId)).thenReturn(null);

        BaseException exception = assertThrows(BaseException.class, () -> {
            liveScriptInterpretationBizService.play(roomId);
        });
        assertEquals("未找到ID为 " + roomId + " 的直播间", exception.getMessage());
    }

    @Test
    void testPlay_RoomExtNotFound() {
        // 模拟直播间扩展信息不存在
        when(iLiveAuiRoomInfoService.getById(roomId)).thenReturn(roomInfo);
        when(iLiveAuiRoomInfoExtService.lambdaQuery()).thenReturn(roomInfoExtQueryWrapper);
        when(roomInfoExtQueryWrapper.eq(any(), eq(roomId))).thenReturn(roomInfoExtQueryWrapper);
        when(roomInfoExtQueryWrapper.list()).thenReturn(Collections.emptyList());

        BaseException exception = assertThrows(BaseException.class, () -> {
            liveScriptInterpretationBizService.play(roomId);
        });
        assertTrue(exception.getMessage().contains("未找到ID为" + roomId + "的直播间扩展信息"));
    }

    @Test
    void testPlay_MultipleRoomExt() {
        // 模拟多个直播间扩展信息
        when(iLiveAuiRoomInfoService.getById(roomId)).thenReturn(roomInfo);
        when(iLiveAuiRoomInfoExtService.lambdaQuery()).thenReturn(roomInfoExtQueryWrapper);
        when(roomInfoExtQueryWrapper.eq(any(), eq(roomId))).thenReturn(roomInfoExtQueryWrapper);
        when(roomInfoExtQueryWrapper.list()).thenReturn(Arrays.asList(roomInfoExt, roomInfoExt));

        BaseException exception = assertThrows(BaseException.class, () -> {
            liveScriptInterpretationBizService.play(roomId);
        });
        assertTrue(exception.getMessage().contains("直播间扩展信息不唯一"));
    }

    @Test
    void testPlay_NoMessageGroupId() {
        // 模拟消息组ID为空
        roomInfoExt.setMessageGroupId(null);
        when(iLiveAuiRoomInfoService.getById(roomId)).thenReturn(roomInfo);
        when(iLiveAuiRoomInfoExtService.lambdaQuery()).thenReturn(roomInfoExtQueryWrapper);
        when(roomInfoExtQueryWrapper.eq(any(), eq(roomId))).thenReturn(roomInfoExtQueryWrapper);
        when(roomInfoExtQueryWrapper.list()).thenReturn(Collections.singletonList(roomInfoExt));

        BaseException exception = assertThrows(BaseException.class, () -> {
            liveScriptInterpretationBizService.play(roomId);
        });
        assertEquals("直播间消息组ID不能为空", exception.getMessage());
    }

    @Test
    void testPlay_TemplateNotFound() {
        // 模拟模板不存在
        when(iLiveAuiRoomInfoService.getById(roomId)).thenReturn(roomInfo);
        when(iLiveAuiRoomInfoExtService.lambdaQuery()).thenReturn(roomInfoExtQueryWrapper);
        when(roomInfoExtQueryWrapper.eq(any(), eq(roomId))).thenReturn(roomInfoExtQueryWrapper);
        when(roomInfoExtQueryWrapper.list()).thenReturn(Collections.singletonList(roomInfoExt));
        when(iLiveAuiMessageTemplateService.getById(4001L)).thenReturn(null);

        BaseException exception = assertThrows(BaseException.class, () -> {
            liveScriptInterpretationBizService.play(roomId);
        });
        assertEquals("未找到ID为 4001 的直播间模板消息", exception.getMessage());
    }

    @Test
    void testPlay_NoScripts() {
        // 模拟没有脚本
        when(iLiveAuiRoomInfoService.getById(roomId)).thenReturn(roomInfo);
        when(iLiveAuiRoomInfoExtService.lambdaQuery()).thenReturn(roomInfoExtQueryWrapper);
        when(roomInfoExtQueryWrapper.eq(any(), eq(roomId))).thenReturn(roomInfoExtQueryWrapper);
        when(roomInfoExtQueryWrapper.list()).thenReturn(Collections.singletonList(roomInfoExt));
        when(iLiveAuiMessageTemplateService.getById(4001L)).thenReturn(messageTemplate);
        when(iLiveAuiMessageTemplateScriptService.lambdaQuery()).thenReturn(scriptQueryWrapper);
        when(scriptQueryWrapper.eq(any(), eq(4001L))).thenReturn(scriptQueryWrapper);
        when(scriptQueryWrapper.orderByAsc(any())).thenReturn(scriptQueryWrapper);
        when(scriptQueryWrapper.list()).thenReturn(Collections.emptyList());

        // 执行测试，应该不抛出异常
        assertDoesNotThrow(() -> liveScriptInterpretationBizService.play(roomId));

        // 验证不会调用scriptTaskManager
        verify(scriptTaskManager, never()).startScriptTasks(any(), any(), any());
    }

    @Test
    void testStop_Success() {
        // 执行测试
        assertDoesNotThrow(() -> liveScriptInterpretationBizService.stop(roomId));

        // 验证调用
        verify(scriptTaskManager).stopScriptTasks(roomId);
    }

    @Test
    void testStop_NullRoomId() {
        // 测试空的直播间ID
        BaseException exception = assertThrows(BaseException.class, () -> {
            liveScriptInterpretationBizService.stop(null);
        });
        assertEquals("直播间ID不能为空", exception.getMessage());
    }

    @Test
    void testGetTaskStatus_Success() {
        // 模拟任务状态
        ScriptTaskManager.TaskStatus expectedStatus = new ScriptTaskManager.TaskStatus(roomId, 5, 2, 1);
        when(scriptTaskManager.getTaskStatus(roomId)).thenReturn(expectedStatus);

        // 执行测试
        ScriptTaskManager.TaskStatus actualStatus = liveScriptInterpretationBizService.getTaskStatus(roomId);

        // 验证结果
        assertNotNull(actualStatus);
        assertEquals(expectedStatus.roomId(), actualStatus.roomId());
        assertEquals(expectedStatus.totalTasks(), actualStatus.totalTasks());
        assertEquals(expectedStatus.completedTasks(), actualStatus.completedTasks());
        assertEquals(expectedStatus.cancelledTasks(), actualStatus.cancelledTasks());
        verify(scriptTaskManager).getTaskStatus(roomId);
    }

    @Test
    void testGetTaskStatus_NullRoomId() {
        // 测试空的直播间ID
        BaseException exception = assertThrows(BaseException.class, () -> {
            liveScriptInterpretationBizService.getTaskStatus(null);
        });
        assertEquals("直播间ID不能为空", exception.getMessage());
    }
}
