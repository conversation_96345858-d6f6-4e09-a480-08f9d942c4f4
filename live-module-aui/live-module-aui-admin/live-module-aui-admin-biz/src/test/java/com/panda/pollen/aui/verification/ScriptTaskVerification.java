package com.panda.pollen.aui.verification;

import com.panda.pollen.aui.model.req.AliSendLiveMessageUserRequest;
import com.panda.pollen.aui.service.impl.ScriptTaskManager;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;
import com.panda.pollen.aui.util.MessageBuilder;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

/**
 * Script task functionality verification class
 * Used to verify that the fixed code works properly
 */
@Slf4j
public class ScriptTaskVerification {

    public static void main(String[] args) {
        System.out.println("Starting script task functionality verification...");

        try {
            // Verify MessageBuilder
            verifyMessageBuilder();

            // Verify TaskStatus
            verifyTaskStatus();

            // Verify script validation logic
            verifyScriptValidation();

            // Verify type conversions
            verifyTypeConversions();

            System.out.println("All verifications passed!");

        } catch (Exception e) {
            System.err.println("Verification failed: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    /**
     * Verify MessageBuilder functionality
     */
    private static void verifyMessageBuilder() {
        System.out.println("Verifying MessageBuilder functionality...");

        // Create test script
        LiveAuiMessageTemplateScript script = createTestScript();
        Long messageGroupId = 2001L;

        try {
            // Test message building
            AliSendLiveMessageUserRequest request = MessageBuilder.buildMessageRequest(messageGroupId, script);

            // Verify basic fields
            assert request != null : "Message request cannot be null";
            assert String.valueOf(messageGroupId).equals(request.getGroupId()) : "Message group ID mismatch";
            assert "anchor001".equals(request.getSenderId()) : "Sender ID mismatch";
            assert request.getBody() != null : "Message body cannot be null";
            assert request.getSenderInfo() != null : "Sender info cannot be null";

            System.out.println("MessageBuilder verification passed");

        } catch (Exception e) {
            throw new RuntimeException("MessageBuilder verification failed", e);
        }
    }
    
    /**
     * Verify TaskStatus functionality
     */
    private static void verifyTaskStatus() {
        System.out.println("Verifying TaskStatus functionality...");

        try {
            // Create TaskStatus instance
            ScriptTaskManager.TaskStatus status = new ScriptTaskManager.TaskStatus(1001L, 10, 5, 2);

            // Verify getter methods
            assert status.getRoomId().equals(1001L) : "RoomId mismatch";
            assert status.getTotalTasks() == 10 : "Total tasks mismatch";
            assert status.getCompletedTasks() == 5 : "Completed tasks mismatch";
            assert status.getCancelledTasks() == 2 : "Cancelled tasks mismatch";
            assert status.getRunningTasks() == 3 : "Running tasks mismatch";

            // Verify toString method
            String statusStr = status.toString();
            assert statusStr.contains("roomId=1001") : "toString does not contain roomId";
            assert statusStr.contains("total=10") : "toString does not contain total";
            assert statusStr.contains("running=3") : "toString does not contain running";

            System.out.println("TaskStatus verification passed");

        } catch (Exception e) {
            throw new RuntimeException("TaskStatus verification failed", e);
        }
    }
    
    /**
     * Verify script validation logic
     */
    private static void verifyScriptValidation() {
        System.out.println("Verifying script validation logic...");

        try {
            // Test valid script
            LiveAuiMessageTemplateScript validScript = createTestScript();
            assert MessageBuilder.isValidScript(validScript) : "Valid script validation failed";

            // Test null script
            assert !MessageBuilder.isValidScript(null) : "Null script should be invalid";

            // Test script without content
            LiveAuiMessageTemplateScript noContentScript = new LiveAuiMessageTemplateScript();
            noContentScript.setSendTime(LocalTime.of(10, 0, 0));
            assert !MessageBuilder.isValidScript(noContentScript) : "Script without content should be invalid";

            // Test script without time
            LiveAuiMessageTemplateScript noTimeScript = new LiveAuiMessageTemplateScript();
            noTimeScript.setMessageContent("Test content");
            assert !MessageBuilder.isValidScript(noTimeScript) : "Script without time should be invalid";

            System.out.println("Script validation logic verification passed");

        } catch (Exception e) {
            throw new RuntimeException("Script validation logic verification failed", e);
        }
    }
    
    /**
     * Create test script
     */
    private static LiveAuiMessageTemplateScript createTestScript() {
        LiveAuiMessageTemplateScript script = new LiveAuiMessageTemplateScript();
        script.setId(1L);
        script.setMessageContent("Test message content");
        script.setSendTime(LocalTime.of(10, 0, 0));
        script.setSenderName("Test Anchor");
        script.setSenderId("anchor001");
        script.setSenderHeadImg("http://example.com/avatar.jpg");
        script.setSenderType(4L); // Anchor
        script.setMessageType(1L);
        script.setMessageContentType(1L); // Text
        script.setDisplaySite(3L); // Bottom
        script.setOpenWay(1L); // Bottom popup
        script.setLinkUrl("http://example.com/link");
        script.setImageUrl("http://example.com/image.jpg");
        script.setReplayMessageId("reply123");
        script.setAssociateId("product456");
        return script;
    }
    
    /**
     * Verify type conversion methods
     */
    private static void verifyTypeConversions() {
        System.out.println("Verifying type conversion methods...");

        try {
            // Verify sender type descriptions
            assert "学员".equals(MessageBuilder.getSenderTypeDescription(1L));
            assert "助教".equals(MessageBuilder.getSenderTypeDescription(2L));
            assert "机器人".equals(MessageBuilder.getSenderTypeDescription(3L));
            assert "主播".equals(MessageBuilder.getSenderTypeDescription(4L));
            assert "其他".equals(MessageBuilder.getSenderTypeDescription(5L));
            assert "系统".equals(MessageBuilder.getSenderTypeDescription(null));
            assert "系统".equals(MessageBuilder.getSenderTypeDescription(99L));

            // Verify content type descriptions
            assert "文本".equals(MessageBuilder.getContentTypeDescription(1L));
            assert "图片".equals(MessageBuilder.getContentTypeDescription(2L));
            assert "文本".equals(MessageBuilder.getContentTypeDescription(null));
            assert "文本".equals(MessageBuilder.getContentTypeDescription(99L));

            // Verify display site descriptions
            assert "置顶".equals(MessageBuilder.getDisplaySiteDescription(1L));
            assert "弹幕".equals(MessageBuilder.getDisplaySiteDescription(2L));
            assert "底部".equals(MessageBuilder.getDisplaySiteDescription(3L));
            assert "弹窗".equals(MessageBuilder.getDisplaySiteDescription(4L));
            assert "底部".equals(MessageBuilder.getDisplaySiteDescription(null));
            assert "底部".equals(MessageBuilder.getDisplaySiteDescription(99L));

            System.out.println("Type conversion methods verification passed");

        } catch (Exception e) {
            throw new RuntimeException("Type conversion methods verification failed", e);
        }
    }
}
