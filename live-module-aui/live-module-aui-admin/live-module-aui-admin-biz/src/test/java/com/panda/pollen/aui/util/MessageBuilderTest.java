package com.panda.pollen.aui.util;

import com.panda.pollen.aui.model.dto.AliLiveMessageBody;
import com.panda.pollen.aui.model.dto.AliLiveSenderInfo;
import com.panda.pollen.aui.model.enums.LiveMsgSenderType;
import com.panda.pollen.aui.model.req.AliSendLiveMessageUserRequest;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MessageBuilder 测试类
 */
class MessageBuilderTest {

    private LiveAuiMessageTemplateScript script;
    private Long messageGroupId;

    @BeforeEach
    void setUp() {
        messageGroupId = 2001L;
        
        script = new LiveAuiMessageTemplateScript();
        script.setId(1L);
        script.setMessageContent("测试消息内容");
        script.setSendTime(LocalTime.of(10, 0, 0));
        script.setSenderName("测试主播");
        script.setSenderId("anchor001");
        script.setSenderHeadImg("http://example.com/avatar.jpg");
        script.setSenderType(4L); // 主播
        script.setMessageType(1L);
        script.setMessageContentType(1L); // 文本
        script.setDisplaySite(3L); // 底部
        script.setOpenWay(1L); // 底部弹出
        script.setLinkUrl("http://example.com/link");
        script.setImageUrl("http://example.com/image.jpg");
        script.setReplayMessageId("reply123");
        script.setAssociateId("product456");
    }

    @Test
    void testBuildMessageRequest_Success() {
        // 执行测试
        AliSendLiveMessageUserRequest request = MessageBuilder.buildMessageRequest(messageGroupId, script);

        // 验证基本信息
        assertNotNull(request);
        assertEquals(String.valueOf(messageGroupId), request.getGroupId());
        assertEquals("anchor001", request.getSenderId());
        assertEquals(1L, request.getMsgType());
        assertNotNull(request.getMsgTid());
        assertTrue(request.isStorage());
        assertFalse(request.isHighReliability());
        assertFalse(request.isNoCache());
        assertEquals(1L, request.getStaticsIncrease());
        assertEquals(1L, request.getWeight());

        // 验证发送者信息
        AliLiveSenderInfo senderInfo = request.getSenderInfo();
        assertNotNull(senderInfo);
        assertEquals("测试主播", senderInfo.getNickName());
        assertEquals("http://example.com/avatar.jpg", senderInfo.getUserAvatar());

        // 验证消息体
        AliLiveMessageBody messageBody = request.getBody();
        assertNotNull(messageBody);
        assertEquals("测试消息内容", messageBody.getContent());
        assertEquals(LiveMsgSenderType.ANCHOR, messageBody.getSenderType());
        assertNotNull(messageBody.getSid());
        assertNotNull(messageBody.getExtend());
        
        // 验证扩展信息
        assertEquals(3L, messageBody.getExtend().get("displaySite"));
        assertEquals(1L, messageBody.getExtend().get("openWay"));
        assertEquals("http://example.com/link", messageBody.getExtend().get("linkUrl"));
        assertEquals("http://example.com/image.jpg", messageBody.getExtend().get("imageUrl"));
        assertEquals("reply123", messageBody.getExtend().get("replyMessageId"));
        assertEquals("product456", messageBody.getExtend().get("associateId"));
        assertEquals(1L, messageBody.getExtend().get("messageContentType"));
    }

    @Test
    void testBuildMessageRequest_NullParameters() {
        // 测试空参数
        assertThrows(IllegalArgumentException.class, () -> {
            MessageBuilder.buildMessageRequest(null, script);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            MessageBuilder.buildMessageRequest(messageGroupId, null);
        });
    }

    @Test
    void testBuildMessageRequest_MinimalScript() {
        // 创建最小化脚本
        LiveAuiMessageTemplateScript minimalScript = new LiveAuiMessageTemplateScript();
        minimalScript.setId(1L);
        minimalScript.setMessageContent("最小消息");
        minimalScript.setSendTime(LocalTime.of(10, 0, 0));

        // 执行测试
        AliSendLiveMessageUserRequest request = MessageBuilder.buildMessageRequest(messageGroupId, minimalScript);

        // 验证
        assertNotNull(request);
        assertEquals("system", request.getSenderId());
        assertEquals(1L, request.getMsgType());
        
        AliLiveSenderInfo senderInfo = request.getSenderInfo();
        assertEquals("系统", senderInfo.getNickName());
        assertEquals("", senderInfo.getUserAvatar());
        
        AliLiveMessageBody messageBody = request.getBody();
        assertEquals("最小消息", messageBody.getContent());
        assertEquals(LiveMsgSenderType.SYSTEM, messageBody.getSenderType());
        assertNull(messageBody.getExtend());
    }

    @Test
    void testIsValidScript() {
        // 测试有效脚本
        assertTrue(MessageBuilder.isValidScript(script));

        // 测试空脚本
        assertFalse(MessageBuilder.isValidScript(null));

        // 测试空消息内容
        LiveAuiMessageTemplateScript emptyContentScript = new LiveAuiMessageTemplateScript();
        emptyContentScript.setSendTime(LocalTime.of(10, 0, 0));
        assertFalse(MessageBuilder.isValidScript(emptyContentScript));

        // 测试空发送时间
        LiveAuiMessageTemplateScript noTimeScript = new LiveAuiMessageTemplateScript();
        noTimeScript.setMessageContent("测试内容");
        assertFalse(MessageBuilder.isValidScript(noTimeScript));
    }

    @Test
    void testGetSenderTypeDescription() {
        assertEquals("学员", MessageBuilder.getSenderTypeDescription(1L));
        assertEquals("助教", MessageBuilder.getSenderTypeDescription(2L));
        assertEquals("机器人", MessageBuilder.getSenderTypeDescription(3L));
        assertEquals("主播", MessageBuilder.getSenderTypeDescription(4L));
        assertEquals("其他", MessageBuilder.getSenderTypeDescription(5L));
        assertEquals("系统", MessageBuilder.getSenderTypeDescription(99L));
        assertEquals("系统", MessageBuilder.getSenderTypeDescription(null));
    }

    @Test
    void testGetContentTypeDescription() {
        assertEquals("文本", MessageBuilder.getContentTypeDescription(1L));
        assertEquals("图片", MessageBuilder.getContentTypeDescription(2L));
        assertEquals("文本", MessageBuilder.getContentTypeDescription(99L));
        assertEquals("文本", MessageBuilder.getContentTypeDescription(null));
    }

    @Test
    void testGetDisplaySiteDescription() {
        assertEquals("置顶", MessageBuilder.getDisplaySiteDescription(1L));
        assertEquals("弹幕", MessageBuilder.getDisplaySiteDescription(2L));
        assertEquals("底部", MessageBuilder.getDisplaySiteDescription(3L));
        assertEquals("弹窗", MessageBuilder.getDisplaySiteDescription(4L));
        assertEquals("底部", MessageBuilder.getDisplaySiteDescription(99L));
        assertEquals("底部", MessageBuilder.getDisplaySiteDescription(null));
    }

    @Test
    void testSenderTypeConversion() {
        // 测试发送者类型转换
        LiveAuiMessageTemplateScript testScript = new LiveAuiMessageTemplateScript();
        testScript.setId(1L);
        testScript.setMessageContent("测试");
        testScript.setSendTime(LocalTime.of(10, 0, 0));
        
        // 测试学员
        testScript.setSenderType(1L);
        AliSendLiveMessageUserRequest request = MessageBuilder.buildMessageRequest(messageGroupId, testScript);
        assertEquals(LiveMsgSenderType.CUSTOMER, request.getBody().getSenderType());
        
        // 测试助教
        testScript.setSenderType(2L);
        request = MessageBuilder.buildMessageRequest(messageGroupId, testScript);
        assertEquals(LiveMsgSenderType.ASSISTANT, request.getBody().getSenderType());
        
        // 测试机器人
        testScript.setSenderType(3L);
        request = MessageBuilder.buildMessageRequest(messageGroupId, testScript);
        assertEquals(LiveMsgSenderType.ROBOT, request.getBody().getSenderType());
        
        // 测试主播
        testScript.setSenderType(4L);
        request = MessageBuilder.buildMessageRequest(messageGroupId, testScript);
        assertEquals(LiveMsgSenderType.ANCHOR, request.getBody().getSenderType());
        
        // 测试其他
        testScript.setSenderType(5L);
        request = MessageBuilder.buildMessageRequest(messageGroupId, testScript);
        assertEquals(LiveMsgSenderType.SYSTEM, request.getBody().getSenderType());
        
        // 测试null
        testScript.setSenderType(null);
        request = MessageBuilder.buildMessageRequest(messageGroupId, testScript);
        assertEquals(LiveMsgSenderType.SYSTEM, request.getBody().getSenderType());
    }
}
