package com.panda.pollen.aui.service.impl;

import com.panda.pollen.aui.service.IAliCloudBizService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplateScript;
import com.panda.pollen.framework.lock.RedisLock;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ScriptTaskManager 测试类
 */
@ExtendWith(MockitoExtension.class)
class ScriptTaskManagerTest {

    @Mock
    private ScheduledExecutorService scheduledExecutorService;

    @Mock
    private IAliCloudBizService iAliCloudBizService;

    @Mock
    private RedisLock redisLock;

    @Mock
    private ScheduledFuture<?> scheduledFuture;

    @InjectMocks
    private ScriptTaskManager scriptTaskManager;

    private Long roomId;
    private Long messageGroupId;
    private List<LiveAuiMessageTemplateScript> scripts;

    @BeforeEach
    void setUp() {
        roomId = 1001L;
        messageGroupId = 2001L;
        
        // 创建测试脚本
        LiveAuiMessageTemplateScript script1 = new LiveAuiMessageTemplateScript();
        script1.setId(1L);
        script1.setMessageContent("欢迎来到直播间！");
        script1.setSendTime(LocalTime.of(10, 0, 0));
        script1.setSenderName("主播");
        script1.setSenderId("anchor001");
        script1.setSenderType(4L);
        script1.setMessageType(1L);

        LiveAuiMessageTemplateScript script2 = new LiveAuiMessageTemplateScript();
        script2.setId(2L);
        script2.setMessageContent("记得关注我们哦！");
        script2.setSendTime(LocalTime.of(10, 5, 0));
        script2.setSenderName("助教");
        script2.setSenderId("assistant001");
        script2.setSenderType(2L);
        script2.setMessageType(1L);

        scripts = Arrays.asList(script1, script2);
    }

    @Test
    void testStartScriptTasks_Success() {
        // 模拟Redis锁成功获取
        doAnswer(invocation -> {
            Runnable successCallback = invocation.getArgument(4);
            successCallback.run();
            return null;
        }).when(redisLock).tryLock(anyString(), anyLong(), anyLong(), any(TimeUnit.class), any(Runnable.class), any(Runnable.class));

        // 模拟定时任务调度
        when(scheduledExecutorService.schedule(any(Runnable.class), anyLong(), any(TimeUnit.class)))
                .thenReturn(scheduledFuture);

        // 执行测试
        scriptTaskManager.startScriptTasks(roomId, messageGroupId, scripts);

        // 验证
        verify(redisLock).tryLock(eq("script_task_" + roomId), anyLong(), anyLong(), 
                eq(TimeUnit.MILLISECONDS), any(Runnable.class), any(Runnable.class));
        verify(scheduledExecutorService, times(2)).schedule(any(Runnable.class), anyLong(), eq(TimeUnit.MILLISECONDS));
    }

    @Test
    void testStartScriptTasks_EmptyScripts() {
        // 执行测试
        scriptTaskManager.startScriptTasks(roomId, messageGroupId, Arrays.asList());

        // 验证不会调用Redis锁
        verify(redisLock, never()).tryLock(anyString(), anyLong(), anyLong(), 
                any(TimeUnit.class), any(Runnable.class), any(Runnable.class));
    }

    @Test
    void testStartScriptTasks_LockFailed() {
        // 模拟Redis锁获取失败
        doAnswer(invocation -> {
            Runnable failCallback = invocation.getArgument(5);
            failCallback.run();
            return null;
        }).when(redisLock).tryLock(anyString(), anyLong(), anyLong(), any(TimeUnit.class), any(Runnable.class), any(Runnable.class));

        // 执行测试
        scriptTaskManager.startScriptTasks(roomId, messageGroupId, scripts);

        // 验证不会调度任务
        verify(scheduledExecutorService, never()).schedule(any(Runnable.class), anyLong(), any(TimeUnit.class));
    }

    @Test
    void testStopScriptTasks() {
        // 先启动任务
        doAnswer(invocation -> {
            Runnable successCallback = invocation.getArgument(4);
            successCallback.run();
            return null;
        }).when(redisLock).tryLock(anyString(), anyLong(), anyLong(), any(TimeUnit.class), any(Runnable.class), any(Runnable.class));

        when(scheduledExecutorService.schedule(any(Runnable.class), anyLong(), any(TimeUnit.class)))
                .thenReturn(scheduledFuture);
        when(scheduledFuture.cancel(false)).thenReturn(true);

        scriptTaskManager.startScriptTasks(roomId, messageGroupId, scripts);

        // 执行停止测试
        scriptTaskManager.stopScriptTasks(roomId);

        // 验证任务被取消
        verify(scheduledFuture, times(2)).cancel(false);
    }

    @Test
    void testGetTaskStatus() {
        // 先启动任务
        doAnswer(invocation -> {
            Runnable successCallback = invocation.getArgument(4);
            successCallback.run();
            return null;
        }).when(redisLock).tryLock(anyString(), anyLong(), anyLong(), any(TimeUnit.class), any(Runnable.class), any(Runnable.class));

        when(scheduledExecutorService.schedule(any(Runnable.class), anyLong(), any(TimeUnit.class)))
                .thenReturn(scheduledFuture);
        when(scheduledFuture.isDone()).thenReturn(false);
        when(scheduledFuture.isCancelled()).thenReturn(false);

        scriptTaskManager.startScriptTasks(roomId, messageGroupId, scripts);

        // 获取任务状态
        ScriptTaskManager.TaskStatus status = scriptTaskManager.getTaskStatus(roomId);

        // 验证状态
        assertNotNull(status);
        assertEquals(roomId, status.roomId());
        assertEquals(2, status.totalTasks());
        assertEquals(0, status.completedTasks());
        assertEquals(0, status.cancelledTasks());
        assertEquals(2, status.getRunningTasks());
    }

    @Test
    void testGetTaskStatus_NoTasks() {
        // 获取不存在的任务状态
        ScriptTaskManager.TaskStatus status = scriptTaskManager.getTaskStatus(roomId);

        // 验证状态
        assertNotNull(status);
        assertEquals(roomId, status.roomId());
        assertEquals(0, status.totalTasks());
        assertEquals(0, status.completedTasks());
        assertEquals(0, status.cancelledTasks());
        assertEquals(0, status.getRunningTasks());
    }

    @Test
    void testTaskStatusToString() {
        ScriptTaskManager.TaskStatus status = new ScriptTaskManager.TaskStatus(1001L, 5, 2, 1);
        String expected = "TaskStatus{roomId=1001, total=5, completed=2, cancelled=1, running=2}";
        assertEquals(expected, status.toString());
    }
}
