# 直播脚本定时任务功能

## 功能概述

这个功能实现了在直播过程中根据预设脚本的时间自动发送消息到阿里云消息组的能力。支持分布式部署，确保在多实例环境下任务不会重复执行。

## 核心特性

- ✅ **分布式支持**: 使用Redis分布式锁确保任务不重复执行
- ✅ **精确定时**: 基于脚本设定的发送时间精确调度消息
- ✅ **任务管理**: 支持任务的启动、停止和状态查询
- ✅ **消息构建**: 自动根据脚本信息构建完整的阿里云消息请求
- ✅ **异常处理**: 完善的异常处理和日志记录
- ✅ **测试覆盖**: 完整的单元测试和集成测试

## 架构设计

### 核心组件

1. **ScriptTaskManager**: 脚本任务管理器，负责任务调度和执行
2. **MessageBuilder**: 消息构建工具类，负责构建阿里云消息请求
3. **LiveScriptInterpretationBizServiceImpl**: 业务服务实现类
4. **LiveScriptTaskController**: REST API控制器

### 分布式锁机制

使用Redis分布式锁确保在多实例环境下：
- 同一直播间的脚本任务只在一个实例上执行
- 单个脚本消息只发送一次
- 锁的粒度：`script_task_{roomId}` 和 `script_task_{roomId}_{scriptId}`

## 使用方法

### 1. 启动脚本任务

```java
@Autowired
private ILiveScriptInterpretationBizService liveScriptInterpretationBizService;

// 启动直播间脚本任务
liveScriptInterpretationBizService.play(roomId);
```

### 2. 停止脚本任务

```java
// 停止直播间脚本任务
liveScriptInterpretationBizService.stop(roomId);
```

### 3. 查询任务状态

```java
// 获取任务状态
ScriptTaskManager.TaskStatus status = liveScriptInterpretationBizService.getTaskStatus(roomId);
System.out.println("总任务数: " + status.getTotalTasks());
System.out.println("已完成: " + status.getCompletedTasks());
System.out.println("已取消: " + status.getCancelledTasks());
System.out.println("运行中: " + status.getRunningTasks());
```

### 4. REST API 调用

```bash
# 启动脚本任务
POST /aui/script/task/start/{roomId}

# 停止脚本任务
POST /aui/script/task/stop/{roomId}

# 查询任务状态
GET /aui/script/task/status/{roomId}
```

## 配置要求

### 1. 数据库表结构

确保以下表存在并包含必要字段：

- `live_aui_room_info`: 直播间基本信息
- `live_aui_room_info_ext`: 直播间扩展信息（包含messageGroupId和liveTemplateId）
- `live_aui_message_template`: 消息模板
- `live_aui_message_template_script`: 脚本信息（包含sendTime字段）

### 2. Redis配置

确保Redis可用，用于分布式锁：

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
```

### 3. 线程池配置

系统会自动创建专用的定时任务执行器，也可以使用现有的：

```java
@Bean("scheduledExecutorService")
public ScheduledExecutorService scheduledExecutorService() {
    return Executors.newScheduledThreadPool(10);
}
```

## 脚本数据格式

脚本表(`live_aui_message_template_script`)需要包含以下关键字段：

```sql
CREATE TABLE live_aui_message_template_script (
    id BIGINT PRIMARY KEY,
    template_id BIGINT NOT NULL,
    message_content VARCHAR(512) NOT NULL,
    send_time TIME NOT NULL,  -- 发送时间，格式：HH:mm:ss
    sender_name VARCHAR(100),
    sender_id VARCHAR(64),
    sender_head_img VARCHAR(255),
    sender_type BIGINT,  -- 1:学员 2:助教 3:机器人 4:主播 5:其他
    message_type BIGINT,
    message_content_type BIGINT,  -- 1:文本 2:图片
    display_site BIGINT,  -- 显示位置
    open_way BIGINT,  -- 打开方式
    link_url VARCHAR(255),
    image_url VARCHAR(255),
    replay_message_id VARCHAR(64),
    associate_id VARCHAR(64)
);
```

## 工作流程

1. **调用play方法**: 传入直播间ID
2. **数据验证**: 验证直播间、扩展信息、模板等数据完整性
3. **脚本加载**: 从数据库加载该模板下的所有脚本，按发送时间排序
4. **任务调度**: 为每个有效脚本创建定时任务
5. **分布式锁**: 使用Redis锁确保任务不重复执行
6. **消息发送**: 到达指定时间时，构建消息并发送到阿里云

## 监控和日志

### 日志级别

- **INFO**: 任务启动、停止、执行成功等关键操作
- **WARN**: 数据缺失、锁获取失败等警告
- **ERROR**: 任务执行失败、异常情况
- **DEBUG**: 详细的任务调度信息

### 关键日志示例

```
INFO  - 开始为直播间 1001 调度脚本任务，共 5 个脚本
INFO  - 成功调度 5 个脚本任务，roomId: 1001
INFO  - 执行脚本任务，roomId: 1001, scriptId: 5001, 内容: 欢迎来到直播间！
INFO  - 脚本消息发送成功，roomId: 1001, scriptId: 5001
```

## 性能考虑

- **内存使用**: 每个活跃直播间会在内存中保存任务引用
- **线程池**: 使用共享的定时任务线程池，避免创建过多线程
- **Redis连接**: 分布式锁使用短连接，避免长时间占用连接
- **任务清理**: 任务完成或取消后自动清理相关资源

## 故障处理

### 常见问题

1. **任务不执行**: 检查Redis连接和分布式锁配置
2. **消息发送失败**: 检查阿里云服务配置和网络连接
3. **重复发送**: 检查分布式锁是否正常工作
4. **内存泄漏**: 确保及时调用stop方法清理任务

### 恢复机制

- 应用重启后，之前的定时任务会自动清理
- 需要重新调用play方法启动新的任务
- 建议在直播开始时自动调用play方法

## 扩展性

### 自定义消息构建

可以扩展MessageBuilder类来支持更多消息类型：

```java
public class CustomMessageBuilder extends MessageBuilder {
    public static AliSendLiveMessageUserRequest buildCustomMessage(...) {
        // 自定义消息构建逻辑
    }
}
```

### 自定义任务调度

可以实现自定义的任务调度策略：

```java
@Component
public class CustomScriptTaskManager extends ScriptTaskManager {
    // 自定义调度逻辑
}
```

## 测试

运行测试用例：

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=ScriptTaskManagerTest
mvn test -Dtest=MessageBuilderTest
mvn test -Dtest=LiveScriptInterpretationBizServiceImplTest
```

## 版本历史

- **v1.0.0**: 初始版本，支持基本的脚本定时发送功能
- 支持分布式部署
- 完整的任务管理功能
- 全面的测试覆盖
