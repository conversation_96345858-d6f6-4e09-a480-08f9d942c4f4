package com.panda.pollen.aui.api;

import com.panda.pollen.aui.system.domain.LiveAuiMessageType;

import java.util.List;

/**
 * @Author: yh
 * @Date: 2025/8/23 13:50
 * @Description: 消息类型对外接口
 */

public interface LiveAuiMessageTypeApi {
    /**
     * 获取消息类型树形结构数据
     * @return List<LiveAuiMessageType>
     */
    List<LiveAuiMessageType> getTree();

    /**
     * 获取所有末端消息类型
     * @return
     */
    List<LiveAuiMessageType> listLeaf();
}
