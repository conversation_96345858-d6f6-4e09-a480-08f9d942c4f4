package com.panda.pollen.aui.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.panda.pollen.aui.model.dto.*;
import com.panda.pollen.aui.model.vo.*;
import com.panda.pollen.aui.service.ILiveAuiLiveBizService;
import com.panda.pollen.aui.sys.service.SystemDeptUserService;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfo;
import com.panda.pollen.aui.utils.NetFileUtils;
import com.panda.pollen.common.core.domain.AjaxPageResult;
import com.panda.pollen.common.core.domain.AjaxResultV2;
import com.panda.pollen.common.core.domain.model.LoginUser;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.exception.base.BaseException;
import com.panda.pollen.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 直播间管理
 *
 * <AUTHOR>
 */
@Api(tags = "直播课程-直播间管理(后台管理)")
@Slf4j
@RestController
@RequestMapping("/liveAuiLiveBiz")
public class LiveAuiLiveBizController {
    @Resource
    private ILiveAuiLiveBizService iLiveAuiLiveBizService;
    @Resource
    private SystemDeptUserService systemDeptUserService;


    @ApiOperation("后台管理创建直播间")
    @PostMapping(path = "/createAdminLiveRoom")
    public AjaxResultV2<Boolean> create(@Valid @RequestBody LiveRoomCreateDto dto) {
        Boolean result = this.iLiveAuiLiveBizService.create(dto);
        return AjaxResultV2.success(result);
    }

    @ApiOperation("课程分组下拉列表")
    @GetMapping("/getDropdownList")
    public AjaxResultV2<List<CourseCategoryDropdownVo>> getDropdownList() {
        List<CourseCategoryDropdownVo> vos = this.iLiveAuiLiveBizService.getDropdownList();
        return AjaxResultV2.success(vos);
    }

    @ApiOperation("分页查询直播间列表")
    @PostMapping(path = "/pageQueryLiveRooms")
    public AjaxPageResult<LiveRoomPageVo> page(@Valid @RequestBody LiveRoomPageQueryDto dto) {
        List<LiveRoomPageVo> page = this.iLiveAuiLiveBizService.page(dto);
        return AjaxPageResult.build(page);
    }

    @ApiOperation("根据直播间id查询详情（用于编辑/回显/复制）")
    @GetMapping("/getAdminLiveRoomDetail")
    public AjaxResultV2<LiveRoomDetailVo> getAdminLiveRoomDetail(@RequestParam String roomId) {
        return AjaxResultV2.success(this.iLiveAuiLiveBizService.getAdminLiveRoomDetail(roomId));
    }

    @ApiOperation("编辑直播间")
    @PostMapping("/editAdminLiveRoom")
    public AjaxResultV2<Void> update(@Valid @RequestBody LiveRoomUpdateDto editDto) {
        boolean result = this.iLiveAuiLiveBizService.update(editDto);
        return result ? AjaxResultV2.success() : AjaxResultV2.error("编辑直播间失败");
    }

    @ApiOperation("验证当前登录账号是否是讲师账号")
    @GetMapping(path = "/verifyStreamer")
    public AjaxResultV2<Boolean> verifyStreamer(@RequestParam String userId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        log.info("当前登录者信息-loginUser:{}", loginUser);
        if (ObjectUtils.isEmpty(loginUser)) {
            return AjaxResultV2.error("请登录");
        }
        //先判断是否是管理员账号
        String username = loginUser.getUsername();
        if (CharSequenceUtil.equals(username, "admin")) {
            return AjaxResultV2.success(true);
        } else {
            return AjaxResultV2.success(Objects.equals(userId, loginUser.getUser().getUserId().toString()));
        }
    }

    @ApiOperation("查询可用的直播模板")
    @PostMapping(path = "/retrieveLiveTemplateList")
    public AjaxResultV2<List<LiveTemplateVo>> retrieveLiveTemplateList() {
        return AjaxResultV2.success(this.iLiveAuiLiveBizService.retrieveLiveTemplateList());
    }

    @ApiOperation("直播间消息下载")
    @GetMapping(path = "/downloadLiveRoomMsg")
    public void downloadLiveRoomMsg(@RequestParam String roomId, HttpServletResponse response) {
        try {
            LiveAuiRoomInfo liveAuiRoomInfo = this.iLiveAuiLiveBizService.retrieveLiveRoomMsg(roomId);
            if (ObjectUtils.isEmpty(liveAuiRoomInfo)) {
                throw new ServiceException("直播还未结束");
            }
            String fileName = CharSequenceUtil.format("{}直播间消息.xlsx", liveAuiRoomInfo.getTitle());
            List<LiveRoomMsgVo> exportList = this.iLiveAuiLiveBizService.downloadLiveRoomMsg(roomId);
            if (CollUtil.isEmpty(exportList)) {
                throw new ServiceException("当前直播间暂无消息");
            }
            ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream(), LiveRoomMsgVo.class).build();
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(fileName).build();
            excelWriter.write(exportList, writeSheet);
            NetFileUtils.downloadEasyExcel(response, fileName, excelWriter);
        } catch (Exception e) {
            log.error("直播间消息下载异常！{}", e.getMessage(), e);
            throw new BaseException("导出异常！");
        }
    }

    @ApiOperation("删除直播间")
    @GetMapping(path = "/deleteLiveRoomById")
    public AjaxResultV2<Boolean> delete(@RequestParam String roomId) {
        Boolean deleted = this.iLiveAuiLiveBizService.delete(roomId);
        return AjaxResultV2.success(deleted);
    }

    @ApiOperation("助教/讲师树形列表")
    @PostMapping(path = "/retrieveUserTreeVoList")
    public AjaxResultV2<List<DeptUserTreeVO>> retrieveUserTreeVoList(@RequestBody UserTreeDto userTreeDto) {
        return AjaxResultV2.success(this.systemDeptUserService.retrieveUserTreeVoList(userTreeDto));
    }

    //根据视频id 获取所有历史直播间
    @ApiOperation("根据视频id 获取所有历史直播间")
    @GetMapping(path = "/retrieveHisLiveRoomList")
    public AjaxResultV2<List<LiveRoomMessageVo>> retrieveHisLiveRoomList(@RequestParam String videoId) {
        return AjaxResultV2.success(this.iLiveAuiLiveBizService.retrieveHisLiveRoomList(videoId));
    }

    @ApiOperation("上/下架")
    @GetMapping(path = "/offShelf")
    public AjaxResultV2<Boolean> offShelf(@RequestParam String liveId) {
        return AjaxResultV2.success(this.iLiveAuiLiveBizService.offShelf(liveId));
    }

    @GetMapping("/corpList")
    @ApiOperation("查询企业微信下拉列表")
    public AjaxResultV2<List<CorpVo>> corpList() {
        return AjaxResultV2.success(this.iLiveAuiLiveBizService.corpList());
    }

    @GetMapping("/domainList")
    @ApiOperation("域名下拉列表")
    public AjaxResultV2<List<DomainVo>> domainList() {
        return AjaxResultV2.success(this.iLiveAuiLiveBizService.domainList());
    }

    /**
     * 生成分享链接
     */
    @PostMapping("/generateShareLink")
    @ApiOperation("生成分享链接")
    public AjaxResultV2<ShareLinkVo> generateShareLink(@Valid @RequestBody ShareLinkDto dto) {
        ShareLinkVo shareLinkVo = this.iLiveAuiLiveBizService.generateShareLink(dto);
        return AjaxResultV2.success(shareLinkVo);
    }

    /**
     * 获取分享设置
     */
//    @GetMapping("/getShareSetting")
//    @ApiOperation("获取分享设置")
    public AjaxResultV2<ShareSettingDto> getShareSetting(@RequestParam String roomId) {
        ShareSettingDto shareSettingDto = this.iLiveAuiLiveBizService.getShareSetting(roomId);
        return AjaxResultV2.success(shareSettingDto);
    }
}