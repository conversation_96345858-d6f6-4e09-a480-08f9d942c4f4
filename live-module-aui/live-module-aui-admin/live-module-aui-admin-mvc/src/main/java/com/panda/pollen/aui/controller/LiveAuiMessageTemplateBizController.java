package com.panda.pollen.aui.controller;

import cn.hutool.core.util.IdUtil;
import com.panda.pollen.aui.model.dto.LiveAuiMessageTemplateRequestDto;
import com.panda.pollen.aui.model.vo.LiveAuiMessageTemplateVo;
import com.panda.pollen.aui.service.ILiveAuiMessageTemplateBizService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageTemplate;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResultV2;
import com.panda.pollen.common.core.page.TableDataInfo;
import com.panda.pollen.common.exception.ServiceException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.security.SecureRandom;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yh
 * @Date : 2025/7/8 21:51
 * @Description :
 */
@Api(tags = "直播消息模板")
@RestController
@RequestMapping("/liveAuiMessageTemplate")
public class LiveAuiMessageTemplateBizController extends BaseController {

    @Resource
    private ILiveAuiMessageTemplateBizService liveAuiMessageTemplateBizService;

    @ApiOperation("创建消息模板")
    @PostMapping(value = "/save")
    @PreAuthorize("@ss.hasPermi('live:liveAuiMessageTemplate:save')")
    public AjaxResultV2<Long> save(@Validated @RequestBody LiveAuiMessageTemplateRequestDto liveAuiMessageTemplate) {
        String random = String.valueOf(new SecureRandom().nextInt(90000000));
        liveAuiMessageTemplate.setTemplateName(liveAuiMessageTemplate.getTemplateName().concat(random));
        liveAuiMessageTemplate.setDeptId(getLoginUser().getDeptId());
        liveAuiMessageTemplate.setUserId(getLoginUser().getUserId());

        Long id = liveAuiMessageTemplateBizService.mySave(liveAuiMessageTemplate);
        return AjaxResultV2.success(id);
    }

    @ApiOperation("复制消息模板")
    @PostMapping(value = "/copy")
    public AjaxResultV2<Long> copy(@RequestBody @Validated LiveAuiMessageTemplateRequestDto requestDto) {
        if(null == requestDto.getIdStr() || requestDto.getIdStr().isEmpty()){
            throw new ServiceException("请求参数不正确！");
        }
        if(null == requestDto.getTemplateName() || requestDto.getTemplateName().isEmpty()){
            throw new ServiceException("模板名称不能为空！");
        }

        Long recordId = liveAuiMessageTemplateBizService.copy(Long.valueOf(requestDto.getIdStr()), requestDto.getTemplateName());
        return AjaxResultV2.success(recordId);
    }


    @ApiOperation("根据ID获取消息模板")
    @GetMapping(value = "/getById")
    public AjaxResultV2<LiveAuiMessageTemplateVo> getById(@RequestParam(value = "id") String id) {
        LiveAuiMessageTemplateVo liveAuiMessageTemplate = liveAuiMessageTemplateBizService.getById(Long.valueOf(id));
        return AjaxResultV2.success(liveAuiMessageTemplate);
    }

    @ApiOperation("修改消息模板")
    @PostMapping(value = "/update")
    @PreAuthorize("@ss.hasPermi('live:liveAuiMessageTemplate:update')")
    public AjaxResultV2<LiveAuiMessageTemplate> update(@Validated @RequestBody LiveAuiMessageTemplateRequestDto liveAuiMessageTemplate) {
        LiveAuiMessageTemplate updateResult = liveAuiMessageTemplateBizService.myUpdateById(liveAuiMessageTemplate);
        return AjaxResultV2.success(updateResult);
    }

    @ApiOperation("开启或者关闭关闭消息模板")
    @PostMapping(value = "/closeOrOpen")
    public AjaxResultV2<String> closeOrOpen(@RequestBody LiveAuiMessageTemplateRequestDto requestDto) {
        if(null == requestDto.getIdStr() || requestDto.getIdStr().isEmpty() || null == requestDto.getOpenStatus()){
            throw new ServiceException("请求参数不正确！");
        }
        boolean updateResult = liveAuiMessageTemplateBizService.closeById(Long.valueOf(requestDto.getIdStr()), requestDto.getOpenStatus());
        return updateResult ? AjaxResultV2.success() : AjaxResultV2.error("修改失败！请联系管理员");
    }

    @ApiOperation("删除消息模板")
    @PreAuthorize("@ss.hasPermi('live:liveAuiMessageTemplate:remove')")
    @DeleteMapping(value = "/mutiRemove")
    public AjaxResultV2<String> mutiRemove(@RequestBody List<String> ids) {
        List<Long> idList = ids.stream().map(Long::parseLong).collect(Collectors.toList());
        boolean removeResult = liveAuiMessageTemplateBizService.removeByIds(idList);
        return removeResult ? AjaxResultV2.success() : AjaxResultV2.error("删除失败 请联系管理员！");
    }

    @ApiOperation("分页查询消息模板")
    @GetMapping("/list")
    public AjaxResultV2<TableDataInfo<LiveAuiMessageTemplateVo>> list(LiveAuiMessageTemplateRequestDto liveAuiMessageTemplate) {
        startPage();
        liveAuiMessageTemplate.setDeleted(0);
        List<LiveAuiMessageTemplateVo> list = liveAuiMessageTemplateBizService.list(liveAuiMessageTemplate);
        return AjaxResultV2.success(getDataTable(list));
    }

    @ApiOperation("分页查询已删除消息模板")
    @GetMapping("/listDeleted")
    public AjaxResultV2<TableDataInfo<LiveAuiMessageTemplateVo>> listDeleted(LiveAuiMessageTemplateRequestDto liveAuiMessageTemplate) {
        startPage();
        liveAuiMessageTemplate.setDeleted(1);
        List<LiveAuiMessageTemplateVo> list = liveAuiMessageTemplateBizService.list(liveAuiMessageTemplate);
        return AjaxResultV2.success(getDataTable(list));
    }


}
