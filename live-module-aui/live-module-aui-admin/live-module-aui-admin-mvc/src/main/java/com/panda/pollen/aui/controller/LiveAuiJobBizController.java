package com.panda.pollen.aui.controller;

import com.panda.pollen.aui.script.ScriptTaskManager;
import com.panda.pollen.aui.service.ILiveAuiJobBizService;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 直播脚本任务管理控制器
 *
 * <AUTHOR>
 */
@Api(tags = "直播脚本任务管理")
@Slf4j
@RestController
@RequestMapping("/liveAuiJobBiz")
public class LiveAuiJobBizController extends BaseController {

    @Resource
    private ILiveAuiJobBizService iLiveScriptTaskBizService;

    @ApiOperation("启动脚本任务[当前时间之后多少分钟后的所有直播间]")
    @PostMapping("/play/{delay}")
    public AjaxResult play(@ApiParam(value = "延迟时间") @PathVariable("delay") Integer delay) {
        try {
            iLiveScriptTaskBizService.play(delay);
            return success("脚本任务启动成功");
        } catch (Exception e) {
            log.error("启动脚本任务失败，delay: {}", delay, e);
            return error("启动脚本任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("启动脚本任务[某个直播间]")
    @PostMapping("/start/{roomId}")
    public AjaxResult start(@ApiParam(value = "直播间ID", required = true) @PathVariable("roomId") Long roomId) {
        try {
            iLiveScriptTaskBizService.start(roomId);
            return success("脚本任务启动成功");
        } catch (Exception e) {
            log.error("启动脚本任务失败，roomId: {}", roomId, e);
            return error("启动脚本任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("停止脚本任务[某个直播间]")
    @PostMapping("/stop/{roomId}")
    public AjaxResult stopScriptTask(@ApiParam(value = "直播间ID", required = true) @PathVariable("roomId") Long roomId) {
        try {
            iLiveScriptTaskBizService.stop(roomId);
            return success("脚本任务停止成功");
        } catch (Exception e) {
            log.error("停止脚本任务失败，roomId: {}", roomId, e);
            return error("停止脚本任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取脚本任务状态[某个直播间]")
    @GetMapping("/status/{roomId}")
    public AjaxResult getTaskStatus(@ApiParam(value = "直播间ID", required = true) @PathVariable("roomId") Long roomId) {
        try {
            ScriptTaskManager.TaskStatus status = iLiveScriptTaskBizService.getTaskStatus(roomId);
            return success(status);
        } catch (Exception e) {
            log.error("获取脚本任务状态失败，roomId: {}", roomId, e);
            return error("获取脚本任务状态失败：" + e.getMessage());
        }
    }
}
