package com.panda.pollen.aui.controller;

import cn.hutool.core.util.NumberUtil;
import com.panda.pollen.aui.anotation.NoRepeatSubmit;
import com.panda.pollen.aui.model.dto.LiveRobotCreateDto;
import com.panda.pollen.aui.model.dto.LiveRobotEditDto;
import com.panda.pollen.aui.model.dto.LiveRobotQueryDto;
import com.panda.pollen.aui.service.ILiveAuiAdminRobotBizService;
import com.panda.pollen.aui.system.domain.LiveAuiRobotPool;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxPageResult;
import com.panda.pollen.common.core.domain.AjaxResultV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 机器人池维护
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/liveAdminRootBiz")
@RequiredArgsConstructor
@Api(tags = "用户库")
public class LiveAuiAdminRobotBizController extends BaseController {
    @Resource
    private ILiveAuiAdminRobotBizService iLiveAuiAdminRobotBizService;

    @PostMapping("/uploadFileSave")
    @ApiOperation(value = "机器人信息导入", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResultV2<Boolean> uploadFileSave(@RequestPart("file") MultipartFile file) {
        try {
            return AjaxResultV2.success(this.iLiveAuiAdminRobotBizService.uploadFileSave(file));
        } catch (Throwable e) {
            log.error("机器人信息导入异常", e);
            return AjaxResultV2.error("导入失败: " + e.getMessage());
        }
    }

    @PostMapping("/addSingleRobot")
    @ApiOperation(value = "新增单个机器人")
    public AjaxResultV2<Boolean> addSingleRobot(@RequestBody @Valid LiveRobotCreateDto liveRobotCreateDto) {
        return AjaxResultV2.success(this.iLiveAuiAdminRobotBizService.addSingleRobot(liveRobotCreateDto));
    }

    @GetMapping("/generateRandomRobots")
    @ApiOperation(value = "随机生成机器人", notes = "根据输入数量生成指定数量的机器人")
    @NoRepeatSubmit(lockTime = 5, key = "generateRandomRobots")
    public AjaxResultV2<Boolean> generateRandomRobots(@RequestParam("count") String count) {
        if (!NumberUtil.isInteger(count)) {
            throw new IllegalArgumentException("数量必须是正整数");
        }
        int countNum = Integer.parseInt(count);
        if (countNum <= 0 || countNum > 1000) {
            throw new IllegalArgumentException("数量正确范围在1-1000之间");
        }
        Boolean result = this.iLiveAuiAdminRobotBizService.generateRandomRobots(countNum);
        return AjaxResultV2.success(result);
    }

    @PostMapping("/retrieveRobotListPage")
    @ApiOperation("机器人列表查询")
    public AjaxPageResult<LiveAuiRobotPool> retrieveRobotListPage(@RequestBody LiveRobotQueryDto dto) {
        List<LiveAuiRobotPool> list = this.iLiveAuiAdminRobotBizService.retrieveRobotListPage(dto);
        return AjaxPageResult.build(list);
    }

    @PostMapping("/editRobot")
    @ApiOperation(value = "编辑机器人")
    public AjaxResultV2<Boolean> editRobot(@RequestBody LiveRobotEditDto liveRobotEditDto) {
        return AjaxResultV2.success(this.iLiveAuiAdminRobotBizService.editRobot(liveRobotEditDto));
    }

    @GetMapping("/deleteRobot")
    @ApiOperation(value = "删除机器人")
    public AjaxResultV2<Boolean> deleteRobot(@RequestParam String robotId) {
        return AjaxResultV2.success(this.iLiveAuiAdminRobotBizService.deleteRobot(robotId));
    }

}
