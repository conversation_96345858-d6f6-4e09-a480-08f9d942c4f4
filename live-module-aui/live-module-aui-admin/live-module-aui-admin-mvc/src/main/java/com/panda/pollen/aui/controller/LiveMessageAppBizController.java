package com.panda.pollen.aui.controller;

import com.panda.pollen.aui.service.ILiveMessageAppBizService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageApp;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResultV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Author: yh
 * @Date: 2025/7/30 22:08
 * @Description: 直播互动消息应用
 */
@Slf4j
@Api(tags = "直播互动消息应用")
@RestController
@RequestMapping({"/liveMessageApp"})
public class LiveMessageAppBizController extends BaseController {

    @Resource
    private ILiveMessageAppBizService liveMessageAppService;

    @ApiOperation("创建互动消息应用")
    @PostMapping(value = "/save")
    public AjaxResultV2<LiveAuiMessageApp> save(@Validated @RequestBody LiveAuiMessageApp liveMessageAppDo) {
        LiveAuiMessageApp saveResult = liveMessageAppService.createLiveMessageApp(liveMessageAppDo);
        return AjaxResultV2.success(saveResult);
    }

    @ApiOperation("根据ID获取互动消息应用")
    @GetMapping(value = "/getById")
    public AjaxResultV2<LiveAuiMessageApp> getById() {
        LiveAuiMessageApp liveAuiMessageTemplate = liveMessageAppService.myGet();
        return AjaxResultV2.success(liveAuiMessageTemplate);
    }

    @ApiOperation("修改互动消息应用")
    @PostMapping(value = "/update")
    public AjaxResultV2<LiveAuiMessageApp> update(@Validated @RequestBody LiveAuiMessageApp liveMessageAppDo) {
        LiveAuiMessageApp updateResult = liveMessageAppService.myUpdateById(liveMessageAppDo);
        return AjaxResultV2.success(updateResult);
    }
}
