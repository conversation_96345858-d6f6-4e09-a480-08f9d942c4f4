package com.panda.pollen.aui.controller;

import com.panda.pollen.aui.service.ILiveScriptInterpretationBizService;
import com.panda.pollen.aui.service.impl.ScriptTaskManager;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 直播脚本任务管理控制器
 * 
 * <AUTHOR>
 */
@Api(tags = "直播脚本任务管理")
@Slf4j
@RestController
@RequestMapping("/aui/script/task")
public class LiveScriptTaskController extends BaseController {

    @Resource
    private ILiveScriptInterpretationBizService iLiveScriptInterpretationBizService;

    /**
     * 启动脚本任务
     */
    @ApiOperation("启动脚本任务")
    @PostMapping("/start/{roomId}")
    public AjaxResult startScriptTask(
            @ApiParam(value = "直播间ID", required = true) @PathVariable Long roomId) {
        try {
            iLiveScriptInterpretationBizService.play(roomId);
            return success("脚本任务启动成功");
        } catch (Exception e) {
            log.error("启动脚本任务失败，roomId: {}", roomId, e);
            return error("启动脚本任务失败：" + e.getMessage());
        }
    }

    /**
     * 停止脚本任务
     */
    @ApiOperation("停止脚本任务")
    @PostMapping("/stop/{roomId}")
    public AjaxResult stopScriptTask(
            @ApiParam(value = "直播间ID", required = true) @PathVariable Long roomId) {
        try {
            iLiveScriptInterpretationBizService.stop(roomId);
            return success("脚本任务停止成功");
        } catch (Exception e) {
            log.error("停止脚本任务失败，roomId: {}", roomId, e);
            return error("停止脚本任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取脚本任务状态
     */
    @ApiOperation("获取脚本任务状态")
    @GetMapping("/status/{roomId}")
    public AjaxResult getTaskStatus(
            @ApiParam(value = "直播间ID", required = true) @PathVariable Long roomId) {
        try {
            ScriptTaskManager.TaskStatus status = iLiveScriptInterpretationBizService.getTaskStatus(roomId);
            return success(status);
        } catch (Exception e) {
            log.error("获取脚本任务状态失败，roomId: {}", roomId, e);
            return error("获取脚本任务状态失败：" + e.getMessage());
        }
    }
}
