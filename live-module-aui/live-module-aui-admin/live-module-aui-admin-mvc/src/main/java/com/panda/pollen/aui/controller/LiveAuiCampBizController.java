package com.panda.pollen.aui.controller;

import com.panda.pollen.aui.model.dto.*;
import com.panda.pollen.aui.model.vo.LiveAuiCampPeriodStudentPageVo;
import com.panda.pollen.aui.model.vo.LiveAuiCampVo;
import com.panda.pollen.aui.model.vo.LiveAuiCourseVo;
import com.panda.pollen.aui.model.vo.LiveCampStudentStaticsVo;
import com.panda.pollen.aui.service.ILiveAuiCampBizService;
import com.panda.pollen.aui.service.ILiveAuiCourseBizService;
import com.panda.pollen.aui.system.domain.LiveAuiCamp;
import com.panda.pollen.common.annotation.Log;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResultV2;
import com.panda.pollen.common.core.domain.entity.SysDictData;
import com.panda.pollen.common.core.page.TableDataInfo;
import com.panda.pollen.common.enums.BusinessType;
import com.panda.pollen.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: yh
 * @Date: 2025/8/29 20:43
 * @Description:
 */
@Api(tags = "训练营")
@RestController
@RequestMapping("/liveAuiCamp")
public class LiveAuiCampBizController extends BaseController {

    @Resource
    private ILiveAuiCampBizService liveAuiCampBizService;

    @ApiOperation("新建训练营")
    @PostMapping(value = "/save")
    public AjaxResultV2<Long> save( @RequestBody LiveAuiCamp liveAuiCamp) {
        Long id = liveAuiCampBizService.mySave(liveAuiCamp);
        return AjaxResultV2.success(id);
    }

    @ApiOperation("根据ID获取训练营详情")
    @RequestMapping(value = "/getById", method = RequestMethod.GET)
    public AjaxResultV2<LiveAuiCamp> getById(@RequestParam(value = "id") String id) {
        LiveAuiCamp liveAuiCamp = liveAuiCampBizService.getById(Long.valueOf(id));
        return AjaxResultV2.success(liveAuiCamp);
    }

    @ApiOperation("修改训练营")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public AjaxResultV2<LiveAuiCamp> update(@RequestBody LiveAuiCampDto liveAuiCampDto) {
        LiveAuiCamp updateResult = liveAuiCampBizService.myUpdate(liveAuiCampDto);
        return AjaxResultV2.success(updateResult);
    }

    @ApiOperation("删除训练营")
    @DeleteMapping(value = "/mutiRemove")
    public AjaxResultV2<String> mutiRemove(@RequestBody List<String> ids) {
        List<Long> idList = ids.stream().map(Long::parseLong).collect(Collectors.toList());
        boolean removeResult = liveAuiCampBizService.myDelete(idList);
        return removeResult ? AjaxResultV2.success("删除成功！") : AjaxResultV2.error("删除失败 请联系管理员！");
    }


    @GetMapping("/page")
    @ApiOperation("分页查询 ")
    public AjaxResultV2<TableDataInfo<LiveAuiCampVo>> page(LiveAuiCampDto liveAuiCampDto) {
        startPage();
        List<LiveAuiCampVo> list = liveAuiCampBizService.list(liveAuiCampDto);
        return AjaxResultV2.success(getDataTable(list));
    }

    @GetMapping("/list")
    @ApiOperation("根据查询条件获取训练营列表 ")
    public AjaxResultV2<List<LiveAuiCampVo>> list(LiveAuiCampDto liveAuiCampDto) {
        List<LiveAuiCampVo> list = liveAuiCampBizService.list(liveAuiCampDto);
        return AjaxResultV2.success(list);
    }

    @GetMapping("/getCampStudentStatics")
    @ApiOperation("获取训练营学员统计数据 ")
    public AjaxResultV2<LiveCampStudentStaticsVo> getCampStudentStatics(String campId) {
        LiveCampStudentStaticsVo res = liveAuiCampBizService.getCampStudentStatics(campId);
        return AjaxResultV2.success(res);
    }

    @GetMapping("/getCampStudentPage")
    @ApiOperation("分页查询营期学员")
    public AjaxResultV2<TableDataInfo<LiveAuiCampPeriodStudentPageVo>> listCampStudent(LiveAuiCampPeriodStudentMyPageDto reqDto) {
        startPage();
        List<LiveAuiCampPeriodStudentPageVo> list = liveAuiCampBizService.listCampStudent(reqDto);
        return AjaxResultV2.success(getDataTable(list));
    }

    @PostMapping("/export")
    @ApiOperation("导出")
    public void export(HttpServletResponse response, LiveAuiCampPeriodStudentMyPageDto reqDto) {
        List<LiveAuiCampPeriodStudentPageVo> list = liveAuiCampBizService.listCampStudent(reqDto);
        ExcelUtil<LiveAuiCampPeriodStudentPageVo> util = new ExcelUtil<>(LiveAuiCampPeriodStudentPageVo.class);
        util.exportExcel(response, list, "训练营学员");
    }

}
