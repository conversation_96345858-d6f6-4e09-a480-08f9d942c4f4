package com.panda.pollen.aui.client.controller;

import com.panda.pollen.aui.client.service.ICustomerLiveRoomMessageTypeService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageType;
import com.panda.pollen.common.core.domain.AjaxResultV2;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户直播间消息类型
 * @Author: liuy
 * @Date: 2025年09月20日 11:34
 **/
@RestController
@RequestMapping("/customer/message/type")
public class CustomerLiveRoomMessageTypeController {

    @Autowired
    private ICustomerLiveRoomMessageTypeService customerLiveRoomMessageTypeService;

    @ApiOperation("消息类型树")
    @GetMapping("/tree")
    public AjaxResultV2<List<LiveAuiMessageType>> tree() {
        return AjaxResultV2.success(customerLiveRoomMessageTypeService.getTree());
    }

    @ApiOperation("获取所有末端消息类型")
    @GetMapping("/list-leaf")
    public AjaxResultV2<List<LiveAuiMessageType>> listLeaf(){
        return AjaxResultV2.success(customerLiveRoomMessageTypeService.listLeaf());
    }
}
