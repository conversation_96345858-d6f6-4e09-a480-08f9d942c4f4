package com.panda.pollen.aui.client.controller;

import com.panda.pollen.aui.client.service.ILiveAuiMsgInteractionService;
import com.panda.pollen.aui.client.vo.CustomerInteractionAuthInfoVO;
import com.panda.pollen.aui.model.req.AliSendLiveMessageUserRequest;
import com.panda.pollen.aui.model.req.DeleteMessageRequest;
import com.panda.pollen.common.annotation.Anonymous;
import com.panda.pollen.common.core.domain.AjaxResultV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户直播间互动消息
 * @Author: liuy
 * @Date: 2025年09月12日 17:22
 **/
@RestController
@RequestMapping("/client/msg")
public class CustomerLiveRoomMiController {

    @Autowired
    private ILiveAuiMsgInteractionService liveAuiMsgInteractionService;

    /**
     * 用户互动消息获取登录鉴权信息
     */
    @GetMapping("/getLoginAuthDomain")
    @Anonymous
    public AjaxResultV2<CustomerInteractionAuthInfoVO> getLoginAuthDomain(@RequestParam String userId) {
        return AjaxResultV2.success("success", liveAuiMsgInteractionService.getLoginAuthDomain(userId));
    }

    /**
     * 发送消息
     */
    @PostMapping(value = "/send")
    public AjaxResultV2<Boolean> sendMessage(@RequestBody AliSendLiveMessageUserRequest param) {
        param.valifyParam();
        return AjaxResultV2.success(liveAuiMsgInteractionService.sendMessage(param));
    }

    /**
     * 删除（撤回）某条消息
     */
    @PostMapping(value = "/remove")
    public AjaxResultV2<Boolean> deleteMessage(@RequestBody DeleteMessageRequest param) {
        return AjaxResultV2.success(liveAuiMsgInteractionService.removeMessage(param));
    }

}
