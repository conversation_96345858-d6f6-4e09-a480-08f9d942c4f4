package com.panda.pollen.aui.client.controller;

import com.panda.pollen.aui.client.dto.CustomerInLiveRoomDTO;
import com.panda.pollen.aui.client.dto.CustomerJoinLiveRoomDTO;
import com.panda.pollen.aui.client.dto.RecordLiveRoomVisitInfoDTO;
import com.panda.pollen.aui.client.service.ICustomerLiveRoomService;
import com.panda.pollen.aui.client.vo.CustomerLiveRoomDetailVO;
import com.panda.pollen.common.core.controller.BaseController;
import com.panda.pollen.common.core.domain.AjaxResultV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户直播间
 * <AUTHOR>
 * @date: 2025年08月13日 15:58
 */
@RestController
@RequestMapping("/customer/live-room")
public class CustomerLiveRoomController extends BaseController {

    @Autowired
    private ICustomerLiveRoomService customerLiveRoomBizService;

    /**
     * 判断用户是否在直播间中
     * @param inLiveRoomDTO
     * @return
     */
    @PostMapping("/isInLiveRoom")
    public AjaxResultV2<Boolean> isInLiveRoom(@RequestBody @Validated CustomerInLiveRoomDTO inLiveRoomDTO) {
        Boolean isInLiveRoom = customerLiveRoomBizService.isInLiveRoom(inLiveRoomDTO);
        return AjaxResultV2.success(isInLiveRoom);
    }


    /**
     * 加入直播间（前置条件：C端用户手机号不为空）
     * 如果该用户已经在直播间中，直接返回成功；
     * 如果该用户不在直播间中，系统会将该用户加入直播间；
     * @param joinLiveRoomDTO
     * @return
     */
    @PostMapping("/joinLiveRoom")
    public AjaxResultV2<Void> joinLiveRoom(@RequestBody @Validated CustomerJoinLiveRoomDTO joinLiveRoomDTO) {
        customerLiveRoomBizService.joinLiveRoom(joinLiveRoomDTO);
        return AjaxResultV2.success();
    }

    /**
     * 记录客户直播间访问信息（每次看直播的时候，都调用一次）
     * @param visitInfoDTO
     * @return
     */
    @PostMapping("/recordLiveRoomVisitInfo")
    public AjaxResultV2<Void> recordLiveRoomVisitInfo(@RequestBody @Validated RecordLiveRoomVisitInfoDTO visitInfoDTO) {
        customerLiveRoomBizService.recordLiveRoomVisitInfo(visitInfoDTO);
        return AjaxResultV2.success();
    }

    /**
     * 直播间详情
     * @param liveRoomId    直播间id
     * @return
     */
    @GetMapping("/getLiveRoomInfo/{liveRoomId}")
    public AjaxResultV2<CustomerLiveRoomDetailVO> getLiveRoomInfo(@PathVariable Long liveRoomId) {
        CustomerLiveRoomDetailVO liveRoomDetailVO = customerLiveRoomBizService.selectLiveRoomInfo(liveRoomId);
        return AjaxResultV2.success(liveRoomDetailVO);
    }

}
