package com.panda.pollen.aui.client.service.impl;

import com.panda.pollen.aui.api.LiveAuiMessageTypeApi;
import com.panda.pollen.aui.client.service.ICustomerLiveRoomMessageTypeService;
import com.panda.pollen.aui.system.domain.LiveAuiMessageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 直播消息类型服务实现类
 * @Author: liuy
 * @Date: 2025年09月20日 11:54
 **/
@Service
public class CustomerLiveRoomMessageTypeServiceImpl implements ICustomerLiveRoomMessageTypeService {

    @Autowired
    private LiveAuiMessageTypeApi liveAuiMessageTypeApi;

    /**
     * 获取消息类型树形结构数据
     * @return List<LiveAuiMessageType>
     */
    @Override
    public List<LiveAuiMessageType> getTree(){
        return liveAuiMessageTypeApi.getTree();
    }

    /**
     * 获取所有末端消息类型
     * @return
     */
    @Override
    public List<LiveAuiMessageType> listLeaf(){
        return liveAuiMessageTypeApi.listLeaf();
    }
}
