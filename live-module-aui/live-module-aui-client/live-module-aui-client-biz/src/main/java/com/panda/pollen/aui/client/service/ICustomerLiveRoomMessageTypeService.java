package com.panda.pollen.aui.client.service;

import com.panda.pollen.aui.system.domain.LiveAuiMessageType;

import java.util.List;

/**
 * 直播消息类型服务接口
 * @Author: liuy
 * @Date: 2025年09月20日 11:54
 **/
public interface ICustomerLiveRoomMessageTypeService {

    /**
     * 获取消息类型树形结构数据
     * @return List<LiveAuiMessageType>
     */
    List<LiveAuiMessageType> getTree();

    /**
     * 获取所有末端消息类型
     * @return
     */
    List<LiveAuiMessageType> listLeaf();
}
