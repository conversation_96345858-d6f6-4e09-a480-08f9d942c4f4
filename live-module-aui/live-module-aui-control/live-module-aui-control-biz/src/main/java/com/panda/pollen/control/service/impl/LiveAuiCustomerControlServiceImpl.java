package com.panda.pollen.control.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.panda.pollen.aui.client.api.LiveAuiRoomActiveMemberApi;
import com.panda.pollen.aui.client.dto.LiveRoomStatusDTO;
import com.panda.pollen.aui.model.AliResponse;
import com.panda.pollen.aui.model.req.LiveRoomRequest;
import com.panda.pollen.aui.service.AliCloudBizInterfaceService;
import com.panda.pollen.common.enums.CommonStatus;
import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.control.service.LiveAuiCustomerControlService;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p> 助教场控-客户控制实现<p/>
 * ClassName com.panda.pollen.service.impl.LiveAuiCustomerControlServiceImpl 
 * <AUTHOR> 
 * @date 2025年8月15日 下午9:47:47 
 * @version v1.0
 */
@Slf4j
@Service
public class LiveAuiCustomerControlServiceImpl implements LiveAuiCustomerControlService {

    @Resource
    private AliCloudBizInterfaceService aliCloudBizService;
    
    @Autowired
    private LiveAuiRoomActiveMemberApi roomActiveMemberApi;

    @Override
    public boolean banned(LiveRoomRequest param) {
        AliResponse res = aliCloudBizService.banned(param);
        modifyRoomCustomer(param, CommonStatus.YES);
        return res.isSuccess();
    }

    @Override
    public boolean unban(LiveRoomRequest param) {
        AliResponse res = aliCloudBizService.unban(param);
        return res.isSuccess();
    }

    @Override
    public boolean addBand(LiveRoomRequest param) {
         AliResponse res = aliCloudBizService.addBand(param);
         modifyRoomCustomer(param, CommonStatus.YES);
         return res.isSuccess();
    }

    @Override
    public boolean removeBand(LiveRoomRequest param) {
        AliResponse res = aliCloudBizService.removeBand(param);
        modifyRoomCustomer(param, CommonStatus.NO);
        return res.isSuccess();
    }

    @Override
    public boolean kickUser(LiveRoomRequest param) {
        AliResponse res = aliCloudBizService.kickUser(param);
        modifyRoomCustomer(param, CommonStatus.YES);
        return res.isSuccess();
    }

    public AliResponse checkUsersOnline(LiveRoomRequest param) {
        return aliCloudBizService.checkUsersOnline(param);
    }
    
    private void modifyRoomCustomer(LiveRoomRequest param, CommonStatus state) {
        List<String> bannedUsers = param.getBannedUsers();
        List<LiveRoomStatusDTO> datas = ListUtils.newArrayList();
        if(ListUtils.isNotEmpty(bannedUsers)) {
            bannedUsers.forEach(customerId -> {
                datas.add(LiveRoomStatusDTO.builder().roomId(param.getRoomId())
                    .muteStatus(state.getCode()).customerId(customerId).build());
            });
        }
        List<String> unbannedUsers = param.getUnbannedUsers();
        if(ListUtils.isNotEmpty(unbannedUsers)) {
            unbannedUsers.forEach(customerId -> {
                datas.add(LiveRoomStatusDTO.builder().roomId(param.getRoomId())
                    .muteStatus(state.getCode()).customerId(customerId).build());
            });
        }
        if(StrUtil.isNotEmpty(param.getKickoffUser())) {
            datas.add(LiveRoomStatusDTO.builder().roomId(param.getRoomId())
                .kickOutStatus(state.getCode()).customerId(param.getKickoffUser()).build());
        }
        roomActiveMemberApi.updateMemberStatus(datas);
    } 
    
}
