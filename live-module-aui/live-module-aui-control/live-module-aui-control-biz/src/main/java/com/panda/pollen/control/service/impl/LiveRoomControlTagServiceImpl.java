package com.panda.pollen.control.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.control.dto.LiveControlTagDTO;
import com.panda.pollen.control.mapper.CustomerTagMapper;
import com.panda.pollen.control.service.LiveRoomControlTagService;
import com.panda.pollen.control.vo.CustomerTagVO;
import com.panda.pollen.scrm.api.AuthCorpTagApi;
import com.panda.pollen.scrm.api.CustomerUserTagApi;
import com.panda.pollen.scrm.dto.EditCustomerTagDTO;
import com.panda.pollen.scrm.vo.AuthCorpTagVO;

import lombok.extern.slf4j.Slf4j;

/**
 * <p> 客户标签服务实现类 <p/>
 * ClassName com.panda.pollen.control.service.impl.LiveRoomControlTagServiceImpl 
 * <AUTHOR> 
 * @date 2025年8月18日 下午9:35:14 
 * @version v1.0   
 */
@Slf4j
@Service
public class LiveRoomControlTagServiceImpl extends ServiceImpl<CustomerTagMapper, CustomerTagVO> implements LiveRoomControlTagService {

	@Autowired
	private AuthCorpTagApi corpTagApi;
	@Autowired
	private CustomerUserTagApi customerUserTagApi;

    @Override
    public List<CustomerTagVO> getTagGodown(LiveControlTagDTO param) {
        List<AuthCorpTagVO> tags = corpTagApi.selectAuthCorpTagList(param.getCompanyId());
        if (ListUtils.isEmpty(tags)) {
            return ListUtils.newArrayList();
        }
        Map<String, List<AuthCorpTagVO>> collect =
            tags.stream().collect(Collectors.groupingBy(AuthCorpTagVO::getGroupName));
        List<CustomerTagVO> customerTags = ListUtils.newArrayList();
        collect.keySet().forEach(key -> {
            List<AuthCorpTagVO> datas = collect.get(key);
            customerTags.add(CustomerTagVO.builder().groupName(key).tags(datas).build());
        });
        return customerTags;
    }
    
    @Override
    public List<AuthCorpTagVO> getTagByCustomerUserId(LiveControlTagDTO param) {
        return customerUserTagApi.selectCustomerUserTags(param.getCustomerId());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveTags(LiveControlTagDTO param) {
    	List<EditCustomerTagDTO> customerTags = param.gainEditCustomerTags();
    	if(ListUtils.isEmpty(customerTags)) {
    	    throw new ServiceException("标签数据为空");
    	}
    	try {
    	    customerUserTagApi.editCustomerUserTagsByTagName(customerTags);
        } catch (Exception e) {
            log.error("【tags】客户打标签时发生异常-->>{}", e);
            throw new ServiceException("客户打标签失败");
        }
    	return Boolean.TRUE;
    } 

}
