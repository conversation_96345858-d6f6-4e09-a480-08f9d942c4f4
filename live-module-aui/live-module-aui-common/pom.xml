<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.panda.pollen</groupId>
        <artifactId>live-module-aui</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>live-module-aui-common</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <!-- 通用工具 -->
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-commons</artifactId>
        </dependency>
        <!-- 核心模块-->
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-framework</artifactId>
        </dependency>
        <!-- 阿里云核心SDK -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>
        <!-- 视频直播SDK -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-live</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-vod</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-vod-upload</artifactId>
            <version>1.4.15</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>
    </dependencies>
</project>