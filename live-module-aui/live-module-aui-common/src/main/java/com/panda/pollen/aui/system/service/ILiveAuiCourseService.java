package com.panda.pollen.aui.system.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.panda.pollen.aui.system.domain.LiveAuiCourse;

/**
 * 直播课程Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface ILiveAuiCourseService extends IService<LiveAuiCourse> {

    /**
     * 查询直播课程列表
     * 
     * @param liveAuiCourse 直播课程
     * @return 直播课程集合
     */
    List<LiveAuiCourse> queryLiveAuiCourseList(LiveAuiCourse liveAuiCourse);
    
}
