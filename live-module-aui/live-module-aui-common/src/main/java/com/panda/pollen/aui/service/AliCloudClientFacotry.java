package com.panda.pollen.aui.service;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.panda.pollen.aui.model.cons.ConsLiveAui;

import cn.hutool.extra.spring.SpringUtil;

/**
 * <p> 阿里云客户工厂类<p/>
 * ClassName com.panda.pollen.aui.service.AliCloudClientFacotry 
 * <AUTHOR> 
 * @date 2025年8月15日 下午9:49:52 
 * @version v1.0   
 */
public class AliCloudClientFacotry {

    private static final AliCloudConfig config = SpringUtil.getBean(AliCloudConfig.class);
    
    private static final DefaultProfile profile = DefaultProfile.getProfile(ConsLiveAui.DATA_CENTER, config.getAccessKeyId(), config.getAccessKeySecret());
    
    public static IAcsClient getClient() {
        return new DefaultAcsClient(profile);
    }
    
    public static AliCloudConfig getConfig() {
        return AliCloudClientFacotry.config;
    }
    
}
