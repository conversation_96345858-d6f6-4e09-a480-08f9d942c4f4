package com.panda.pollen.aui.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.aui.system.domain.LiveAuiCampPeriodStudent;
import com.panda.pollen.aui.system.mapper.LiveAuiCampPeriodStudentMapper;
import com.panda.pollen.aui.system.service.ILiveAuiCampPeriodStudentService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 训练营学员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
@Service
public class LiveAuiCampPeriodStudentServiceImpl extends ServiceImpl<LiveAuiCampPeriodStudentMapper, LiveAuiCampPeriodStudent> implements ILiveAuiCampPeriodStudentService {

    /**
     * 查询训练营学员列表
     * 
     * @param liveAuiCampPeriodStudent 训练营学员
     * @return 训练营学员
     */
    @Override
    public List<LiveAuiCampPeriodStudent> queryLiveAuiCampPeriodStudentList(LiveAuiCampPeriodStudent liveAuiCampPeriodStudent) {
        QueryWrapper<LiveAuiCampPeriodStudent> queryWrapper = Wrappers.query();
        // 完善查询条件,例如:queryWrapper.eq("student_name", sysStudent.getStudentName());
        return this.list(queryWrapper);
    }

}
