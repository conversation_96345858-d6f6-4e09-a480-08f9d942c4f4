package com.panda.pollen.aui.system.service.impl;

import java.util.List;
import java.util.Arrays;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.aui.system.domain.LiveAuiRoomInfo;
import com.panda.pollen.aui.system.mapper.LiveAuiRoomInfoMapper;
import com.panda.pollen.aui.system.service.ILiveAuiRoomInfoService;
import com.panda.pollen.common.utils.DateUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

/**
 * 直播间Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class LiveAuiRoomInfoServiceImpl extends ServiceImpl<LiveAuiRoomInfoMapper, LiveAuiRoomInfo> implements ILiveAuiRoomInfoService {

    /**
     * 查询直播间列表
     * 
     * @param liveAuiRoomInfo 直播间
     * @return 直播间
     */
    @Override
    public List<LiveAuiRoomInfo> queryLiveAuiRoomInfoList(LiveAuiRoomInfo liveAuiRoomInfo) {
        QueryWrapper<LiveAuiRoomInfo> queryWrapper = Wrappers.query();
        // 完善查询条件,例如:queryWrapper.eq("student_name", sysStudent.getStudentName());
        return this.list(queryWrapper);
    }

}
