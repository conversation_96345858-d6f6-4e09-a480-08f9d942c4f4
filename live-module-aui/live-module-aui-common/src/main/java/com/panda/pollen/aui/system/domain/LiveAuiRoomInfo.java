package com.panda.pollen.aui.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.panda.pollen.common.annotation.Excel;
import com.panda.pollen.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 直播间对象 live_aui_room_info
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "live_aui_room_info")
public class LiveAuiRoomInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**  */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /** 直播间标题 */
    @Excel(name = "直播间标题")
    private String title;

    /** 主播 */
    @Excel(name = "主播")
    private String anchor;

    /** 直播间状态;0-未开始，1-直播中，2-已结束 */
    @Excel(name = "直播间状态;0-未开始，1-直播中，2-已结束")
    private Long status;

    /** 先不使用 */
    @Excel(name = "先不使用")
    private Long mode;

    /** 直播间会话ID */
    @Excel(name = "直播间会话ID")
    private String chatId;

    /** 直播间公告 */
    @Excel(name = "直播间公告")
    private String notice;

    /** 封面地址 */
    @Excel(name = "封面地址")
    private String coverUrl;

    /** 主播ID */
    @Excel(name = "主播ID")
    private String anchorId;

    /** 主播昵称 */
    @Excel(name = "主播昵称")
    private String anchorNick;

    /** 直播开始时间 */
    @Excel(name = "直播开始时间")
    private LocalDateTime startedAt;

    /** 直播结束时间 */
    @Excel(name = "直播结束时间")
    private LocalDateTime stoppedAt;

    /** 删除标识 */
    @Excel(name = "删除标识")
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /** 备注 */
    @TableField(exist = false)
    @JsonIgnore
    private String remark;

    @TableField(exist = false)
    @JsonIgnore
    private Integer oper;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("title", getTitle())
                .append("anchor", getAnchor())
                .append("status", getStatus())
                .append("mode", getMode())
                .append("chatId", getChatId())
                .append("notice", getNotice())
                .append("coverUrl", getCoverUrl())
                .append("anchorId", getAnchorId())
                .append("anchorNick", getAnchorNick())
                .append("startedAt", getStartedAt())
                .append("stoppedAt", getStoppedAt())
                .append("deleted", getDeleted())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
