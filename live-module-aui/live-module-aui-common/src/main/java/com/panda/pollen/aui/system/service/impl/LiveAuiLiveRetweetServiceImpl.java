package com.panda.pollen.aui.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.aui.system.domain.LiveAuiLiveRetweet;
import com.panda.pollen.aui.system.mapper.LiveAuiLiveRetweetMapper;
import com.panda.pollen.aui.system.service.ILiveAuiLiveRetweetService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 云转推管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
public class LiveAuiLiveRetweetServiceImpl extends ServiceImpl<LiveAuiLiveRetweetMapper, LiveAuiLiveRetweet> implements ILiveAuiLiveRetweetService {

    /**
     * 查询云转推管理列表
     *
     * @param liveAuiLiveRetweet 云转推管理
     * @return 云转推管理
     */
    @Override
    public List<LiveAuiLiveRetweet> queryLiveAuiLiveRetweetList(LiveAuiLiveRetweet liveAuiLiveRetweet) {
        QueryWrapper<LiveAuiLiveRetweet> queryWrapper = Wrappers.query();
        // 完善查询条件,例如:queryWrapper.eq("student_name", sysStudent.getStudentName());
        return this.list(queryWrapper);
    }

}
