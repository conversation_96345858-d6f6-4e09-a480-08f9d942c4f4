package com.panda.pollen.aui.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.panda.pollen.common.annotation.Excel;
import com.panda.pollen.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.util.Date;

/**
 * 直播间live_aui_room_info信息扩展对象 live_aui_room_info_ext
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@TableName(value = "live_aui_room_info_ext")
public class LiveAuiRoomInfoExt extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 直播间ID
     */
    @Excel(name = "直播间ID")
    private Long roomInfoId;

    /**
     * 消息群组ID(live_aui_message_group表的主键id)
     */
    @Excel(name = "消息群组ID(live_aui_message_group表的主键id)")
    private Long messageGroupId;

    /**
     * 直播名称
     */
    @Excel(name = "直播名称")
    private String roomName;

    /**
     * 直播模式(0-全屏,1-分屏)
     */
    @Excel(name = "直播模式(0-全屏,1-分屏)")
    private Long liveMode;

    /**
     * 课程类型(0-直播课程,1-录播直播)
     */
    @Excel(name = "课程类型(0-直播课程,1-录播直播)")
    private Long courseType;

    /**
     * 课程分组
     */
    @Excel(name = "课程分组")
    private String courseGroupId;

    /**
     * 直播简介
     */
    @Excel(name = "直播简介")
    private String introduction;

    /**
     * 直播封面id
     */
    @Excel(name = "直播封面id")
    private Long imageId;

    /**
     * 直播封面地址
     */
    @Excel(name = "直播封面地址")
    private String imageUrl;

    /**
     * 暖场视频ID
     */
    @Excel(name = "暖场视频ID")
    private Long warmUpVideoId;
    @ApiModelProperty(value = "暖场图片ID", example = "456")
    private Long warmUpImageId;
    @Excel(name = "暖场图片地址")
    private String warmUpImageUrl;
    /**
     * 暖场视频地址
     */
    @Excel(name = "暖场视频地址")
    private String warmUpVideoUrl;

    /**
     * 内容来源(0-自定义,1-直播模板)
     */
    @Excel(name = "内容来源(0-自定义,1-直播模板)")
    private Long contentSource;

    /**
     * 课程视频ID(内容来源是自定义的时候设置)
     */
    @Excel(name = "课程视频ID(内容来源是自定义的时候设置)")
    private Long courseVideoId;

    /**
     * 源视频地址
     */
    @Excel(name = "源视频地址")
    private String sourceVideoUrl;

    /**
     * 引用消息内容ID--关联历史直播间id,用户获取历史直播间的直播消息(内容来源是自定义的时候设置)
     */
    @Excel(name = "引用消息内容ID--关联历史直播间id,用户获取历史直播间的直播消息(内容来源是自定义的时候设置)")
    private Long sourceMessageContentId;

    /**
     * 直播模板ID(内容来源是直播模板的时候设置)
     */
    @Excel(name = "直播模板ID(内容来源是直播模板的时候设置)")
    private Long liveTemplateId;

    /**
     * 启用聊天：0-禁用，1-启用
     */
    @Excel(name = "启用聊天：0-禁用，1-启用")
    private Long enableChat;
    @Excel(name = "是否上架：上架状态(0-上架,1-暂不上架,2-下架)")
    private Integer listedStatus;

    @Excel(name = "上下架时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date listedTime;

    @ApiModelProperty(value = "直播间类型：0-普通直播间，1-课程直播间")
    private Integer type;

    @ApiModelProperty(value = "暖场类型：0-无暖场，1-自定义暖场，2-模板暖场")
    private Integer warmType;

    /**
     * 消息是否已下载 (默认0，未下载——0，已下载——1)
     */
    private Boolean messageDownloaded;
    /**
     * 删除标识
     */
    @Excel(name = "删除标识")
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /**
     * 备注
     */
    @TableField(exist = false)
    @JsonIgnore
    private String remark;

    @TableField(exist = false)
    @JsonIgnore
    private Integer oper;
}
