package com.panda.pollen.aui.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.panda.pollen.common.annotation.Excel;
import com.panda.pollen.common.base.BaseEntityV2;
import com.panda.pollen.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;

/**
 * 分类管理对象 live_aui_category
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "live_aui_category")
public class LiveAuiCategory extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 请求ID
     */
    @Excel(name = "请求ID")
    private String requestId;

    /**
     * 分类 ID
     */
    @Excel(name = "分类 ID")
    private Long cateId;

    /**
     * 分类名称
     */
    @Excel(name = "分类名称")
    private String cateName;

    /**
     * 父分类 ID
     */
    @Excel(name = "父分类 ID")
    private Long parentId;

    /**
     * 分类类型: default（默认值）：音视频/图片分类 , material：短视频素材分类
     */
    @Excel(name = "分类类型: default", readConverterExp = "默=认值")
    private String type;

    /**
     * 分类层级
     */
    @Excel(name = "分类层级")
    private Long level;

    /**
     * 删除标识
     */
    @Excel(name = "删除标识")
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /** 备注 */
    @TableField(exist = false)
    @JsonIgnore
    private String remark;

    @TableField(exist = false)
    @JsonIgnore
    private Integer oper;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("requestId", getRequestId())
                .append("cateId", getCateId())
                .append("cateName", getCateName())
                .append("parentId", getParentId())
                .append("type", getType())
                .append("level", getLevel())
                .append("deleted", getDeleted())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
