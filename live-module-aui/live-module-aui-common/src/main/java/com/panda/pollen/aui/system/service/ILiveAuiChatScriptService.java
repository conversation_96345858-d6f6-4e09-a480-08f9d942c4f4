package com.panda.pollen.aui.system.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.panda.pollen.aui.system.domain.LiveAuiChatScript;

/**
 * 聊天记录自动播放配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface ILiveAuiChatScriptService extends IService<LiveAuiChatScript> {

    /**
     * 查询聊天记录自动播放配置列表
     * 
     * @param liveAuiChatScript 聊天记录自动播放配置
     * @return 聊天记录自动播放配置集合
     */
    List<LiveAuiChatScript> queryLiveAuiChatScriptList(LiveAuiChatScript liveAuiChatScript);
    
}
