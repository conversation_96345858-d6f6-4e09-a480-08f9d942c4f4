package com.panda.pollen.aui.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.aui.system.domain.LiveAuiImageAuth;
import com.panda.pollen.aui.system.mapper.LiveAuiImageAuthMapper;
import com.panda.pollen.aui.system.service.ILiveAuiImageAuthService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 图片文件上传凭证Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
public class LiveAuiImageAuthServiceImpl extends ServiceImpl<LiveAuiImageAuthMapper, LiveAuiImageAuth> implements ILiveAuiImageAuthService {

    /**
     * 查询图片文件上传凭证列表
     *
     * @param liveAuiImageAuth 图片文件上传凭证
     * @return 图片文件上传凭证
     */
    @Override
    public List<LiveAuiImageAuth> queryLiveAuiImageAuthList(LiveAuiImageAuth liveAuiImageAuth) {
        QueryWrapper<LiveAuiImageAuth> queryWrapper = Wrappers.query();
        // 完善查询条件,例如:queryWrapper.eq("student_name", sysStudent.getStudentName());
        return this.list(queryWrapper);
    }

}
