package com.panda.pollen.aui.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.aui.system.domain.LiveAuiCampPeriodTags;
import com.panda.pollen.aui.system.mapper.LiveAuiCampPeriodTagsMapper;
import com.panda.pollen.aui.system.service.ILiveAuiCampPeriodTagsService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 训练营营期标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
@Service
public class LiveAuiCampPeriodTagsServiceImpl extends ServiceImpl<LiveAuiCampPeriodTagsMapper, LiveAuiCampPeriodTags> implements ILiveAuiCampPeriodTagsService {

    /**
     * 查询训练营营期标签列表
     * 
     * @param liveAuiCampPeriodTags 训练营营期标签
     * @return 训练营营期标签
     */
    @Override
    public List<LiveAuiCampPeriodTags> queryLiveAuiCampPeriodTagsList(LiveAuiCampPeriodTags liveAuiCampPeriodTags) {
        QueryWrapper<LiveAuiCampPeriodTags> queryWrapper = Wrappers.query();
        // 完善查询条件,例如:queryWrapper.eq("student_name", sysStudent.getStudentName());
        return this.list(queryWrapper);
    }

}
