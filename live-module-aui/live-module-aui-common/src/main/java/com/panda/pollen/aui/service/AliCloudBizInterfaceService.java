package com.panda.pollen.aui.service;

import java.io.InputStream;
import java.util.List;

import com.aliyun.vod.upload.resp.UploadStreamResponse;
import com.aliyun.vod.upload.resp.UploadVideoResponse;
import com.aliyuncs.live.model.v20161101.CreateLiveMessageGroupRequest;
import com.aliyuncs.live.model.v20161101.CreateLiveMessageGroupResponse;
import com.aliyuncs.live.model.v20161101.CreateLivePullToPushResponse;
import com.aliyuncs.live.model.v20161101.DeleteLivePullToPushResponse;
import com.aliyuncs.live.model.v20161101.GetMessageTokenResponse;
import com.aliyuncs.live.model.v20161101.ListLiveMessageGroupMessagesResponse;
import com.aliyuncs.live.model.v20161101.UpdateLivePullToPushResponse;
import com.aliyuncs.vod.model.v20170321.AddCategoryResponse;
import com.aliyuncs.vod.model.v20170321.AddTranscodeTemplateGroupResponse;
import com.aliyuncs.vod.model.v20170321.CreateAppInfoResponse;
import com.aliyuncs.vod.model.v20170321.CreateUploadImageResponse;
import com.aliyuncs.vod.model.v20170321.CreateUploadVideoResponse;
import com.aliyuncs.vod.model.v20170321.DeleteCategoryResponse;
import com.aliyuncs.vod.model.v20170321.DeleteImageResponse;
import com.aliyuncs.vod.model.v20170321.DeleteTranscodeTemplateGroupResponse;
import com.aliyuncs.vod.model.v20170321.DeleteVideoResponse;
import com.aliyuncs.vod.model.v20170321.GetCategoriesResponse;
import com.aliyuncs.vod.model.v20170321.GetImageInfoResponse;
import com.aliyuncs.vod.model.v20170321.GetImageInfosResponse;
import com.aliyuncs.vod.model.v20170321.GetPlayInfoResponse;
import com.aliyuncs.vod.model.v20170321.GetTranscodeTemplateGroupResponse;
import com.aliyuncs.vod.model.v20170321.GetUploadDetailsResponse;
import com.aliyuncs.vod.model.v20170321.GetVideoInfoResponse;
import com.aliyuncs.vod.model.v20170321.GetVideoInfosResponse;
import com.aliyuncs.vod.model.v20170321.GetVideoListResponse;
import com.aliyuncs.vod.model.v20170321.GetVideoPlayAuthResponse;
import com.aliyuncs.vod.model.v20170321.ListAppInfoResponse;
import com.aliyuncs.vod.model.v20170321.ListTranscodeTemplateGroupResponse;
import com.aliyuncs.vod.model.v20170321.RefreshUploadVideoResponse;
import com.aliyuncs.vod.model.v20170321.SetDefaultTranscodeTemplateGroupResponse;
import com.aliyuncs.vod.model.v20170321.SubmitTranscodeJobsResponse;
import com.aliyuncs.vod.model.v20170321.UpdateCategoryResponse;
import com.aliyuncs.vod.model.v20170321.UpdateImageInfosResponse;
import com.aliyuncs.vod.model.v20170321.UpdateTranscodeTemplateGroupResponse;
import com.panda.pollen.aui.model.AliResponse;
import com.panda.pollen.aui.model.LinkInfo;
import com.panda.pollen.aui.model.PullLiveInfo;
import com.panda.pollen.aui.model.PushLiveInfo;
import com.panda.pollen.aui.model.dto.NewImTokenResponseDto;
import com.panda.pollen.aui.model.dto.RoomInfoDto;
import com.panda.pollen.aui.model.req.AliAddCategoryRequest;
import com.panda.pollen.aui.model.req.AliAddTranscodeTemplateGroupRequest;
import com.panda.pollen.aui.model.req.AliCreateAppInfoRequest;
import com.panda.pollen.aui.model.req.AliCreateLiveMessageAppRequest;
import com.panda.pollen.aui.model.req.AliCreateLivePullToPushRequest;
import com.panda.pollen.aui.model.req.AliCreateUploadImageRequest;
import com.panda.pollen.aui.model.req.AliCreateUploadVideoRequest;
import com.panda.pollen.aui.model.req.AliDeleteImageRequest;
import com.panda.pollen.aui.model.req.AliDeleteLivePullToPushRequest;
import com.panda.pollen.aui.model.req.AliDeleteTranscodeTemplateGroupRequest;
import com.panda.pollen.aui.model.req.AliDeleteVideoRequest;
import com.panda.pollen.aui.model.req.AliGetCategoriesRequest;
import com.panda.pollen.aui.model.req.AliGetImageInfoRequest;
import com.panda.pollen.aui.model.req.AliGetImageInfosRequest;
import com.panda.pollen.aui.model.req.AliGetMessageTokenRequest;
import com.panda.pollen.aui.model.req.AliGetPlayInfoRequest;
import com.panda.pollen.aui.model.req.AliGetUploadDetailsRequest;
import com.panda.pollen.aui.model.req.AliGetVideoInfoRequest;
import com.panda.pollen.aui.model.req.AliGetVideoListRequest;
import com.panda.pollen.aui.model.req.AliGetVideoPlayAuthRequest;
import com.panda.pollen.aui.model.req.AliListAppInfoRequest;
import com.panda.pollen.aui.model.req.AliRefreshUploadVideoRequest;
import com.panda.pollen.aui.model.req.AliSendLiveMessageUserRequest;
import com.panda.pollen.aui.model.req.AliSetDefaultTranscodeTemplateGroupRequest;
import com.panda.pollen.aui.model.req.AliSubmitTranscodeJobsRequest;
import com.panda.pollen.aui.model.req.AliUpdateCategoryRequest;
import com.panda.pollen.aui.model.req.AliUpdateImageInfosRequest;
import com.panda.pollen.aui.model.req.AliUpdateLivePullToPushRequest;
import com.panda.pollen.aui.model.req.AliUpdateTranscodeTemplateGroupRequest;
import com.panda.pollen.aui.model.req.DeleteMessageRequest;
import com.panda.pollen.aui.model.req.LiveRoomRequest;
import com.panda.pollen.aui.system.domain.LiveAuiMessageApp;

/**
 * <p> 阿里云服务接口<p/>
 * ClassName com.panda.pollen.aui.service.AliCloudBizInterfaceService 
 * <AUTHOR> 
 * @date 2025年8月14日 下午11:53:54 
 * @version v1.0
 */
public interface AliCloudBizInterfaceService {

    /**
     * 获取Im的Token。见文档：<a href="https://help.aliyun.com/document_detail/465127.html">...</a>
     *
     * <AUTHOR>
     */
    GetMessageTokenResponse getImToken(AliGetMessageTokenRequest req);

    /**
     * 获取新Im的Token。
     *
     * <AUTHOR>
     */
    NewImTokenResponseDto getNewImToken(AliGetMessageTokenRequest req);

    /**
     * 创建消息组。见文档：<a href="https://help.aliyun.com/document_detail/465128.html">...</a>
     *
     * <AUTHOR>
     */
    String createMessageGroup(String anchor);

    /**
     * 创建新im的消息组。
     *
     * <AUTHOR>
     */
    CreateLiveMessageGroupResponse createNewImMessageGroup(CreateLiveMessageGroupRequest req);

    /**
     * 获取推流地址。见文档：<a href="https://help.aliyun.com/document_detail/199339.html">...</a>
     *
     * <AUTHOR>
     */
    PushLiveInfo getPushLiveInfo(String streamName);


    /**
     * 获取拉流地址。见文档：<a href="https://help.aliyun.com/document_detail/199339.html">...</a>
     *
     * <AUTHOR>
     */
    PullLiveInfo getPullLiveInfo(String streamName);


    /**
     * 获取RTC地址。见文档：<a href="https://help.aliyun.com/document_detail/450515.html">...</a>
     *
     * <AUTHOR>
     */
    LinkInfo getRtcInfo(String channelId, String userId, String anchorId);

    /**
     * 从点播搜索录制的视频Id。见文档：<a href="https://help.aliyun.com/document_detail/436559.htm">...</a>
     *
     * <AUTHOR>
     */
    String searchMediaByTitle(String title);


    /**
     * 通过音视频ID直接获取视频的播放地址。见文档：<a href="https://help.aliyun.com/document_detail/436555.html">...</a>
     *
     * <AUTHOR>
     */
    RoomInfoDto.VodInfo getPlayInfo(String mediaId);

    /**
     * <AUTHOR>
     */
    RoomInfoDto.Metrics getNewImGroupDetails(String groupId);

    /**
     * <AUTHOR>
     */
    RoomInfoDto.Metrics getGroupDetails(String groupId);

    /**
     * 用ListMessageGroupUserById通过用户ID列表查询用户信息。见文档；<a href="https://help.aliyun.com/document_detail/465143.html">...</a>
     *
     * <AUTHOR>
     */
    RoomInfoDto.UserStatus getUserInfo(String groupId, String anchor);

    /**
     * 校验直播推流状态回调事件签名。见文档；<a href="https://help.aliyun.com/document_detail/199365.html?spm=5176.13499635.help.dexternal.35d92699jvVrc7#section-mxt-vfh-b6s">...</a>
     *
     * <AUTHOR>
     */
    boolean validLiveCallbackSign(String liveSignature, String liveTimestamp);

    /**
     * 获取rtc token信息。见文档<a href="https://help.aliyun.com/document_detail/450516.htm">...</a>、
     *
     * <AUTHOR>
     */
    String getRtcAuth(String channelId, String userId, long timestamp);

    /**
     * 给N对N连麦做测试
     *
     * @param channelId 频道ID
     * @param userId 用户ID
     * @param timestamp 时间戳
     * @return 给N对N连麦做测试
     */
    String getSpecialRtcAuth(String channelId, String userId, long timestamp);

    /**
     * <p> 直播间禁言<p/>
     * <AUTHOR> 
     * @date 2025年8月14日 下午10:07:13 
     * @param param 参数对象
     * @return AliResponse 
     * @throws
     */
    AliResponse banned(LiveRoomRequest param);

    /**
     * <p> 取消解除群组全员禁言<p/>
     * <AUTHOR> 
     * @date 2025年8月14日 下午10:19:21 
     * @param param
     * @return AliResponse 
     * @throws
     */
    AliResponse unban(LiveRoomRequest param);

    /**
     * 新增禁言用户
     *
     * @param req req
     * @return boolean
     */

    AliResponse addBand(LiveRoomRequest req);

    /**
     * <p> 解除禁言用户 <p/>
     * <AUTHOR> 
     * @date 2025年8月15日 下午9:23:05 
     * @param req
     * @return boolean 
     * @throws
     */
    AliResponse removeBand(LiveRoomRequest req);

    /**
     * <p> 将指定用户从群组中踢出去<p/>
     * <AUTHOR> 
     * @date 2025年8月15日 下午9:26:05 
     * @param req
     * @return AliResponse 
     * @throws
     */
    AliResponse kickUser(LiveRoomRequest req);

    /**
     * <p> 直播间消息群组消息推送，包括私聊（receiverId）、群聊（groupId）<p/>
     * <AUTHOR> 
     * @date 2025年8月14日 下午9:21:01 
     * @param param 参数对象
     * @return AliResponse 
     * @throws
     */
    AliResponse sendMessage(AliSendLiveMessageUserRequest param);
    
    /**
     * 删除（撤回）消息
     *
     * @param param req
     * @return boolean
     */
    AliResponse removeMessage(DeleteMessageRequest param);
    
    /**
     * <p> 查询指定的用户是否在线 <p/>
     * <AUTHOR> 
     * @date 2025年8月15日 下午9:38:01 
     * @param param
     * @return AliResponse 
     * @throws
     */
    AliResponse checkUsersOnline(LiveRoomRequest param);

    /**
     * 播放完成后  获取本次直播  阿里云群组消息列表
     *
     * @param appId         消息应用ID
     * @param groupId       消息群组ID
     * @param sortType      排序方式【1，2】 正序 倒序
     * @param dataCenter    数据中心
     * @param nextPageToken 查询页的起始位置，若为空则默认为首页
     * @return 群组消息列表
     */
    ListLiveMessageGroupMessagesResponse recordRoomMessageAfterPlay(String appId, String groupId, Integer sortType, String dataCenter, Long nextPageToken);

    /**
     * 获取上传地址和凭证
     *
     * @param req 上传视频参数
     * @return 视频id
     */
    CreateUploadVideoResponse createUploadVideo(AliCreateUploadVideoRequest req);

    /**
     * 创建拉流推流
     *
     * @param req 创建拉流推流参数
     * @return 推拉流id
     */
    CreateLivePullToPushResponse createLivePullToPush(AliCreateLivePullToPushRequest req);

    /**
     * 删除拉流推流
     *
     * @param req 删除拉流推流参数
     * @return 删除结果
     */
    DeleteLivePullToPushResponse deleteLivePullToPush(AliDeleteLivePullToPushRequest req);

    /**
     * 本地文件上传接口
     *
     * @param title    标题
     * @param fileName 文件名
     * @return 响应
     */
    UploadVideoResponse uploadVideo(String title, String fileName);

    /**
     * URL网络流上传。支持断点续传，最大支持48.8TB的单个文件。
     * 该上传方式需要先将网络文件下载到本地磁盘，再进行上传，所以要保证本地磁盘有充足的空间。
     * 当您设置的URL中不包括文件扩展名时，需要单独设置fileExtension，表示文件扩展名。
     *
     * @param title         标题
     * @param url           url
     * @param fileExtension 文件后缀
     */
    void uploadUrlStream(String title, String url, String fileExtension);

    /**
     * 文件流上传接口
     *
     * @param title    标题
     * @param fileName 文件名
     */
    void uploadFileStream(String title, String fileName);

    /**
     * 流式上传接口
     *
     * @param title       标题
     * @param fileName    文件名
     * @param inputStream 文件流
     * @return 响应
     */
    UploadStreamResponse uploadStream(String title, String fileName, InputStream inputStream);

    /**
     * 上传本地m3u8视频或音频文件到点播，m3u8文件和分片文件默认在同一目录（sliceFilenames为空时，会按照同一目录去解析分片地址）
     *
     * @param title          视频标题
     * @param m3u8Filename   本地m3u8索引文件的绝对路径，且m3u8文件的分片信息必须是相对路径，不能含有URL或本地绝对路径
     * @param sliceFilenames ts分片文件的绝对路径列表，如指定则以此为准，若不指定，则自动解析 m3u8Filename 里的m3u8文件
     */
    void uploadLocalM3u8(String title, String m3u8Filename, String[] sliceFilenames);

    /**
     * 上传网络m3u8视频或音频文件到点播，需本地磁盘空间足够，会先下载到本地临时目录，再上传到点播存储
     *
     * @param title         视频标题
     * @param m3u8FileUrl   网络m3u8索引文件的URL地址，且m3u8文件的分片信息必须是相对地址，不能含有URL或本地绝对路径
     * @param sliceFileUrls ts分片文件的URL地址列表；需自行拼接ts分片的URL地址列表
     */
    void uploadWebM3u8(String title, String m3u8FileUrl, String[] sliceFileUrls);

    /**
     * 获取视频信息
     *
     * @param req 视频信息参数
     * @return 视频信息
     */
    GetVideoInfoResponse getVideoInfo(AliGetVideoInfoRequest req);

    /**
     * 分页查询视频列表
     *
     * @param req 视频列表查询参数
     * @return 视频列表
     */
    GetVideoListResponse getVideoList(AliGetVideoListRequest req);

    /**
     * 添加分类
     *
     * @param req 添加分类参数
     * @return 分类信息
     */
    AddCategoryResponse addCategory(AliAddCategoryRequest req);

    /**
     * 查询分类
     *
     * @param cateId 分类ID
     * @return 分类信息
     */
    GetCategoriesResponse getCategories(Long cateId);

    /**
     * 查询分类
     *
     * @param req 查询分类参数
     * @return 分类信息
     */
    GetCategoriesResponse getCategories(AliGetCategoriesRequest req);

    /**
     * 删除分类
     *
     * @param cateId 分类ID
     * @return 删除结果
     */
    DeleteCategoryResponse deleteCategory(Long cateId);

    /**
     * 获取图片上传地址和凭证
     *
     * @param req 上传图片参数
     * @return 创建上传图片结果
     */
    CreateUploadImageResponse createUploadImage(AliCreateUploadImageRequest req);

    /**
     * 更新分类
     *
     * @param req 更新分类参数
     * @return 更新结果
     */
    UpdateCategoryResponse updateCategory(AliUpdateCategoryRequest req);

    /**
     * 刷新视频上传凭证
     *
     * @param req 刷新视频上传凭证参数
     * @return 刷新视频上传凭证结果
     */
    RefreshUploadVideoResponse refreshUploadVideo(AliRefreshUploadVideoRequest req);

    /**
     * 获取转码模板组列表
     *
     * @param appId 应用ID
     * @return 转码模板组列表
     */
    ListTranscodeTemplateGroupResponse listTranscodeTemplateGroup(String appId);

    /**
     * 获取应用信息列表
     *
     * @param req 获取应用信息列表参数
     * @return 应用信息列表
     */
    ListAppInfoResponse listAppInfo(AliListAppInfoRequest req);

    /**
     * 获取转码模板组信息
     *
     * @param transcodeTemplateGroupId 转码模板组ID
     * @return 转码模板组信息
     */
    GetTranscodeTemplateGroupResponse getTranscodeTemplateGroup(String transcodeTemplateGroupId);

    /**
     * 视频点播-媒体处理-图片管理-获取单个图片信息
     *
     * @param req 获取图片信息参数
     * @return 图片信息
     */
    GetImageInfoResponse getImageInfo(AliGetImageInfoRequest req);

    /**
     * 批量获取图片信息
     *
     * @param req 批量获取图片信息参数
     * @return 图片信息列表
     */
    GetImageInfosResponse getImageInfos(AliGetImageInfosRequest req);

    /**
     * 删除图片或视频截图
     *
     * @param req 删除图片或视频截图参数
     * @return 删除结果
     */
    DeleteImageResponse deleteImage(AliDeleteImageRequest req);

    /**
     * 提交转码作业
     *
     * @param req 提交转码作业参数
     * @return 转码作业列表
     */
    SubmitTranscodeJobsResponse submitTranscodeJobs(AliSubmitTranscodeJobsRequest req);

    /**
     * 获取上传图片或视频截图的详情
     *
     * @param req 获取上传图片或视频截图的详情参数
     * @return 上传图片或视频截图的详情
     */
    GetUploadDetailsResponse getUploadDetails(AliGetUploadDetailsRequest req);

    /**
     * 更新云转推的
     * @param req 更新云转推的参数
     * @return 更新结果
     */
    UpdateLivePullToPushResponse updateLivePullToPush(AliUpdateLivePullToPushRequest req);

    /**
     * 获取视频信息列表
     *
     * @param mediaIds 视频ID列表
     * @return 视频信息列表
     */
    GetVideoInfosResponse getVideoInfos(List<String> mediaIds);

    /**
     * 获取视频播放信息
     *
     * @param req 获取视频播放信息参数
     * @return 视频播放信息
     */
    GetPlayInfoResponse getPlayInfo(AliGetPlayInfoRequest req);

    /**
     * 获取视频播放凭证
     *
     * @param req 获取视频播放凭证参数
     * @return 播放凭证
     */
    GetVideoPlayAuthResponse getVideoPlayAuth(AliGetVideoPlayAuthRequest req);

    /**
     * 更新图片信息
     *
     * @param req@return 更新结果
     */
    UpdateImageInfosResponse updateImageInfos(AliUpdateImageInfosRequest req);

    /**
     * 创建应用
     * @param req 创建应用参数
     * @return 应用信息
     */
    CreateAppInfoResponse createAppInfo(AliCreateAppInfoRequest req);

    /**
     * 添加转码模板组
     *
     * @param req 添加转码模板组参数
     * @return 转码模板组信息
     */
    AddTranscodeTemplateGroupResponse addTranscodeTemplateGroup(AliAddTranscodeTemplateGroupRequest req);

    /**
     * 修改转码模板组
     *
     * @param req 修改转码模板组参数
     * @return 转码模板组信息
     */
    UpdateTranscodeTemplateGroupResponse updateTranscodeTemplateGroup(AliUpdateTranscodeTemplateGroupRequest req);

    /**
     *  设置默认转码模板组
     * @param req 设置默认转码模板组参数
     * @return 设置结果
     */
    SetDefaultTranscodeTemplateGroupResponse setDefaultTranscodeTemplateGroup(AliSetDefaultTranscodeTemplateGroupRequest req);

    /**
     * 删除转码模板组
     *
     * @param req 删除转码模板组参数
     * @return 删除结果
     */
    DeleteTranscodeTemplateGroupResponse deleteTranscodeTemplateGroup(AliDeleteTranscodeTemplateGroupRequest req);

    /***
     * 创建直播消息应用
     * @param req 创建直播消息应用参数
     * @return 直播消息应用信息
     */
    LiveAuiMessageApp createLiveMessageApp(AliCreateLiveMessageAppRequest req);

    /**
     * 修改互动消息应用回调地址
     * @param eventCallbackUrl 回调地址
     */
    LiveAuiMessageApp updateLiveMessageApp(String eventCallbackUrl);

    /**
     * 删除视频
     *
     * @param req 删除视频参数
     * @return 删除结果
     */
    DeleteVideoResponse deleteVideo(AliDeleteVideoRequest req);
}
