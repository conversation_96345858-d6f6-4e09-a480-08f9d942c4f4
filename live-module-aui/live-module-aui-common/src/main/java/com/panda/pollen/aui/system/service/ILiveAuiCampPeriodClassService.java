package com.panda.pollen.aui.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panda.pollen.aui.system.domain.LiveAuiCampPeriodClass;

import java.util.List;

/**
 * 章节Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
public interface ILiveAuiCampPeriodClassService extends IService<LiveAuiCampPeriodClass> {

    /**
     * 查询章节列表
     * 
     * @param liveAuiCampPeriodClass 章节
     * @return 章节集合
     */
    public List<LiveAuiCampPeriodClass> queryLiveAuiCampPeriodClassList(LiveAuiCampPeriodClass liveAuiCampPeriodClass);
    
}
