package com.panda.pollen.aui.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panda.pollen.aui.system.domain.LiveAuiCampPeriod;

import java.util.List;

/**
 * 训练营营期Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
public interface LiveAuiCampPeriodMapper extends BaseMapper<LiveAuiCampPeriod> {

    /**
     * 查询训练营营期列表
     * 
     * @param liveAuiCampPeriod 训练营营期
     * @return 训练营营期集合
     */
    public List<LiveAuiCampPeriod> selectLiveAuiCampPeriodList(LiveAuiCampPeriod liveAuiCampPeriod);

}
