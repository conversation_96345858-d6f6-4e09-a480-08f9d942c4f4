package com.panda.pollen.aui.sys.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.panda.pollen.aui.model.dto.UserTreeDto;
import com.panda.pollen.aui.model.vo.DeptTreeVo;
import com.panda.pollen.aui.model.vo.DeptUserTreeVO;
import com.panda.pollen.aui.model.vo.UserTreeVo;
import com.panda.pollen.aui.sys.mapper.SystemDeptUserMapper;
import com.panda.pollen.aui.sys.service.SystemDeptUserService;
import com.panda.pollen.common.utils.SecurityUtils;

/**
 * TODO(这里用一句话描述这个类的作用) 
 * ClassName com.panda.pollen.aui.sys.impl.SystemDeptUserServiceImpl 
 * <AUTHOR> 
 * @date 2025年8月30日 下午6:02:28 
 * @version v1.0   
 */
@Service
public class SystemDeptUserServiceImpl implements SystemDeptUserService {
    
    @Autowired
    private SystemDeptUserMapper systemDeptUserMapper;

    @Override
    public List<DeptUserTreeVO> retrieveUserTreeVoList(UserTreeDto param) {
        Long deptId = SecurityUtils.getCompanyId();
        List<DeptTreeVo> deptTreeVos = this.systemDeptUserMapper.retrieveDeptTreeVoList(deptId);
        List<Long> deptIds = deptTreeVos.stream().map(DeptTreeVo::getDeptId).toList();
        param.setDeptIds(deptIds);
        List<Long> userIds = param.getUserIds();
        List<UserTreeVo> userTreeVos = this.systemDeptUserMapper.retrieveUserToDept(param);
        if (userIds != null && !userIds.isEmpty()) {
            userTreeVos = userTreeVos.stream().filter(userTreeVo -> !userIds.contains(userTreeVo.getUserId())).toList();
        }

        // 3. 构建部门Map
        Map<Long, DeptUserTreeVO> deptMap = new HashMap<>();
        for (DeptTreeVo deptTreeVo : deptTreeVos) {
            DeptUserTreeVO node = new DeptUserTreeVO();
            node.setId(deptTreeVo.getDeptId());
            node.setLabel(deptTreeVo.getDeptName());
            node.setType(0);
            node.setChildren(new ArrayList<>());
            deptMap.put(deptTreeVo.getDeptId(), node);
        }
        // 4. 挂载用户到部门
        for (UserTreeVo userTreeVo : userTreeVos) {
            DeptUserTreeVO userNode = new DeptUserTreeVO();
            userNode.setId(userTreeVo.getUserId());
            userNode.setLabel(userTreeVo.getUserName());
            userNode.setType(1);
            userNode.setAvatar(userTreeVo.getAvatar());
            userNode.setChildren(new ArrayList<>());
            DeptUserTreeVO deptNode = deptMap.get(userTreeVo.getDeptId());
            if (deptNode != null) {
                deptNode.getChildren().add(userNode);
            }
        }
        // 5. 组装树
        List<DeptUserTreeVO> tree = new ArrayList<>();
        for (DeptTreeVo deptTreeVo : deptTreeVos) {
            DeptUserTreeVO node = deptMap.get(deptTreeVo.getDeptId());
            if (deptTreeVo.getParentId() == 0 || !deptMap.containsKey(deptTreeVo.getParentId())) {
                // 根部门
                tree.add(node);
            } else {
                DeptUserTreeVO parent = deptMap.get(deptTreeVo.getParentId());
                if (parent != null) {
                    parent.getChildren().add(node);
                }
            }

        }
        return tree;
    }

}
