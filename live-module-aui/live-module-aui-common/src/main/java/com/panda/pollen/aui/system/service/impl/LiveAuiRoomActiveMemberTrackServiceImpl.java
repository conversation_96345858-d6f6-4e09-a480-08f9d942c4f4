package com.panda.pollen.aui.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.aui.model.enums.EnumMessageType;
import com.panda.pollen.aui.system.domain.LiveAuiMessageRecord;
import com.panda.pollen.aui.system.domain.LiveAuiRoomActiveMember;
import com.panda.pollen.aui.system.domain.LiveAuiRoomActiveMemberTrack;
import com.panda.pollen.aui.system.mapper.LiveAuiRoomActiveMemberMapper;
import com.panda.pollen.aui.system.mapper.LiveAuiRoomActiveMemberTrackMapper;
import com.panda.pollen.aui.system.service.ILiveAuiMessageRecordService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomActiveMemberService;
import com.panda.pollen.aui.system.service.ILiveAuiRoomActiveMemberTrackService;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

/**
 * 直播间活跃客户轨迹服务实现类
 *
 * @Author: liuy
 * @Date: 2025年08月29日 16:56
 **/
@Service
public class LiveAuiRoomActiveMemberTrackServiceImpl extends ServiceImpl<LiveAuiRoomActiveMemberTrackMapper, LiveAuiRoomActiveMemberTrack> implements ILiveAuiRoomActiveMemberTrackService {

    @Autowired
    private LiveAuiRoomActiveMemberTrackMapper liveAuiRoomActiveMemberTrackMapper;

    @Autowired
    private LiveAuiRoomActiveMemberMapper liveAuiRoomActiveMemberMapper;

    @Autowired
    private ILiveAuiRoomActiveMemberService liveAuiRoomActiveMemberService;

    @Autowired
    private ILiveAuiMessageRecordService liveAuiMessageRecordService;

    /**
     * 进入直播间时客户的轨迹处理
     * @param customerId
     */
    @Override
    public void joinLiveRoomCustomerTrackHandler(Long customerId, String groupId) {
        // 进入直播间需要更新客户访问次数
        liveAuiRoomActiveMemberService.updateLiveMemberVisitCount(customerId, groupId);
        save(buildBaseTrackInfo(customerId,3));
    }

    /**
     * 离开直播间时客户的轨迹处理
     * @param customerId 客户id
     */
    public void leaveLiveRoomCustomerTrackHandler(Long customerId) {
        LiveAuiRoomActiveMemberTrack track = buildBaseTrackInfo(Long.valueOf(customerId),4);
        // 获取用户上一次加入直播间信息
        LiveAuiRoomActiveMemberTrack lastTimeJoinTrack = liveAuiRoomActiveMemberTrackMapper.getLastTimeJoinTrack(customerId);
        if(ObjectUtil.isEmpty(lastTimeJoinTrack)){
            throw new ServiceException("未获取到成语上一次离开直播间的数据，customerId:{}", customerId);
        }
        // 计算用户停留时长（秒）
        long diff = DateUtils.getNowDate().getTime() - lastTimeJoinTrack.getEventTime().toEpochSecond(ZoneOffset.of("+8"));
        track.setDuration((int) (diff/1000));
        save(track);
        // 处理客户数据
        customerUserDataHandler(customerId, track);
    }

    /**
     * 构建轨迹基本信息
     */
    private LiveAuiRoomActiveMemberTrack buildBaseTrackInfo(Long customerId,Integer eventType) {
        if (customerId == null){
            throw new ServiceException("uid为空,数据异常");
        }
        // 根据uid查询客户信息
        LiveAuiRoomActiveMember member = liveAuiRoomActiveMemberService.getById(customerId);
        if(ObjectUtil.isEmpty(member) || member.getDeleted() != 0){
            throw new ServiceException("客户信息不存在或已删除,请检查参数");
        }

        LiveAuiRoomActiveMemberTrack track = new LiveAuiRoomActiveMemberTrack();
        track.setGroupId(member.getGroupId());
        track.setCustomerId(member.getCustomerId());
        track.setCustomerName(member.getCustomerName());
        track.setEventTime(LocalDateTime.now());
        track.setEventType(eventType);
        return track;
    }

    /**
     * 计算用户相关数据
     * @param track
     * @return
     */
    private void customerUserDataHandler(Long customerId, LiveAuiRoomActiveMemberTrack track) {
        List<LiveAuiRoomActiveMemberTrack> trackList = getLiveGroupUserTrackList(track.getCustomerId(), track.getRoomId(), 4);
        if(CollUtil.isEmpty(trackList)){
            throw new ServiceException("离开直播间时未获取到有效用户轨迹！");
        }
        // 1、计算听课总时长（分钟） eventType为4时的停留时长总和
        Integer totalDuration = trackList.stream()
                .filter(item -> item.getDuration() != null)
                .mapToInt(item -> Math.toIntExact(item.getDuration() / 60))
                .sum();
        // 2、计算持续最长听课时长（分钟） eventType为4时的停留时长最长的数据
        Integer maxDuration = trackList.stream()
                .filter(item -> item.getDuration() != null)
                .mapToInt(item -> Math.toIntExact(item.getDuration() / 60))
                .max()
                .orElse(0);
        LiveAuiRoomActiveMember liveAuiRoomActiveMember = liveAuiRoomActiveMemberMapper.selectById(Long.valueOf(customerId));
        if(ObjectUtil.isEmpty(liveAuiRoomActiveMember)){
            throw new ServiceException("未查询到直播间客户，customerId:{}", customerId);
        }
        liveAuiRoomActiveMember.setWatchDuration(totalDuration);
        liveAuiRoomActiveMember.setContinueWatchDuration(maxDuration);
        liveAuiRoomActiveMemberMapper.updateById(liveAuiRoomActiveMember);
    }

    /**
     * 获取用户直播间的轨迹
     * @param roomId
     * @param customerId
     * @return
     */
    @Override
    public List<LiveAuiRoomActiveMemberTrack> getLiveGroupUserTrackList(Long roomId, Long customerId, Integer eventType) {
        LambdaQueryWrapper<LiveAuiRoomActiveMemberTrack> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LiveAuiRoomActiveMemberTrack::getRoomId, roomId);
        queryWrapper.eq(LiveAuiRoomActiveMemberTrack::getCustomerId, customerId);
        if(eventType != null){
            queryWrapper.eq(LiveAuiRoomActiveMemberTrack::getEventType, eventType);
        }
        queryWrapper.orderByAsc(LiveAuiRoomActiveMemberTrack::getEventTime);
        return list(queryWrapper);
    }

    /**
     * 获取用户在直播间的消息记录
     * @param customerId 用户id
     * @param roomId 群组id
     * @param msgType 消息类型
     * @return
     */
    @Override
    public List<LiveAuiMessageRecord> getLiveRoomMessageRecordList(Long customerId, Long roomId, EnumMessageType msgType) {
        LambdaQueryWrapper<LiveAuiMessageRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LiveAuiMessageRecord::getRoomId, roomId);
        queryWrapper.eq(LiveAuiMessageRecord::getMsgType, msgType.getCode());
        if(EnumMessageType.COMMENT == msgType){
            queryWrapper.eq(LiveAuiMessageRecord::getSenderId, customerId);
        }
        if(EnumMessageType.PRIVATE_LETTER == msgType){
            queryWrapper.eq(LiveAuiMessageRecord::getReceiverId, customerId);
        }
        queryWrapper.eq(LiveAuiMessageRecord::getDeleted, 0);
        queryWrapper.orderByAsc(LiveAuiMessageRecord::getSendTime);
        return liveAuiMessageRecordService.list(queryWrapper);
    }
}
