package com.panda.pollen.aui.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * <p> 阿里云环境参数配置<p/>
 * ClassName com.panda.pollen.aui.service.AliCloudConfig 
 * <AUTHOR> 
 * @date 2025年8月15日 下午9:51:01 
 * @version v1.0   
 */
@Data
@Configuration
public class AliCloudConfig {
    
    @Value("${biz.openapi.access.key}")
    private String accessKeyId;
    @Value("${biz.openapi.access.secret}")
    private String accessKeySecret;
    @Value("${biz.new_im.appId}")
    private String imAppId;
    @Value("${biz.live_stream.push_url}")
    private String liveStreamPushUrl;
    @Value("${biz.live_stream.pull_url}")
    private String liveStreamPullUrl;
    @Value("${biz.live_stream.push_auth_key}")
    private String liveStreamPushAuthKey;
    @Value("${biz.live_stream.pull_auth_key}")
    private String liveStreamPullAuthKey;
    @Value("${biz.live_stream.app_name}")
    private String liveStreamAppName;
    @Value("${biz.live_stream.auth_expires}")
    private Long liveStreamAuthExpires;
    @Value("${biz.live_mic.app_id}")
    private String liveMicAppId;
    @Value("${biz.live_mic.app_key}")
    private String liveMicAppKey;
    @Value("${biz.live_callback.auth_key}")
    private String liveCallbackAuthKey;
    @Value("${biz.new_im.appId}")
    private String appId;
    @Value("${biz.new_im.appKey}")
    private String appKey;
    @Value("${biz.new_im.appSign}")
    private String appSign;
    @Value("${biz.new_im.notifyDomain}")
    private String notifyDomain;
    
}
