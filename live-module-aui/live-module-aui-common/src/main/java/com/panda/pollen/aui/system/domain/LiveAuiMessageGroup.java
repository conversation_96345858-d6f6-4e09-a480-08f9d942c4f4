package com.panda.pollen.aui.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.panda.pollen.common.annotation.Excel;
import com.panda.pollen.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;

/**
 * 直播互动群组对象 live_aui_message_group
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "live_aui_message_group")
public class LiveAuiMessageGroup extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 直播间id
     */
    @Excel(name = "直播间id")
    private Long roomInfoId;

    /**
     * 互动消息应用ID
     */
    @Excel(name = "互动消息应用ID")
    private String appid;

    /**
     * 群组ID
     */
    @Excel(name = "群组ID")
    private String groupId;

    /**
     * 群组名称
     */
    @Excel(name = "群组名称")
    private String groupName;

    /**
     * 群组扩展信息
     */
    @Excel(name = "群组扩展信息")
    private String groupInfo;

    /**
     * 数据中心
     */
    @Excel(name = "数据中心")
    private String dataCenter;

    /**
     * 管理员ID;最多三人
     */
    @Excel(name = "管理员ID;最多三人")
    private String administrators;

    /**
     * 请求ID
     */
    @Excel(name = "请求ID")
    private String requestId;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private Long status;

    /**
     * 删除标识
     */
    @Excel(name = "删除标识")
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /** 备注 */
    @TableField(exist = false)
    @JsonIgnore
    private String remark;

    @TableField(exist = false)
    @JsonIgnore
    private Integer oper;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId()).append("appid", getAppid()).append("groupId", getGroupId()).append("groupName", getGroupName()).append("groupInfo", getGroupInfo()).append("dataCenter", getDataCenter()).append("administrators", getAdministrators()).append("requestId", getRequestId()).append("status", getStatus()).append("deleted", getDeleted()).append("createBy", getCreateBy()).append("createTime", getCreateTime()).append("updateBy", getUpdateBy()).append("updateTime", getUpdateTime()).toString();
    }
}
