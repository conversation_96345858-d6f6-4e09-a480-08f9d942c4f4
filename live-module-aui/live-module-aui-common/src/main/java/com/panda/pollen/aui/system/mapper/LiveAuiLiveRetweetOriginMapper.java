package com.panda.pollen.aui.system.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panda.pollen.aui.system.domain.LiveAuiLiveRetweetOrigin;

/**
 * 云转推管理(源流URL地址列)Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface LiveAuiLiveRetweetOriginMapper extends BaseMapper<LiveAuiLiveRetweetOrigin> {

    /**
     * 查询云转推管理(源流URL地址列)列表
     * 
     * @param liveAuiLiveRetweetOrigin 云转推管理(源流URL地址列)
     * @return 云转推管理(源流URL地址列)集合
     */
    List<LiveAuiLiveRetweetOrigin> selectLiveAuiLiveRetweetOriginList(LiveAuiLiveRetweetOrigin liveAuiLiveRetweetOrigin);

}
