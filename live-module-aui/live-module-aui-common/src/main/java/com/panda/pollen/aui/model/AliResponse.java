package com.panda.pollen.aui.model;

import java.util.List;

import com.aliyuncs.live.model.v20161101.CheckLiveMessageUsersOnlineResponse;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p> 阿里接口响应结果对象<p/>
 * ClassName com.panda.pollen.aui.model.AliResponse 
 * <AUTHOR> 
 * @date 2025年8月14日 下午9:03:29 
 * @version v1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AliResponse {

    private String requestId;

    private String msgTid;
    
    private List<CheckLiveMessageUsersOnlineResponse.Users> users;
    
    public boolean isSuccess() {
        return StrUtil.isNotEmpty(requestId);
    }
    
}
