<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.aui.system.mapper.LiveAuiRobotPoolMapper">
    <resultMap type="com.panda.pollen.aui.system.domain.LiveAuiRobotPool" id="LiveAuiRobotPoolResult">
        <result property="id" column="id"/>
        <result property="robotCode" column="robot_code"/>
        <result property="nickname" column="nickname"/>
        <result property="avatarUrl" column="avatar_url"/>
        <result property="deleted" column="deleted"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectLiveAuiRobotPoolVo">
        select id,
               robot_code,
               nickname,
               avatar_url,
               deleted,
               create_by,
               create_time,
               update_by,
               update_time
        from live_aui_robot_pool
    </sql>

    <select id="selectLiveAuiRobotPoolList" parameterType="com.panda.pollen.aui.system.domain.LiveAuiRobotPool"
            resultMap="LiveAuiRobotPoolResult">
        <include refid="selectLiveAuiRobotPoolVo"/>
        <where>
            <if test="robotCode != null  and robotCode != ''">
                and robot_code = #{robotCode}
            </if>
            <if test="nickname != null  and nickname != ''">
                and nickname like concat('%', #{nickname}, '%')
            </if>
            <if test="avatarUrl != null  and avatarUrl != ''">
                and avatar_url = #{avatarUrl}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
        </where>
    </select>

    <select id="selectLiveAuiRobotPoolById" parameterType="Long" resultMap="LiveAuiRobotPoolResult">
        <include refid="selectLiveAuiRobotPoolVo"/>
        where id = #{id}
    </select>
</mapper>