<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.domain.mapper.DomainInfoMapper">
    
    <resultMap type="com.panda.pollen.modules.ads.domain.DomainInfo" id="DomainInfoResult">
        <result property="id"    column="id"    />
        <result property="companyId"    column="company_id"    />
        <result property="companyName"    column="company_name"    />
        <result property="domainName"    column="domain_name"    />
        <result property="domainUrl"    column="domain_url"    />
        <result property="expiresIn"    column="expires_in"    />
        <result property="validityDays"    column="validity_days"    />
        <result property="deleted"    column="deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDomainInfoVo">
        select id, company_id, company_name, domain_name, domain_url, expires_in, validity_days, deleted, create_by, update_by, create_time, update_time, remark from ads_domain_info
    </sql>

    <select id="selectDomainInfoList" parameterType="com.panda.pollen.modules.ads.domain.DomainInfo" resultMap="DomainInfoResult">
        <include refid="selectDomainInfoVo"/>
        <where>
            and deleted = 0
            <if test="companyId != null"> and company_id = #{companyId}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="domainName != null  and domainName != ''"> and domain_name like concat('%', #{domainName}, '%')</if>
            <if test="domainUrl != null  and domainUrl != ''"> and domain_url like concat('%', #{domainUrl}, '%')</if>
            <if test="expiresIn != null "> and expires_in = #{expiresIn}</if>
            <if test="validityDays != null "> and validity_days &lt;= #{validityDays}</if>
            <if test="deleted != null "> and deleted = #{deleted}</if>
            <if test="createBy != null and createBy != '' "> and create_by = #{createBy}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectDomainInfoById" parameterType="Long" resultMap="DomainInfoResult">
        <include refid="selectDomainInfoVo"/>
        where id = #{id}
    </select>

    <update id="removeDomainByIds" parameterType="java.util.Set">
        update ads_domain_info set deleted = 1 where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="removeDomainByCompanyId" parameterType="com.panda.pollen.modules.ads.vo.DomainInfoVO">
        UPDATE ads_domain_info
        <set>deleted = 1</set>
        <where>
            deleted = 0
            <if test="companyId != null">
                AND company_id = #{companyId}
            </if>
            <if test="domainUrls != null and domainUrls.size() > 0">
                AND domain_url IN
                <foreach collection="domainUrls" item="url" open="(" separator="," close=")">
                    #{url}
                </foreach>
            </if>
        </where>
    </update>

    <select id="findDomainInfoByCompanyIdAndDomainUrl" resultType="Integer">
        select count(1) from ads_domain_info where deleted=0 and company_id = #{companyId} and domain_url = #{domainUrl} limit 1;
    </select>

    <select id="selectDomainUrlByCompanyId" parameterType="Long" resultType="String" >
        select domain_url from ads_domain_info where deleted=0 and company_id = #{companyId};
    </select>

</mapper>