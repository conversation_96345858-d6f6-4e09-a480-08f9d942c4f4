<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.ads.mapper.MediaUserInfoMapper">
    
    <resultMap type="com.panda.pollen.modules.ads.domain.MediaUserInfo" id="MediaUserInfoResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="email"    column="email"    />
        <result property="displayName"    column="display_name"    />
        <result property="accessToken"    column="access_token"    />
        <result property="expiresIn"    column="expires_in"    />
        <result property="refreshToken"    column="refresh_token"    />
        <result property="refreshTokenExpiresIn"    column="refresh_token_expires_in"    />
        <result property="tokenRefreshErrorMsg"    column="token_refresh_error_msg"    />
    </resultMap>

    <sql id="selectMediaUserInfoVo">
        select id, user_id, email, display_name, access_token, expires_in, refresh_token, refresh_token_expires_in, token_refresh_error_msg from ads_media_user_info
    </sql>

    <select id="selectMediaUserInfoList" parameterType="com.panda.pollen.modules.ads.domain.MediaUserInfo" resultMap="MediaUserInfoResult">
        <include refid="selectMediaUserInfoVo"/>
        <where>  
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="displayName != null  and displayName != ''"> and display_name like concat('%', #{displayName}, '%')</if>
        </where>
    </select>
    
    <select id="selectMediaUserInfoById" parameterType="Long" resultMap="MediaUserInfoResult">
        <include refid="selectMediaUserInfoVo"/>
        where id = #{id}
    </select>

</mapper>