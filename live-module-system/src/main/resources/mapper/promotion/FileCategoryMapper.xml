<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.ads.mapper.FileCategoryMapper">
    
    <resultMap type="com.panda.pollen.modules.ads.domain.FileCategory" id="FileCategoryResult">
        <result property="id"    		column="id"    />
        <result property="categoryName" column="category_name"    />
        <result property="communal"    	column="communal"    />
        <result property="remark"    	column="remark"    />
        <result property="createBy"    	column="create_by"    />
        <result property="createTime"  	column="create_time"    />
        <result property="updateBy"    	column="update_by"    />
        <result property="updateTime"   column="update_time"    />
    </resultMap>

    <sql id="selectFileCategoryVo">
        select id, category_name, communal, remark, create_by, create_time, update_by, update_time from sys_file_category
    </sql>

    <select id="selectFileCategoryList" parameterType="com.panda.pollen.modules.ads.domain.FileCategory" resultMap="FileCategoryResult">
        select c.id, c.category_name, c.communal, c.remark 
        	from sys_file_category c
        <where>  
        	deleted = 0 and (c.create_by = #{createBy} or c.communal = 1)
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
        </where>
		order by c.communal DESC, c.create_time DESC
    </select>
    
    <select id="selectFileCategoryById" parameterType="String" resultMap="FileCategoryResult">
        <include refid="selectFileCategoryVo"/>
        where id = #{id}
    </select>

</mapper>