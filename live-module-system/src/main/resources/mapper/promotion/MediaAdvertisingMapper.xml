<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.ads.mapper.MediaAdvertisingMapper">

    <!--查询媒体广告列表计划-->
    <select id="selectMediaAdvertisingList" resultType="com.panda.pollen.modules.ads.vo.MediaAdvertisingListVO">
        select * from ads_media_advertising t
        <where>
            <if test="dto.promotionId != null "> and t.promotion_id = #{dto.promotionId}</if>
            <if test="dto.advertiserId != null "> and t.advertiser_id = #{dto.advertiserId}</if>
            <if test="dto.promotionIds != null and dto.promotionIds.size() > 0">
            	AND t.promotion_id IN
		        <foreach collection="dto.promotionIds" item="id" open="(" separator="," close=")">
		            #{id}
		        </foreach>
            </if>
        </where>
    </select>

    <!--查询媒体视频集合-->
    <select id="selectAdsMediaAdvertisingList" resultType="com.panda.pollen.modules.ads.vo.MediaMaterialListVO">
        SELECT
            amm.id,
            amm.material_type,
            amm.url,
            amm.format,
            amm.signature,
            amm.filename,
            amm.source,
            amm.poster_url,
            amm.bit_rate,
            amm.duration,
            amma.promotion_id
        FROM ads_media_material_advertising amma
                 INNER JOIN
             ads_media_material amm
             ON
                 amma.video_id=amm.type_id
        WHERE
            amm.advertiser_id=#{advertiserId}
        and
            amma.promotion_id in
        <foreach collection="promotionIdList" item="st" open="(" separator="," close=")">
            #{st}
        </foreach>
    </select>

    <select id="selectPageById" resultType="com.panda.pollen.modules.ads.domain.MediaAdvertising">
        SELECT advertiser_id,
               project_id,
               project_name,
               promotion_id,
               promotion_name,
               id,media_platform_type
        FROM ads_media_advertising
        WHERE id > #{id}
        ORDER BY id
        LIMIT 1000
    </select>

</mapper>