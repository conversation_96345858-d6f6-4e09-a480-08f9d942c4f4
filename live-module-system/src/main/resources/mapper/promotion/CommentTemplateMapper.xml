<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.ads.mapper.CommentTemplateMapper">

    <select id="commentTemplateList" resultType="com.panda.pollen.modules.ads.vo.CommentTemplateListVO">
        SELECT t.* FROM
            ads_comment_template t
        LEFT JOIN sys_user u on u.user_name = t.create_by AND u.del_flag = '0'
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id AND d.del_flag = '0'
        where
            t.deleted = 0
        <if test="templateName!=null and templateName!='' "> AND t.template_name=#{templateName} </if>
        <if test="status!=null"> AND t.status=#{status} </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by t.id desc
    </select>

    <select id="templateList" resultType="com.panda.pollen.modules.ads.vo.CommentTemplateSelectListVO">
        SELECT
        t.id, t.template_name
        FROM
            ads_comment_template t
        LEFT JOIN sys_user u on u.user_name = t.create_by AND u.del_flag = '0'
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id AND d.del_flag = '0'
        where
            t.deleted = 0 AND t.status=1
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        order by t.id desc
    </select>
</mapper>