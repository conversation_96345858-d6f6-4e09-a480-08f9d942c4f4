<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.ads.mapper.PlanOperateRecordMapper">

    <select id="selectByIdLogList" resultType="com.panda.pollen.modules.ads.vo.PlanOperateRecordVO">
        SELECT
        id, trace_id, media_platform_type, business_id, business_name, advertiser_id, advertiser_name, goods_id, operate_type, operate_data,
        max(operate_time) as operate_time, max(operate_state) as operate_state, min(operate_error_message) as operate_error_message,source
        FROM
        ads_plan_operate_record
        <where>
            operate_type!=5 and
            trace_id =(
            SELECT
            trace_id
            FROM
            ads_plan_operate_record
            WHERE
            operate_type!=5 and
            media_platform_type=#{mediaPlatformType}
            <if test="goodsId != null and goodsId != ''">and goods_id=#{goodsId} </if>
            <if test="advertiserId !=null and advertiserId != ''">and advertiser_id=#{advertiserId}</if>
            <if test="operateState != null "> and operate_state = #{operateState}</if>
            <if test="operateType != null and operateType != ''">and operate_type=#{operateType} </if>
            order by id desc
            limit 1
            )
            <if test="goodsId != null and goodsId != ''">and goods_id=#{goodsId} </if>
            <if test="businessId != null  and businessId != ''"> and business_id = #{businessId}</if>
            <if test="businessName != null  and businessName != ''"> and business_name like concat('%', #{businessName}, '%')</if>
            <if test="operateState != null "> and operate_state = #{operateState}</if>
            <if test="advertiserId != null "> and advertiser_id = #{advertiserId}</if>
            <if test="operateType != null and operateType != ''">and operate_type=#{operateType} </if>
        </where>
        GROUP BY business_id
        ORDER BY
        operate_state,
        operate_time DESC
    </select>

    <select id="selectModificationRecord" resultType="com.panda.pollen.modules.ads.vo.PlanOperateRecordVO">
        SELECT id,
        trace_id,
        media_platform_type,
        business_id,
        business_name,
        advertiser_id,
        advertiser_name,
        goods_id,
        operate_type,
        operate_data,
        operate_time,
        operate_state,
        operate_error_message,
        create_by as operateBy,
        create_time,
        update_by,
        update_time,
        source
        FROM
        ads_plan_operate_record d
        <where>
            d.operate_type=5
            <if test="advertiserId != null and advertiserId != ''">and d.advertiser_id=#{advertiserId}</if>
            <if test="source!=null and source!=''">and d.source=#{source}</if>
            <if test="businessId!=null and businessId!=''">and d.business_id=#{businessId}</if>
        </where>
        order by d.operate_time desc
    </select>

</mapper>