<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.ads.mapper.NoticeGroupMapper">

        <select id="queryNoticeGroupList" resultType="com.panda.pollen.modules.ads.domain.NoticeGroup">
                SELECT distinct(t.notice_group_name), t.id, t.create_time, t.update_time, t.create_by, t.update_by,t.business_types,t.notice_group_type FROM
                ads_notice_group t
                LEFT JOIN sys_user u on u.user_name = t.create_by AND u.del_flag = '0'
                LEFT JOIN sys_dept d ON d.dept_id = u.dept_id AND d.del_flag = '0'
                <if test="phonenumber !=null and phonenumber !='' ">
                    LEFT JOIN ads_notice_group_detail g ON g.notice_group_id = t.id
                </if>
                where
                t.deleted = 0
                <if test="noticeGroupName!=null and noticeGroupName!='' "> AND t.notice_group_name like concat('%', #{noticeGroupName}, '%') </if>
                <if test="deptId!=null and deptId!='' "> AND u.dept_id=#{deptId} </if>
                <if test="noticeGroupType !=null and noticeGroupType!='' "> AND t.notice_group_type = #{noticeGroupType} </if>
                <if test="phonenumber !=null and phonenumber !='' ">
                        AND g.phone_number = #{phonenumber}
                </if>
                <if test="createBy!=null and createBy!='' ">
                        AND t.create_by = #{createBy}
                </if>
                <if test="userName!=null and userName!='' "> AND t.id in (
                        SELECT
                        n.notice_group_id
                        FROM
                        sys_user u
                        LEFT JOIN ads_notice_business_join n
                        ON n.business_id = u.user_id
                        WHERE
                        u.user_name = #{userName}
                        OR u.phonenumber = #{userName}
                        )</if>
                <!-- 数据范围过滤 -->
                ${params.dataScope}
                order by t.id desc
        </select>

</mapper>