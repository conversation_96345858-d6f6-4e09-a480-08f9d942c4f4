<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.ads.mapper.MediaMaterialAdvertisingMapper">
    
    <resultMap type="com.panda.pollen.modules.ads.domain.MediaMaterialAdvertising" id="AdsMediaMaterialAdvertisingResult">
        <result property="id"    column="id"    />
        <result property="promotionId"    column="promotion_id"    />
        <result property="imageMode"    column="image_mode"    />
        <result property="videoId"    column="video_id"    />
        <result property="videoCoverId"    column="video_cover_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="videoTemplateType"    column="video_template_type"    />
        <result property="videoTaskIds"    column="video_task_ids"    />
        <result property="materialId"    column="material_id"    />
        <result property="materialStatus"    column="material_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAdsMediaMaterialAdvertisingVo">
        select id, promotion_id, image_mode, video_id, video_cover_id, item_id, video_template_type, video_task_ids, material_id, material_status, create_by, create_time, update_by, update_time, remark from ads_media_material_advertising
    </sql>

    <select id="selectAdsMediaMaterialAdvertisingList" parameterType="com.panda.pollen.modules.ads.domain.MediaMaterialAdvertising" resultMap="AdsMediaMaterialAdvertisingResult">
        <include refid="selectAdsMediaMaterialAdvertisingVo"/>
        <where>  
            <if test="promotionId != null "> and promotion_id = #{promotionId}</if>
            <if test="imageMode != null  and imageMode != ''"> and image_mode = #{imageMode}</if>
            <if test="videoId != null  and videoId != ''"> and video_id = #{videoId}</if>
            <if test="videoCoverId != null  and videoCoverId != ''"> and video_cover_id = #{videoCoverId}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="videoTemplateType != null  and videoTemplateType != ''"> and video_template_type = #{videoTemplateType}</if>
            <if test="videoTaskIds != null  and videoTaskIds != ''"> and video_task_ids = #{videoTaskIds}</if>
            <if test="materialId != null "> and material_id = #{materialId}</if>
            <if test="materialStatus != null  and materialStatus != ''"> and material_status = #{materialStatus}</if>
        </where>
    </select>
    
    <select id="selectAdsMediaMaterialAdvertisingById" parameterType="Long" resultMap="AdsMediaMaterialAdvertisingResult">
        <include refid="selectAdsMediaMaterialAdvertisingVo"/>
        where id = #{id}
    </select>

</mapper>