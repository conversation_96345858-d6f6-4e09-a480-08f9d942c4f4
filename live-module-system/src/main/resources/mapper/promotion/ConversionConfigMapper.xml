<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.ads.mapper.ConversionConfigMapper">

    <resultMap type="com.panda.pollen.modules.ads.domain.ConversionConfig" id="ConversionConfigResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="businessId" column="business_id"/>
        <result property="enableConversion" column="enable_conversion"/>
        <result property="conversionProportion" column="conversion_proportion"/>
        <result property="conversionStartOrderCount" column="conversion_start_order_count"/>
        <result property="enableAmount" column="enable_amount"/>
        <result property="amountProportion" column="amount_proportion"/>
        <result property="deductAreaEnable" column="deduct_area_enable"/>
        <result property="deductAreaMin" column="deduct_area_min"/>
        <result property="deductAreaMax" column="deduct_area_max"/>
        <result property="deductOutsideAreaEnable" column="deduct_outside_area_enable"/>
        <result property="newBid" column="new_bid"/>
        <result property="bid" column="bid"/>
        <result property="conversionDayOrHour" column="conversion_day_or_hour"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
        <result property="remark" column="remark"/>
        <result property="goodsPlatformType" column="goods_platform_type"/>
        <result property="mediaPlatformType" column="media_platform_type"/>
        <result property="delayEnable" column="delay_enable"/>
        <result property="delaySecond" column="delay_second"/>
        <result property="conversionAmountType" column="conversion_amount_type"/>
        <result property="conversionAmount" column="conversion_amount"/>
        <result property="conversionDeductionCsite" column="conversion_deduction_csite"/>
        <result property="conversionDeductionScope" column="conversion_deduction_scope"/>
        <result property="timeSpanConversionProportionJson" column="time_span_conversion_proportion_json"/>
        <result property="conversionDeductionCsiteJson" column="conversion_deduction_csite_json"/>
        <result property="entrapmentEnable" column="entrapment_enable"/>
        <result property="entrapmentCity" column="entrapment_city"/>
        <result property="batchConversionEnable" column="batch_conversion_enable"/>
        <result property="batchNum" column="batch_num"/>
        <result property="batchConversionNum" column="batch_conversion_num"/>
    </resultMap>

    <sql id="selectConversionConfigVo">
        select *
        from ads_conversion_config
    </sql>

    <select id="selectConversionConfigById" parameterType="Long" resultMap="ConversionConfigResult">
        <include refid="selectConversionConfigVo"/>
        where id = #{id}
    </select>

    <select id="getConversionConfigList" resultType="com.panda.pollen.modules.ads.vo.ConversionConfigListVO">
        SELECT t.* FROM ads_conversion_config t
        LEFT JOIN sys_user u ON u.user_name = t.create_by
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
        <where>
            AND u.del_flag = 0
            AND d.del_flag = 0
            ${params.dataScope}
        </where>
        order by t.id desc
    </select>

    <insert id="insertConversionConfig">
        insert into ads_conversion_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="type != null ">type,</if>
            <if test="businessId != null  and businessId != ''">business_id,</if>
            <if test="goodsPlatformType != null  and goodsPlatformType != ''">goods_platform_type,</if>
            <if test="mediaPlatformType != null  and mediaPlatformType != ''">media_platform_type,</if>
            <if test="enableConversion != null ">enable_conversion,</if>
            <if test="conversionProportion != null ">conversion_proportion,</if>
            <if test="conversionStartOrderCount != null ">conversion_start_order_count,</if>
            <if test="deductAreaEnable != null ">deduct_area_enable,</if>
            <if test="deductAreaMin != null ">deduct_area_min,</if>
            <if test="deductAreaMax != null ">deduct_area_max,</if>
            <if test="deductOutsideAreaEnable != null ">deduct_outside_area_enable,</if>
            <if test="newBid != null ">new_bid,</if>
            <if test="bid != null ">bid,</if>
            <if test="conversionDayOrHour != null ">conversion_day_or_hour,</if>
            <if test="enableAmount != null ">enable_amount,</if>
            <if test="amountProportion != null ">amount_proportion,</if>
            <if test="delayEnable != null ">delay_enable,</if>
            <if test="delaySecond != null ">delay_second,</if>
            <if test="createBy != null ">create_by,</if>
            <if test="updateBy != null  and updateBy != ''">update_by,</if>
            <if test="remark != null ">remark,</if>
            <if test="conversionAmountType != null ">conversion_amount_type,</if>
            <if test="conversionAmount != null ">conversion_amount,</if>
            <if test="conversionDeductionCsite != null ">conversion_deduction_csite,</if>
            <if test="conversionDeductionScope != null ">conversion_deduction_scope,</if>
            <if test="timeSpanConversionProportionJson != null ">time_span_conversion_proportion_json,</if>
            <if test="conversionDeductionCsiteJson != null ">conversion_deduction_csite_json,</if>
            <if test="entrapmentEnable != null ">entrapment_enable,</if>
            <if test="entrapmentCity != null ">entrapment_city,</if>
            <if test="batchConversionEnable != null ">batch_conversion_enable,</if>
            <if test="batchNum != null ">batch_num,</if>
            <if test="batchConversionNum != null ">batch_conversion_num,</if>
            create_time,update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="type != null ">#{type},</if>
            <if test="businessId != null  and businessId != ''">#{businessId},</if>
            <if test="goodsPlatformType != null  and goodsPlatformType != ''">#{goodsPlatformType},</if>
            <if test="mediaPlatformType != null  and mediaPlatformType != ''">#{mediaPlatformType},</if>
            <if test="enableConversion != null ">#{enableConversion},</if>
            <if test="conversionProportion != null ">#{conversionProportion},</if>
            <if test="conversionStartOrderCount != null ">#{conversionStartOrderCount},</if>
            <if test="deductAreaEnable != null ">#{deductAreaEnable},</if>
            <if test="deductAreaMin != null ">#{deductAreaMin},</if>
            <if test="deductAreaMax != null ">#{deductAreaMax},</if>
            <if test="deductOutsideAreaEnable != null ">#{deductOutsideAreaEnable},</if>
            <if test="newBid != null ">#{newBid},</if>
            <if test="bid != null ">#{bid},</if>
            <if test="conversionDayOrHour != null ">#{conversionDayOrHour},</if>
            <if test="enableAmount != null ">#{enableAmount},</if>
            <if test="amountProportion != null ">#{amountProportion},</if>
            <if test="delayEnable != null ">#{delayEnable},</if>
            <if test="delaySecond != null ">#{delaySecond},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null  and updateBy != ''">#{updateBy},</if>
            <if test="remark != null ">remark,</if>
            <if test="conversionAmountType != null ">#{conversionAmountType},</if>
            <if test="conversionAmount != null ">#{conversionAmount},</if>
            <if test="conversionDeductionCsite != null ">#{conversionDeductionCsite},</if>
            <if test="conversionDeductionScope != null ">#{conversionDeductionScope},</if>
            <if test="timeSpanConversionProportionJson != null ">#{timeSpanConversionProportionJson},</if>
            <if test="conversionDeductionCsiteJson != null ">#{conversionDeductionCsiteJson},</if>
            <if test="entrapmentEnable != null ">#{entrapmentEnable},</if>
            <if test="entrapmentCity != null ">#{entrapmentCity},</if>
            <if test="batchConversionEnable != null ">#{batchConversionEnable},</if>
            <if test="batchNum != null ">#{batchNum},</if>
            <if test="batchConversionNum != null ">#{batchConversionNum},</if>
            sysdate(),sysdate()
        </trim>
    </insert>

    <update id="updateConversionConfig">
        update ads_conversion_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null ">type = #{type},</if>
            <if test="businessId != null  and businessId != ''">business_id = #{businessId},</if>
            <if test="goodsPlatformType != null  and goodsPlatformType != ''">goods_platform_type = #{goodsPlatformType},</if>
            <if test="mediaPlatformType != null  and mediaPlatformType != ''">media_platform_type = #{mediaPlatformType},</if>
            <if test="enableConversion != null ">enable_conversion = #{enableConversion},</if>
            <if test="conversionProportion != null ">conversion_proportion = #{conversionProportion},</if>
            <if test="conversionStartOrderCount != null ">conversion_start_order_count = #{conversionStartOrderCount},</if>
            <if test="deductAreaEnable != null ">deduct_area_enable = #{deductAreaEnable},</if>
            <if test="deductAreaMin != null ">deduct_area_min = #{deductAreaMin},</if>
            <if test="deductAreaMax != null ">deduct_area_max = #{deductAreaMax},</if>
            <if test="deductOutsideAreaEnable != null ">deduct_outside_area_enable = #{deductOutsideAreaEnable},</if>
            <if test="newBid != null ">new_bid = #{newBid},</if>
            <if test="bid != null ">bid = #{bid},</if>
            <if test="conversionDayOrHour != null ">conversion_day_or_hour = #{conversionDayOrHour},</if>
            <if test="enableAmount != null ">enable_amount = #{enableAmount},</if>
            <if test="amountProportion != null ">amount_proportion = #{amountProportion},</if>
            <if test="delayEnable != null ">delay_enable = #{delayEnable},</if>
            <if test="delaySecond != null ">delay_second = #{delaySecond},</if>
            <if test="updateBy != null  and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null ">remark = #{remark},</if>
            <if test="deleted != null ">deleted = #{deleted},</if>
            <if test="conversionAmountType != null ">conversion_amount_type = #{conversionAmountType},</if>
            <if test="conversionAmount != null ">conversion_amount = #{conversionAmount},</if>
            <if test="conversionDeductionCsite != null ">conversion_deduction_csite = #{conversionDeductionCsite},</if>
            <if test="conversionDeductionScope != null ">conversion_deduction_scope = #{conversionDeductionScope},</if>
            <if test="timeSpanConversionProportionJson != null ">time_span_conversion_proportion_json = #{timeSpanConversionProportionJson},</if>
            <if test="conversionDeductionCsiteJson != null ">conversion_deduction_csite_json = #{conversionDeductionCsiteJson},</if>
            <if test="entrapmentEnable != null ">entrapment_enable = #{entrapmentEnable},</if>
            <if test="entrapmentCity != null ">entrapment_city = #{entrapmentCity},</if>
            <if test="batchConversionEnable != null ">batch_conversion_enable = #{batchConversionEnable},</if>
            <if test="batchNum != null ">batch_num = #{batchNum},</if>
            <if test="batchConversionNum != null ">batch_conversion_num = #{batchConversionNum},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

</mapper>