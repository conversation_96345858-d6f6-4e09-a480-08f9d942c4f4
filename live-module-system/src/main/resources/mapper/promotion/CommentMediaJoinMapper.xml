<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.ads.mapper.CommentMediaJoinMapper">

<select id="getDetails" resultType="com.panda.pollen.modules.ads.vo.CommentTemplateSelectListVO">
    SELECT
        te.id,
        te.template_name
    FROM
        ads_comment_media_join acm
            INNER JOIN ads_comment_template te ON acm.template_id = te.id
    WHERE
         te.deleted = 0
      AND te.`status` =1
      AND acm.advertiser_id=#{advertiserId}
     AND acm.media_type=#{mediaType}
    </select>

    <select id="selectTokenAndadvertiserIdList" resultType="com.panda.pollen.modules.ads.vo.CommentMediaJoinListVO">
        SELECT
            info.advertiser_id,
            info.access_token,
            info.evaluate_start_time,
            info.emotion_type
        FROM
            ads_media_account_info info
        WHERE
            media_type = #{mediaType} AND deleted=0 AND control_status=2
    </select>

    <select id="listCommentMediaJoin" resultType="com.panda.pollen.modules.ads.vo.CommentMediaJoinGetVO">
        SELECT
            t.`comment`,
            t.reply,
            t.top_or_not,
            t.match_type
        FROM
            ads_comment_media_join n
                INNER JOIN
            ads_comment t
            ON
                        n.template_id=t.template_id
                    AND
                        t.deleted=0
        WHERE
            n.advertiser_id=#{advertiserId}
          AND n.media_type=#{mediaType}
    </select>

    <select id="commentMediaJoinGetVOMap" resultType="com.panda.pollen.modules.ads.vo.CommentMediaJoinGetVO">
        SELECT
            t.`comment`,
            t.reply,
            t.top_or_not,
            t.match_type
        FROM
            ads_comment_media_join n
                INNER JOIN
            ads_comment t
            ON
                        n.template_id=t.template_id
                    AND
                        t.deleted=0
                    AND
                        t.status=0
        WHERE
            n.advertiser_id=#{advertiserId}
          AND n.media_type=#{mediaType}
    </select>

    <select id="tiktokList" resultType="java.lang.String" >
        SELECT
            t.trill_id
        FROM
            ads_comment_media_join n
                INNER JOIN
            ads_comment t
            ON
                        n.template_id=t.template_id
                    AND
                        t.deleted=0
                    AND
                        t.status=1
        WHERE
            n.advertiser_id=#{advertiserId}
          AND n.media_type=#{mediaType}
        </select>
</mapper>