<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.ads.mapper.NoticeGroupDetailMapper">


    <select id="queryNoticeGroupDetailListByBusinessInfo" resultType="com.panda.pollen.modules.ads.domain.NoticeGroupDetail">
        select t.* from ads_notice_group_detail t
        LEFT JOIN ads_notice_group t1 on t.notice_group_id = t1.id
        LEFT JOIN ads_notice_business_join t2 on t1.id = t2.notice_group_id
        WHERE t2.business_id = #{businessId} and t2.business_type = #{businessType} and t1.deleted = 0
    </select>

    <select id="queryNoticeGroupDetailListByBusinessInfoAndBusinessTypes" resultType="com.panda.pollen.modules.ads.vo.NoticeGroupDetailVO">
        select t.*,t1.business_types as businessTypes from ads_notice_group_detail t
                            LEFT JOIN ads_notice_group t1 on t.notice_group_id = t1.id
                            LEFT JOIN ads_notice_business_join t2 on t1.id = t2.notice_group_id
        WHERE t2.business_id = #{businessId} and t2.business_type = #{businessType}
        and t1.notice_group_type =#{noticeGroupType} and t1.deleted = 0
    </select>

    <update id="updateNoticeGroupDetailBatch">
        update ads_notice_group_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="phone_number =case" suffix="end,">
                <foreach collection="noticeGroupDetail" item="item" index="index">
                    <if test="item.phoneNumber!=null and item.phoneNumber!='' ">
                        when id=#{item.id} then #{item.phoneNumber}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in (
        <foreach collection="noticeGroupDetail" separator="," item="item" index="index">
            #{item.id}
        </foreach>
        )
    </update>
</mapper>