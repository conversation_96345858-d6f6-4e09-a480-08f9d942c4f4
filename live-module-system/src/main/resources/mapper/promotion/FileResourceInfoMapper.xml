<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.ads.mapper.FileResourceInfoMapper">
    
    <resultMap type="com.panda.pollen.modules.ads.domain.FileResourceInfo" id="FileResourceInfoResult">
        <result property="id"    column="id"    />
        <result property="fileCategory"    column="file_category"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileSuffix"    column="file_suffix"    />
        <result property="newFileName"    column="new_file_name"    />
        <result property="originalFileName"    column="original_file_name"    />
        <result property="bucketName"    column="bucket_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileType"    column="file_type"    />
        <result property="url"    column="url"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectFileResourceInfoVo">
        select id, file_category, file_name, file_suffix, new_file_name, original_file_name, bucket_name, 
        	   file_path, file_size, file_type, url, remark, create_by, create_time, update_by, update_time 
       	from sys_file_resource_info
    </sql>

    <select id="selectFileResourceInfoList" parameterType="com.panda.pollen.modules.ads.domain.FileResourceInfo" resultType="com.panda.pollen.modules.ads.vo.FileResourceInfoVO">
        select r.id, r.file_category, c.category_name as file_category_name, r.file_name, r.file_suffix, r.new_file_name, r.original_file_name, r.bucket_name, 
        	   r.file_path, r.file_size, r.file_type, r.url, r.remark, r.create_by, r.create_time, r.update_by, r.update_time 
       	from sys_file_resource_info r
       		left join sys_file_category c on c.id = r.file_category 
        <where>  
        	r.deleted = 0 and r.create_by = #{createBy}
            <if test="fileCategory != null  and fileCategory != ''"> and r.file_category = #{fileCategory}</if>
            <if test="fileName != null  and fileName != ''"> and r.file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileSuffix != null  and fileSuffix != ''"> and r.file_suffix = #{fileSuffix}</if>
            <if test="originalFileName != null  and originalFileName != ''"> and r.original_file_name like concat('%', #{originalFileName}, '%')</if>
            <if test="bucketName != null  and bucketName != ''"> and r.bucket_name like concat('%', #{bucketName}, '%')</if>
            <if test="fileType != null  and fileType != ''"> and r.file_type = #{fileType}</if>
        </where>
		order by r.id desc
    </select>

    <select id="selectByResourceId" resultType="com.panda.pollen.modules.ads.vo.FileResourceInfoVO">
        select * from sys_file_resource_info where id = #{id}
    </select>
    <select id="selectUrls" resultType="java.lang.String">
        SELECT f.url
        FROM sys_file_resource_info f
        <where>
            f.deleted = 0
            AND f.file_category = '1657921982836342785'
            AND f.file_type='jpeg'
            AND f.file_name LIKE CONCAT(#{goodsId}, '%')
        </where>
        LIMIT 9
    </select>

</mapper>