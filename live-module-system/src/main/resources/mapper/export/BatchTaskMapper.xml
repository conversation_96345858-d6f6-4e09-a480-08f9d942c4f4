<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.export.mapper.BatchTaskMapper">

    <sql id="selectBatchTaskVo">
        select id, media_type, type, category, status, file_resource_id, execute_params, login_user_info, fail_msg, create_time, update_time, create_by, update_by, deleted from ads_batch_task
    </sql>

    <select id="selectBatchTaskList" parameterType="com.panda.pollen.modules.export.vo.BatchTaskListVO" resultType="com.panda.pollen.modules.export.vo.BatchTaskListVO">
        select
        t.id, t.media_type, t.type, t.category, t.status, t.file_resource_id, t.create_time, t.fail_msg, t.create_by,
        s.file_path, s.file_name, t.update_time
        <if test="createBy == null  or createBy == ''">
               ,t.execute_params
        </if>
        from ads_batch_task t
        left join sys_file_resource_info s on t.file_resource_id = s.id
        <where>
            and t.deleted = 0
            <if test="id != null "> and t.id = #{id}</if>
            <if test="mediaType != null "> and t.media_type = #{mediaType}</if>
            <if test="type != null "> and t.type = #{type.value}</if>
            <if test="categoryList != null and categoryList.size() > 0">
                and t.category in
                <foreach collection="categoryList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null "> and t.status = #{status.value}</if>
            <if test="fileResourceId != null "> and t.file_resource_id = #{fileResourceId}</if>
            <if test="executeParams != null  and executeParams != ''"> and t.execute_params = #{executeParams}</if>
            <if test="loginUserInfo != null  and loginUserInfo != ''"> and t.login_user_info = #{loginUserInfo}</if>
            <if test="failMsg != null  and failMsg != ''"> and t.fail_msg = #{failMsg}</if>
            <if test="createBy != null  and createBy != ''"> and t.create_by = #{createBy}</if>
            <if test="startTime != null and startTime != ''"> and t.create_time &gt;= #{startTime} </if>
            <if test="endTime != null and endTime != ''">and t.create_time &lt;= #{endTime} </if>
            <if test="fileName != null and fileName != ''">and s.file_name like concat('%', #{fileName}, '%') </if>
        </where>
        order by id desc
    </select>

    <update id="removeBatchTaskByIds" parameterType="List">
        update ads_batch_task set deleted = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>