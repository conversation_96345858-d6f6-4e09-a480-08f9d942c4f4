<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.system.mapper.SysUserConfigMapper">

    <resultMap type="com.panda.pollen.system.domain.SysUserConfig" id="SysUserConfigResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="configKey" column="config_key"/>
        <result property="configValue" column="config_value"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectUserConfigVo">
        select id, user_id, config_key, config_value, remark, create_by, create_time, update_by, update_time
        from sys_user_config
    </sql>

    <select id="selectUserConfigList" parameterType="SysUserConfig" resultMap="SysUserConfigResult">
        <include refid="selectUserConfigVo"/>
        <where>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="configKey != null and configKey != ''">
                AND config_key = #{configKey}
            </if>
        </where>
    </select>

    <select id="selectUserConfig" resultMap="SysUserConfigResult">
        <include refid="selectUserConfigVo"/>
        where user_id = #{userId} and config_key = #{configKey}
    </select>

    <insert id="batchInsert">
        insert into sys_user_config(id, user_id, config_key, config_value, remark, create_by, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.userId}, #{item.configKey}, #{item.configValue},
            #{item.remark}, #{item.createBy}, sysdate(), sysdate())
        </foreach>
    </insert>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update sys_user_config
            <set>
                <if test="item.configValue != null">config_value = #{item.configValue},</if>
                <if test="item.remark != null">remark = #{item.remark},</if>
                <if test="item.updateBy != null">update_by = #{item.updateBy},</if>
                update_time = sysdate()
            </set>
            where id = #{item.id}
        </foreach>
    </update>

</mapper> 