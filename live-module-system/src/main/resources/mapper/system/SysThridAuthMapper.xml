<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.system.mapper.SysThridAuthMapper">
    
    <resultMap type="com.panda.pollen.system.domain.SysThridAuth" id="SysThridAuthResult">
        <result property="id"    column="id"    />
        <result property="authKey"    column="auth_key"    />
        <result property="authCode"    column="auth_code"    />
        <result property="thirdParty"    column="third_party"    />
        <result property="taobaokePid"    column="taobaoke_pid"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysThridAuthVo">
        select id, auth_key, auth_code, third_party, taobaoke_pid, create_by, create_time, update_by, update_time from sys_thrid_auth
    </sql>

    <select id="selectSysThridAuthList" parameterType="com.panda.pollen.system.domain.SysThridAuth" resultMap="SysThridAuthResult">
        <include refid="selectSysThridAuthVo"/>
        <where>  
            <if test="authKey != null  and authKey != ''"> and auth_key = #{authKey}</if>
            <if test="authCode != null  and authCode != ''"> and auth_code = #{authCode}</if>
            <if test="thirdParty != null  and thirdParty != ''"> and third_party = #{thirdParty}</if>
        </where>
    </select>
    
    <select id="selectSysThridAuthById" parameterType="Long" resultMap="SysThridAuthResult">
        <include refid="selectSysThridAuthVo"/>
        where id = #{id}
    </select>

    <select id="findThridAuthByAuthKey" parameterType="String" resultMap="SysThridAuthResult">
        <include refid="selectSysThridAuthVo"/>
        where auth_key = #{authKey} and deleted = 0
    </select>

</mapper>