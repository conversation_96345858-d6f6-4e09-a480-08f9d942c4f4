<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.modules.ads.mapper.MediaWarnConfigMapper">

    <select id="getMediaWarnConfig" resultType="com.panda.pollen.modules.ads.vo.MediaWarnConfigVo">
        select
           *
        from ads_media_warn_config
        <where>
            <if test="businessId != null and businessId != ''">
                and business_id = #{businessId}
            </if>
            <if test="businessType != null ">
                and business_type = #{businessType}
            </if>
        </where>
    </select>

</mapper>