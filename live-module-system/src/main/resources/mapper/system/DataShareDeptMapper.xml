<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.system.mapper.DataShareDeptMapper">

    <select id="selectDataShareDeptList" resultType="com.panda.pollen.system.domain.vo.DataShareDeptInfoVO">
        select * from sys_data_share_dept t where t.share_id = #{shareId}
    </select>

    <select id="selectBusinessDataWithDataScope" resultType="com.panda.pollen.common.core.domain.entity.SysUser">
        select t.* from ${tableName} t
        left join sys_user u on u.user_name = t.create_by
        left join sys_dept d on d.dept_id = u.dept_id
        where t.${primaryKeyName} = #{businessId}
          and u.del_flag = '0'
          and d.del_flag = '0'
        ${baseEntity.dataScope}
        limit 1
    </select>
</mapper>