<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.system.mapper.SysCategoryBizMapper">
    <select id="getByName" resultType="com.panda.pollen.system.domain.SysCategory">
        SELECT t.*
        FROM sys_category t
                 left join sys_user u on u.user_name = t.create_by and u.del_flag = '0'
                 left join sys_dept d on d.dept_id = u.dept_id and d.del_flag = '0'
        <where>
            t.deleted = FALSE and t.first_dept_id = #{firstDeptId}
            <if test="categoryName != null and categoryName != ''">
                AND t.category_name = #{categoryName}
            </if>
            ${params.dataScope}
        </where>
        limit 1
    </select>


    <select id="list" resultType="com.panda.pollen.system.domain.SysCategory">
        SELECT t.*
        FROM sys_category t
                 left join sys_user u on u.user_name = t.create_by and u.del_flag = '0'
                 left join sys_dept d on d.dept_id = u.dept_id and d.del_flag = '0'
        <where>
            t.deleted = FALSE and t.first_dept_id = #{firstDeptId}
            <if test="null != categoryName and '' != categoryName">
                and t.category_name like concat('%', #{categoryName}, '%')
            </if>
            <if test="null != categoryType and '' != categoryType">
                and t.category_type = #{categoryType}
            </if>
            <if test="null != systemed">
                and t.systemed = #{systemed}
            </if>
            <if test="null != status">
                and t.`status` = #{status}
            </if>
        </where>
        order by t.create_time desc
    </select>
</mapper>
