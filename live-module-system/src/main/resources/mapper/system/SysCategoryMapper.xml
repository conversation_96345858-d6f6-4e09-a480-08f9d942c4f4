<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.system.mapper.SysCategoryMapper">
    <resultMap type="com.panda.pollen.system.domain.SysCategory" id="LiveAuiCourseCategoryResult">
        <result property="id" column="id"/>
        <result property="categoryName" column="category_name"/>
        <result property="categoryType" column="category_type"/>
        <result property="systemed" column="systemed"/>
        <result property="status" column="status"/>
        <result property="deleted" column="deleted"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectLiveAuiCourseCategoryVo">
        select id,
               category_name,
               category_type,
               systemed,
               status,
               deleted,
               create_by,
               create_time,
               update_by,
               update_time
        from sys_category
    </sql>

    <select id="selectLiveAuiCourseCategoryList"
            parameterType="com.panda.pollen.system.domain.SysCategory"
            resultMap="LiveAuiCourseCategoryResult">
        <include refid="selectLiveAuiCourseCategoryVo"/>
        <where>
            <if test="categoryName != null  and categoryName != ''">
                and category_name like concat('%', #{categoryName}, '%')
            </if>
            <if test="categoryType != null  and categoryType != ''">
                and category_type = #{categoryType}
            </if>
            <if test="systemed != null">
                and systemed = #{systemed}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
        </where>
    </select>

    <select id="selectLiveAuiCourseCategoryById" parameterType="Long" resultMap="LiveAuiCourseCategoryResult">
        <include refid="selectLiveAuiCourseCategoryVo"/>
        where id = #{id}
    </select>
</mapper>