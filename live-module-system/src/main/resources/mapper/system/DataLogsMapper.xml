<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.system.mapper.DataLogsMapper">
    
    <resultMap type="com.panda.pollen.system.domain.DataLogs" id="DataLogsResult">
        <result property="id"    			column="id"    />
        <result property="businessId"    	column="business_id"    />
        <result property="businessName"    	column="business_name"    />
        <result property="dataType"    		column="data_type"    />
        <result property="fieldName"    	column="field_name"    />
        <result property="commentName"    	column="comment_name"    />
        <result property="originalValue"    column="original_value"    />
        <result property="currentValue"    	column="current_value"    />
        <result property="createBy"    		column="create_by"    />
        <result property="createTime"    	column="create_time"    />
    </resultMap>

    <sql id="selectDataLogsVo">
        select id, business_id, business_name, data_type, field_name, comment_name, original_value, current_value, create_by, create_time from sys_data_logs
    </sql>

    <select id="selectDataLogsList" parameterType="com.panda.pollen.system.domain.DataLogs" resultMap="DataLogsResult">
        <include refid="selectDataLogsVo"/>
        <where>  
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="businessName != null  and businessName != ''"> and business_name like concat('%', #{businessName}, '%')</if>
            <if test="dataType != null "> and data_type = #{dataType}</if>
            <if test="fieldName != null  and fieldName != ''"> and field_name like concat('%', #{fieldName}, '%')</if>
            <if test="commentName != null  and commentName != ''"> and comment_name like concat('%', #{commentName}, '%')</if>
            <if test="originalValue != null  and originalValue != ''"> and original_value = #{originalValue}</if>
            <if test="currentValue != null  and currentValue != ''"> and current_value = #{currentValue}</if>
        </where>
    </select>
    
    <select id="selectDataLogsById" parameterType="Long" resultMap="DataLogsResult">
        <include refid="selectDataLogsVo"/>
        where id = #{id}
    </select>

</mapper>