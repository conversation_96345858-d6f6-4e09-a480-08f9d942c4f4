<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.system.mapper.SysNoticeReceiverMapper">

    <resultMap type="com.panda.pollen.system.domain.vo.SysNoticeReceiverVO" id="SysNoticeReceiverResult">
        <result property="id" column="id"/>
        <result property="noticeId" column="notice_id"/>
        <result property="receiver" column="receiver"/>
        <result property="receiverName" column="user_name"/>
        <result property="noticeTitle" column="notice_title"/>
        <result property="noticeContent" column="notice_content"/>
        <result property="noticeType" column="notice_type"/>
        <result property="sender" column="sender"/>
        <result property="status" column="status"/>
        <result property="receiverTime" column="receiver_time"/>
        <result property="readTime" column="read_time"/>
    </resultMap>

    <sql id="selectSysNoticeReceiverVo">
        select id, notice_id, receiver, status, receiver_time, read_time, deleted
        from sys_notice_receiver
    </sql>

    <select id="selectSysNoticeReceiverList" parameterType="com.panda.pollen.system.domain.dto.SysNoticeReceiverDTO"
            resultMap="SysNoticeReceiverResult">
        select t.id, t.notice_id, t.receiver, n.notice_title, n.notice_content, n.notice_type,
        u.nick_name as user_name, s.nick_name as sender, t.status, t.receiver_time, t.read_time
        from sys_notice_receiver t
        left join sys_notice n ON n.notice_id = t.notice_id
        left join sys_user u on u.user_id = t.receiver
        left join sys_user s on s.user_name = n.create_by
        <where>
            <if test="receiver != null">and t.receiver = #{receiver}</if>
            <if test="status != null ">and t.status = #{status}</if>
            <if test="noticeType != null ">and n.notice_type = #{noticeType}</if>
            <if test="receiverTime != null ">and t.receiver_time &gt;= #{receiverTime}</if>
            <if test="readTime != null ">and t.read_time = #{readTime}</if>
        </where>
        order by t.status, t.receiver_time desc
    </select>

    <select id="selectUnreadNotices" parameterType="Long" resultMap="SysNoticeReceiverResult">
        select t.id,
               t.notice_id,
               t.receiver,
               n.notice_title,
               n.notice_content,
               n.notice_type,
               u.nick_name as user_name,
               s.nick_name as sender,
               t.status,
               t.receiver_time,
               t.read_time
        from sys_notice_receiver t
                 left join sys_notice n ON n.notice_id = t.notice_id
                 left join sys_user u on u.user_id = t.receiver
                 left join sys_user s on s.user_name = n.create_by
        where t.status = 0
          and t.deleted = 0
          and n.deleted = 0
          and n.status = 0
          and t.receiver = #{receiver}
        limit 20
    </select>

    <select id="selectByNoticeId" parameterType="Long" resultMap="SysNoticeReceiverResult">
        select id, notice_id, receiver, status, receiver_time, read_time
        from sys_notice_receiver t
        where t.notice_id = #{noticeId}
    </select>

    <select id="selectSysNoticeReceiverById" parameterType="Long" resultMap="SysNoticeReceiverResult">
        select t.id,
               t.notice_id,
               t.receiver,
               n.notice_title,
               n.notice_content,
               n.notice_type,
               u.nick_name as user_name,
               s.nick_name as sender,
               t.status,
               t.receiver_time,
               t.read_time
        from sys_notice_receiver t
                 left join sys_notice n ON n.notice_id = t.notice_id
                 left join sys_user u on u.user_id = t.receiver
                 left join sys_user s on s.user_name = n.create_by
        where t.id = #{id}
    </select>

    <insert id="batchSave" parameterType="com.panda.pollen.system.domain.SysNoticeReceiver">
        insert into sys_notice_receiver(id, notice_id, receiver, receiver_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{id}, #{item.noticeId}, #{item.receiver}, sysdate())
        </foreach>
    </insert>

    <delete id="deleteByNoticeId" parameterType="Long">
        delete
        from sys_notice_receiver
        where notice_id = #{noticeId}
    </delete>

    <delete id="deleteByNoticeIds" parameterType="Long">
        delete from sys_notice_receiver where notice_id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>

    <update id="batchChangeStatus">
        update sys_notice_receiver set status = 1, read_time = sysdate() where id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </update>

    <select id="findUnReadCount" resultType="com.panda.pollen.system.domain.vo.SysNoticeCountVO">
        SELECT COALESCE(SUM(IF(sn.notice_type = '1', 1, 0)), 0) AS notice_count,
               COALESCE(SUM(IF(sn.notice_type = '2', 1, 0)), 0) AS bulletin_count,
               COALESCE(SUM(IF(sn.notice_type = '3', 1, 0)), 0) AS upgrade_log_count,
               COALESCE(SUM(IF(sn.notice_type = '4', 1, 0)), 0) AS popup_log_count
        FROM sys_notice_receiver snr
        LEFT JOIN sys_notice sn ON sn.notice_id = snr.notice_id
        WHERE snr.receiver = #{userId}
          and sn.deleted = 0
          and snr.deleted = 0
          AND snr.status = 0
        order by sn.notice_type
    </select>


</mapper>