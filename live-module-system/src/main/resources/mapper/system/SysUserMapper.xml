<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.system.mapper.SysUserMapper">

    <resultMap type="SysUser" id="SysUserResult">
        <id     property="userId"       column="user_id"      />
        <result property="deptId"       column="dept_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="email"        column="email"        />
        <result property="phonenumber"  column="phonenumber"  />
        <result property="sex"          column="sex"          />
        <result property="avatar"       column="avatar"       />
        <result property="password"     column="password"     />
        <result property="status"       column="status"       />
        <result property="delFlag"      column="del_flag"     />
        <result property="loginIp"      column="login_ip"     />
        <result property="loginDate"    column="login_date"   />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
        <result property="commissionRate"    column="commission_rate"   />
        <association property="dept"    column="dept_id" javaType="SysDept" resultMap="deptResult" />
        <collection  property="roles"   javaType="java.util.List"           resultMap="RoleResult" />
    </resultMap>
	
    <resultMap id="deptResult" type="SysDept">
        <id     property="deptId"    column="dept_id"     />
        <result property="parentId"  column="parent_id"   />
        <result property="deptName"  column="dept_name"   />
        <result property="ancestors" column="ancestors"   />
        <result property="orderNum"  column="order_num"   />
        <result property="leader"    column="leader"      />
        <result property="status"    column="dept_status" />
        <result property="firstDeptId"    column="first_dept_id" />
        <result property="firstDeptName"    column="first_dept_name" />
    </resultMap>
	
    <resultMap id="RoleResult" type="SysRole">
        <id     property="roleId"       column="role_id"        />
        <result property="roleName"     column="role_name"      />
        <result property="roleKey"      column="role_key"       />
        <result property="roleSort"     column="role_sort"      />
        <result property="dataScope"     column="data_scope"    />
        <result property="status"       column="role_status"    />
    </resultMap>

    <resultMap id="SysAuthUserResult" type="SysAuthUser">
		<id     property="authId"       column="auth_id"        />
		<result property="uuid"         column="uuid"           />
		<result property="userId"       column="user_id"        />
		<result property="userName"     column="user_name"      />
		<result property="nickName"     column="nick_name"      />
		<result property="avatar"       column="avatar"         />
		<result property="email"        column="email"          />
		<result property="source"       column="source"         />
		<result property="createTime"   column="create_time"    />
	</resultMap>

	<sql id="selectUserVo">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.commission_rate, u.create_by, u.create_time, u.remark, 
        d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status, d.first_dept_id, d.first_dept_name,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>
    
    <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
		select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.commission_rate, u.create_by, u.create_time, u.remark, d.dept_name, d.leader
		, (select GROUP_CONCAT(DISTINCT role_name) from sys_role r join sys_user_role ur on r.role_id = ur.role_id where ur.user_id = u.user_id and r.del_flag = '0') role_name
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		</if>
		<if test="nickName != null and nickName != '' ">
			AND  u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectUserByDept" parameterType="Long" resultMap="SysUserResult">
		select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.commission_rate, u.create_by, u.create_time, u.remark, d.dept_name, d.leader
		, (select GROUP_CONCAT(DISTINCT role_name) from sys_role r join sys_user_role ur on r.role_id = ur.role_id where u.user_id = ur.user_id and r.del_flag = '0') role_name
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0' 
			AND u.dept_id IN (
				WITH RECURSIVE temp AS (SELECT dept_id, parent_id, dept_name FROM sys_dept WHERE dept_id IN 
				<foreach collection="array" item="deptId" open="(" separator="," close=")">
		 			#{deptId}
		        </foreach>
		        UNION ALL
		        SELECT d.dept_id, d.parent_id, d.dept_name FROM sys_dept d 
		        	INNER JOIN temp t ON d.parent_id = t.dept_id 
		        ) SELECT dept_id FROM temp
	        )
	</select>
	
	<select id="selectUserByIds" parameterType="Long" resultMap="SysUserResult">
		select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.commission_rate, u.create_by, u.create_time, u.remark, d.dept_name, d.leader
		, (select GROUP_CONCAT(DISTINCT role_name) from sys_role r join sys_user_role ur on r.role_id = ur.role_id where u.user_id = ur.user_id and r.del_flag = '0') role_name
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0' 
			AND u.user_id IN 
			<foreach collection="array" item="userId" open="(" separator="," close=")">
	 			#{userId}
	        </foreach>
	</select>
	
	<select id="selectAllocatedList" resultType="com.panda.pollen.common.core.domain.vo.SysUserDistributionVO">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time, d.dept_name
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and r.role_id = #{roleId}
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	<select id="selectUnallocatedList" resultType="com.panda.pollen.common.core.domain.vo.SysUserDistributionVO">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>
	
	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName} and u.del_flag = '0'
	</select>
	
	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>

	<select id="checkUserNameUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, user_name from sys_user where user_name = #{userName} limit 1
	</select>
	
	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, phonenumber from sys_user where phonenumber = #{phonenumber} and del_flag = '0' limit 1
	</select>
	
	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, email from sys_user where email = #{email} and del_flag = '0' limit 1
	</select>
	
	<insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
 		insert into sys_user(
 			<if test="userId != null and userId != 0">user_id,</if>
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="nickName != null and nickName != ''">nick_name,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
 			<if test="sex != null and sex != ''">sex,</if>
 			<if test="password != null and password != ''">password,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="remark != null and remark != ''">remark,</if>
			<if test="duoId != null and duoId != ''">duo_id,</if>
			<if test="commissionRate != null ">commission_rate,</if>
 			create_time
 		)values(
 			<if test="userId != null and userId != ''">#{userId},</if>
 			<if test="deptId != null and deptId != ''">#{deptId},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="nickName != null and nickName != ''">#{nickName},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="avatar != null and avatar != ''">#{avatar},</if>
 			<if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
 			<if test="sex != null and sex != ''">#{sex},</if>
 			<if test="password != null and password != ''">#{password},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
			<if test="duoId != null and duoId != ''">#{duoId},</if>
			<if test="commissionRate != null ">#{commissionRate},</if>
 			sysdate()
 		)
	</insert>
	
	<update id="updateUser" parameterType="SysUser">
 		update sys_user
 		<set>
 			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
 			<if test="userName != null and userName != ''">user_name = #{userName},</if>
 			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
 			<if test="email != null ">email = #{email},</if>
 			<if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
 			<if test="sex != null and sex != ''">sex = #{sex},</if>
 			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
 			<if test="password != null and password != ''">password = #{password},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
 			<if test="loginDate != null">login_date = #{loginDate},</if>
 			<if test="commissionRate != null ">commission_rate = #{commissionRate},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			update_time = sysdate()
 		</set>
 		where user_id = #{userId}
	</update>
	
	<update id="updateUserStatus" parameterType="SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>
	
	<update id="updateUserAvatar" parameterType="SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>
	
	<update id="resetUserPwd" parameterType="SysUser">
 		update sys_user set password = #{password}, password_modify_date = now() where user_name = #{userName}
	</update>
	
	<delete id="deleteUserById" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id = #{userId}
 	</delete>
 	
 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach> 
 	</delete>

	<select id="selectById" resultType="com.panda.pollen.modules.domain.vo.UserVO">
		select
			u.user_id,
			d.dept_id,
			u.user_name,
			d.dept_name,
			d.first_dept_id,
			d.first_dept_name,
			u.commission_rate
		from sys_user u,sys_dept d
		where u.dept_id = d.dept_id
		  and u.user_id = #{userId}
		  and u.del_flag = 0
	</select>

	<!--通知组用户集合-->
	<select id="selectUserNotice" resultType="SysUserNoticeVO" parameterType="com.panda.pollen.common.core.domain.dto.NoticeGroupUserDTO">
		select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phoneNumber != null and phoneNumber != ''">
			AND u.phonenumber like concat('%', #{phoneNumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<!--通知组用户集合-->
	<select id="selectUserNoticeCheck" resultType="Long">
		select distinct u.user_id
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		left join ads_notice_business_join n on n.business_id = u.user_id and n.business_type = 1
		where u.del_flag = '0' and n.notice_group_id = #{noticeGroupId}
	</select>

	<!--获取用户信息-->
	<select id="getUserInformation" resultType="com.panda.pollen.common.core.domain.vo.SysUserDistributionVO">
		select phonenumber,user_name
		from sys_user
		where
		status='0' and del_flag='0'
		<if test="userName!= null and userName!= ''">
			and user_name like #{userName}
		</if>
		<if test="nickName!= null and nickName!= ''">
			and nick_name = #{nickName}
		</if>
	</select>

	<select id="selectGetUserName" resultType="SysUser">
		select   u.user_name
		from sys_user u
		where u.del_flag = '0'
		  and u.user_id=#{dto.userId}
		${dto.params.dataScope}
	</select>

	<!-- 第三方授权登录集成 -->
	<select id="selectAuthUserByUuid" parameterType="String" resultMap="SysUserResult">
	    select b.user_id as user_id, b.user_name as user_name, b.password as password
	    from sys_auth_user a left join sys_user b on a.user_id = b.user_id
		where a.uuid = #{uuid} and b.del_flag = '0'
	</select>

 	<select id="selectAuthUserListByUserId" parameterType="Long" resultMap="SysAuthUserResult">
	    select auth_id, uuid, user_id, user_name, nick_name, avatar, email, source, create_time
	    from sys_auth_user where user_id = #{userId}
	</select>

	<select id="checkAuthUser" parameterType="SysAuthUser" resultType="int">
		select count(1) from sys_auth_user where user_id=#{userId} and source=#{source} limit 1
	</select>

	<insert id="insertAuthUser" parameterType="SysAuthUser">
 		insert into sys_auth_user(
 		    <if test="uuid != null and uuid != ''">uuid,</if>
 			<if test="userId != null and userId != 0">user_id,</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="nickName != null and nickName != ''">nick_name,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="source != null and source != ''">source,</if>
 			create_time
 		)values(
 		    <if test="uuid != null and uuid != ''">#{uuid},</if>
 			<if test="userId != null and userId != 0">#{userId},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="nickName != null and nickName != ''">#{nickName},</if>
 			<if test="avatar != null and avatar != ''">#{avatar},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="source != null and source != ''">#{source},</if>
 			sysdate()
 		)
	</insert>

	<delete id="deleteAuthUser" parameterType="Long">
 		delete from sys_auth_user where auth_id = #{authId}
 	</delete>


	<select id="selectByUserName" resultType="com.panda.pollen.modules.domain.vo.UserVO">
		select
			u.user_id,
			d.dept_id,
			u.user_name,
			d.dept_name,
			d.first_dept_id,
			d.first_dept_name,
			u.commission_rate
		from sys_user u,sys_dept d
		where u.dept_id = d.dept_id
		  and u.user_name = #{userName}
		  and u.del_flag = 0
	</select>
	<select id="selectUserByPhone" resultType="com.panda.pollen.common.core.domain.entity.SysUser"
			parameterType="com.panda.pollen.common.core.domain.entity.SysUser">
		select  *
		from sys_user u
		where u.del_flag = 0
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber = #{phonenumber}
		</if>
		limit 1
	</select>
	<!-- 批量更新数据 -->
	<update id="updateUserBatch" parameterType="com.panda.pollen.common.core.domain.entity.SysUser">
		update sys_user
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="phonenumber =case" suffix="end,">
				<foreach collection="sysUser" item="item" index="index">
					<if test="item.phonenumber!=null and item.phonenumber!='' ">
						when user_id=#{item.userId} then #{item.phonenumber}
					</if>
				</foreach>
			</trim>
		</trim>
		where
		 user_id in (
		<foreach collection="sysUser" separator="," item="item" index="index">
			#{item.userId}
		</foreach>
		)
	</update>
	<select id="selectFirstDeptIds" resultType="java.lang.Long">
		select  d.first_dept_id  from sys_user u
		LEFT JOIN sys_dept d ON d.dept_id = u.dept_id and d.del_flag = 0
		where  u.login_date  > NOW() - INTERVAL 30 day
		group by  d.first_dept_id
	</select>

	<select id="selectFullSysUserInfo" resultType="com.panda.pollen.common.core.domain.entity.SysUser">
		select * from sys_user where user_id = #{userId}
	</select>

	<select id="selectSysUserByMpOpenId" resultType="com.panda.pollen.common.core.domain.entity.SysUser">
		select * from sys_user where mp_open_id = #{openId}
	</select>

	<select id="selectLiveRoomRole" resultType="com.panda.pollen.common.core.domain.vo.LiveRoomRoleVO">
		SELECT t.id as room_id,
				CASE 
					WHEN t.anchor_id = #{userId} then 1
					WHEN a.assistant_id IS NOT NULL AND a.assistant_type = 0 THEN 2
					WHEN a.assistant_id IS NOT NULL AND a.assistant_type = 1 THEN 3
					ELSE 0
				END AS liveRoomRoleType
			FROM live_aui_room_info t 
				LEFT JOIN live_aui_room_assistant a ON t.id = a.room_info_id  
			WHERE t.id = #{roomId} 
					AND a.assistant_id = #{userId} 
					AND t.deleted = 0
					AND a.deleted = 0
		<!-- SELECT
			lari.id AS room_id,
			CASE
				WHEN lari.anchor_id IS NOT NULL THEN 1
				WHEN lara.assistant_id IS NOT NULL AND lara.assistant_type=0 THEN 2
				WHEN lara.assistant_id IS NOT NULL AND lara.assistant_type=1 THEN 3
				ELSE 0
				END AS liveRoomRoleType
		FROM sys_user u
		LEFT JOIN live_aui_room_info lari ON u.user_id = lari.anchor_id
		LEFT JOIN live_aui_room_assistant lara ON u.user_id = lara.assistant_id
		WHERE
			u.user_id = #{userId}
		  and lari.id = #{roomId}
			and lari.deleted = 0
			and u.del_flag = '0'
		group by lari.id
		 -->
	</select>
	<select id="selectSingleByUserName" resultType="com.panda.pollen.common.core.domain.entity.SysUser">
		select * from sys_user t
		where t.user_name = #{userName} and t.del_flag = '0' limit 1
	</select>

    <select id="selectByGoodsInfo" resultType="com.panda.pollen.modules.domain.vo.UserVO">
        select
        u.nick_name,
        u.user_id,
        d.dept_id,
        u.user_name,
        d.dept_name,
        d.first_dept_id,
        d.first_dept_name,
        g.goods_id,
        u.commission_rate
        from sys_user u
        LEFT JOIN sys_dept d on u.dept_id = d.dept_id
        LEFT JOIN ads_goods_info g ON u.user_name = g.create_by
        where
        u.del_flag = 0
        AND g.platform = #{platform}
        AND g.goods_id = #{goodsId}
        AND g.media_platform_type = #{mediaPlatformType}
        order by g.deleted limit 1
    </select>


</mapper>