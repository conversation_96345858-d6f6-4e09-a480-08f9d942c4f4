<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.panda.pollen.system.mapper.RegionMapper">

    <delete id="deleteAllData">
        delete from live_region where true
    </delete>

    <cache eviction="FIFO" flushInterval="3600000" size="512" readOnly="true"/>
    <select id="findAllRegion" resultType="com.panda.pollen.common.core.domain.vo.RegionVO">
        select code, name, geoname_id, parent_geoname_id from live_region where level != 'FOUR_LEVEL'
    </select>

</mapper>