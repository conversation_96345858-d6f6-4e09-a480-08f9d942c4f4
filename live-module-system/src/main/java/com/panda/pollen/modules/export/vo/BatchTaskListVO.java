package com.panda.pollen.modules.export.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.panda.pollen.common.enums.advanced.export.BatchTaskCategory;
import com.panda.pollen.modules.export.BatchTask;
import lombok.Data;

import java.util.List;

/**
 * @ClassName: BatchTaskListVO
 * @Description : 列表查询VO
 * <AUTHOR> <PERSON><PERSON>
 * @create: 2024/8/30
 */
@Data
public class BatchTaskListVO extends BatchTask {
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件URL
     */
    private String filePath;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 媒体类型中文
     */
    private String mediaTypeDesc;

    /**
     * 查询的任务类别条件
     */
    @JsonIgnore
    private List<BatchTaskCategory> categoryList;

}