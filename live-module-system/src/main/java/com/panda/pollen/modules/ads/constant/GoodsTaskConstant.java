package com.panda.pollen.modules.ads.constant;

/**
 * ClassName:com.panda.pollen.modules.ads.contant
 * Description:商品类型常量
 *
 * <AUTHOR> <br>
 * date 2023年 11月01日 9:30 <br>
 * @version v1.0 <br>
 */
public interface GoodsTaskConstant {

    /**
     * 类型：限推触发事件
     */
    Integer TYPE_TRIGGER = 1;

    /**
     * 类型：配置商品订单
     */
    Integer TYPE_DISPOSITION = 0;

    /**
     * 替换状态：1是
     */
    Integer REPLACE_STATUS_IS = 1;

    /**
     * 替换状态：2失效
     */
    Integer REPLACE_STATUS_LOSE_EFFECTIVENESS = 2;
}
