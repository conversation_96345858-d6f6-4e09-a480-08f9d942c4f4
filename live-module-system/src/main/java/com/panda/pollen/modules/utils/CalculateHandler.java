
/**
 * All rights Reserved, Designed By http://www.eternal.com/ <br>
 * Title：CalculateHandler.java <br>
 * Package：com.ruoyi.modules.ads.utils <br>
 * Copyright © 2023 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 *
 * <AUTHOR> <br>
 * date 2023年12月13日 下午10:46:33 <br>
 * @version v1.0 <br>
 */
package com.panda.pollen.modules.utils;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.panda.pollen.modules.ads.vo.PlanStatisticsBaseVO;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * ClassName：com.ruoyi.modules.ads.utils.CalculateHandler <br>
 * Description：报表运算处理器 <br>
 * Copyright © 2023 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2023年12月13日 下午10:46:33 <br>
 * @version v1.0 <br>  
 */
public class CalculateHandler {

    private PlanStatisticsBaseVO vo;

    public void calculate(PlanStatisticsBaseVO vo) {
        this.vo = vo;
        amountConvert();
        calcAvgShowCost();
        calcAvgClickCost();
        calcClickRate();
        calcROI();
        calcRealROI();
        calcConvertCost();
        calcCvtRate();
        calcDeepConvertCost();
        calcDctRate();
        calcChargeBack();
        calcChargeBackRate(vo);
        calcDeduct();
        calcAvgPrice();
        calculatePlayRate();
        calcOrderCost();
        calcTransactionCost();
        calcDeductTheReturnCost();
        calcReturnRate();
    }

    /**
     * 计算退单率
     *
     * @param vo
     * @return
     */
    private void calcChargeBackRate(PlanStatisticsBaseVO vo) {
        if (vo.getOrderCount() != null && vo.getOrderCount() > 0 && ObjectUtil.isNotEmpty(vo.getChargeBack())) {
            BigDecimal product = NumberUtil.mul(vo.getChargeBack(), new BigDecimal(100));
            vo.setChargeBackRate(NumberUtil.div(product, vo.getOrderCount(), 2, RoundingMode.CEILING) + "%");
        } else {
            vo.setChargeBackRate("0%");
        }
    }

    /**
     * 实际回传率
     */
    private void calcReturnRate() {
        if (ObjectUtils.isEmpty(vo.getOrderCount()) || ObjectUtils.isEmpty(vo.getOrderCountDeduct())) {
            vo.setReturnRate("0.00%");
        } else {
            vo.setReturnRate(calculateRate(new BigDecimal(vo.getOrderCount()), new BigDecimal(vo.getOrderCountDeduct())));
        }
    }

    /**
     * Description：计算扣除回传后成本 <br>
     * author：朱程宇 <br>
     * date：2023年11月15日 上午10:32:05 <br>
     */
    private void calcDeductTheReturnCost() {
        if (vo.getCost() != null && isNotNullGtZero(vo.getConversionDeduct())) {
            vo.setDeductTheReturnCost(div(vo.getCost(), new BigDecimal(vo.getConversionDeduct())));
        } else {
            vo.setDeductTheReturnCost(new BigDecimal(0.00));
        }
    }

    /**
     * Description：计算成交成本 <br>
     * author：朱程宇 <br>
     * date：2023年11月15日 上午10:32:05 <br>
     */
    private void calcTransactionCost() {
        if (vo.getCost() != null && isNotNullGtZero(vo.getRealOrderCount())) {
            vo.setTransactionCost(div(vo.getCost(), new BigDecimal(vo.getRealOrderCount())));
        } else {
            vo.setTransactionCost(new BigDecimal(0.00));
        }
    }

    /**
     * Description：计算下单成本 <br>
     * author：朱程宇 <br>
     * date：2023年11月15日 上午10:32:05 <br>
     */
    private void calcOrderCost() {
        if (vo.getCost() != null && isNotNullGtZero(vo.getOrderCount())) {
            vo.setOrderCost(div(vo.getCost(), new BigDecimal(vo.getOrderCount())));
        } else {
            vo.setOrderCost(new BigDecimal(0.00));
        }
    }

    /**
     * 计算各种播放率
     */
    private void calculatePlayRate() {
        vo.setPlayDuration3sRate(calculateRate(vo.getTotalPlay(), vo.getPlayDuration3s()));
        vo.setPlay25FeedBreakRate(calculateRate(vo.getTotalPlay(), vo.getPlay25FeedBreak()));
        vo.setPlay50FeedBreakRate(calculateRate(vo.getTotalPlay(), vo.getPlay50FeedBreak()));
        vo.setPlay75FeedBreakRate(calculateRate(vo.getTotalPlay(), vo.getPlay75FeedBreak()));
        vo.setPlay99FeedBreakRate(calculateRate(vo.getTotalPlay(), vo.getPlay99FeedBreak()));
    }

    /**
     * Description：平均单价 <br>
     * author：罗江林 <br>
     * date：2023年6月14日 下午10:32:05 <br>
     */
    private void calcAvgPrice() {
        BigDecimal orderAmount = vo.getOrderAmount();
        Integer orderCount = vo.getOrderCount();
        if (orderAmount != null && isNotNullGtZero(orderCount)) {
            vo.setAvgPrice(div(orderAmount, new BigDecimal(orderCount)));
        } else {
            vo.setAvgPrice(new BigDecimal(0.00));
        }
    }

    /**
     * Description：扣除回传运算 <br>
     * author：罗江林 <br>
     * date：2023年6月4日 下午11:04:53 <br>
     */
    private void calcDeduct() {
        //扣除回传后订单数
        vo.setOrderCountDeduct(NumberUtil.sub(vo.getOrderCount(), vo.getConversionDeduct()).longValue());
        //扣除回传后金额
        vo.setAmountAfterDeducting(NumberUtil.sub(vo.getOrderAmount(), vo.getRealOrderAmountDeduct()));
        //扣除回传后ROI
        if (vo.getCost() != null && vo.getCost().doubleValue() > 0) {
            vo.setDeductRoi(div(vo.getAmountAfterDeducting(), vo.getCost()));
            vo.setMediaRoi(div(vo.getOrderAmountMedia(), vo.getCost()));
        } else {
            vo.setDeductRoi(new BigDecimal("0.00"));
        }
    }

    /**
     * 计算退单数量
     */
    private void calcChargeBack() {
        if (ObjectUtils.isNotEmpty(vo.getOrderCount()) && ObjectUtils.isNotEmpty(vo.getRealOrderCount())) {
            vo.setChargeBack(vo.getOrderCount() - vo.getRealOrderCount());
        } else {
            vo.setChargeBack(0);
        }
    }

    /**
     * 深度转化率
     */
    private void calcDctRate() {
        if (vo.getDeepConvert() != null && isNotNullGtZero(vo.getClick())) {
            BigDecimal product = NumberUtil.mul(vo.getDeepConvert(), new BigDecimal(100));
            vo.setDctRate(div(product, vo.getClick(), "%"));
        } else {
            vo.setDctRate(new BigDecimal(0.00) + "%");
        }
    }

    /**
     * calc深转换成本
     */
    private void calcDeepConvertCost() {
        if (vo.getCost() != null && isNotNullGtZero(vo.getDeepConvert())) {
            vo.setDeepConvertCost(div(vo.getCost(), vo.getDeepConvert()));
        } else {
            vo.setDeepConvertCost(new BigDecimal(0.00));
        }
    }

    /**
     * 转化率
     */
    private void calcCvtRate() {
        if (vo.getConvert() != null && isNotNullGtZero(vo.getClick())) {
            BigDecimal product = NumberUtil.mul(vo.getConvert(), new BigDecimal(100));
            vo.setCvtRate(div(product, vo.getClick(), "%"));
        } else {
            vo.setCvtRate(new BigDecimal(0.00) + "%");
        }
    }

    /**
     * calc转换成本
     */
    private void calcConvertCost() {
        if (vo.getCost() != null && isNotNullGtZero(vo.getConvert())) {
            vo.setConvertCost(div(vo.getCost(), vo.getConvert()));
        } else {
            vo.setConvertCost(new BigDecimal(0.00));
        }
    }

    /**
     * Description：计算ROI <br>
     * author：罗江林 <br>
     * date：2023年3月24日 下午11:06:19 <br>
     */
    private void calcRealROI() {
        if (vo.getCost() != null && vo.getCost().doubleValue() > 0) {
            vo.setRealRoi(div(vo.getRealOrderAmount(), vo.getCost()));
        } else {
            vo.setRealRoi(new BigDecimal(0.00));
        }
    }

    /**
     * Description：计算ROI <br>
     * author：罗江林 <br>
     * date：2023年3月24日 下午11:06:19 <br>
     */
    private void calcROI() {
        if (vo.getCost() != null && vo.getCost().doubleValue() > 0) {
            vo.setRoi(div(vo.getOrderAmount(), vo.getCost()));
        } else {
            vo.setRoi(new BigDecimal(0.00));
        }
    }

    /** 点击率 */
    private void calcClickRate() {
        if (vo.getClick() != null && isNotNullGtZero(vo.getShow())) {
            BigDecimal product = NumberUtil.mul(vo.getClick(), new BigDecimal(100));
            vo.setClickRate(div(product, vo.getShow()) + "%");
        } else {
            vo.setClickRate(new BigDecimal(0.00) + "%");
        }
    }


    /** 平均点击单价 */
    private void calcAvgClickCost() {
        if (vo.getCost() != null && isNotNullGtZero(vo.getClick())) {
            vo.setAvgClickCost(div(vo.getCost(), vo.getClick()));
        } else {
            vo.setAvgClickCost(new BigDecimal(0.00));
        }
    }

    /**
     * Description：金额分转元 <br>
     */
    private void amountConvert() {
        vo.setOrderAmount(centToYuan(vo.getOrderAmount()));
        vo.setRealOrderAmount(centToYuan(vo.getRealOrderAmount()));
        vo.setRealOrderAmountDeduct(centToYuan(vo.getRealOrderAmountDeduct()));
        vo.setTotalRefundAmount(NumberUtil.sub(vo.getOrderAmount(), vo.getRealOrderAmount()));
    }

    /**
     * Description：千展平均费用 <br>
     * author：罗江林 <br>
     * date：2023年12月13日 下午11:26:41 <br> <br>
     */
    private void calcAvgShowCost() {
        if (vo.getCost() != null && isNotNullGtZero(vo.getShow())) {
            BigDecimal product = NumberUtil.mul(vo.getCost(), new BigDecimal(1000));
            vo.setAvgShowCost(div(product, vo.getShow()));
        } else {
            vo.setAvgShowCost(new BigDecimal(0.00));
        }
    }

    /**
     * Description：分转元 <br>
     * @param cent
     * @return <br>
     */
    public BigDecimal centToYuan(BigDecimal cent) {
        if (cent == null) {
            return new BigDecimal(0.00);
        }
        return NumberUtil.div(cent, new BigDecimal(100), 2, RoundingMode.CEILING);
    }

    /**
     * Description：保留小数点后两位的除法 <br>
     * author：罗江林 <br>
     * date：2023年12月13日 下午11:13:33 <br>
     * @param a
     * @param b
     * @return <br>
     */
    private static BigDecimal div(BigDecimal a, BigDecimal b) {
        return NumberUtil.div(a, b, 2, RoundingMode.valueOf(ROUND_HALF_UP));
    }

    private String div(BigDecimal a, BigDecimal b, String rate) {
        return NumberUtil.div(a, b, 2, RoundingMode.CEILING) + rate;
    }

    //计算各种率并返回
    private String calculateRate(BigDecimal totalNum, BigDecimal num) {
        if (isNotNullGtZero(totalNum) && isNotNullGtZero(num)) {
            BigDecimal product = NumberUtil.mul(num, new BigDecimal(100));
            return div(product, totalNum, "%");
        } else {
            return new BigDecimal(0.00) + "%";
        }
    }

    private static boolean isNotNullGtZero(BigDecimal val) {
        return val != null && val.longValue() > 0;
    }

    private boolean isNotNullGtZero(Integer val) {
        return val != null && val.intValue() > 0;
    }

    private boolean isNotNullGtZero(Long val) {
        return val != null && val.longValue() > 0;
    }

}

