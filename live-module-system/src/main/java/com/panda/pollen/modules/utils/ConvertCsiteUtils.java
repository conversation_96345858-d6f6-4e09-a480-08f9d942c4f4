package com.panda.pollen.modules.utils;

import cn.hutool.core.util.NumberUtil;
import com.panda.pollen.common.enums.ads.KsCsiteEnum;
import com.panda.pollen.common.enums.ads.MediaTypeEnum;
import com.panda.pollen.common.enums.ads.OceanCsiteEnum;
import com.panda.pollen.common.enums.ads.TencentCsiteEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * 版位名称转换
 *
 * <AUTHOR>
 */
@Slf4j
public class ConvertCsiteUtils {

    public static String getConvertCsite(Integer mediaType, String csite) {
        MediaTypeEnum mediaTypeEnum = MediaTypeEnum.get(mediaType);
        if (mediaTypeEnum != null) {
            switch (mediaTypeEnum) {
                case OCEAN:
                    if (NumberUtil.isNumber(csite)) {
                        return OceanCsiteEnum.findTextByValue(Integer.valueOf(csite));
                    }
                    break;
                case KUAISHOU:
                    if (NumberUtil.isNumber(csite)) {
                        return KsCsiteEnum.getName(Integer.valueOf(csite));
                    }
                    break;
                case TENCENT:
                    return TencentCsiteEnum.getName(csite);
                default:
                    return "";
            }
        }
        return "";
    }

    /**
     * @param csite 巨量的版位数字
     * <AUTHOR>
     * @param:
     * @Date: 2025/7/21 10:49
     * @return: boolean 1今日头条,2西瓜视频,3火山小视频,4抖音,5番茄小说,6穿山甲开屏广告,7穿山甲网盟非开屏广告,8通投广告位,9搜索
     */
    public static Integer oceanCsiteToCode(String csite) {
        try {
            if (NumberUtil.isNumber(csite)) {
                int csiteInt = NumberUtil.parseInt(csite);
                /**
                 * 今日头条：1-10000，80000-110001;西瓜视频：10001-10099;火山小视频：30001-30099;抖音：40001-40099
                 * 番茄小说：26001-26099;穿山甲开屏广告：800000000;穿山甲网盟非开屏广告：900000000;通投广告位：33013;搜索：38016
                 */
                if ((csiteInt >= 1 && csiteInt <= 10000) || (csiteInt >= 80000 && csiteInt <= 110001))
                    return 1;
                else if (csiteInt >= 10001 && csiteInt <= 10099) {
                    return 2;
                } else if (csiteInt >= 30001 && csiteInt <= 30099) {
                    return 3;
                } else if (csiteInt >= 40001 && csiteInt <= 40099) {
                    return 4;
                } else if (csiteInt >= 26001 && csiteInt <= 26099) {
                    return 5;
                } else if (csiteInt == 800000000) {
                    return 6;
                } else if (csiteInt == 900000000) {
                    return 7;
                } else if (csiteInt == 33013) {
                    return 8;
                } else if (csiteInt == 38016) {
                    return 9;
                }
            }
        } catch (Exception e) {
            log.error("巨量版位转换错误：{}", e.getMessage());
        }
        return 8;
    }
}
