package com.panda.pollen.modules.ads.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panda.pollen.modules.ads.domain.NoticeGroupDetail;
import com.panda.pollen.modules.ads.vo.NoticeGroupDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通知组详情表Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-07
 */
public interface NoticeGroupDetailMapper extends BaseMapper<NoticeGroupDetail> {

    List<NoticeGroupDetail> queryNoticeGroupDetailListByBusinessInfo(@Param("businessId") Long businessId,@Param("businessType") int businessType);

    /**
     * 批量更新
     *
     * @param noticeGroupDetail
     */
    void updateNoticeGroupDetailBatch(@Param("noticeGroupDetail") List<NoticeGroupDetail> noticeGroupDetail);

    List<NoticeGroupDetailVO> queryNoticeGroupDetailListByBusinessInfoAndBusinessTypes(@Param("businessId") Long businessId, @Param("businessType") int businessType, @Param("noticeGroupType") int noticeGroupType);
}
