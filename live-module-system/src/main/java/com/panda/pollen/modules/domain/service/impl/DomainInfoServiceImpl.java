package com.panda.pollen.modules.domain.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.common.core.domain.entity.SysUser;
import com.panda.pollen.common.enums.NoticeGroupBusinessTypeEnum;
import com.panda.pollen.common.utils.Dater;
import com.panda.pollen.common.utils.SecurityUtils;
import com.panda.pollen.common.utils.counter.ThreadLocalCounter;
import com.panda.pollen.common.utils.sentinel.ThreadPoolUtils;
import com.panda.pollen.modules.ads.domain.DomainInfo;
import com.panda.pollen.modules.ads.vo.DomainInfoVO;
import com.panda.pollen.modules.ads.manger.RedisInfoManager;
import com.panda.pollen.modules.ads.service.INoticeGroupSendMsgService;
import com.panda.pollen.modules.domain.mapper.DomainInfoMapper;
import com.panda.pollen.modules.domain.service.IDomainInfoService;
import com.panda.pollen.modules.domain.vo.UserVO;
import com.panda.pollen.sender.notice.constants.NoticeConstants;
import com.panda.pollen.sender.notice.enums.NoticeTemplate;
import com.panda.pollen.sender.notice.model.DingTalkMessage;
import com.panda.pollen.sender.notice.utils.GlobalSenderUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;
import java.net.Socket;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.panda.pollen.common.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 域名Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-02
 */
@Slf4j
@Service
public class DomainInfoServiceImpl extends ServiceImpl<DomainInfoMapper, DomainInfo> implements IDomainInfoService {


    @Autowired
    private RedisInfoManager redisInfoManager;

    @Autowired
    private GlobalSenderUtils globalSenderUtils;

    @Autowired
    private INoticeGroupSendMsgService noticeGroupSendMsgService;


    /**
     * 查询域名信息列表
     *
     * @param domainInfo 域名信息
     * @return
     */
    @Override
    public List<DomainInfo> selectDomainInfoList(DomainInfo domainInfo) {
        domainInfo.setCompanyId(getFirstDeptId());
        return baseMapper.selectDomainInfoList(domainInfo);
    }

    /**
     * 删除域名
     *
     * @param ids 主键ID集合
     * @return 影响条数
     */
    @Override
    public int removeDomainByIds(List<Long> ids) {
        return baseMapper.removeDomainByIds(ids);
    }

    /**
     * 新增域名
     *
     * @param domainInfo 主键ID集合
     * @return 影响条数
     */
    @Override
    public boolean add(DomainInfo domainInfo) {
        // 域名不能为空
        String domainUrl = domainInfo.getDomainName();
        if (StringUtils.isBlank(domainUrl)) {
            return false;
        }
        Long companyId = domainInfo.getCompanyId();

        // 新增域名信息
        List<String> addDomainUrls = Arrays.stream(domainUrl.split(",")).collect(Collectors.toList());

        DomainInfo domain;
        for (String url : addDomainUrls) {
            // 查重
            if (findDomainInfoByCompanyIdAndDomainUrl(companyId, url)) {
                continue;
            }
            domain = new DomainInfo();
            domain.setCompanyId(companyId);
            domain.setCompanyName(domainInfo.getCompanyName());
            domain.setDomainUrl(url);
            domain.setCreateBy(domainInfo.getCreateBy());
            domain.setCreateTime(Dater.now());
            domain.setRemark(domainInfo.getRemark());
            // 获取证书信息
            Pair<Date, Long> immutablePair = checkSslCert(url);
            if (immutablePair != null) {
                domain.setExpiresIn(immutablePair.getLeft());
                domain.setValidityDays(immutablePair.getRight());
            }
            this.save(domain);
        }
        return true;
    }

    public void updateDomainInfoByCompanyId(DomainInfo domainInfo) {
        String domainUrl = domainInfo.getDomainName();
        // 获取公司下的所有域名
        List<String> urlsByCompany = baseMapper.selectDomainUrlByCompanyId(domainInfo.getCompanyId());

        // 新增域名信息
        List<String> addDomainUrls = Arrays.stream(domainUrl.split(",")).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(urlsByCompany)) {
            Set<String> notExistUrls = urlsByCompany.stream().filter(url -> !addDomainUrls.contains(url)).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(notExistUrls)) {
                // 删除不在新增域名集合中的域名
                this.removeDomainByCompanyId(DomainInfoVO.of(domainInfo.getCompanyId(), notExistUrls));
            }
        }
        this.add(domainInfo);
    }

    /**
     * 查询待提醒域名列表
     *
     * @return 域名信息 集合
     */
    @Override
    public List<DomainInfo> findPendingReminderDomainList(DomainInfo domainInfo) {
        List<DomainInfo> domainInfos = baseMapper.selectDomainInfoList(domainInfo);
        //线程安全
        List<DomainInfo> result = Collections.synchronizedList(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(domainInfos)) {
            //计数器
            ThreadLocalCounter.add(domainInfos.size());
            //创建线程池跑任务，并通过sentinel限流
            ExecutorService qpsFlowRuleThreadPool = ThreadPoolUtils.createThreadPoolExecutor("domainNameWarningJobExecute", 1, 1, 1);
            for (DomainInfo domain : domainInfos) {
                //多线程，线程最多等待20秒，现在不知道为什么任务会一直卡住
                Future<?> future = qpsFlowRuleThreadPool.submit(() -> doCheckSslCert(domain, result));
                try {
                    future.get(20, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    log.error("主线程被中断");
                    domain.setUpdateBy("InterruptedException");
                    domain.setUpdateTime(Dater.now());
                } catch (ExecutionException e) {
                    log.error("任务执行过程中出现异常: " + e.getCause());
                    domain.setUpdateBy("ExecutionException");
                    domain.setUpdateTime(Dater.now());
                } catch (TimeoutException e) {
                    log.error("任务执行超时，已取消:{}", domain.getDomainUrl());
                    // 超时，取消任务
                    future.cancel(true);
                    domain.setUpdateBy("TimeoutException");
                    domain.setUpdateTime(Dater.now());
                }
            }
            //等待线程池任务完成
            ThreadPoolUtils.waitThreadPoolFinish(qpsFlowRuleThreadPool);
            // 批量更新域名的有效期
            this.updateBatchById(domainInfos);
        }
        return result;
    }

    public void doCheckSslCert(DomainInfo domain, List<DomainInfo> result) {
        Pair<Date, Long> dateLongPair = checkSslCert(domain.getDomainUrl());
        if (dateLongPair == null) {
            domain.setUpdateBy("证书异常");
            domain.setUpdateTime(Dater.now());
            return;
        }
        domain.setExpiresIn(dateLongPair.getLeft());
        domain.setValidityDays(dateLongPair.getRight());
        domain.setUpdateBy("xxl");
        domain.setUpdateTime(Dater.now());
        // 过期时间小于等于10天的证书进行提醒
        if (dateLongPair.getRight() <= 10 && dateLongPair.getRight() > 0) {
            result.add(domain);
        }
    }


    /**
     * 发送钉钉通知开发人员
     *
     * @param domainInfoList 待通知的域名列表
     */
    @Override
    public void sendDingTalkNotification(List<DomainInfo> domainInfoList) {
        // 拼装需要通知的内容参数
        StringBuilder warningContent = new StringBuilder();
        for (DomainInfo domainInfo : domainInfoList) {
            String domainUrl = domainInfo.getDomainUrl();
            // 去掉域名后面的“/”
            if (StrUtil.endWith(domainUrl, "/")) {
                domainUrl = domainUrl.substring(0, domainUrl.length() - 1);
            }
            warningContent.append(StrUtil.format("#### <b>公司【{}】下属的域名【{}】证书将于{}天后到期，" +
                    "到期时间为{}</b>\n> ", domainInfo.getCompanyName(), domainUrl, domainInfo.getValidityDays(), DateUtil.format(domainInfo.getExpiresIn(), YYYY_MM_DD_HH_MM_SS)));
            // 拼接一段分隔符
            warningContent.append("#### <b>  </b>\n >");

        }
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("warningContent", warningContent.toString());

        // 发送钉钉消息
        globalSenderUtils.sendMessage(DingTalkMessage.buildMarkdownMessage(NoticeTemplate.DOMAIN_EXPIRE_WARNING_DEVELOPER,
                templateParams, NoticeConstants.DING_TALK_DEFAULT_TOKEN, NoticeConstants.DING_TALK_DEFAULT_SECRET));
    }

    /**
     * 更新域名
     *
     * @param domainInfo 主键ID集合
     * @return 影响条数
     */
    @Override
    public boolean update(DomainInfo domainInfo) {
        Long validityDays = null;
        Date expiresIn = null;
        Pair<Date, Long> immutablePair = checkSslCert(domainInfo.getDomainUrl());
        if (immutablePair != null) {
            expiresIn = immutablePair.getLeft();
            validityDays = immutablePair.getRight();
        }
        domainInfo.setExpiresIn(expiresIn);
        domainInfo.setValidityDays(validityDays);
        return this.updateById(domainInfo);
    }

    /**
     * 发消息到通知组
     *
     * @param domainsToNotify
     */
    @Override
    public void sendNotice(List<DomainInfo> domainsToNotify) {
        Map<String, Object> params;
        for (DomainInfo domainInfo : domainsToNotify) {
            UserVO user = redisInfoManager.getUser(domainInfo.getCreateBy());
            if (ObjectUtils.isEmpty(user)) {
                log.info("查询用户对象为空 -------->>");
                return;
            }
            params = new HashMap<>();
            params.put("domainUrl", domainInfo.getDomainUrl());
            params.put("count", domainInfo.getValidityDays());
            try {
                noticeGroupSendMsgService.sendMessages(user.getUserName(), params, NoticeTemplate.DOMAIN_EXPIRATION_WARNING, NoticeGroupBusinessTypeEnum.DOMAIN, true, false);
            } catch (Exception e) {
                log.error("发送通知组消息失败：{}", domainInfo);
            }
        }
    }

    /**
     * 根据公司ID删除域名
     *
     * @param vo
     * @return
     */
    @Override
    public void removeDomainByCompanyId(DomainInfoVO vo) {
        baseMapper.removeDomainByCompanyId(vo);
    }


    /**
     * 初始化域名信息
     */
    @Override
    public boolean findDomainInfoByCompanyIdAndDomainUrl(Long companyId, String domainUrl) {
        return baseMapper.findDomainInfoByCompanyIdAndDomainUrl(companyId, domainUrl) > 0;
    }

    /**
     * 查询域名信息
     *
     * @param companyId
     * @return
     */
    @Override
    public String selectDomainUrlByCompanyId(Long companyId) {
        List<String> urls = baseMapper.selectDomainUrlByCompanyId(companyId);
        if (CollectionUtils.isEmpty(urls)) {
            return StringUtils.EMPTY;
        }
        return String.join(",", urls);
    }

    /**
     * 获取一级部门域名
     * @param firstDeptId
     * @return
     */
    @Override
    public List<String> selectUserDomain(Long firstDeptId) {
        return baseMapper.selectDomainUrlByCompanyId(firstDeptId);
    }


    /**
     * 检查域名证书
     *
     * @param domain
     * @return
     * @throws Exception
     */
    private Pair<Date, Long> checkSslCert(String domain) {
        domain = domain.endsWith("/") ? domain.substring(0, domain.length() - 1) : domain;
        log.info(domain);
        SSLSocketFactory sslSocketFactory = (SSLSocketFactory) SSLSocketFactory.getDefault();
        try (Socket socket = sslSocketFactory.createSocket(domain, 443)) {
            if (socket instanceof SSLSocket) {
                SSLSocket sslSocket = (SSLSocket) socket;
                sslSocket.startHandshake();
                X509Certificate cert = (X509Certificate) sslSocket.getSession().getPeerCertificates()[0];
                // 过期时间
                Date notAfter = cert.getNotAfter();
                log.info(domain + " 过期时间 " + Dater.ymdhms(notAfter));

                // 计算剩余天数
                long remainingDays = (notAfter.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24);
                log.info("剩余天数: " + remainingDays);
                return Pair.of(notAfter, remainingDays);
            }
        } catch (Exception e) {
            log.error("检查域名证书失败", e);
        }
        return null;
    }

    private Long getFirstDeptId() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user.isAdmin()) {
            return null;
        }
        return user.getDept().getFirstDeptId();
    }
}

