
/**  
 * All rights Reserved, Designed By http://www.eternal.com/ <br>
 * Title：MediaAccountComboVO.java <br>
 * Package：com.panda.pollen.modules.ads.domain.vo <br>
 * Copyright © 2023 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2023年4月15日 下午3:50:02 <br>
 * @version v1.0 <br>
 */ 
package com.panda.pollen.modules.ads.vo;

import com.panda.pollen.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**   
 * ClassName：com.panda.pollen.modules.ads.vo.MediaAccountComboVO <br>
 * Description：媒体账户下拉框数据源VO <br>
 * Copyright © 2023 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2023年4月15日 下午3:50:02 <br>
 * @version v1.0 <br>  
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class MediaAccountComboVO extends BaseEntity{
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 媒体类型(com.panda.pollen.common.enums.ads.MediaTypeEnum)
     */
    private Integer mediaType;
    
    /**
     * 账户ID和账户名称
     */
    private String keyWords;

    /**
     * 分页条数
     */
    private Integer size;

    /**
     * 账户ID集合
     */
    private List<String> advertiserIds;

}
