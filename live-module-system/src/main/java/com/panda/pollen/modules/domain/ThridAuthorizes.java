/**  
 * All rights Reserved, Designed By <br>
 * Title：ThridAuthorizes.java <br>
 * Package：com.panda.pollen.modules.system.domain <br>
 * Description：(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年3月6日 下午3:20:34 <br>
 * @version v1.0 <br>
 */ 
package com.panda.pollen.modules.domain;

import lombok.Builder;
import lombok.Data;

/**   
 * ClassName：com.panda.pollen.modules.system.domain.ThridAuthorizes <br>
 * Description：第三授权信息 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年3月6日 下午3:20:34 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
public class ThridAuthorizes {

	private String clientId;
	
	private String clientSecret;
	
	private String redirectUri;
	
	private String applyDomain;

}
