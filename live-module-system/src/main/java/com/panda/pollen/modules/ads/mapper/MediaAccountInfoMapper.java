package com.panda.pollen.modules.ads.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panda.pollen.modules.ads.dto.MediaAccountConditionDTO;
import com.panda.pollen.ocean.model.fund.AdFundResultInfo;
import com.panda.pollen.common.core.domain.BaseEntity;
import com.panda.pollen.common.core.domain.model.OceanAuthorize;
import com.panda.pollen.modules.ads.domain.MediaAccountInfo;
import com.panda.pollen.modules.ads.dto.MediaAccountDTO;
import com.panda.pollen.modules.ads.vo.*;
import com.panda.pollen.modules.domain.vo.UserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;
import org.apache.ibatis.session.ResultHandler;

import java.util.Date;
import java.util.List;

/**
 * 媒体账户Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-10
 */
@Mapper
public interface MediaAccountInfoMapper extends BaseMapper<MediaAccountInfo> {
    /**
     * 查询媒体账户
     *
     * @param id 媒体账户主键
     * @return 媒体账户
     */
    public MediaAccountInfo selectMediaAccountInfoById(Long id);

    /**
     * 查询媒体账户列表--列表专用，禁止其他地方使用！！！
     *
     * @param mediaAccountInfo 媒体账户
     * @return 媒体账户集合
     */
    public List<MediaAccountInfo> selectMediaAccountInfoList(MediaAccountInfo mediaAccountInfo);

    /**
     * Title：selectMediaAccountList <br>
     * Description：根据条件查询媒体账户，不分页 <br>
     * author：罗江林 <br>
     * date：2023年4月23日 下午11:36:44 <br>
     *
     * @param dto 查询参数对象
     * @return <br>
     */
    public List<MediaAccountInfo> selectMediaAccountList(MediaAccountDTO dto);

    /**
     * Title：selectMasterMediaAccountInfoList <br>
     * Description：查询所有主账户 -授权使用<br>
     * author：罗江林 <br>
     * date：2023年3月12日 下午6:14:02 <br>
     *
     * @return <br>
     */
    public List<MediaAccountInfo> selectMasterMediaAccountInfoList(@Param("mediaType") List<Integer> mediaType, @Param("id") Long id);

    /**
     * 新增媒体账户
     *
     * @param mediaAccountInfo 媒体账户
     * @return 结果
     */
    public int insertMediaAccountInfo(MediaAccountInfo mediaAccountInfo);

    /**
     * 修改媒体账户
     *
     * @param mediaAccountInfo 媒体账户
     * @return 结果
     */
    public int updateMediaAccountInfo(MediaAccountInfo mediaAccountInfo);

    /**
     * 删除媒体账户
     *
     * @param id 媒体账户主键
     * @return 结果
     */
    public int deleteMediaAccountInfoById(Long id);

    /**
     * 批量删除媒体账户
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMediaAccountInfoByIds(String[] ids);

    /**
     * 查询媒体账户
     *
     * @param advertiserId 媒体账户ID
     * @param mediaType    媒体类型
     * @return 媒体账户
     */
    public MediaAccountInfo selectMediaAccountInfoByAdvertiserId(
            @Param("advertiserId") String advertiserId, @Param("mediaType") Integer mediaType);

    /**
     * Title：updateMediaAccountInfoTokenByAdvertiserId <br>
     * Description：批量更新媒体主账户及下属所有子账户Token <br>
     * author：罗江林 <br>
     * date：2023年3月12日 下午5:48:48 <br>
     *
     * @param authorize
     * @return <br>
     */
    public int updateMediaAccountInfoTokenByAdvertiserId(OceanAuthorize authorize);

    /**
     * 更新主账户及下属的所有子账户的Token相关信息
     *
     * @param mediaAccountInfo
     * @return
     */
    public int updateTokenWithChild(MediaAccountInfo mediaAccountInfo);

    /**
     * Title：selectMediaAccountInfoList <br>
     * Description：查询所有媒体账户,没有权限限制 <br>
     * author：罗江林 <br>
     * date：2023年3月15日 下午2:57:47 <br>
     *
     * @return <br>
     */
    public List<MediaAccountInfo> selectMediaAccountInfoListChild(Integer type);

    /**
     * Title：changeControlStatus <br>
     * Description：变更评控状态 <br>
     * author：罗江林 <br>
     * date：2023年4月23日 下午8:57:06 <br>
     *
     * @param mac
     * @return <br>
     */
    public int changeControlStatus(MediaAccountDTO mac);

    /**
     * Title：selectMediaAccountForCombo <br>
     * Description：媒体账户下拉框数据源查询 <br>
     * author：罗江林 <br>
     * date：2023年4月15日 下午3:53:12 <br>
     *
     * @param mac
     * @return <br>
     */
    public List<MediaAccountComboVO> selectMediaAccountForCombo(MediaAccountComboVO mac);

    /**
     * 通过媒体的id来更新报表和token同步状态
     *
     * @param mediaAccountInfo
     * @return
     */
    public int updateMediaTokenOrReportState(MediaAccountInfo mediaAccountInfo);

    /**
     * 查询开启回传提升的广告
     *
     * @return
     */
    List<MediaAccountInfo> selectMediaAccountInfoListByIC(@Param("mediaType") Integer mediaType, @Param("id") Long id);

    /**
     * 通过advertiserId获取简单媒体信息
     *
     * @param advertiserId
     * @return
     */
    MediaAccountInfoBriefVO getBriefInfoByAdvertiserId(@Param("advertiserId") String advertiserId);

    /**
     * @Description 查询账户id跟账户名称
     * <AUTHOR>
     * @param: advertiserIdList 账户id集合
     * @Date : 2023/7/12 15:16
     * @return: List<MediaAccountInfoListVO>
     */
    List<MediaAccountInfoListVO> selectMediaAccountInfoByAdvertiserIds(@Param("advertiserIdList") List<String> advertiserIdList);

    /**
     * 获取一段时间内更新过余额的账户
     *
     * @param mediaType
     * @param fundUpdateTime
     * @return
     */
    List<MediaAccountInfo> selectMediaAccountFundList(@Param("mediaType") Integer mediaType,
                                                      @Param("fundUpdateTime") Date fundUpdateTime);

    /**
     * 修改媒体账户余额
     *
     * @param mediaAccountInfo 媒体账户
     * @return 结果
     */
    public int updateMediaAccountFund(MediaAccountInfo mediaAccountInfo);

    /**
     * 修改媒体账户余额
     *
     * @param mediaAccountInfo 媒体账户
     * @return 结果
     */
    public int updateMediaAccountIndustry(MediaAccountInfo mediaAccountInfo);

    /**
     * 分页查询所有余额大于0的媒体账户,没有权限限制
     *
     * @return
     */
    public List<MediaAccountInfo> selectMediaAccountInfoListChildWithFundPositive(
            @Param("dto") MediaAccountDTO dto,
            @Param("id") Long id,
            @Param("size") Integer size);

    /**
     * 分页查询所有余额等于0或为空的媒体账户,没有权限限制
     *
     * @param type
     * @return
     */
    public List<MediaAccountInfo> selectMediaAccountInfoListChildWithFundZero(
            @Param("type") Integer type,
            @Param("id") Long id,
            @Param("size") Integer size);

    /**
     * 分页查询所有媒体账户,没有权限限制
     *
     * @param type
     * @param id
     * @param size
     * @return
     */
    List<MediaAccountInfo> pageSelectMediaAccountInfoListChild(@Param("type") Integer type,
                                                               @Param("id") Long id,
                                                               @Param("size") Integer size);

    /**
     * 分页查询没有行业名称的媒体账户，按渠道筛选
     *
     * @param type
     * @param id
     * @param size
     * @return
     */
    List<MediaAccountInfo> pageSelectMediaAccountInfoListChildWithNullIndustry(@Param("type") Integer type,
                                                                               @Param("id") Long id,
                                                                               @Param("size") Integer size);

    /**
     * 更新媒体账户的授权相关信息
     *
     * @param mediaAccountInfo 媒体账户
     * @return 结果
     */
    public int updateMediaAccountAuthorizeInfo(MediaAccountInfo mediaAccountInfo);

    /**
     * 根据父账户id查询其下包含多少refresh_token
     *
     * @param parentAccountId
     * @return
     */
    List<String> selectRefreshTokenByParentAccountId(@Param("parentAccountId") String parentAccountId);

    /**
     * 根据refresh_token刷新账户令牌信息
     *
     * @param oldRefreshToken
     * @param accessToken
     * @param expiresIn
     * @param refreshToken
     * @param refreshTokenExpiresIn
     * @return
     */
    int updateTokenInfoByRefreshToken(@Param("oldRefreshToken") String oldRefreshToken,
                                      @Param("accessToken") String accessToken,
                                      @Param("expiresIn") Date expiresIn,
                                      @Param("refreshToken") String refreshToken,
                                      @Param("refreshTokenExpiresIn") Date refreshTokenExpiresIn);

    /**
     * 根据refresh_token更新账户令牌刷新错误信息
     *
     * @param oldRefreshToken
     * @param tokenRefreshErrorMsg
     * @return
     */
    int updateTokenErrorInfoByRefreshToken(@Param("oldRefreshToken") String oldRefreshToken,
                                           @Param("tokenRefreshErrorMsg") String tokenRefreshErrorMsg);

    /**
     * 根据媒体用户更新令牌
     *
     * @param accessToken
     * @param expiresIn
     * @param refreshToken
     * @param refreshTokenExpiresIn
     * @param userId
     * @return
     */
    int updateTokenByMediaUser(@Param("accessToken") String accessToken,
                               @Param("expiresIn") Date expiresIn,
                               @Param("refreshToken") String refreshToken,
                               @Param("refreshTokenExpiresIn") Date refreshTokenExpiresIn,
                               @Param("userId") Long userId);

    /**
     * 游标根据条件查询媒体账户，不分页
     *
     * @param dto
     * @return
     */
    Cursor<MediaAccountInfo> selectMediaAccountListByCursor(MediaAccountDTO dto);


    /**
     * @param mediaType
     * @param baseEntity
     * @Description 父账户列表(列表下拉框)
     * <AUTHOR>
     * @param:
     * @Date : 2023/10/21 11:00
     * @return: java.util.List<com.panda.pollen.modules.ads.domain.MediaAccountInfo>
     */
    List<MediaAccountInfo> parentAccountList(@Param("mediaType") Integer mediaType, @Param("entity") BaseEntity baseEntity);


    /**
     * 根据advertiserId集合以及媒体类型批量查询
     *
     * @param advertiserIdList
     * @param mediaType
     * @return
     */
    List<MediaAccountInfo> selectMediaAccountListByAdvertiserIdList(@Param("advertiserIdList") List<String> advertiserIdList, @Param("mediaType") int mediaType);

    /**
     * 查询所有的媒体一二级行业信息
     *
     * @return
     */
    List<MediaIndustryVO> selectMediaIndustry();


    /**
     * 通过条件查询巨量ls的订阅数据
     *
     * @param lsAdvSubscribe 1是查询已订阅的数据(执行删除订阅),0查询未订阅的数据(执行添加订阅)
     * @return
     */
    List<MediaAccountInfo> selectOceanLsAdvSubscribe(@Param("lsAdvSubscribe") boolean lsAdvSubscribe, @Param("advType") Integer advType);

    /**
     * 通过mediaUserId来修改账户的巨量ls订阅状态
     *
     * @param idsList
     * @param lsAdvSubscribe
     * @return
     */
    int batchUpdateOceanLsAdvSubscribeInfoByAdvertiserId(@Param("idsList") List<Long> idsList, @Param("lsAdvSubscribe") boolean lsAdvSubscribe, @Param("advType") Integer advType);

    /**
     * @param mediaPlatformType
     * @param mediaAccountId
     * @Description 通过媒体类型和广告主id获取用户数据
     * <AUTHOR>
     * @Date : 2024/5/17 10:23
     * @return: com.panda.pollen.modules.system.domain.vo.UserVO
     */
    UserVO selectMediaAccountUserVo(@Param("mediaPlatformType") Integer mediaPlatformType, @Param("mediaAccountId") String mediaAccountId);

    /**
     * 批量更新媒体账户的token或者上报状态
     *
     * @param advertiserIdList 媒体账户
     * @param message          消息
     * @return
     */
    public int batchUpdateMediaTokenOrReportState(@Param("advertiserIdList") List<String> advertiserIdList, @Param("message") String message);

    /**
     * 批量更新媒体账户的余额
     *
     * @param adFundResultInfos 账户余额信息
     * @param fundUpdateTime    更新时间
     * @return
     */
    public int batchUpdateMediaAccountFund(@Param("adFundResultInfos") List<AdFundResultInfo> adFundResultInfos, @Param("fundUpdateTime") Date fundUpdateTime);

    /**
     * @param statDate
     * @Description 获取有报表未订阅的媒体素材数据
     * <AUTHOR>
     * @param:
     * @Date : 2024/5/29 10:11
     * @return: java.util.List<com.panda.pollen.modules.ads.domain.MediaAccountInfo>
     */
    List<MediaAccountInfo> selectOceanMaterialLsAdvSubscribe(@Param("statDate") String statDate);

    /**
     * @param statDate
     * @Description 获取指定一级部门有报表未订阅的媒体素材数据
     * <AUTHOR>
     * @param:
     * @Date : 2024/5/29 10:11
     * @return: java.util.List<com.panda.pollen.modules.ads.domain.MediaAccountInfo>
     */
    List<MediaAccountInfo> selectOceanMaterialLsAdvSubscribe2(@Param("statDate") String statDate, @Param("firstDeptIds") String[] firstDeptIds);


    /**
     * @param advertisrId       账户ID
     * @param mediaPlatformType 媒体类型
     * @Description 根据账户ID还有媒体类型获取媒体账户信息
     * <AUTHOR>
     * @Date : 2024/6/5 14:17
     * @return: com.panda.pollen.modules.ads.vo.MediaAccountInfoBriefVO
     */
    public MediaAccountInfoBriefVO getMediaAccountInfo(@Param("advertisrId") String advertisrId, @Param("mediaPlatformType") Integer mediaPlatformType);

    /**
     * @Description 根据账户ID还有媒体类型获取媒体账户信息
     * <AUTHOR>
     * @Date : 2024/6/5 14:17
     * @return: com.panda.pollen.modules.ads.vo.MediaAccountInfoBriefVO
     */
    public MediaAccountInfo getMediaAccountInfoByAdvertisrId(MediaAccountInfo mediaAccountInfoQuery);

    /**
     * @param resultHandler
     * @Description 获取媒体账户信息
     * <AUTHOR>
     * @param:
     * @Date : 2024/6/5 14:17
     * @return: void
     */
    public void selectMediaAccountInfo(ResultHandler resultHandler);

    /**
     * @param mediaPlatformType 媒体类型
     * @param fund              是否获取余额
     * @param date              日期
     * @Description 获取媒体账户信息
     * <AUTHOR>
     * @param:
     * @Date : 2024/6/5 14:17
     * @return: void
     */
    public void findAdvertStatReport(@Param("mediaPlatformType") int mediaPlatformType, @Param("fund") int fund, @Param("date") String date, ResultHandler<MediaAccountInfo> resultHandler);

    /**
     * 查询accessToken未过期的媒体账号
     *
     * @param mediaType
     * @return
     */
    MediaAccountInfo selectAccessTokenUnexpiredOne(@Param("mediaType") int mediaType);


    int batchUpdateOceanLsAdvSubscribeInfoByAdvertiserId(@Param("idsList") List<Long> idsList, @Param("lsAdvSubscribe") boolean lsAdvSubscribe);


    List<MediaAccountInfo> selectSetConversion();

    /**
     * @param dto
     * @param id
     * @Description 获取媒体账户信息(定时任务使用)
     * <AUTHOR>
     * @Date : 2024/4/15 13:58
     * @return: java.util.List<com.panda.pollen.modules.ads.domain.MediaAccountInfo>
     */
    List<MediaAccountInfo> selectMediaAccountInfoLists(@Param("dto") MediaAccountDTO dto, @Param("id") Long id);

    List<MediaAccountInfo> test(@Param("dto") MediaAccountDTO dto, @Param("id") Long id, @Param("size") int i);

    /**
     * @param mediaPlatformType 媒体类型
     * @param id                主键ID
     * @param size              分页条数
     * @param startTime         报表开始时间
     * @param endTime           报表结束时间
     * @Description 查询行业字段为空的帐户
     * <AUTHOR>
     * @param:
     * @Date : 2024/8/30 14:01
     * @return: java.util.List<com.panda.pollen.modules.ads.domain.MediaAccountInfo>
     */
    List<MediaAccountInfo> selectMediaAccountInfoListChildWithNullIndustrys(@Param("mediaPlatformType") Integer mediaPlatformType,
                                                                            @Param("id") Long id,
                                                                            @Param("size") Integer size,
                                                                            @Param("startTime") String startTime,
                                                                            @Param("endTime") String endTime);

    /**
     * 查询部门下的媒体账户信息
     *
     * @param deptId
     * @return
     */
    List<MediaAccountInfoListVO> listMediaAccountInfoByDeptId(@Param("deptId") Long deptId, @Param("advertiserName") String advertiserName);

    /**
     * 批量更新名称
     *
     * @param mediaAccountInfo
     */
    void updateMediaAccountNameBatch(List<MediaAccountInfo> mediaAccountInfo);


    /**
     *
     * Description:
     * @param null
     * return: 查询重复的媒体平台用户ID+类型
     * author: quanzhihao
     * time: 2025/6/4 16:49
     */
    List<MediaAccountInfo> selectDuplicateMediaAccount();

    /**
     * 按各种查询条件查询媒体账户列表
     * @param conditionDTO
     * @return
     */
    List<MediaAccountInfo> selectMediaAccountListByCondition(MediaAccountConditionDTO conditionDTO);

    /**
     * 记录媒体账户报表拉取异常信息
     * @param advertiserId
     * @param code
     * @param errMsg
     */
    void recordMediaAccountReportStatisticsErrorMsg(@Param("advertiserId") String advertiserId, @Param("code") Integer code, @Param("errMsg") String errMsg);
}
