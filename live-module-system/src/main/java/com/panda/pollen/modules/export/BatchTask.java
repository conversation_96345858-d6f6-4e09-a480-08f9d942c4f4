package com.panda.pollen.modules.export;

import com.baomidou.mybatisplus.annotation.*;
import com.panda.pollen.common.base.BaseEntityV2;
import com.panda.pollen.common.enums.ads.MediaTypeEnum;
import com.panda.pollen.common.enums.advanced.export.BatchTaskCategory;
import com.panda.pollen.common.enums.advanced.export.BatchTaskStatus;
import com.panda.pollen.common.enums.advanced.export.BatchTaskType;
import com.panda.pollen.modules.ads.domain.FileResourceInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * 报表导出任务
 * <AUTHOR>
 * @Date : 2024年08月23日 17:01
 */
@Data
@TableName(value = "ads_batch_task")
public class BatchTask extends BaseEntityV2 implements Serializable {

    private static final long serialVersionUID = -903747352317299163L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 媒体类型
     * {@link MediaTypeEnum}
     */
    private Integer mediaType;

    /**
     * 导出任务类型
     */
    private BatchTaskType type;

    /**
     * 导出任务类别
     */
    private BatchTaskCategory category;

    /**
     * 导出状态
     */
    private BatchTaskStatus status;

    /**
     * 执行时的参数
     */
    private String executeParams;

    /**
     * 登录者用户信息
     */
    private String loginUserInfo;

    /**
     * 导出生成的文件资源id
     * 如果是下载类型的任务，执行成功，就会生成一条FileResourceInfo记录
     * {@link FileResourceInfo}
     */
    private Long fileResourceId;

    /**
     * 失败原因
     */
    private String failMsg;

    /**
     * 删除状态（1-删除 0-未删除 ）
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;


}
