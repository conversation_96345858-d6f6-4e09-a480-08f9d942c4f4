package com.panda.pollen.modules.domain.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panda.pollen.modules.ads.domain.DomainInfo;
import com.panda.pollen.modules.ads.vo.DomainInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-02
 */
public interface DomainInfoMapper extends BaseMapper<DomainInfo> {

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param adsDomainInfo 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<DomainInfo> selectDomainInfoList(DomainInfo adsDomainInfo);

    /**
     * 删除域名
     *
     * @param ids 主键ID集合
     * @return    影响条数
     */
    public int removeDomainByIds(@Param("ids")List<Long> ids);


    /**
     * 根据公司ID删除域名
     *
     * @param companyId
     * @return
     */
    public int removeDomainByCompanyId(DomainInfoVO vo);

    /**
     * 初始化域名信息
     */
    public int findDomainInfoByCompanyIdAndDomainUrl(@Param("companyId") Long companyId, @Param("domainUrl") String domainUrl);

    /**
     *  根据公司ID查询域名
     */
    public List<String> selectDomainUrlByCompanyId(Long companyId);

}
