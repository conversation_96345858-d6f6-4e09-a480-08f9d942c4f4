package com.panda.pollen.modules.export.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.common.core.domain.entity.SysUser;
import com.panda.pollen.common.core.domain.model.LoginUser;
import com.panda.pollen.common.enums.ads.MediaTypeEnum;
import com.panda.pollen.common.enums.advanced.export.BatchTaskCategory;
import com.panda.pollen.common.enums.advanced.export.BatchTaskStatus;
import com.panda.pollen.common.enums.advanced.export.BatchTaskType;
import com.panda.pollen.common.exception.ServiceException;
import com.panda.pollen.common.utils.SecurityUtils;
import com.panda.pollen.modules.export.BatchTask;
import com.panda.pollen.modules.export.mapper.BatchTaskMapper;
import com.panda.pollen.modules.export.service.IBatchTaskService;
import com.panda.pollen.modules.export.vo.BatchTaskListVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date : 2024年08月23日 18:22
 */
@Service
public class BatchTaskServiceImpl extends ServiceImpl<BatchTaskMapper, BatchTask> implements IBatchTaskService {

    /**
     * 查询“等待导出”的任务列表
     *
     * @param mediaTypes
     * @param taskTypes
     * @param notIn
     * @return
     */
    @Override
    public List<BatchTask> selectPendingList(JSONArray mediaTypes, JSONArray taskTypes, boolean notIn, List<BatchTaskStatus> status) {
        LambdaQueryWrapper<BatchTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BatchTask::getDeleted, 0);

        if (CollUtil.isNotEmpty(status)) {
            if (status.size() == 1) {
                queryWrapper.eq(BatchTask::getStatus, status.get(0));
            } else {
                queryWrapper.in(BatchTask::getStatus, status);
            }
        }
        if (CollUtil.isNotEmpty(mediaTypes)) {
            if (notIn) {
                queryWrapper.notIn(BatchTask::getMediaType, mediaTypes);
            } else {
                queryWrapper.in(BatchTask::getMediaType, mediaTypes);
            }
        }
        if (CollUtil.isNotEmpty(taskTypes)) {
            if (notIn) {
                queryWrapper.notIn(BatchTask::getType, taskTypes);
            } else {
                queryWrapper.in(BatchTask::getType, taskTypes);
            }

        }
        return list(queryWrapper);
    }

    /**
     * 新增批量任务
     *
     * @param mediaType
     * @param taskType
     * @param category
     * @param executeParams
     * @return
     */
    @Override
    public BatchTask saveBatchTask(int mediaType, BatchTaskType taskType, BatchTaskCategory category, String executeParams) {
        BatchTask batchTask = new BatchTask();
        batchTask.setType(taskType);
        batchTask.setCategory(category);
        batchTask.setExecuteParams(executeParams);
        batchTask.setStatus(BatchTaskStatus.PENDING);
        LoginUser loginUser = null;
        if(SecurityUtils.isUserLogin()){
            loginUser = SecurityUtils.getLoginUser();
            batchTask.setLoginUserInfo(JSON.toJSONString(loginUser));
        }
        // 媒体类型
        int mt = mediaType == 0 ? RandomUtil.randomInt(0, 5) : mediaType;
        batchTask.setMediaType(mt);
        // 检查是否存在已经重复的任务
        checkExists(batchTask, loginUser);
        save(batchTask);
        return batchTask;
    }

    /**
     * 检查是否存在重复的任务
     *
     * @param batchTask
     */
    private void checkExists(BatchTask batchTask, LoginUser loginUser) {
        LambdaQueryWrapper<BatchTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BatchTask::getMediaType, batchTask.getMediaType());
        queryWrapper.eq(BatchTask::getType, batchTask.getType());
        queryWrapper.eq(BatchTask::getCategory, batchTask.getCategory());
        queryWrapper.eq(BatchTask::getStatus, BatchTaskStatus.PENDING);
        queryWrapper.eq(BatchTask::getExecuteParams, batchTask.getExecuteParams());
        if(ObjectUtils.isNotEmpty(loginUser)){
            queryWrapper.eq(BatchTask::getCreateBy, loginUser.getUsername());
        }
        queryWrapper.eq(BatchTask::getDeleted, 0);
        int count = count(queryWrapper);
        if (count > 0) {
            throw new ServiceException("您已经存在相同的导出任务，如需了解任务进度，请前往右上角【任务中心】查看");
        }
    }

    /**
     * 查询任务列表
     *
     * @param batchTaskListVO 任务对象
     * @return 任务列表集合
     */
    @Override
    public List<BatchTaskListVO> selectBatchTaskList(BatchTaskListVO batchTaskListVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if(!user.isAdmin()){
            batchTaskListVO.setCreateBy(user.getUserName());
            // 普通人只看下载
            batchTaskListVO.setCategoryList(CollUtil.newArrayList(BatchTaskCategory.DOWNLOAD, BatchTaskCategory.VIEW));
        }
        if (batchTaskListVO.getMediaType() != null && batchTaskListVO.getMediaType() == MediaTypeEnum.UNKNOWN.getCode()) {
            batchTaskListVO.setMediaType(0);
        }
        List<BatchTaskListVO> batchTaskListVOList = baseMapper.selectBatchTaskList(batchTaskListVO);
        // 遍历设置媒体类型中文描述
        // 如果当前登录用户不是超级管理员，那么不返回报错原因
        for (BatchTaskListVO taskListVO : batchTaskListVOList) {
            Integer mediaType = taskListVO.getMediaType();
            if (mediaType == 0) {
                mediaType = -1;
            }
            taskListVO.setMediaTypeDesc(MediaTypeEnum.getDesc(mediaType));
            if (!user.isAdmin()) {
                taskListVO.setFailMsg("");
            }
        }
        return batchTaskListVOList;
    }

    /**
     * 删除素材
     *
     * @param ids 素材ID集合
     * @return 影响条数
     */
    @Override
    public int removeBatchTaskByIds(List<Long> ids) {
        return baseMapper.removeBatchTaskByIds(ids);
    }


}
