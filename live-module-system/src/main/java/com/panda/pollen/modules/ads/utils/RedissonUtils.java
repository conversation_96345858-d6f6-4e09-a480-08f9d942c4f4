package com.panda.pollen.modules.ads.utils;

import com.panda.pollen.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingDeque;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Redisson帮助类
 */
@Component
@Slf4j
public class RedissonUtils {

    @Autowired
    private RedissonClient redisson;


    public RDelayedQueue<Object> getDelayedQueueProducer(String queueName) {
        RBlockingDeque<Object> blockingDeque = redisson.getBlockingDeque(queueName);
        RDelayedQueue<Object> delayedQueue = redisson.getDelayedQueue(blockingDeque);
        return delayedQueue;
    }

    public RBlockingDeque<Object> getDelayedQueueConsumer(String queueName) {
        RBlockingDeque<Object> blockingDeque = redisson.getBlockingDeque(queueName);
        //会定时任务移动数据到RBlockingDeque
        redisson.getDelayedQueue(blockingDeque);
        return blockingDeque;
    }

    /**
     * 获取锁，如果获取不到锁，则抛出异常
     *
     * @param lockName
     * @return {@link RLock}
     */
    public RLock getLockThrowException(String lockName) {
        RLock lock = redisson.getLock(lockName);
        //判断未获取到锁的情况
        if (lock == null || !lock.tryLock()) {
            log.info("未获取到分布式锁");
            throw new ServiceException("执行中,请勿重复操作！");
        }
        return lock;
    }

    /**
     * 释放锁
     *
     * @param lock
     */
    public void unlock(RLock lock) {
        if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
