package com.panda.pollen.modules.export.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panda.pollen.modules.export.BatchTask;
import com.panda.pollen.modules.export.vo.BatchTaskListVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date : 2024年08月23日 17:43
 */
@Mapper
public interface BatchTaskMapper extends BaseMapper<BatchTask> {
    /**
     * 查询报表导出任务列表
     *
     * @param batchTask 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<BatchTaskListVO> selectBatchTaskList(BatchTask batchTask);

    /**
     * 删除素材
     *
     * @param ids 素材ID集合
     * @return    影响条数
     */
    public int removeBatchTaskByIds(List<Long> ids);
}
