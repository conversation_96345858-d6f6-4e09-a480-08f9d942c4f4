package com.panda.pollen.modules.domain.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panda.pollen.modules.ads.domain.DomainInfo;
import com.panda.pollen.modules.ads.vo.DomainInfoVO;

import java.util.List;

/**
 * 域名信息Service接口
 *
 * <AUTHOR>
 * @date 2024-09-02
 */
public interface IDomainInfoService extends IService<DomainInfo> {

    /**
     * 查询域名信息列表
     *
     * @param domainInfo 【域名信息
     * @return 域名信息集合
     */
    public List<DomainInfo> selectDomainInfoList(DomainInfo domainInfo);

    /**
     * 查询域名信息列表
     *
     * @return 域名信息 集合
     */
    public List<DomainInfo> findPendingReminderDomainList(DomainInfo domainInfo);

    /**
     * 删除域名
     *
     * @param ids 主键ID集合
     * @return 影响条数
     */
    public int removeDomainByIds(List<Long> ids);

    /**
     * 新增域名
     *
     * @param domainInfo 主键ID集合
     * @return 影响条数
     */
    public boolean add(DomainInfo domainInfo);

    /**
     * 发送钉钉通知
     * @param domainInfoList 待通知的域名列表
     */
    public void sendDingTalkNotification(List<DomainInfo> domainInfoList);

    /**
     * 更新域名
     *
     * @param domainInfo 主键ID集合
     * @return 影响条数
     */
    public boolean update(DomainInfo domainInfo);

    /**
     * 发送通知组消息
     *
     * @param domainsToNotify
     */
    public void sendNotice(List<DomainInfo> domainsToNotify);

    /**
     * 根据公司ID删除域名
     *
     * @param vo
     * @return
     */
    public void removeDomainByCompanyId(DomainInfoVO vo);

    /**
     * 初始化域名信息
     */
    public boolean findDomainInfoByCompanyIdAndDomainUrl(Long companyId, String domainUrl);

    /**
     * 查询域名信息
     *
     * @param companyId
     * @return
     */
    public String selectDomainUrlByCompanyId(Long companyId);

    /**
     * 获取一级部门域名
     * @param firstDeptId
     * @return
     */
    List<String> selectUserDomain(Long firstDeptId);

    /**
     * 根据公司更新域名信息
     *
     * @param domainInfo
     */
    public void updateDomainInfoByCompanyId(DomainInfo domainInfo);

}
