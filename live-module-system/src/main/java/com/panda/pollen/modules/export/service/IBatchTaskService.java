package com.panda.pollen.modules.export.service;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.extension.service.IService;
import com.panda.pollen.common.enums.advanced.export.BatchTaskCategory;
import com.panda.pollen.common.enums.advanced.export.BatchTaskStatus;
import com.panda.pollen.common.enums.advanced.export.BatchTaskType;
import com.panda.pollen.modules.export.BatchTask;
import com.panda.pollen.modules.export.vo.BatchTaskListVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date : 2024年08月23日 17:53
 */
public interface IBatchTaskService extends IService<BatchTask> {

    /**
     * 查询“等待导出”的任务列表
     *
     * @return
     */
    List<BatchTask> selectPendingList(JSONArray mediaTypes, JSONArray taskTypes, boolean notIn, List<BatchTaskStatus> status);

    /**
     * 新增批量任务
     *
     * @param mediaType
     * @param taskType
     * @param category
     * @param executeParams
     * @return
     */
    BatchTask saveBatchTask(int mediaType, BatchTaskType taskType, BatchTaskCategory category, String executeParams);

    /**
     * 查询报表导出任务列表
     *
     * @param batchTaskListVO 报表导出任务对象
     * @return 报表导出任务列表集合
     */
    List<BatchTaskListVO> selectBatchTaskList(BatchTaskListVO batchTaskListVO);

    /**
     * 删除任务
     *
     * @param ids 主键ID集合
     * @return 影响条数
     */
    int removeBatchTaskByIds(List<Long> ids);


}
