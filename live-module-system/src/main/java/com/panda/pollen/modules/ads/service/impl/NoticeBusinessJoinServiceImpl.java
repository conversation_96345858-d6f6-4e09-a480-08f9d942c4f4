package com.panda.pollen.modules.ads.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panda.pollen.modules.ads.domain.NoticeBusinessJoin;
import com.panda.pollen.modules.ads.dto.NoticeBusinessJoinDTO;
import com.panda.pollen.modules.ads.mapper.NoticeBusinessJoinMapper;
import com.panda.pollen.modules.ads.service.INoticeBusinessJoinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 通知组和部门或者用户的关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-07
 */
@Service
public class NoticeBusinessJoinServiceImpl extends ServiceImpl<NoticeBusinessJoinMapper, NoticeBusinessJoin> implements INoticeBusinessJoinService {

    @Autowired
    private NoticeBusinessJoinMapper noticeBusinessJoinMapper;

    @Override
    public Long businessJoinNoticeGroup(Long businessId,int businessType) {
        return noticeBusinessJoinMapper.businessJoinNoticeGroup(businessId,businessType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveNoticeBusinessJoin(NoticeBusinessJoinDTO noticeBusinessJoinDTO) {
        QueryWrapper<NoticeBusinessJoin> queryWrapper = new QueryWrapper<NoticeBusinessJoin>();
        queryWrapper.lambda().eq(NoticeBusinessJoin::getNoticeGroupId,noticeBusinessJoinDTO.getNoticeGroupId());
        this.remove(queryWrapper);
        for (Long businessId:
                noticeBusinessJoinDTO.getBusinessIds()) {
            NoticeBusinessJoin noticeBusinessJoin = new NoticeBusinessJoin();
            noticeBusinessJoin.setBusinessId(businessId);
            noticeBusinessJoin.setBusinessType(noticeBusinessJoinDTO.getBusinessType());
            noticeBusinessJoin.setNoticeGroupId(noticeBusinessJoinDTO.getNoticeGroupId());
            this.save(noticeBusinessJoin);
        }
    }
}
