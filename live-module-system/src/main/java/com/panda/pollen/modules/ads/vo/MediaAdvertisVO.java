package com.panda.pollen.modules.ads.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * ClassName：com.ruoyi.modules.ads.domain.vo.MediaAdvertisVO <br>
 * Description：广告素材组合VO<br>
 * <AUTHOR> <br>
 * date 2023年 05月29日 10:27 <br>
 * @version v1.0 <br>
 */
@Data
public class MediaAdvertisVO {
    /**
     * 链接类型(落地页)
     */
    @JsonProperty("params_type")
    private String paramsType ;

    /**
     * 落地页链接字段选择
     */
    @JsonProperty("external_url_field")
    private String externalUrlField ;

    /**
     * 落地页检测参数
     */
    @JsonProperty("external_url_params")
    private String externalUrlParams ;

    /**
     * 直达链接类型
     */
    @JsonProperty("open_url_type")
    private String openUrlType ;

    /**
     * 直达链接字段选择
     */
    @JsonProperty("open_url_field")
    private String openUrlField ;

    /**
     * 直达链接检测参数
     */
    @JsonProperty("open_url_params")
    private String openUrlParams ;

    /**
     * 直达链接，用于打开电商app，调起店铺
     */
    @JsonProperty("open_url")
    private String openUrl ;

    /**
     * 直达备用链接
     */
    @JsonProperty("ulink")
    private String ulink ;

    /**
     * 普通落地页链接素材
     */
    @JsonProperty("external_url_material_list")
    private String externalUrlMaterialList ;

    /**
     * 视频素材信息
     */
    @JsonProperty("video_material_list")
    private List<MediaMaterialVO> mediaMaterialVO ;
}
