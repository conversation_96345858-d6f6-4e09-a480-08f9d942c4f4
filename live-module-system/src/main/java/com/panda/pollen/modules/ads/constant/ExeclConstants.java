package com.panda.pollen.modules.ads.constant;

/**
 * ClassName:com.panda.pollen.modules.ads.constant
 * Description:导出Execl前缀名常量
 *
 * <AUTHOR> <br>
 * date 2023年 11月14日 15:05 <br>
 * @version v1.0 <br>
 */
public class ExeclConstants {

    /*汇总账户报表导出csv前缀名*/
    public static final String SUMMARY_REPORT_NAME = "汇总账户报表";

    /*分日账户报表导出csv前缀名*/
    public static final String NAME_OF_THE_DAILY_REPORT = "分日账户报表";

    /*用户报表导出csv前缀名*/
    public static final String USER_REPORT = "用户报表";

    /*公司报表导出CSV前缀名*/
    public static final String COMPANY_REPORT = "公司报表";

    /*导出商品信息前缀*/
    public static final String COMMODITY_INFORMATION = "商品信息";

    /*导出商品信息csv后缀名*/
    public static final String CSV_NAME = "_goods.csv";

    /*导出商品信息csv后缀名*/
    public static final String PLAN_REPORT_NAME = "_planReport.csv";

    /*导出订单结算csv前缀名*/
    public static final String NAME = "结算订单";

    /*导出拼多多订单csv前缀名*/
    public static final String PDD_ORDER = "拼多多订单";

    /*汇总计划报表导出csv前缀名*/
    public static final String SUMMARY_PLANNING_REPORT = "汇总计划报表";

    /*分日计划报表导出csv前缀名*/
    public static final String DAILY_PLAN_REPORT = "分日计划报表";

    /*商品报表导出csv前缀名*/
    public static final String COMMODITY_STATEMENT = "商品报表";

    /*素材报表导出csv前缀名*/
    public static final String MATERIAL_STATISTICS_REPORT = "素材报表";

    /**
     * 表单订单导出csv前缀名
     */
    public static final String FORMS_RESULTS_ORDER = "表单订单";

    /**
     * 表单订单结算导出csv前缀名
     */
    public static final String FORMS_RESULTS_ORDER_SETTLEMENT = "表单订单结算";

    /**
     * 财务消息
     */
    public static final String FINANCE_MESSAGE = "财务消息";
    /*导出淘宝订单csv后缀名*/
    public static final String TAOBAO_ORDER = "淘宝订单";

    /*导出流量通订单csv后缀名*/
    public static final String TAOBAO_PRO_ORDER = "流量通Pro订单";

    /*导出淘宝订单csv后缀名*/
    public static final String TAOBAO_SETTLEMENT_ORDER = "淘宝结算订单";

    /*导出商务报表csv前缀名*/
    public static final String BUSINESS_STATEMENT = "商务报表";

    /*导出京东列表订单csv前缀名*/
    public static final String JD_ORDER = "京东订单";

    /*导出佣金结算列表订单csv前缀名*/
    public static final String COMMISSION_SETTLEMENT = "佣金结算";

    /*导出佣金结算订单明细csv前缀名*/
    public static final String COMMISSION_SETTLEMENT_DETAIL = "佣金结算订单明细";

    /*缓存账户信息csv前缀名*/
    public static final String CACHE_ACCOUNT_STATEMENT = "缓存账户报表信息";

    /*公司销售日报csv前缀名*/
    public static final String COMPANY_SALES_DAILY = "利润报表";

    /*导出提现记录列表csv前缀名*/
    public static final String WITHDRAWAL_RECORD = "提现记录列表";

    /*导出用户佣金csv前缀名*/
    public static final String USER_COMMISSION = "消耗返佣";

    /*导出美团订单前缀名*/
    public static final String MT_ORDER = "美团订单";

    /*爆品推荐列表导出csv前缀名*/
    public static final String EXPLOSIVE_RECOMMENDATION = "爆品推荐";

    /*素材库列表导出csv前缀名*/
    public static final String MATERIAL_LIBRARY = "素材库";

    /*计划报表导出xlsx前缀名*/
    public static final String REPORT_LIBRARY = "计划报表";

    /*巨量报表导出csv前缀名*/
    public static final String PROJECT_STATEMENT = "巨量报表";

    /*店铺报表导出csv前缀名*/
    public static final String STORE_REPORT = "店铺报表";

    /*人群包订单信息导出csv后缀名*/
    public static final String CROWD_DATA_CSV = ".csv";
    /*人群包订单信息导出csv前缀名*/
    public static final String CROWD_DATA_ORDER_INFO = "人群包订单信息";

    /*导出熊猫良选订单前缀名*/
    public static final String PD_ORDER = "熊猫良选订单";

    /*汇总多多报表导出csv前缀名*/
    public static final String SUMMARY_MULTIPLE_REPORT = "汇总多多报表";

    /*分日多多报表导出csv前缀名*/
    public static final String DAY_BY_DAY_MANY_REPORTS = "分日多多报表";

    /*消耗报表导出csv前缀名*/
    public static final String CONSUMPTION_REPORT = "消耗报表";

    /*公司信息导出CSV前缀名*/
    public static final String COMPANY_INFORMATION = "公司信息";

    /*导出阿里大健康列表订单csv前缀名*/
    public static final String ALIHEALTH_ORDER = "阿里大健康";

    /*导出udSmart订单csv后缀名*/
    public static final String UDSMART_ORDER = "UdSmart订单";

    /*导出京东pro订单csv后缀名*/
    public static final String JD_PRO_ORDER = "京东pro订单";

    /*引流实时导出csv前缀名*/
    public static final String REALTIME_DRAINAGE = "实时引流数据";

    /*千川实时导出csv前缀名*/
    public static final String REALTIME_QIANCHUAN = "实时千川数据";

    /*导出淘宝打印订单csv后缀名*/
    public static final String TAOBAO_PRINT_ORDER = "淘宝打印订单";

    /*cid爆品t-1导出csv前缀名*/
    public static final String CID_PRODUCTS_DATA = "cid爆品t-1数据";

    /*淘宝爆品日更导出csv前缀名*/
    public static final String TAOBAO_DAILY_DATA = "淘宝爆款日更";

}
