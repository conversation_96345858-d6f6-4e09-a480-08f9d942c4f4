

package com.panda.pollen.modules.utils;

import com.panda.pollen.modules.ads.domain.MediaAccountInfo;
import com.panda.pollen.modules.ads.manger.RedisInfoManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 过滤广告账户--屏蔽30内未登陆的用户公司
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class MediaAccountInfoUtils {

    @Autowired
    RedisInfoManager redisInfoManager;

    public List<MediaAccountInfo> filterMediaAccountInfo(List<MediaAccountInfo> mediaAccountInfoList) {
        List<Long> firstIds = redisInfoManager.selectFirstDeptIds();
        List<MediaAccountInfo> newMediaAccountInfoList = new ArrayList<>();
        if (!mediaAccountInfoList.isEmpty()) {
            // 记录最后一个id
            MediaAccountInfo mediaAccountInfoLast = mediaAccountInfoList.get(mediaAccountInfoList.size() - 1);
            for (MediaAccountInfo mediaAccount : mediaAccountInfoList) {
                boolean isStatus = mediaAccount.getFirstDeptId() != null && firstIds.contains(mediaAccount.getFirstDeptId());
                if (isStatus) {
                    newMediaAccountInfoList.add(mediaAccount);
                }
            }
            // 如果是空则设置进最后一个
            if (newMediaAccountInfoList.isEmpty()) {
                newMediaAccountInfoList.add(mediaAccountInfoLast);
            } else {
                MediaAccountInfo mediaAccountInfo = newMediaAccountInfoList.get(newMediaAccountInfoList.size() - 1);
                if (!Objects.equals(mediaAccountInfo.getId(), mediaAccountInfoLast.getId())) {
                    newMediaAccountInfoList.add(mediaAccountInfoLast);
                }
            }
        }
        return newMediaAccountInfoList;
    }

}
