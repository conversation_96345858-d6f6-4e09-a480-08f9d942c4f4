/**  
 * All rights Reserved, Designed By <br>
 * Title：ThridAuthConfig.java <br>
 * Package：com.panda.pollen.modules.system.domain <br>
 * Description：(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年3月6日 下午3:16:24 <br>
 * @version v1.0 <br>
 */ 
package com.panda.pollen.modules.domain;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.panda.pollen.common.utils.collect.ListUtils;
import com.panda.pollen.common.utils.collect.MapUtils;

import lombok.Data;

/**   
 * ClassName：com.panda.pollen.modules.system.domain.ThridAuthConfig <br>
 * Description：第三方授权登录配置 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年3月6日 下午3:16:24 <br>
 * @version v1.0 <br>  
 */
@Data
public class ThridAuthConfig {
	
	private static Map<String, ThridAuthConfig> configs = null;

	/** 授权资源名称，需要与AuthUtils.getAuthRequest参数source对应*/
	private String source;
	
	private List<ThridAuthorizes> authorizes;
	
	public static Map<String, ThridAuthConfig> initialize() {
		if(MapUtils.isEmpty(configs)) {
			configs = MapUtils.newHashMap();
			ThridAuthConfig wechatOpen = initWechatOpen();
			configs.put(wechatOpen.getSource(), wechatOpen);
		}
		return configs;
	}
	
	public static Map<String, ThridAuthorizes> get(String source){
		ThridAuthConfig config = getBySource(source);
		return config.getAuthorizes().stream().collect(Collectors.toMap(ThridAuthorizes::getApplyDomain, a -> a));
	}
	
	public static ThridAuthConfig getBySource(String source) {
		if(MapUtils.isEmpty(configs)) {
			initialize();
		}
		return configs.get(source);
	}
	
	public ThridAuthConfig(String source) {
		this.source = source;
	}
	
	public ThridAuthConfig add(ThridAuthorizes auth) {
		if(authorizes == null) {
			authorizes = ListUtils.newArrayList();
		}
		authorizes.add(auth);
		return this;
	}
	
	/**
	 * Description：微信开放平台配置初始化 <br>
	 * author：罗江林 <br>
	 * date：2024年3月6日 下午3:25:58 <br>
	 * @return <br>
	 */
	private static ThridAuthConfig initWechatOpen() {
		ThridAuthConfig wechat = new ThridAuthConfig("wechat_open");
		wechat.add(ThridAuthorizes.builder().clientId("wxfc2b4dd3b1193c93").clientSecret("16b119e38ad67623959fae14ed9bd90f").redirectUri("https://pangdasc.com/social-login?source=wechat_open").applyDomain("pangdasc.com").build())
		.add(ThridAuthorizes.builder().clientId("wx53c15b1abf24acd5").clientSecret("2e9fbd3a206e16d7df2cc82664842a47").redirectUri("https://cmcid.pangdaup.com/social-login?source=wechat_open").applyDomain("cmcid.pangdaup.com").build())
		.add(ThridAuthorizes.builder().clientId("wx53c15b1abf24acd5").clientSecret("2e9fbd3a206e16d7df2cc82664842a47").redirectUri("https://blue.pangdaup.com/social-login?source=wechat_open").applyDomain("blue.pangdaup.com").build())
		.add(ThridAuthorizes.builder().clientId("wxd5809e847ef299be").clientSecret("9fba711bb00764e70ed67c05923f532d").redirectUri("https://test.fanspvt.com/social-login?source=wechat_open").applyDomain("test.fanspvt.com").build())
		.add(ThridAuthorizes.builder().clientId("wx6e8dcb548dbbaf5c").clientSecret("300d8336451d0f638d04cd611a7ada89").redirectUri("https://fanspvt.com/social-login?source=wechat_open").applyDomain("fanspvt.com").build());
		return wechat;
	}

}
