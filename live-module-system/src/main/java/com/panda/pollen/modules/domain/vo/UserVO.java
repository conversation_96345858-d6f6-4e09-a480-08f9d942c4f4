package com.panda.pollen.modules.domain.vo;

import java.math.BigDecimal;

import com.panda.pollen.common.encrypt.annotation.EncryptField;
import lombok.Data;

/**   
 * ClassName：com.panda.pollen.modules.system.domain.vo.UserVO <br>
 * Description：用户缓存VO <br>
 * Copyright © 2023 eternal.net Inc. All rights reserved. <br>
 * Company：Eternal Fire Team <br>
 * <AUTHOR> <br>
 * date 2023年7月3日 下午9:50:21 <br>
 * @version v1.0 <br>  
 */
@Data
public class UserVO {

    private Long userId;
    
    /** 部门ID */
    private Long deptId;
    
    private String userName;
    
    private String deptName;
    
    private Long firstDeptId;
    
    private String firstDeptName;

    private String nickName;

    @EncryptField()
    private String phonenumber;

    //商品id,因为淘宝订单没有数字id
    private String goodsId;
    
    private BigDecimal commissionRate;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;
    
}
