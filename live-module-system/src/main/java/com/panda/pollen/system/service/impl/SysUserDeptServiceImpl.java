package com.panda.pollen.system.service.impl;

import com.panda.pollen.modules.domain.vo.UserVO;
import com.panda.pollen.system.mapper.SysUserMapper;
import com.panda.pollen.system.service.ISysUserDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SysUserDeptServiceImpl implements ISysUserDeptService {

    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    public UserVO selectByGoodsInfo(String goodsId, String platform, Integer mediaPlatformType) {
        return sysUserMapper.selectByGoodsInfo(goodsId,platform,mediaPlatformType);
    }

}
