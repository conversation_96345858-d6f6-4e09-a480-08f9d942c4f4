package com.panda.pollen.system.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.panda.pollen.system.domain.DataLogs;

/**
 * 数据日志Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-17
 */
public interface IDataLogsService extends IService<DataLogs> {

    /**
     * 查询数据日志列表
     * 
     * @param dataLogs 数据日志
     * @return 数据日志集合
     */
    public List<DataLogs> queryDataLogsList(DataLogs dataLogs);
    
}
