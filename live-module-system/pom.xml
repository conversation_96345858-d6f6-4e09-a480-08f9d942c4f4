<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.panda.pollen</groupId>
        <artifactId>pollen</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>live-module-system</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>
        <!-- mongodb -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>ocean-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>tencent-common</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <!--解决lombok与mapstruct一起使用问题-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <version>0.2.0</version>
            <scope>provided</scope>
        </dependency>

        <!--mapstruct-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.6.3</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.6.3</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.panda.pollen</groupId>
            <artifactId>live-module-leaf</artifactId>
        </dependency>

    </dependencies>
</project>